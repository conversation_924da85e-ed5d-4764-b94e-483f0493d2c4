<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= $title ?></h1>
        <div>
            <?php if (isset($business['id'])): ?>
                <a href="<?= BASE_URL ?>admin/compliance/business/<?= $business['id'] ?>" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Business Evidence
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="card border-0 rounded-3 shadow-sm mb-4">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
             <h5 class="mb-0">Evidence Details</h5>
        </div>
        <div class="card-body">
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (empty($evidence)): ?>
                <p class="text-center">Evidence record not found.</p>
            <?php else: ?>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6>Business:</h6>
                        <p><?= htmlspecialchars($business['name'] ?? 'N/A') ?></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Compliance Type:</h6>
                        <p><?= htmlspecialchars($compliance_types[$evidence['compliance_type']] ?? $evidence['compliance_type'] ?? 'N/A') ?></p>
                    </div>
                </div>

                <div class="row mb-3">
                     <div class="col-md-6">
                        <h6>Uploaded At:</h6>
                        <p><?= isset($evidence['created_at']) ? date('M d, Y H:i', strtotime($evidence['created_at'])) : 'N/A' ?></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Current Status:</h6>
                         <p>
                            <span class="badge bg-<?= $evidence['status'] === 'verified' ? 'success' : ($evidence['status'] === 'rejected' ? 'danger' : 'warning') ?>">
                                <?= ucfirst($evidence['status'] ?? 'N/A') ?>
                            </span>
                        </p>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>Uploaded Photo:</h6>
                    <?php if (isset($evidence['photo_path'])): ?>
                        <a href="<?= BASE_URL . htmlspecialchars($evidence['photo_path']) ?>" target="_blank">
                            <img src="<?= BASE_URL . htmlspecialchars($evidence['photo_path']) ?>" alt="Evidence Photo" class="img-fluid" style="max-height: 300px;">
                        </a>
                    <?php else: ?>
                        <p>No photo uploaded.</p>
                    <?php endif; ?>
                </div>

                <hr>

                <h5>Verification</h5>
                <form action="<?= BASE_URL ?>admin/compliance/verify/<?= $evidence['id'] ?? '' ?>" method="POST">
                    <div class="mb-3">
                        <label for="status" class="form-label">Verification Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="verified" <?= (isset($evidence['status']) && $evidence['status'] === 'verified') ? 'selected' : '' ?>>Verified</option>
                            <option value="rejected" <?= (isset($evidence['status']) && $evidence['status'] === 'rejected') ? 'selected' : '' ?>>Rejected</option>
                            <option value="pending" <?= (isset($evidence['status']) && $evidence['status'] === 'pending') ? 'selected' : '' ?>>Pending</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Verification Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4"><?= htmlspecialchars($evidence['verification_notes'] ?? '') ?></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">Submit Verification</button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>
