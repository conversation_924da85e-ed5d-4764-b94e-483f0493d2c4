-- Sample Business Accounts and Businesses (Corrected Version)
-- All accounts use password: test123 (hashed)
-- Password hash for 'test123': $2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

-- Insert Business Owner Users
INSERT INTO users (id, email, password_hash, full_name, role, status, created_at) VALUES
-- District 1 - Bacoor West Business Owners
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON>', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON>', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ana Reyes', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Carlos Garcia', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Elena Martinez', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Roberto Lopez', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sofia Hernandez', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Miguel Torres', 'business_owner', 'active', NOW()),

-- District 2 - Bacoor East Business Owners
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Carmen Flores', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Diego Morales', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lucia Rivera', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Fernando Castro', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Patricia Jimenez', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ricardo Mendoza', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Gabriela Vargas', 'business_owner', 'active', NOW()),
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Antonio Ruiz', 'business_owner', 'active', NOW());

-- Insert Businesses with complete information
INSERT INTO businesses (id, owner_id, owner_name, name, registration_number, email, contact_number, address, category_id, barangay_id, employee_count, date_established, status, compliance_status, created_at) VALUES

-- District 1 - Bacoor West Businesses
(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Maria Santos', 'Santos Construction & Supply', 'BC-CONST-2024-001', '<EMAIL>', '***********', '123 Rizal Street, Alima, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Construction'), (SELECT id FROM barangays WHERE name = 'Alima'), 25, '2018-03-15', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Juan Dela Cruz', 'Dela Cruz Food Corner', 'BC-FOOD-2024-002', '<EMAIL>', '***********', '456 Bonifacio Avenue, Banalo, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Food & Beverage'), (SELECT id FROM barangays WHERE name = 'Banalo'), 12, '2020-06-20', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Ana Reyes', 'Reyes Manufacturing Corp', 'BC-MANUF-2024-003', '<EMAIL>', '***********', '789 Industrial Road, Habay I, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Manufacturing'), (SELECT id FROM barangays WHERE name = 'Habay I'), 45, '2015-09-10', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Carlos Garcia', 'Garcia Auto Repair Shop', 'BC-AUTO-2024-004', '<EMAIL>', '***********', '321 Mechanic Street, Ligas I, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Automotive'), (SELECT id FROM barangays WHERE name = 'Ligas I'), 8, '2019-11-05', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Elena Martinez', 'Martinez Beauty Salon & Spa', 'BC-BEAUTY-2024-005', '<EMAIL>', '***********', '654 Beauty Lane, Mabolo, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Beauty & Wellness'), (SELECT id FROM barangays WHERE name = 'Mabolo'), 15, '2021-02-14', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Roberto Lopez', 'Lopez General Merchandise', 'BC-RETAIL-2024-006', '<EMAIL>', '***********', '987 Commerce Street, Niog, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Retail'), (SELECT id FROM barangays WHERE name = 'Niog'), 20, '2017-08-22', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Sofia Hernandez', 'Hernandez Learning Center', 'BC-EDU-2024-007', '<EMAIL>', '***********', '147 Education Drive, Real, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Education'), (SELECT id FROM barangays WHERE name = 'Real'), 18, '2019-05-30', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Miguel Torres', 'Torres Tech Solutions', 'BC-TECH-2024-008', '<EMAIL>', '***********', '258 Technology Park, Salinas I, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Technology'), (SELECT id FROM barangays WHERE name = 'Salinas I'), 30, '2020-01-15', 'active', 'pending_review', NOW()),

-- District 2 - Bacoor East Businesses
(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Carmen Flores', 'Flores Family Restaurant', 'BC-REST-2024-009', '<EMAIL>', '***********', '369 Food Street, Bayanan, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Food & Beverage'), (SELECT id FROM barangays WHERE name = 'Bayanan'), 22, '2018-07-12', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Diego Morales', 'Morales Transport Services', 'BC-TRANS-2024-010', '<EMAIL>', '***********', '741 Highway Road, Mambog 1, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Transportation'), (SELECT id FROM barangays WHERE name = 'Mambog 1'), 35, '2016-04-08', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Lucia Rivera', 'Rivera Medical Clinic', 'BC-MED-2024-011', '<EMAIL>', '***********', '852 Health Avenue, Molino I, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Healthcare'), (SELECT id FROM barangays WHERE name = 'Molino I'), 28, '2017-12-03', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Fernando Castro', 'Castro Entertainment Center', 'BC-ENT-2024-012', '<EMAIL>', '***********', '963 Entertainment Plaza, Molino III, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Entertainment'), (SELECT id FROM barangays WHERE name = 'Molino III'), 40, '2019-09-18', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Patricia Jimenez', 'Jimenez Financial Services', 'BC-FIN-2024-013', '<EMAIL>', '***********', '159 Banking Street, Molino V, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Financial Services'), (SELECT id FROM barangays WHERE name = 'Molino V'), 16, '2020-03-25', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Ricardo Mendoza', 'Mendoza Farm Supply', 'BC-AGRI-2024-014', '<EMAIL>', '***********', '357 Agricultural Road, Queens Row Central, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Agriculture'), (SELECT id FROM barangays WHERE name = 'Queens Row Central'), 12, '2018-10-14', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Gabriela Vargas', 'Vargas Hotel & Resort', 'BC-HOTEL-2024-015', '<EMAIL>', '***********', '468 Resort Boulevard, Queens Row East, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Hospitality'), (SELECT id FROM barangays WHERE name = 'Queens Row East'), 55, '2015-06-07', 'active', 'pending_review', NOW()),

(UUID(), (SELECT id FROM users WHERE email = '<EMAIL>'), 'Antonio Ruiz', 'Ruiz Legal & Accounting Services', 'BC-PROF-2024-016', '<EMAIL>', '***********', '579 Professional Center, Queens Row West, Bacoor, Cavite', (SELECT id FROM business_categories WHERE name = 'Professional Services'), (SELECT id FROM barangays WHERE name = 'Queens Row West'), 14, '2021-01-20', 'active', 'pending_review', NOW());

-- Verify the data
SELECT 'Total Business Owners' as info, COUNT(*) as count FROM users WHERE role = 'business_owner'
UNION ALL
SELECT 'Total Businesses' as info, COUNT(*) as count FROM businesses;

-- Show business summary
SELECT 
    b.name as business_name,
    b.owner_name,
    u.email as owner_email,
    bc.name as category,
    br.name as barangay,
    b.employee_count,
    b.status
FROM businesses b
JOIN users u ON b.owner_id = u.id
JOIN business_categories bc ON b.category_id = bc.id
JOIN barangays br ON b.barangay_id = br.id
ORDER BY br.name, b.name;
