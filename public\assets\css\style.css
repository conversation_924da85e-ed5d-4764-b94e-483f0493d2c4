/* Main Styles */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --sidebar-width: 250px;
    --topbar-height: 70px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* Wrapper */
.wrapper {
    display: flex;
    min-height: 100vh;
}

/* Main Content */
.main-content {
    flex: 1;
    min-width: 0;
    background-color: #f8f9fa;
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s ease;
}

/* Topbar */
.topbar {
    height: var(--topbar-height);
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,.075);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.toggle-sidebar {
    cursor: pointer;
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.user-profile {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-profile span {
    font-weight: 600;
    color: var(--dark-color);
}

.user-profile small {
    color: var(--secondary-color);
}

/* Main Content Area */
main {
    padding: 1.5rem;
}

/* Cards */
.card {
    background-color: #fff;
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem rgba(0, 0, 0, 0.15);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h6 {
    margin: 0;
    font-weight: 700;
    color: var(--primary-color);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table thead th {
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    color: var(--secondary-color);
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e3e6f0;
}

/* Buttons */
.btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.35rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Forms */
.form-control {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
    
    .sidebar {
        margin-left: calc(var(--sidebar-width) * -1);
    }
    
    .sidebar.active {
        margin-left: 0;
    }
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.3rem 0.6rem;
    margin: 0 0.2rem;
    border-radius: 0.35rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white !important;
}

/* Status Badges */
.badge {
    padding: 0.35rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.25rem;
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

/* Action Buttons */
.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    margin: 0 0.2rem;
}

/* ==========================================================================
   Layout styles
   ========================================================================== */
.layout {
    display: flex;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.layout.no-sidebar .main-content {
    margin-left: 0;
    width: 100%;
}

.main-content {
    flex: 1;
    margin-left: 250px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

/* ==========================================================================
   Base styles
   ========================================================================== */
body {
    font-size: .875rem;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ==========================================================================
   Sidebar styles
   ========================================================================== */
.sidebar {
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    background: #2c3e50;
    color: #fff;
    z-index: 100;
    transition: all 0.3s ease;
    box-shadow: 2px 0 5px rgba(0,0,0,.1);
}

.sidebar-header {
    padding: 1.5rem;
    text-align: center;
    background: #243444;
    border-bottom: 1px solid rgba(255,255,255,.1);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #fff;
    font-weight: 500;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-item {
    margin: 0.2rem 0;
}

.sidebar-item a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-item a:hover {
    color: #fff;
    background: rgba(255,255,255,.1);
}

.sidebar-item.active a {
    color: #fff;
    background: rgba(255,255,255,.1);
    border-left: 4px solid #3498db;
}

.sidebar-item i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,.1);
}

.sidebar-footer a {
    display: flex;
    align-items: center;
    color: rgba(255,255,255,.7);
    text-decoration: none;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.sidebar-footer a:hover {
    color: #fff;
    background: rgba(255,255,255,.1);
}

/* ==========================================================================
   Table styles
   ========================================================================== */
.table {
    margin-bottom: 0;
}

.table thead th {
    border-top: none;
    border-bottom-width: 1px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    color: #6c757d;
}

/* ==========================================================================
   Form styles
   ========================================================================== */
.form-label {
    font-weight: 500;
    margin-bottom: .5rem;
}

.form-control:focus {
    border-color: #2470dc;
    box-shadow: 0 0 0 0.2rem rgba(36, 112, 220, 0.25);
}

/* ==========================================================================
   Button styles
   ========================================================================== */
.btn-group-sm > .btn i,
.btn-sm i {
    margin-right: .3rem;
}

.btn-primary {
    background-color: #2470dc;
    border-color: #2470dc;
}

.btn-primary:hover {
    background-color: #1b5bb9;
    border-color: #1b5bb9;
}

/* ==========================================================================
   Alert styles
   ========================================================================== */
.alert {
    border: none;
    border-radius: .25rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-dismissible .btn-close {
    padding: 1.25rem;
}

/* ==========================================================================
   Utility classes
   ========================================================================== */
.border-bottom {
    border-bottom: 1px solid #e9ecef !important;
}

.bg-dark {
    background-color: #212529 !important;
}

/* ==========================================================================
   Responsive adjustments
   ========================================================================== */
@media (max-width: 991.98px) {
    .toggle-sidebar {
        display: block;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .layout.no-sidebar .main-content {
        margin-left: 0;
    }
}

/* General Styles */
body {
    background-color: #f4f4f4;
}

.auth-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: #555;
}

input[type="text"],
input[type="password"],
input[type="email"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.btn {
    width: 100%;
    padding: 10px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn:hover {
    background: #0056b3;
}

.alert {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
}

.alert.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

p {
    text-align: center;
    margin-top: 15px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Header styles */
header {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo h1 {
    color: white;
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 20px;
}

.nav-links a {
    color: white;
}

.nav-links a:hover {
    text-decoration: underline;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

/* Dashboard cards */
.dashboard-cards, .admin-actions {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.card h2 {
    margin-bottom: 10px;
    color: #2c3e50;
}

/* Footer */
footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 50px;
}

/* Error page */
.error-container {
    text-align: center;
    margin-top: 50px;
}

.error-container h1 {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 20px;
}

.error-container p {
    font-size: 1.2rem;
    margin-bottom: 20px;
}

/* Table styles */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th, .table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.table tr:hover {
    background-color: #f5f5f5;
}

/* Badge styles */
.badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

/* Form styles */
.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
}

textarea.form-control {
    min-height: 100px;
}

/* Card styles */
.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
}

.card-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
}

/* Button styles */
.btn-sm {
    padding: 5px 10px;
    font-size: 14px;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    color: white;
}

/* Utility classes */
.mb-3 {
    margin-bottom: 15px;
}

.mb-4 {
    margin-bottom: 20px;
}

/* Responsive grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Business Table Styles */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th, .table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.table tr:hover {
    background-color: #f5f5f5;
}

/* Form Styles */
.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
}

textarea.form-control {
    min-height: 100px;
}

/* Button Styles */
.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Document Styles */
.list-group {
    margin-bottom: 20px;
}

.list-group-item {
    padding: 10px 15px;
}

.badge {
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 12px;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.form-control-file {
    display: block;
    width: 100%;
    padding: 10px 0;
}

/* Admin Styles */
.card {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.card-header {
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.card-body {
    padding: 20px;
}

/* Badge Styles */
.badge {
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 12px;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

/* Inspection Styles */
.table th, .table td {
    vertical-align: middle;
}

.datetime-input {
    position: relative;
}

.datetime-input input {
    padding-right: 30px;
}

.datetime-input::after {
    content: "📅";
    position: absolute;
    right: 10px;
    top: 35px;
    pointer-events: none;
}

/* Status Badges */
.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

/* Dashboard Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    padding-bottom: 10px;
}

.card-header h3 {
    margin: 0;
    color: #333;
}

.card-body {
    padding: 10px 0;
}

.list-group {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-group-item {
    padding: 15px;
    border: 1px solid #ddd;
    margin-bottom: -1px;
    background: #fff;
}

.list-group-item:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.list-group-item:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    margin-bottom: 0;
}

.badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.badge-warning {
    background-color: #ffc107;
    color: #000;
}

.badge-info {
    background-color: #17a2b8;
    color: #fff;
}

.float-right {
    float: right;
}

/* Navigation Styles */
header {
    background: #333;
    padding: 1rem;
    color: white;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 20px;
    margin: 0;
    padding: 0;
}

.nav-links a {
    color: white;
    text-decoration: none;
}

.nav-links a:hover {
    color: #007bff;
}

/* Footer Styles */
footer {
    text-align: center;
    padding: 20px;
    background: #333;
    color: white;
    margin-top: 40px;
}

/* Main Content Area */
main {
    flex: 1;
    padding: 20px;
    background: #f5f7fa;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 20px;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-weight: bold;
}

.card-body {
    padding: 20px;
}

/* Add this to existing style.css */
body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
}

/* Avatar */
.avatar-sm {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.bg-primary-subtle {
    background-color: rgba(78, 115, 223, 0.1);
}

.bg-success-subtle {
    background-color: rgba(28, 200, 138, 0.1);
}

.bg-warning-subtle {
    background-color: rgba(246, 194, 62, 0.1);
}

.bg-danger-subtle {
    background-color: rgba(231, 74, 59, 0.1);
}

/* Status badges */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* Gap utility */
.gap-2 {
    gap: 0.5rem;
}

/* Table improvements */
.table > :not(caption) > * > * {
    padding: 1rem;
}

.datatable thead th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
}

/* Empty state */
.empty-state {
    padding: 3rem 0;
    text-align: center;
}

.empty-state img {
    max-width: 200px;
    margin-bottom: 1.5rem;
}

/* Button improvements */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn i {
    font-size: 0.875rem;
}

/* Spacing utilities */
.me-1 {
    margin-right: 0.25rem !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

/* Text utilities */
.text-gray-800 {
    color: #2d3748;
}

.font-weight-bold {
    font-weight: 700 !important;
}


