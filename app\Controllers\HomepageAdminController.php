<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\HomepageContent;

class HomepageAdminController extends Controller {
    protected $auth;
    protected $homepageModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->auth->requireAdmin();
        $this->homepageModel = new HomepageContent();
    }

    /**
     * Display homepage content management
     */
    public function index() {
        $content = $this->homepageModel->getAllContent();
        $announcements = $this->homepageModel->getAnnouncements();
        
        return $this->render('admin/homepage/index', [
            'title' => 'Homepage Management',
            'content' => $content,
            'announcements' => $announcements
        ]);
    }

    /**
     * Show form to create new content section
     */
    public function createContent() {
        return $this->render('admin/homepage/create_content', [
            'title' => 'Create Homepage Content'
        ]);
    }

    /**
     * Store new content section
     */
    public function storeContent() {
        if (!$this->isPost()) {
            return $this->redirect('admin/homepage');
        }

        $data = [
            'page' => $_POST['page'] ?? 'home',
            'section_type' => $_POST['section_type'] ?? 'content',
            'title' => $_POST['title'] ?? '',
            'content' => $_POST['content'] ?? '',
            'image_url' => $_POST['image_url'] ?? '',
            'button_text' => $_POST['button_text'] ?? '',
            'button_url' => $_POST['button_url'] ?? '',
            'sort_order' => intval($_POST['sort_order'] ?? 0)
        ];

        // Validate required fields
        if (empty($data['title'])) {
            $this->setFlash('error', 'Title is required.');
            return $this->redirect('admin/homepage/create-content');
        }

        try {
            if ($this->homepageModel->createContent($data)) {
                $this->setFlash('success', 'Content section created successfully.');
                return $this->redirect('admin/homepage');
            } else {
                $this->setFlash('error', 'Failed to create content section.');
                return $this->redirect('admin/homepage/create-content');
            }
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::storeContent: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while creating content.');
            return $this->redirect('admin/homepage/create-content');
        }
    }

    /**
     * Show form to edit content section
     */
    public function editContent($id) {
        $content = $this->homepageModel->getContentById($id);
        
        if (!$content) {
            $this->setFlash('error', 'Content section not found.');
            return $this->redirect('admin/homepage');
        }

        return $this->render('admin/homepage/edit_content', [
            'title' => 'Edit Homepage Content',
            'content' => $content
        ]);
    }

    /**
     * Update content section
     */
    public function updateContent($id) {
        if (!$this->isPost()) {
            return $this->redirect('admin/homepage');
        }

        $data = [
            'title' => $_POST['title'] ?? '',
            'content' => $_POST['content'] ?? '',
            'image_url' => $_POST['image_url'] ?? '',
            'button_text' => $_POST['button_text'] ?? '',
            'button_url' => $_POST['button_url'] ?? '',
            'sort_order' => intval($_POST['sort_order'] ?? 0)
        ];

        // Validate required fields
        if (empty($data['title'])) {
            $this->setFlash('error', 'Title is required.');
            return $this->redirect("admin/homepage/edit-content/$id");
        }

        try {
            if ($this->homepageModel->updateContent($id, $data)) {
                $this->setFlash('success', 'Content section updated successfully.');
                return $this->redirect('admin/homepage');
            } else {
                $this->setFlash('error', 'Failed to update content section.');
                return $this->redirect("admin/homepage/edit-content/$id");
            }
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::updateContent: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while updating content.');
            return $this->redirect("admin/homepage/edit-content/$id");
        }
    }

    /**
     * Delete content section
     */
    public function deleteContent($id) {
        if (!$this->isPost()) {
            $this->setFlash('error', 'Invalid request method.');
            return $this->redirect('admin/homepage');
        }

        try {
            if ($this->homepageModel->deleteContent($id)) {
                $this->setFlash('success', 'Content section deleted successfully.');
            } else {
                $this->setFlash('error', 'Failed to delete content section.');
            }
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::deleteContent: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while deleting content.');
        }

        return $this->redirect('admin/homepage');
    }

    /**
     * Show form to create new announcement
     */
    public function createAnnouncement() {
        return $this->render('admin/homepage/create_announcement', [
            'title' => 'Create Announcement'
        ]);
    }

    /**
     * Store new announcement
     */
    public function storeAnnouncement() {
        if (!$this->isPost()) {
            return $this->redirect('admin/homepage');
        }

        $data = [
            'title' => $_POST['title'] ?? '',
            'content' => $_POST['content'] ?? '',
            'image_url' => $_POST['image_url'] ?? '',
            'publish_date' => $_POST['publish_date'] ?? null,
            'expire_date' => $_POST['expire_date'] ?? null,
            'status' => $_POST['status'] ?? 'active',
            'author_id' => $this->auth->getUser()['id']
        ];

        // Validate required fields
        if (empty($data['title']) || empty($data['content'])) {
            $this->setFlash('error', 'Title and content are required.');
            return $this->redirect('admin/homepage/create-announcement');
        }

        try {
            $stmt = $this->homepageModel->db->prepare("
                INSERT INTO announcements (title, content, image_url, author_id, publish_date, expire_date, status, created_at)
                VALUES (:title, :content, :image_url, :author_id, :publish_date, :expire_date, :status, NOW())
            ");
            
            if ($stmt->execute([
                ':title' => $data['title'],
                ':content' => $data['content'],
                ':image_url' => $data['image_url'],
                ':author_id' => $data['author_id'],
                ':publish_date' => $data['publish_date'],
                ':expire_date' => $data['expire_date'],
                ':status' => $data['status']
            ])) {
                $this->setFlash('success', 'Announcement created successfully.');
                return $this->redirect('admin/homepage');
            } else {
                $this->setFlash('error', 'Failed to create announcement.');
                return $this->redirect('admin/homepage/create-announcement');
            }
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::storeAnnouncement: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while creating announcement.');
            return $this->redirect('admin/homepage/create-announcement');
        }
    }

    /**
     * Delete announcement
     */
    public function deleteAnnouncement($id) {
        if (!$this->isPost()) {
            $this->setFlash('error', 'Invalid request method.');
            return $this->redirect('admin/homepage');
        }

        try {
            $stmt = $this->homepageModel->db->prepare("DELETE FROM announcements WHERE id = :id");
            if ($stmt->execute([':id' => $id])) {
                $this->setFlash('success', 'Announcement deleted successfully.');
            } else {
                $this->setFlash('error', 'Failed to delete announcement.');
            }
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::deleteAnnouncement: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while deleting announcement.');
        }

        return $this->redirect('admin/homepage');
    }

    /**
     * View contact messages
     */
    public function contactMessages() {
        try {
            $stmt = $this->homepageModel->db->query("
                SELECT * FROM contact_messages 
                ORDER BY created_at DESC
            ");
            $messages = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            return $this->render('admin/homepage/contact_messages', [
                'title' => 'Contact Messages',
                'messages' => $messages
            ]);
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::contactMessages: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while loading contact messages.');
            return $this->redirect('admin/homepage');
        }
    }

    /**
     * Mark contact message as read
     */
    public function markMessageRead($id) {
        if (!$this->isPost()) {
            return $this->redirect('admin/homepage/contact-messages');
        }

        try {
            $stmt = $this->homepageModel->db->prepare("
                UPDATE contact_messages 
                SET status = 'read' 
                WHERE id = :id
            ");
            
            if ($stmt->execute([':id' => $id])) {
                $this->setFlash('success', 'Message marked as read.');
            } else {
                $this->setFlash('error', 'Failed to update message status.');
            }
        } catch (\Exception $e) {
            error_log("Error in HomepageAdminController::markMessageRead: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while updating message.');
        }

        return $this->redirect('admin/homepage/contact-messages');
    }
}
?>
