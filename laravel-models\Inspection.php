<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inspection extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'inspector_id',
        'assigned_by',
        'scheduled_date',
        'completed_date',
        'status',
        'inspection_type',
        'priority',
        'score',
        'notes',
        'findings',
        'recommendations',
        'compliance_rating',
        'inspector_notes',
        'admin_notes',
        'admin_verified',
        'verification_status',
        'verified_by',
        'verified_at',
        'verification_notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'scheduled_date' => 'datetime',
            'completed_date' => 'datetime',
            'verified_at' => 'datetime',
            'admin_verified' => 'boolean',
        ];
    }

    /**
     * Get the business being inspected
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the assigned inspector
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Get the admin who assigned the inspection
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Get the admin who verified the inspection
     */
    public function verifiedBy()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get checklist responses for this inspection
     */
    public function checklistResponses()
    {
        return $this->hasMany(InspectionChecklistResponse::class);
    }

    /**
     * Get inspection assignments
     */
    public function assignments()
    {
        return $this->hasMany(InspectionAssignment::class);
    }

    /**
     * Check if inspection is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if inspection is verified
     */
    public function isVerified(): bool
    {
        return $this->verification_status === 'approved';
    }

    /**
     * Get inspection score percentage
     */
    public function getScorePercentage(): ?int
    {
        if (!$this->score) {
            return null;
        }

        // Calculate based on total possible points from checklist responses
        $totalPossiblePoints = $this->checklistResponses()
            ->join('inspection_checklist_items', 'inspection_checklist_responses.checklist_item_id', '=', 'inspection_checklist_items.id')
            ->sum('inspection_checklist_items.points');

        if ($totalPossiblePoints === 0) {
            return null;
        }

        return round(($this->score / $totalPossiblePoints) * 100);
    }

    /**
     * Get critical violations count
     */
    public function getCriticalViolationsCount(): int
    {
        return $this->checklistResponses()
            ->join('inspection_checklist_items', 'inspection_checklist_responses.checklist_item_id', '=', 'inspection_checklist_items.id')
            ->where('inspection_checklist_items.is_critical', true)
            ->where('inspection_checklist_responses.compliance_status', 'non_compliant')
            ->count();
    }

    /**
     * Get compliance status summary
     */
    public function getComplianceSummary(): array
    {
        $responses = $this->checklistResponses;
        
        return [
            'total' => $responses->count(),
            'compliant' => $responses->where('compliance_status', 'compliant')->count(),
            'needs_improvement' => $responses->where('compliance_status', 'needs_improvement')->count(),
            'non_compliant' => $responses->where('compliance_status', 'non_compliant')->count(),
            'not_applicable' => $responses->where('compliance_status', 'not_applicable')->count(),
        ];
    }
}
