<?php
namespace App\Services;

use App\Config\Database;
use PDO;

class NotificationService {
    private $db;
    
    public function __construct() {
        $this->db = (new Database())->getConnection();
    }
    
    public function checkExpiringDocuments() {
        // Get documents expiring in the next 30 days
        $query = "SELECT d.*, b.name as business_name, b.email as business_email 
                 FROM documents d 
                 JOIN businesses b ON d.business_id = b.id 
                 WHERE d.expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                 AND d.notification_sent = 0";
                 
        $stmt = $this->db->query($query);
        $expiringDocs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($expiringDocs as $doc) {
            $this->sendExpiryNotification($doc);
            $this->markNotificationSent($doc['id']);
        }
        
        return count($expiringDocs);
    }
    
    private function sendExpiryNotification($doc) {
        $to = $doc['business_email'];
        $subject = "Document Expiry Notice - {$doc['name']}";
        
        $message = "Dear {$doc['business_name']},\n\n";
        $message .= "This is to notify you that your document '{$doc['name']}' ";
        $message .= "will expire on {$doc['expiry_date']}.\n\n";
        $message .= "Please ensure to renew this document before its expiration.\n\n";
        $message .= "Best regards,\nOHS System";
        
        $headers = "From: <EMAIL>\r\n";
        $headers .= "Reply-To: <EMAIL>\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion();
        
        mail($to, $subject, $message, $headers);
    }
    
    private function markNotificationSent($documentId) {
        $query = "UPDATE documents SET notification_sent = 1 WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute(['id' => $documentId]);
    }
}