.layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--primary-color) 0%, #224abe 100%);
    color: #fff;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: margin-left 0.3s ease;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
}

.sidebar-nav {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
    flex-grow: 1;
}

.sidebar-item {
    margin: 0.25rem 1rem;
}

.sidebar-item a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 0.35rem;
    transition: all 0.2s ease;
}

.sidebar-item a:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
}

.sidebar-item.active a {
    color: #fff;
    background-color: rgba(255,255,255,0.2);
}

.sidebar-item i {
    width: 1.25rem;
    margin-right: 0.75rem;
    text-align: center;
}

.sidebar-item span {
    font-size: 0.875rem;
    font-weight: 500;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.sidebar-footer a {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 0.35rem;
    transition: all 0.2s ease;
}

.sidebar-footer a:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
}

.sidebar-footer i {
    width: 1.25rem;
    margin-right: 0.75rem;
    text-align: center;
}

.main-content {
    flex: 1;
    background: #f5f6fa;
    position: relative;
}

.topbar {
    background: #fff;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.toggle-sidebar {
    font-size: 1.5rem;
    cursor: pointer;
    color: #2c3e50;
}

.user-profile {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-profile span {
    font-weight: bold;
    color: #2c3e50;
}

.user-profile small {
    color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        margin-left: calc(var(--sidebar-width) * -1);
    }

    .sidebar.active {
        margin-left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .toggle-sidebar {
        display: block;
    }
}

/* Main Content Padding */
main {
    padding: 20px;
}

/* Sidebar Collapse Animation */
.sidebar-collapsed .sidebar {
    margin-left: calc(var(--sidebar-width) * -1);
}

.sidebar-collapsed .main-content {
    margin-left: 0;
} 