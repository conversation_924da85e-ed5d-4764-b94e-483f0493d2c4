<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-list"></i> Manage Inspections
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                <i class="fas fa-calendar-check"></i> Schedule Inspections
            </a>
        </div>
    </div>

    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Admin Authority:</strong> Only administrators can schedule inspections and assign them to inspectors. Inspectors can view their assigned inspections and perform the actual inspections using the checklist system.
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php
        $statusCounts = [
            'scheduled' => 0,
            'in_progress' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];

        foreach ($inspections as $inspection) {
            $status = $inspection['status'] ?? 'scheduled';
            if (isset($statusCounts[$status])) {
                $statusCounts[$status]++;
            }
        }
        ?>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Scheduled</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['scheduled'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">In Progress</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['in_progress'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['completed'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Cancelled</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['cancelled'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inspections Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> All Inspections
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($inspections)): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="inspectionsTable">
                        <thead>
                            <tr>
                                <th>Inspection ID</th>
                                <th>Business</th>
                                <th>Inspector</th>
                                <th>Location</th>
                                <th>Schedule</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Verification</th>
                                <th>Checklist Progress</th>
                                <th>Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inspections as $inspection): ?>
                                <tr>
                                    <td>
                                        <code><?= substr($inspection['id'], 0, 8) ?>...</code>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                        <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars($inspection['inspector_name']) ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($inspection['barangay_name'] ?? 'N/A') ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?= date('M d, Y g:i A', strtotime($inspection['scheduled_date'])) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?=
                                            $inspection['status'] === 'completed' ? 'success' :
                                            ($inspection['status'] === 'in_progress' ? 'warning' :
                                            ($inspection['status'] === 'cancelled' ? 'danger' : 'secondary'))
                                        ?>">
                                            <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($inspection['status'] === 'completed'): ?>
                                            <?php
                                            $verificationStatus = $inspection['verification_status'] ?? 'pending';
                                            $badgeClass = '';
                                            $icon = '';
                                            $text = '';

                                            switch ($verificationStatus) {
                                                case 'approved':
                                                    $badgeClass = 'success';
                                                    $icon = 'check-circle';
                                                    $text = 'Approved';
                                                    break;
                                                case 'rejected':
                                                    $badgeClass = 'danger';
                                                    $icon = 'times-circle';
                                                    $text = 'Rejected';
                                                    break;
                                                default:
                                                    $badgeClass = 'warning';
                                                    $icon = 'clock';
                                                    $text = 'Pending';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?= $badgeClass ?>">
                                                <i class="fas fa-<?= $icon ?> me-1"></i><?= $text ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($inspection['checklist_completion'] && $inspection['checklist_completion']['is_started']): ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-<?= $inspection['checklist_completion']['completion_percentage'] >= 100 ? 'success' : 'info' ?>"
                                                     role="progressbar"
                                                     style="width: <?= $inspection['checklist_completion']['completion_percentage'] ?>%">
                                                    <?= $inspection['checklist_completion']['completion_percentage'] ?>%
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                <?php if ($inspection['checklist_completion']['completed_items'] > 1): ?>
                                                    <?= $inspection['checklist_completion']['completed_items'] ?>/<?= $inspection['checklist_completion']['total_items'] ?> items
                                                <?php else: ?>
                                                    Inspection started by inspector
                                                <?php endif; ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">Not started</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($inspection['checklist_score']): ?>
                                            <span class="badge bg-<?=
                                                $inspection['checklist_score']['grade'] === 'A' ? 'success' :
                                                (in_array($inspection['checklist_score']['grade'], ['B', 'C']) ? 'warning' : 'danger')
                                            ?> fs-6">
                                                <?= $inspection['checklist_score']['percentage'] ?>% (<?= $inspection['checklist_score']['grade'] ?>)
                                            </span>
                                            <?php if ($inspection['checklist_score']['critical_violations'] > 0): ?>
                                                <br><small class="text-danger">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <?= $inspection['checklist_score']['critical_violations'] ?> critical
                                                </small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">No score</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($inspection['checklist_score']): ?>
                                                <a href="<?= BASE_URL ?>admin/inspections/report/<?= $inspection['id'] ?>"
                                                   class="btn btn-sm btn-outline-info" title="View Report">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted mb-3">No Inspections Found</h5>
                    <p class="text-muted mb-3">There are no inspections scheduled in the system yet.</p>
                    <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                        <i class="fas fa-calendar-check"></i> Schedule Inspections
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Initialize DataTables
$(document).ready(function() {
    $('#inspectionsTable').DataTable({
        order: [[4, 'desc']], // Sort by schedule date by default
        pageLength: 25,
        responsive: true,
        language: {
            search: '',
            searchPlaceholder: 'Search inspections...'
        },
        columnDefs: [
            { orderable: false, targets: [8, 9, 10] } // Disable sorting for progress, score, and actions columns
        ]
    });
});
</script>
<?php $this->endSection(); ?>