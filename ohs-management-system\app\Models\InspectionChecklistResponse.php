<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InspectionChecklistResponse extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'inspection_id',
        'checklist_item_id',
        'inspector_id',
        'compliance_status',
        'score',
        'notes',
        'photo_evidence',
        'corrective_action',
        'deadline',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'deadline' => 'date',
        ];
    }

    /**
     * Get the inspection this response belongs to
     */
    public function inspection()
    {
        return $this->belongsTo(Inspection::class);
    }

    /**
     * Get the checklist item this response is for
     */
    public function checklistItem()
    {
        return $this->belongsTo(InspectionChecklistItem::class, 'checklist_item_id');
    }

    /**
     * Get the inspector who made this response
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Check if response is compliant
     */
    public function isCompliant(): bool
    {
        return $this->compliance_status === 'compliant';
    }

    /**
     * Check if response is non-compliant
     */
    public function isNonCompliant(): bool
    {
        return $this->compliance_status === 'non_compliant';
    }

    /**
     * Check if response needs improvement
     */
    public function needsImprovement(): bool
    {
        return $this->compliance_status === 'needs_improvement';
    }

    /**
     * Check if response is not applicable
     */
    public function isNotApplicable(): bool
    {
        return $this->compliance_status === 'not_applicable';
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->compliance_status) {
            'compliant' => 'badge-success',
            'needs_improvement' => 'badge-warning',
            'non_compliant' => 'badge-danger',
            'not_applicable' => 'badge-secondary',
            default => 'badge-light'
        };
    }

    /**
     * Get status display text
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->compliance_status) {
            'compliant' => 'Compliant',
            'needs_improvement' => 'Needs Improvement',
            'non_compliant' => 'Non-Compliant',
            'not_applicable' => 'Not Applicable',
            default => 'Unknown'
        };
    }
}
