<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Settings</h1>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Inspector Preferences</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= BASE_URL ?>inspector/settings/update">
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Notification Settings</h6>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" checked>
                                <label class="form-check-label" for="email_notifications">
                                    Email Notifications
                                </label>
                                <small class="form-text text-muted d-block">Receive email notifications for inspection assignments</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="schedule_reminders" name="schedule_reminders" checked>
                                <label class="form-check-label" for="schedule_reminders">
                                    Schedule Reminders
                                </label>
                                <small class="form-text text-muted d-block">Get reminders about upcoming inspections</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="compliance_alerts" name="compliance_alerts" checked>
                                <label class="form-check-label" for="compliance_alerts">
                                    Compliance Evidence Alerts
                                </label>
                                <small class="form-text text-muted d-block">Receive alerts for new compliance evidence submissions</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="mobile_notifications" name="mobile_notifications">
                                <label class="form-check-label" for="mobile_notifications">
                                    Mobile Push Notifications
                                </label>
                                <small class="form-text text-muted d-block">Receive push notifications on mobile devices</small>
                            </div>
                        </div>

                        <hr>

                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Inspection Preferences</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="default_inspection_view" class="form-label">Default Inspection View</label>
                                        <select class="form-select" id="default_inspection_view" name="default_inspection_view">
                                            <option value="calendar">Calendar View</option>
                                            <option value="list" selected>List View</option>
                                            <option value="map">Map View</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reminder_time" class="form-label">Reminder Time (before inspection)</label>
                                        <select class="form-select" id="reminder_time" name="reminder_time">
                                            <option value="15">15 minutes</option>
                                            <option value="30">30 minutes</option>
                                            <option value="60" selected>1 hour</option>
                                            <option value="120">2 hours</option>
                                            <option value="1440">1 day</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="auto_assign" name="auto_assign">
                                <label class="form-check-label" for="auto_assign">
                                    Accept Auto-Assignment
                                </label>
                                <small class="form-text text-muted d-block">Allow automatic assignment of inspections based on availability</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="travel_optimization" name="travel_optimization" checked>
                                <label class="form-check-label" for="travel_optimization">
                                    Travel Route Optimization
                                </label>
                                <small class="form-text text-muted d-block">Optimize inspection routes to minimize travel time</small>
                            </div>
                        </div>

                        <hr>

                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Report Preferences</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="report_format" class="form-label">Default Report Format</label>
                                        <select class="form-select" id="report_format" name="report_format">
                                            <option value="pdf" selected>PDF</option>
                                            <option value="word">Word Document</option>
                                            <option value="excel">Excel Spreadsheet</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="signature_method" class="form-label">Digital Signature Method</label>
                                        <select class="form-select" id="signature_method" name="signature_method">
                                            <option value="draw" selected>Draw Signature</option>
                                            <option value="type">Type Name</option>
                                            <option value="upload">Upload Image</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="auto_save_drafts" name="auto_save_drafts" checked>
                                <label class="form-check-label" for="auto_save_drafts">
                                    Auto-save Report Drafts
                                </label>
                                <small class="form-text text-muted d-block">Automatically save inspection reports as drafts while working</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="include_photos" name="include_photos" checked>
                                <label class="form-check-label" for="include_photos">
                                    Include Photos in Reports
                                </label>
                                <small class="form-text text-muted d-block">Automatically include inspection photos in generated reports</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Inspector Stats</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Inspections This Month:</strong><br>
                        <span class="text-muted">12 completed</span>
                    </div>

                    <div class="mb-3">
                        <strong>Average Rating:</strong><br>
                        <div class="d-flex align-items-center">
                            <span class="text-warning me-1">★★★★★</span>
                            <span class="text-muted">4.8/5.0</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <strong>Compliance Rate:</strong><br>
                        <div class="progress mb-1" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted">85% businesses compliant after inspection</small>
                    </div>

                    <div class="mb-3">
                        <strong>Response Time:</strong><br>
                        <span class="text-muted">Average 2.3 hours</span>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>inspector/profile" class="btn btn-outline-primary">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="<?= BASE_URL ?>inspector/dashboard" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-outline-info">
                            <i class="fas fa-calendar"></i> My Schedule
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>
