<?php

namespace App\Http\Controllers\BusinessOwner;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessCategory;
use App\Models\District;
use App\Models\Barangay;
use App\Models\BusinessChecklistEvidence;
use App\Models\InspectionChecklistItem;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class BusinessController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Display business owner dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        $business = $user->businesses()->with([
            'category',
            'barangay.district',
            'inspections.inspector'
        ])->first();

        if (!$business) {
            return view('business-owner.no-business');
        }

        // Get statistics
        $stats = [
            'total_inspections' => $business->inspections()->count(),
            'pending_inspections' => $business->inspections()->where('status', 'scheduled')->count(),
            'completed_inspections' => $business->inspections()->where('status', 'completed')->count(),
            'compliance_status' => $business->compliance_status,
            'last_inspection' => $business->inspections()->latest('completed_date')->first(),
        ];

        // Get recent inspections
        $recentInspections = $business->inspections()
            ->with('inspector')
            ->latest('scheduled_date')
            ->limit(5)
            ->get();

        // Get compliance evidence statistics
        $evidenceStats = [
            'total_evidence' => $business->checklistEvidence()->count(),
            'approved_evidence' => $business->checklistEvidence()->where('status', 'approved')->count(),
            'pending_evidence' => $business->checklistEvidence()->where('status', 'pending')->count(),
            'rejected_evidence' => $business->checklistEvidence()->where('status', 'rejected')->count(),
        ];

        return view('business-owner.dashboard', compact(
            'user',
            'business',
            'stats',
            'recentInspections',
            'evidenceStats'
        ));
    }

    /**
     * Display business profile
     */
    public function profile()
    {
        $business = Auth::user()->businesses()->with([
            'category',
            'barangay.district',
            'inspections.inspector',
            'checklistEvidence.checklistItem'
        ])->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found. Please contact administrator.');
        }

        return view('business-owner.business.profile', compact('business'));
    }

    /**
     * Show the form for editing business profile
     */
    public function edit()
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        $categories = BusinessCategory::all();
        $districts = District::with('barangays')->get();

        return view('business-owner.business.edit', compact('business', 'categories', 'districts'));
    }

    /**
     * Update business profile
     */
    public function update(Request $request)
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'contact_number' => 'required|string|max:50',
            'address' => 'required|string|max:500',
            'category_id' => 'required|exists:business_categories,id',
            'barangay_id' => 'required|exists:barangays,id',
            'employee_count' => 'required|integer|min:1',
            'date_established' => 'nullable|date|before:today',
            'business_permit' => 'boolean',
            'sanitary_permit' => 'boolean',
            'fire_safety_certificate' => 'boolean',
            'environmental_permit' => 'boolean',
            'safety_officer_count' => 'required|integer|min:0',
            'has_safety_signage' => 'boolean',
            'has_first_aid' => 'boolean',
            'has_fire_extinguishers' => 'boolean',
            'has_cctv' => 'boolean',
            'has_waste_segregation' => 'boolean',
        ]);

        $business->update($validated);

        return redirect()->route('business-owner.business.profile')
            ->with('success', 'Business profile updated successfully.');
    }

    /**
     * Display compliance checklist
     */
    public function checklist()
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        // Get checklist items with categories
        $checklistItems = InspectionChecklistItem::with([
            'category',
            'businessEvidence' => function($query) use ($business) {
                $query->where('business_id', $business->id);
            }
        ])->active()->ordered()->get();

        // Group by category
        $checklistByCategory = $checklistItems->groupBy('category.name');

        // Get business evidence
        $businessEvidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->with('checklistItem')
            ->get()
            ->keyBy('checklist_item_id');

        return view('business-owner.business.checklist', compact(
            'business',
            'checklistByCategory',
            'businessEvidence'
        ));
    }

    /**
     * Upload evidence for checklist item
     */
    public function uploadEvidence(Request $request)
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return response()->json(['success' => false, 'message' => 'No business profile found.']);
        }

        $validated = $request->validate([
            'checklist_item_id' => 'required|exists:inspection_checklist_items,id',
            'evidence_file' => 'required|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            // Upload file
            $filePath = $this->fileUploadService->uploadFile(
                $request->file('evidence_file'),
                'business-evidence',
                [
                    'business_id' => $business->id,
                    'checklist_item_id' => $validated['checklist_item_id']
                ]
            );

            // Create or update evidence record
            BusinessChecklistEvidence::updateOrCreate(
                [
                    'business_id' => $business->id,
                    'checklist_item_id' => $validated['checklist_item_id'],
                ],
                [
                    'file_path' => $filePath['path'],
                    'file_name' => $filePath['original_name'],
                    'file_type' => $filePath['mime_type'],
                    'file_size' => $filePath['size'],
                    'notes' => $validated['notes'],
                    'status' => 'pending',
                    'uploaded_by' => Auth::id(),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Evidence uploaded successfully and is pending review.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload evidence: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete evidence
     */
    public function deleteEvidence(BusinessChecklistEvidence $evidence)
    {
        $business = Auth::user()->businesses()->first();

        if (!$business || $evidence->business_id !== $business->id) {
            return response()->json(['success' => false, 'message' => 'Unauthorized.']);
        }

        try {
            // Delete file from storage
            if (Storage::disk('public')->exists($evidence->file_path)) {
                Storage::disk('public')->delete($evidence->file_path);
            }

            // Delete record
            $evidence->delete();

            return response()->json([
                'success' => true,
                'message' => 'Evidence deleted successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete evidence: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display inspection history
     */
    public function inspections()
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        $inspections = $business->inspections()
            ->with(['inspector', 'assignedBy', 'verifiedBy'])
            ->latest('scheduled_date')
            ->paginate(15);

        return view('business-owner.business.inspections', compact('business', 'inspections'));
    }

    /**
     * Display specific inspection details
     */
    public function inspectionDetails($inspectionId)
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        $inspection = $business->inspections()
            ->with([
                'inspector',
                'assignedBy',
                'verifiedBy',
                'checklistResponses.checklistItem.category'
            ])
            ->findOrFail($inspectionId);

        // Group responses by category
        $responsesByCategory = $inspection->checklistResponses
            ->groupBy('checklistItem.category.name');

        return view('business-owner.business.inspection-details', compact(
            'business',
            'inspection',
            'responsesByCategory'
        ));
    }

    /**
     * Display compliance dashboard
     */
    public function compliance()
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        // Get compliance statistics
        $stats = [
            'total_checklist_items' => InspectionChecklistItem::active()->count(),
            'submitted_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)->count(),
            'approved_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'approved')->count(),
            'pending_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'pending')->count(),
            'rejected_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'rejected')->count(),
        ];

        // Calculate compliance percentage
        $stats['compliance_percentage'] = $stats['total_checklist_items'] > 0 
            ? round(($stats['approved_evidence'] / $stats['total_checklist_items']) * 100, 1)
            : 0;

        // Get recent inspections
        $recentInspections = $business->inspections()
            ->with('inspector')
            ->latest('scheduled_date')
            ->limit(5)
            ->get();

        // Get pending evidence items
        $pendingEvidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->where('status', 'pending')
            ->with('checklistItem')
            ->latest()
            ->limit(5)
            ->get();

        return view('business-owner.business.compliance', compact(
            'business',
            'stats',
            'recentInspections',
            'pendingEvidence'
        ));
    }

    /**
     * Display business settings
     */
    public function settings()
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        return view('business-owner.business.settings', compact('business'));
    }

    /**
     * Update business settings
     */
    public function updateSettings(Request $request)
    {
        $user = Auth::user();
        $business = $user->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'inspection_reminders' => 'boolean',
            'compliance_alerts' => 'boolean',
        ]);

        // Update user preferences
        $user->update([
            'preferences' => json_encode($validated)
        ]);

        return back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Display business inspection report
     */
    public function inspectionReport($inspectionId)
    {
        $business = Auth::user()->businesses()->first();

        if (!$business) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'No business profile found.');
        }

        $inspection = $business->inspections()
            ->with([
                'inspector',
                'assignedBy',
                'verifiedBy',
                'checklistResponses.checklistItem.category'
            ])
            ->findOrFail($inspectionId);

        // Group responses by category
        $responsesByCategory = $inspection->checklistResponses
            ->groupBy('checklistItem.category.name');

        return view('business-owner.business.inspection-report', compact(
            'business',
            'inspection',
            'responsesByCategory'
        ));
    }

    /**
     * View specific evidence
     */
    public function viewEvidence($businessId, $checklistItemId)
    {
        $business = Auth::user()->businesses()->findOrFail($businessId);

        $evidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->where('checklist_item_id', $checklistItemId)
            ->with(['checklistItem', 'verifiedBy'])
            ->first();

        $checklistItem = InspectionChecklistItem::findOrFail($checklistItemId);

        return view('business-owner.business.view-evidence', compact(
            'business',
            'evidence',
            'checklistItem'
        ));
    }

    /**
     * Create business (for new business owners)
     */
    public function create()
    {
        $user = Auth::user();

        // Check if user already has a business
        if ($user->businesses()->exists()) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'You already have a business registered.');
        }

        $categories = BusinessCategory::all();
        $districts = District::with('barangays')->get();

        return view('business-owner.business.create', compact('categories', 'districts'));
    }

    /**
     * Store new business
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        // Check if user already has a business
        if ($user->businesses()->exists()) {
            return redirect()->route('business-owner.dashboard')
                ->with('error', 'You already have a business registered.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'contact_number' => 'required|string|max:50',
            'address' => 'required|string|max:500',
            'category_id' => 'required|exists:business_categories,id',
            'barangay_id' => 'required|exists:barangays,id',
            'employee_count' => 'required|integer|min:1',
            'date_established' => 'nullable|date|before:today',
            'business_permit' => 'boolean',
            'sanitary_permit' => 'boolean',
            'fire_safety_certificate' => 'boolean',
            'environmental_permit' => 'boolean',
            'safety_officer_count' => 'required|integer|min:0',
            'has_safety_signage' => 'boolean',
            'has_first_aid' => 'boolean',
            'has_fire_extinguishers' => 'boolean',
            'has_cctv' => 'boolean',
            'has_waste_segregation' => 'boolean',
        ]);

        $validated['owner_id'] = $user->id;
        $validated['owner_name'] = $user->full_name;
        $validated['owner_email'] = $user->email;
        $validated['status'] = 'pending_approval';

        Business::create($validated);

        return redirect()->route('business-owner.dashboard')
            ->with('success', 'Business registration submitted successfully. Please wait for admin approval.');
    }

    /**
     * Display business index (for business owners with multiple businesses)
     */
    public function index()
    {
        $businesses = Auth::user()->businesses()
            ->with(['category', 'barangay.district'])
            ->get();

        return view('business-owner.business.index', compact('businesses'));
    }

    /**
     * View specific business
     */
    public function view($id)
    {
        $business = Auth::user()->businesses()
            ->with([
                'category',
                'barangay.district',
                'inspections.inspector',
                'checklistEvidence.checklistItem'
            ])
            ->findOrFail($id);

        return view('business-owner.business.view', compact('business'));
    }
}
