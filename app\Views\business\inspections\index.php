<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-list"></i> My Business Inspections
        </h1>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php
        $statusCounts = [
            'scheduled' => 0,
            'in_progress' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];

        foreach ($inspections as $inspection) {
            $status = $inspection['status'] ?? 'scheduled';
            if (isset($statusCounts[$status])) {
                $statusCounts[$status]++;
            }
        }
        ?>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Scheduled</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['scheduled'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">In Progress</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['in_progress'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['completed'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Cancelled</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $statusCounts['cancelled'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inspections Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> All Business Inspections
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($inspections)): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="inspectionsTable">
                        <thead>
                            <tr>
                                <th>Inspection ID</th>
                                <th>Business Name</th>
                                <th>Inspector</th>
                                <th>Scheduled Date</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Checklist Progress</th>
                                <th>Compliance Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inspections as $inspection): ?>
                                <tr>
                                    <td>
                                        <code><?= substr($inspection['id'], 0, 8) ?>...</code>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($inspection['business_name'] ?? 'N/A') ?></strong>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars($inspection['inspector_name'] ?? 'Not assigned') ?>
                                    </td>
                                    <td>
                                        <?= date('M d, Y g:i A', strtotime($inspection['scheduled_date'])) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'] ?? 'routine')) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?=
                                            $inspection['status'] === 'completed' ? 'success' :
                                            ($inspection['status'] === 'in_progress' ? 'warning' :
                                            ($inspection['status'] === 'cancelled' ? 'danger' : 'secondary'))
                                        ?>">
                                            <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                        </span>
                                    </td>

                                    <td>
                                        <?php if ($inspection['checklist_completion'] && $inspection['checklist_completion']['is_started']): ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-<?= $inspection['checklist_completion']['completion_percentage'] >= 100 ? 'success' : 'info' ?>"
                                                     role="progressbar"
                                                     style="width: <?= $inspection['checklist_completion']['completion_percentage'] ?>%">
                                                    <?= $inspection['checklist_completion']['completion_percentage'] ?>%
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                <?php if ($inspection['checklist_completion']['completed_items'] > 1): ?>
                                                    <?= $inspection['checklist_completion']['completed_items'] ?>/<?= $inspection['checklist_completion']['total_items'] ?> items
                                                <?php else: ?>
                                                    Inspection started by inspector
                                                <?php endif; ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">Not started</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($inspection['checklist_score']): ?>
                                            <span class="badge bg-<?=
                                                $inspection['checklist_score']['grade'] === 'A' ? 'success' :
                                                (in_array($inspection['checklist_score']['grade'], ['B', 'C']) ? 'warning' : 'danger')
                                            ?> fs-6">
                                                <?= $inspection['checklist_score']['percentage'] ?>% (<?= $inspection['checklist_score']['grade'] ?>)
                                            </span>
                                            <?php if ($inspection['checklist_score']['critical_violations'] > 0): ?>
                                                <br><small class="text-danger">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <?= $inspection['checklist_score']['critical_violations'] ?> critical
                                                </small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">No score</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>business/inspection/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($inspection['checklist_score']): ?>
                                                <a href="<?= BASE_URL ?>business/inspection-report/<?= $inspection['id'] ?>"
                                                   class="btn btn-sm btn-outline-info" title="View Report">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted mb-3">No Inspections Found</h5>
                    <p class="text-muted mb-3">You don't have any inspections scheduled for your businesses yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Initialize DataTables
$(document).ready(function() {
    $('#inspectionsTable').DataTable({
        order: [[3, 'desc']], // Sort by scheduled date by default
        pageLength: 25,
        responsive: true,
        language: {
            search: '',
            searchPlaceholder: 'Search inspections...'
        },
        columnDefs: [
            { orderable: false, targets: [6, 7, 8] } // Disable sorting for progress, score, and actions columns
        ]
    });
});
</script>

<?php $this->endSection(); ?>