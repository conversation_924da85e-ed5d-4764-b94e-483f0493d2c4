<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Business: <?= htmlspecialchars($business['name']) ?></h1>
        <div>
            <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Business
            </a>
            <a href="<?= BASE_URL ?>business" class="btn btn-outline-secondary">
                <i class="fas fa-building"></i> My Businesses
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-edit me-2"></i>Edit Business Information
            </h6>
        </div>
        <div class="card-body">
            <form action="<?= BASE_URL ?>business/edit/<?= $business['id'] ?>" method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Business Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="<?= htmlspecialchars($business['name']) ?>" required>
                            <div class="invalid-feedback">Please provide a business name.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="registration_number" class="form-label">Registration Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number"
                                   value="<?= htmlspecialchars($business['registration_number']) ?>" required>
                            <div class="invalid-feedback">Please provide a registration number.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="category_id" class="form-label">Business Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= htmlspecialchars($category['id']) ?>"
                                            <?= $category['id'] === $business['category_id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Please select a business category.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="district_id" class="form-label">District <span class="text-danger">*</span></label>
                            <select class="form-select" id="district_id" onchange="loadBarangays()">
                                <option value="">Select District First</option>
                                <?php foreach ($districts as $district): ?>
                                    <option value="<?= $district['id'] ?>">
                                        <?= htmlspecialchars($district['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="barangay_id" class="form-label">Barangay <span class="text-danger">*</span></label>
                            <select class="form-select" id="barangay_id" name="barangay_id" required>
                                <option value="">Select District First</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a barangay.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">Contact Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?= htmlspecialchars($business['email']) ?>" required>
                            <div class="invalid-feedback">Please provide a valid email address.</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_number" class="form-label">Contact Phone <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="contact_number" name="contact_number"
                                   value="<?= htmlspecialchars($business['contact_number']) ?>" required>
                            <div class="invalid-feedback">Please provide a contact phone number.</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="address" class="form-label">Business Address <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" required><?= htmlspecialchars($business['address']) ?></textarea>
                            <div class="invalid-feedback">Please provide the business address.</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="employee_count" class="form-label">Number of Employees</label>
                            <input type="number" class="form-control" id="employee_count" name="employee_count"
                                   min="1" value="<?= htmlspecialchars($business['employee_count']) ?>">
                            <div class="form-text">Optional: Enter the number of employees</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="date_established" class="form-label">Date Established</label>
                            <input type="date" class="form-control" id="date_established" name="date_established"
                                   value="<?= htmlspecialchars($business['date_established'] ?? '') ?>">
                            <div class="form-text">Optional: When was the business established</div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Business
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Store data for JavaScript use
const barangaysData = <?= json_encode($barangays) ?>;
const currentBarangayId = '<?= $business['barangay_id'] ?? '' ?>';

function loadBarangays() {
    const districtId = document.getElementById('district_id').value;
    const barangaySelect = document.getElementById('barangay_id');

    // Clear existing options
    barangaySelect.innerHTML = '<option value="">Select Barangay</option>';

    if (!districtId) {
        barangaySelect.innerHTML = '<option value="">Select District First</option>';
        return;
    }

    // Filter barangays by district
    const districtBarangays = barangaysData.filter(barangay => barangay.district_id == districtId);

    // Add barangay options
    districtBarangays.forEach(barangay => {
        const option = document.createElement('option');
        option.value = barangay.id;
        option.textContent = barangay.name;
        if (barangay.id == currentBarangayId) {
            option.selected = true;
        }
        barangaySelect.appendChild(option);
    });
}

// Initialize form with current values
document.addEventListener('DOMContentLoaded', function() {
    // Find the district for the current barangay
    if (currentBarangayId) {
        const currentBarangay = barangaysData.find(b => b.id == currentBarangayId);
        if (currentBarangay) {
            document.getElementById('district_id').value = currentBarangay.district_id;
            loadBarangays();
        }
    }
});

// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
<?php $this->endSection() ?>