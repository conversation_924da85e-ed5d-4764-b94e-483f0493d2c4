<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Chat;
use App\Models\Business;
use App\Models\User;
use App\Models\Notification;

class ChatController extends Controller {
    private $chatModel;
    private $businessModel;
    private $userModel;
    private $notificationModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->chatModel = new Chat();
        $this->businessModel = new Business();
        $this->userModel = new User();
        $this->notificationModel = new Notification();
    }

    public function index() {
        $this->auth->requireLogin();
        
        $user = $this->auth->getUser();
        $rooms = $this->chatModel->getRoomsByUser($user['id'], $user['role']);
        
        return $this->render('chat/index', [
            'title' => 'Chat',
            'active_page' => 'chat',
            'rooms' => $rooms,
            'user' => $user
        ]);
    }

    public function room($roomId) {
        $this->auth->requireLogin();
        
        $user = $this->auth->getUser();
        $room = $this->chatModel->getRoomById($roomId);
        
        if (!$room) {
            $_SESSION['error'] = 'Chat room not found.';
            $this->redirect('chat');
            return;
        }
        
        // Check if user has access to this room
        $hasAccess = false;
        switch ($user['role']) {
            case 'admin':
                $hasAccess = true; // Admins can access all rooms
                break;
            case 'inspector':
                $hasAccess = ($room['inspector_id'] === $user['id'] || $room['inspector_id'] === null);
                break;
            case 'business_owner':
                $hasAccess = ($room['business_owner_id'] === $user['id']);
                break;
        }
        
        if (!$hasAccess) {
            $_SESSION['error'] = 'You do not have access to this chat room.';
            $this->redirect('chat');
            return;
        }
        
        // Assign admin/inspector to room if they're accessing it for the first time
        if ($user['role'] === 'admin' && !$room['admin_id']) {
            $this->chatModel->assignAdminToRoom($roomId, $user['id']);
            $room['admin_id'] = $user['id'];
            $room['admin_name'] = $user['full_name'];
        } elseif ($user['role'] === 'inspector' && !$room['inspector_id']) {
            $this->chatModel->assignInspectorToRoom($roomId, $user['id']);
            $room['inspector_id'] = $user['id'];
            $room['inspector_name'] = $user['full_name'];
        }
        
        // Mark messages as read
        $this->chatModel->markMessagesAsRead($roomId, $user['id']);
        
        // Get messages
        $messages = $this->chatModel->getMessages($roomId);
        
        return $this->render('chat/room', [
            'title' => 'Chat - ' . $room['business_name'],
            'active_page' => 'chat',
            'room' => $room,
            'messages' => array_reverse($messages), // Show oldest first
            'user' => $user
        ]);
    }

    public function createRoom($businessId) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();
        
        $user = $this->auth->getUser();
        $business = $this->businessModel->getById($businessId);
        
        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('business');
            return;
        }
        
        // Check if user owns this business
        if ($business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You can only create chat rooms for your own businesses.';
            $this->redirect('business');
            return;
        }
        
        // Check if room already exists
        $existingRoom = $this->chatModel->getRoomByBusiness($businessId, $user['id']);
        if ($existingRoom) {
            $this->redirect('chat/room/' . $existingRoom['id']);
            return;
        }
        
        if ($this->isPost()) {
            $subject = $_POST['subject'] ?? '';
            $message = $_POST['message'] ?? '';
            
            if (empty($subject) || empty($message)) {
                $_SESSION['error'] = 'Subject and message are required.';
                $this->redirect('chat/create/' . $businessId);
                return;
            }
            
            // Create chat room
            $roomData = [
                'business_id' => $businessId,
                'business_owner_id' => $user['id'],
                'subject' => $subject,
                'status' => 'active'
            ];
            
            $roomId = $this->chatModel->createRoom($roomData);
            
            if ($roomId) {
                // Send initial message
                $messageData = [
                    'chat_room_id' => $roomId,
                    'sender_id' => $user['id'],
                    'message' => $message,
                    'message_type' => 'text'
                ];
                
                $this->chatModel->sendMessage($messageData);
                
                // Notify admins about new chat room
                $admins = $this->userModel->getByRole('admin');
                foreach ($admins as $admin) {
                    $this->notificationModel->createChatNotification(
                        $admin['id'],
                        $user['full_name'],
                        $business['name'],
                        $roomId
                    );
                }
                
                $_SESSION['success'] = 'Chat room created successfully.';
                $this->redirect('chat/room/' . $roomId);
            } else {
                $_SESSION['error'] = 'Failed to create chat room.';
                $this->redirect('chat/create/' . $businessId);
            }
            return;
        }
        
        return $this->render('chat/create', [
            'title' => 'Create Chat Room',
            'active_page' => 'chat',
            'business' => $business,
            'user' => $user
        ]);
    }

    public function sendMessage($roomId) {
        $this->auth->requireLogin();
        
        if (!$this->isPost()) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $user = $this->auth->getUser();
        $room = $this->chatModel->getRoomById($roomId);
        
        if (!$room) {
            $this->jsonResponse(['success' => false, 'message' => 'Chat room not found']);
            return;
        }
        
        $message = $_POST['message'] ?? '';
        if (empty($message)) {
            $this->jsonResponse(['success' => false, 'message' => 'Message cannot be empty']);
            return;
        }
        
        $messageData = [
            'chat_room_id' => $roomId,
            'sender_id' => $user['id'],
            'message' => $message,
            'message_type' => 'text'
        ];
        
        if ($this->chatModel->sendMessage($messageData)) {
            // Notify other participants
            $participants = [];
            if ($room['business_owner_id'] !== $user['id']) {
                $participants[] = $room['business_owner_id'];
            }
            if ($room['admin_id'] && $room['admin_id'] !== $user['id']) {
                $participants[] = $room['admin_id'];
            }
            if ($room['inspector_id'] && $room['inspector_id'] !== $user['id']) {
                $participants[] = $room['inspector_id'];
            }
            
            foreach ($participants as $participantId) {
                $this->notificationModel->createChatNotification(
                    $participantId,
                    $user['full_name'],
                    $room['business_name'],
                    $roomId
                );
            }
            
            $this->jsonResponse(['success' => true, 'message' => 'Message sent successfully']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to send message']);
        }
    }

    public function getMessages($roomId) {
        $this->auth->requireLogin();
        
        $messages = $this->chatModel->getMessages($roomId, 50);
        $this->jsonResponse(['success' => true, 'messages' => array_reverse($messages)]);
    }

    public function markAsRead($roomId) {
        $this->auth->requireLogin();
        
        $user = $this->auth->getUser();
        $this->chatModel->markMessagesAsRead($roomId, $user['id']);
        
        $this->jsonResponse(['success' => true]);
    }

    // jsonResponse() method inherited from base Controller class
}
