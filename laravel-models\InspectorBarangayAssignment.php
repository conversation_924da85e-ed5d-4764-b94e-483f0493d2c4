<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InspectorBarangayAssignment extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'inspector_id',
        'barangay_id',
        'assigned_by',
        'assigned_at',
        'status',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'assigned_at' => 'datetime',
        ];
    }

    /**
     * Get the inspector
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Get the barangay
     */
    public function barangay()
    {
        return $this->belongsTo(Barangay::class);
    }

    /**
     * Get the district through barangay
     */
    public function district()
    {
        return $this->hasOneThrough(District::class, Barangay::class, 'id', 'id', 'barangay_id', 'district_id');
    }

    /**
     * Get the admin who made the assignment
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Check if assignment is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Scope for active assignments
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for assignments by inspector
     */
    public function scopeByInspector($query, User $inspector)
    {
        return $query->where('inspector_id', $inspector->id);
    }

    /**
     * Scope for assignments by barangay
     */
    public function scopeByBarangay($query, Barangay $barangay)
    {
        return $query->where('barangay_id', $barangay->id);
    }
}
