<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\User;
use App\Models\Business;
use App\Models\Inspection;
use App\Models\District;
use App\Models\Barangay;
use App\Models\BusinessCategory;
use App\Models\InspectionChecklistCategory;
use App\Models\InspectionChecklistItem;
use App\Models\InspectionChecklistResponse;
use App\Models\BusinessChecklistEvidence;
use App\Services\InspectionService;

class InspectionWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $inspector;
    protected $businessOwner;
    protected $business;
    protected $inspection;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestUsers();
        $this->createTestBusiness();
        $this->createTestChecklist();
    }

    /** @test */
    public function admin_can_create_inspection()
    {
        $this->actingAs($this->admin);

        $inspectionData = [
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
            'scheduled_date' => now()->addDays(7)->format('Y-m-d H:i'),
            'inspection_type' => 'routine',
            'priority' => 'medium',
            'notes' => 'Regular compliance inspection',
        ];

        $response = $this->post(route('admin.inspections.store'), $inspectionData);

        $response->assertRedirect();
        $this->assertDatabaseHas('inspections', [
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
            'status' => 'scheduled',
        ]);
    }

    /** @test */
    public function inspector_can_access_assigned_inspection()
    {
        $this->actingAs($this->inspector);
        
        $inspection = Inspection::factory()->create([
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
            'status' => 'scheduled',
        ]);

        $response = $this->get(route('inspector.inspections.show', $inspection));
        
        $response->assertStatus(200);
        $response->assertSee($this->business->name);
    }

    /** @test */
    public function inspector_cannot_access_unassigned_inspection()
    {
        $otherInspector = User::factory()->create(['role' => 'inspector']);
        $this->actingAs($this->inspector);
        
        $inspection = Inspection::factory()->create([
            'business_id' => $this->business->id,
            'inspector_id' => $otherInspector->id,
            'status' => 'scheduled',
        ]);

        $response = $this->get(route('inspector.inspections.show', $inspection));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function inspector_can_submit_checklist_responses()
    {
        $this->actingAs($this->inspector);
        
        $inspection = Inspection::factory()->create([
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
            'status' => 'scheduled',
        ]);

        $checklistItem = InspectionChecklistItem::factory()->create([
            'points' => 10,
        ]);

        Storage::fake('public');
        $photo = UploadedFile::fake()->image('evidence.jpg');

        $responseData = [
            'responses' => [
                '0_0' => [
                    'checklist_item_id' => $checklistItem->id,
                    'compliance_status' => 'compliant',
                    'notes' => 'All requirements met',
                ]
            ],
            'photo_evidence' => [
                $checklistItem->id => $photo
            ]
        ];

        $response = $this->post(
            route('inspector.inspections.checklist.submit', $inspection),
            $responseData
        );

        $response->assertRedirect();
        $this->assertDatabaseHas('inspection_checklist_responses', [
            'inspection_id' => $inspection->id,
            'checklist_item_id' => $checklistItem->id,
            'compliance_status' => 'compliant',
            'score' => 10,
        ]);
    }

    /** @test */
    public function inspector_can_complete_inspection()
    {
        $this->actingAs($this->inspector);
        
        $inspection = Inspection::factory()->create([
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
            'status' => 'in_progress',
        ]);

        // Create some checklist responses
        InspectionChecklistResponse::factory()->create([
            'inspection_id' => $inspection->id,
            'inspector_id' => $this->inspector->id,
            'score' => 10,
        ]);

        $completionData = [
            'findings' => 'Business meets all safety requirements',
            'recommendations' => 'Continue current safety practices',
            'inspector_notes' => 'Excellent compliance',
            'compliance_rating' => 'A',
        ];

        $response = $this->post(
            route('inspector.inspections.complete', $inspection),
            $completionData
        );

        $response->assertRedirect();
        $this->assertDatabaseHas('inspections', [
            'id' => $inspection->id,
            'status' => 'completed',
            'compliance_rating' => 'A',
            'verification_status' => 'pending',
        ]);
    }

    /** @test */
    public function admin_can_verify_completed_inspection()
    {
        $this->actingAs($this->admin);
        
        $inspection = Inspection::factory()->create([
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
            'status' => 'completed',
            'verification_status' => 'pending',
        ]);

        $verificationData = [
            'status' => 'approved',
            'notes' => 'Inspection approved',
            'admin_notes' => 'Good work by inspector',
        ];

        $response = $this->post(
            route('admin.inspections.verify', $inspection),
            $verificationData
        );

        $response->assertRedirect();
        $this->assertDatabaseHas('inspections', [
            'id' => $inspection->id,
            'verification_status' => 'approved',
            'verified_by' => $this->admin->id,
        ]);
    }

    /** @test */
    public function business_owner_can_upload_evidence()
    {
        $this->actingAs($this->businessOwner);
        
        Storage::fake('public');
        $evidenceFile = UploadedFile::fake()->image('safety_certificate.jpg');
        
        $checklistItem = InspectionChecklistItem::factory()->create();

        $evidenceData = [
            'evidence' => $evidenceFile,
            'notes' => 'Safety certificate for 2024',
        ];

        $response = $this->post(
            route('business-owner.checklist.upload-evidence', $checklistItem),
            $evidenceData
        );

        $response->assertRedirect();
        $this->assertDatabaseHas('business_checklist_evidence', [
            'business_id' => $this->business->id,
            'checklist_item_id' => $checklistItem->id,
            'status' => 'pending',
        ]);
        
        Storage::disk('public')->assertExists('evidence/' . $this->business->id . '/' . $checklistItem->id . '/' . $evidenceFile->hashName());
    }

    /** @test */
    public function business_owner_cannot_upload_evidence_for_other_business()
    {
        $otherBusiness = Business::factory()->create();
        $otherOwner = User::factory()->create(['role' => 'business_owner']);
        $otherBusiness->update(['owner_id' => $otherOwner->id]);
        
        $this->actingAs($this->businessOwner);
        
        Storage::fake('public');
        $evidenceFile = UploadedFile::fake()->image('certificate.jpg');
        
        $checklistItem = InspectionChecklistItem::factory()->create();

        // Try to upload evidence with other business context
        $this->business->update(['owner_id' => $otherOwner->id]);

        $evidenceData = [
            'evidence' => $evidenceFile,
            'notes' => 'Unauthorized upload attempt',
        ];

        $response = $this->post(
            route('business-owner.checklist.upload-evidence', $checklistItem),
            $evidenceData
        );

        $response->assertStatus(403);
    }

    /** @test */
    public function inspection_service_calculates_score_correctly()
    {
        $inspection = Inspection::factory()->create([
            'business_id' => $this->business->id,
            'inspector_id' => $this->inspector->id,
        ]);

        // Create checklist responses with different scores
        InspectionChecklistResponse::factory()->create([
            'inspection_id' => $inspection->id,
            'score' => 10,
        ]);
        
        InspectionChecklistResponse::factory()->create([
            'inspection_id' => $inspection->id,
            'score' => 7,
        ]);
        
        InspectionChecklistResponse::factory()->create([
            'inspection_id' => $inspection->id,
            'score' => 0,
        ]);

        $inspectionService = new InspectionService();
        $totalScore = $inspectionService->calculateInspectionScore($inspection);

        $this->assertEquals(17, $totalScore);
    }

    /** @test */
    public function inspection_service_detects_scheduling_conflicts()
    {
        $scheduledDate = now()->addDays(3)->setTime(10, 0);
        
        // Create existing inspection
        Inspection::factory()->create([
            'inspector_id' => $this->inspector->id,
            'scheduled_date' => $scheduledDate,
            'status' => 'scheduled',
        ]);

        $inspectionService = new InspectionService();
        
        // Test conflict detection (within 2 hours)
        $conflictingDate = $scheduledDate->copy()->addHour();
        $hasConflict = $inspectionService->hasSchedulingConflict(
            $this->inspector->id,
            $conflictingDate
        );
        
        $this->assertTrue($hasConflict);
        
        // Test no conflict (more than 2 hours apart)
        $nonConflictingDate = $scheduledDate->copy()->addHours(3);
        $hasConflict = $inspectionService->hasSchedulingConflict(
            $this->inspector->id,
            $nonConflictingDate
        );
        
        $this->assertFalse($hasConflict);
    }

    /** @test */
    public function api_authentication_works_correctly()
    {
        // Test login
        $response = $this->postJson('/api/login', [
            'email' => $this->inspector->email,
            'password' => 'password',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'user',
                'token',
                'token_type'
            ]
        ]);

        $token = $response->json('data.token');

        // Test authenticated request
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/me');

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $this->inspector->id,
                    'email' => $this->inspector->email,
                ]
            ]
        ]);
    }

    /** @test */
    public function api_returns_inspector_inspections()
    {
        $this->actingAs($this->inspector, 'sanctum');

        $inspection = Inspection::factory()->create([
            'inspector_id' => $this->inspector->id,
            'business_id' => $this->business->id,
        ]);

        $response = $this->getJson('/api/inspections');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'inspections' => [
                    '*' => [
                        'id',
                        'business_id',
                        'inspector_id',
                        'status',
                        'scheduled_date',
                    ]
                ],
                'pagination'
            ]
        ]);
    }

    private function createTestUsers()
    {
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'status' => 'active',
        ]);

        $this->inspector = User::factory()->create([
            'role' => 'inspector',
            'status' => 'active',
        ]);

        $this->businessOwner = User::factory()->create([
            'role' => 'business_owner',
            'status' => 'active',
        ]);
    }

    private function createTestBusiness()
    {
        $district = District::factory()->create();
        $barangay = Barangay::factory()->create(['district_id' => $district->id]);
        $category = BusinessCategory::factory()->create();

        $this->business = Business::factory()->create([
            'owner_id' => $this->businessOwner->id,
            'category_id' => $category->id,
            'barangay_id' => $barangay->id,
            'status' => 'active',
        ]);
    }

    private function createTestChecklist()
    {
        $category = InspectionChecklistCategory::factory()->create();
        
        InspectionChecklistItem::factory()->count(5)->create([
            'category_id' => $category->id,
            'is_active' => true,
        ]);
    }
}
