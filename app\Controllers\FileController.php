<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;

class FileController extends Controller {

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
    }

    /**
     * Serve compliance evidence files securely
     */
    public function serveComplianceEvidence($filename) {
        // Debug logging
        error_log("FileController::serveComplianceEvidence called with filename: " . $filename);

        $this->auth->requireLogin();

        // Only allow admins, inspectors, and business owners to view evidence
        if (!$this->auth->isAdmin() && !$this->auth->isInspector() && !$this->auth->isBusinessOwner()) {
            error_log("FileController: Access denied for user role");
            http_response_code(403);
            echo 'Access denied';
            return;
        }

        // Sanitize filename to prevent directory traversal
        $filename = basename($filename);
        $filePath = ROOT_PATH . '/public/uploads/compliance_evidence/' . $filename;

        error_log("FileController: Looking for file at: " . $filePath);

        // Check if file exists
        if (!file_exists($filePath)) {
            error_log("FileController: File not found at: " . $filePath);
            http_response_code(404);
            echo 'File not found: ' . htmlspecialchars($filename);
            return;
        }

        error_log("FileController: File found, serving: " . $filePath);

        // Get file info
        $fileInfo = pathinfo($filePath);
        $extension = strtolower($fileInfo['extension']);

        // Set appropriate content type
        $contentTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        $contentType = $contentTypes[$extension] ?? 'application/octet-stream';

        // Set headers
        header('Content-Type: ' . $contentType);
        header('Content-Length: ' . filesize($filePath));
        header('Content-Disposition: inline; filename="' . $filename . '"');
        header('Cache-Control: private, max-age=3600');

        // Output file
        readfile($filePath);
        exit;
    }

    /**
     * Serve general uploaded files securely
     */
    public function serveUpload($type, $filename) {
        $this->auth->requireLogin();

        // Sanitize inputs
        $type = basename($type);
        $filename = basename($filename);
        
        // Define allowed upload types and their access rules
        $allowedTypes = [
            'compliance_evidence' => ['admin', 'inspector', 'business_owner'],
            'documents' => ['admin', 'inspector', 'business_owner']
        ];

        if (!isset($allowedTypes[$type])) {
            http_response_code(404);
            echo 'Invalid file type';
            return;
        }

        // Check permissions
        $userRole = $this->auth->getUser()['role'] ?? '';
        $allowedRoles = $allowedTypes[$type];
        
        if (!in_array($userRole, $allowedRoles)) {
            http_response_code(403);
            echo 'Access denied';
            return;
        }

        $filePath = ROOT_PATH . '/public/uploads/' . $type . '/' . $filename;

        if (!file_exists($filePath)) {
            http_response_code(404);
            echo 'File not found';
            return;
        }

        // Get file info and serve
        $fileInfo = pathinfo($filePath);
        $extension = strtolower($fileInfo['extension']);

        $contentTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        $contentType = $contentTypes[$extension] ?? 'application/octet-stream';

        header('Content-Type: ' . $contentType);
        header('Content-Length: ' . filesize($filePath));
        header('Content-Disposition: inline; filename="' . $filename . '"');
        header('Cache-Control: private, max-age=3600');

        readfile($filePath);
        exit;
    }
}
