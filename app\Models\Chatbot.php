<?php
namespace App\Models;

use App\Config\Database;
use PDO;
use PDOException;

class Chatbot {
    private $db;

    public function __construct() {
        $this->db = (new Database())->getConnection();
    }

    /**
     * Check if chatbot is enabled globally
     */
    public function isEnabled() {
        try {
            $stmt = $this->db->prepare("SELECT setting_value FROM chatbot_settings WHERE setting_name = 'chatbot_enabled'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['setting_value'] === 'true' : false;
        } catch (PDOException $e) {
            error_log("Error checking chatbot status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get chatbot setting value
     */
    public function getSetting($settingName) {
        try {
            $stmt = $this->db->prepare("SELECT setting_value FROM chatbot_settings WHERE setting_name = ?");
            $stmt->execute([$settingName]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? $result['setting_value'] : null;
        } catch (PDOException $e) {
            error_log("Error getting chatbot setting: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update chatbot setting
     */
    public function updateSetting($settingName, $value) {
        try {
            $stmt = $this->db->prepare("UPDATE chatbot_settings SET setting_value = ?, updated_at = NOW() WHERE setting_name = ?");
            return $stmt->execute([$value, $settingName]);
        } catch (PDOException $e) {
            error_log("Error updating chatbot setting: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if any admin is currently online
     */
    public function areAdminsOnline() {
        try {
            // Consider admin online if they've been active in the last 5 minutes
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count 
                FROM users 
                WHERE role = 'admin' 
                AND status = 'active' 
                AND last_login > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
        } catch (PDOException $e) {
            error_log("Error checking admin online status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process user message and generate bot response
     */
    public function processMessage($chatRoomId, $userId, $message) {
        try {
            // Clean and normalize the message
            $cleanMessage = strtolower(trim($message));
            
            // Find best matching response
            $response = $this->findBestResponse($cleanMessage);
            
            if (!$response) {
                // Try knowledge base
                $response = $this->searchKnowledgeBase($cleanMessage);
            }
            
            if (!$response) {
                // Use fallback response
                $response = [
                    'id' => null,
                    'response_text' => $this->getSetting('fallback_message'),
                    'response_type' => 'text',
                    'confidence_score' => 0.1
                ];
            }

            // Log the conversation
            $this->logConversation($chatRoomId, $userId, $message, $response['response_text'], $response['id'], $response['confidence_score']);

            // Update usage count if response was from predefined responses
            if ($response['id']) {
                $this->updateResponseUsage($response['id']);
            }

            return $response;
        } catch (PDOException $e) {
            error_log("Error processing chatbot message: " . $e->getMessage());
            return [
                'response_text' => 'I apologize, but I\'m experiencing technical difficulties. Please try again later.',
                'response_type' => 'text',
                'confidence_score' => 0.0
            ];
        }
    }

    /**
     * Find best matching response from predefined responses
     */
    private function findBestResponse($message) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, trigger_keywords, response_text, response_type, priority 
                FROM chatbot_responses 
                WHERE is_active = 1 
                ORDER BY priority DESC
            ");
            $stmt->execute();
            $responses = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $bestMatch = null;
            $highestScore = 0;

            foreach ($responses as $response) {
                $keywords = explode(',', strtolower($response['trigger_keywords']));
                $score = $this->calculateMatchScore($message, $keywords);
                
                if ($score > $highestScore && $score > 0.3) { // Minimum confidence threshold
                    $highestScore = $score;
                    $bestMatch = $response;
                    $bestMatch['confidence_score'] = $score;
                }
            }

            return $bestMatch;
        } catch (PDOException $e) {
            error_log("Error finding best response: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Search knowledge base for relevant information
     */
    private function searchKnowledgeBase($message) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, topic, question, answer, keywords 
                FROM chatbot_knowledge_base 
                WHERE is_active = 1
            ");
            $stmt->execute();
            $knowledge = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $bestMatch = null;
            $highestScore = 0;

            foreach ($knowledge as $item) {
                $keywords = explode(',', strtolower($item['keywords']));
                $questionWords = explode(' ', strtolower($item['question']));
                $allKeywords = array_merge($keywords, $questionWords);
                
                $score = $this->calculateMatchScore($message, $allKeywords);
                
                if ($score > $highestScore && $score > 0.4) { // Higher threshold for knowledge base
                    $highestScore = $score;
                    $bestMatch = [
                        'id' => null,
                        'response_text' => $item['answer'],
                        'response_type' => 'text',
                        'confidence_score' => $score
                    ];
                    
                    // Update view count
                    $this->updateKnowledgeViewCount($item['id']);
                }
            }

            return $bestMatch;
        } catch (PDOException $e) {
            error_log("Error searching knowledge base: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Calculate match score between message and keywords
     */
    private function calculateMatchScore($message, $keywords) {
        $messageWords = explode(' ', $message);
        $matches = 0;
        $totalWords = count($messageWords);

        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if (empty($keyword)) continue;
            
            foreach ($messageWords as $word) {
                if (strpos($word, $keyword) !== false || strpos($keyword, $word) !== false) {
                    $matches++;
                    break;
                }
            }
        }

        return $totalWords > 0 ? $matches / $totalWords : 0;
    }

    /**
     * Log conversation for analytics and learning
     */
    private function logConversation($chatRoomId, $userId, $userMessage, $botResponse, $responseId, $confidenceScore) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO chatbot_conversations 
                (id, chat_room_id, user_id, user_message, bot_response, response_id, confidence_score) 
                VALUES (UUID(), ?, ?, ?, ?, ?, ?)
            ");
            return $stmt->execute([$chatRoomId, $userId, $userMessage, $botResponse, $responseId, $confidenceScore]);
        } catch (PDOException $e) {
            error_log("Error logging conversation: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update response usage count
     */
    private function updateResponseUsage($responseId) {
        try {
            $stmt = $this->db->prepare("UPDATE chatbot_responses SET usage_count = usage_count + 1 WHERE id = ?");
            return $stmt->execute([$responseId]);
        } catch (PDOException $e) {
            error_log("Error updating response usage: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update knowledge base view count
     */
    private function updateKnowledgeViewCount($knowledgeId) {
        try {
            $stmt = $this->db->prepare("UPDATE chatbot_knowledge_base SET view_count = view_count + 1 WHERE id = ?");
            return $stmt->execute([$knowledgeId]);
        } catch (PDOException $e) {
            error_log("Error updating knowledge view count: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Escalate conversation to human
     */
    public function escalateToHuman($chatRoomId, $userId, $reason) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO chatbot_escalations 
                (id, chat_room_id, user_id, reason) 
                VALUES (UUID(), ?, ?, ?)
            ");
            
            if ($stmt->execute([$chatRoomId, $userId, $reason])) {
                // Deactivate chatbot for this room
                $this->setChatbotActive($chatRoomId, false);
                return true;
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error escalating to human: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Set chatbot active status for a chat room
     */
    public function setChatbotActive($chatRoomId, $active) {
        try {
            $stmt = $this->db->prepare("UPDATE chat_rooms SET chatbot_active = ? WHERE id = ?");
            return $stmt->execute([$active ? 1 : 0, $chatRoomId]);
        } catch (PDOException $e) {
            error_log("Error setting chatbot active status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if chatbot is active for a specific room
     */
    public function isChatbotActiveInRoom($chatRoomId) {
        try {
            $stmt = $this->db->prepare("SELECT chatbot_active FROM chat_rooms WHERE id = ?");
            $stmt->execute([$chatRoomId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (bool)$result['chatbot_active'] : false;
        } catch (PDOException $e) {
            error_log("Error checking chatbot room status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get chatbot analytics
     */
    public function getAnalytics() {
        try {
            $analytics = [];
            
            // Total conversations
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM chatbot_conversations");
            $analytics['total_conversations'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Escalations
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM chatbot_escalations");
            $analytics['total_escalations'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Average confidence score
            $stmt = $this->db->query("SELECT AVG(confidence_score) as avg_score FROM chatbot_conversations");
            $avgScore = $stmt->fetch(PDO::FETCH_ASSOC)['avg_score'];
            $analytics['avg_confidence'] = $avgScore ? round($avgScore, 2) : 0;
            
            // Most used responses
            $stmt = $this->db->query("
                SELECT trigger_keywords, usage_count 
                FROM chatbot_responses 
                WHERE usage_count > 0 
                ORDER BY usage_count DESC 
                LIMIT 5
            ");
            $analytics['top_responses'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $analytics;
        } catch (PDOException $e) {
            error_log("Error getting chatbot analytics: " . $e->getMessage());
            return [];
        }
    }

    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
