-- Create inspection checklist tables for the OHS system
-- This script creates the necessary tables for the checklist system

-- Create inspection_checklist_categories table
CREATE TABLE IF NOT EXISTS inspection_checklist_categories (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) DEFAULT 1.00,
    sort_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create inspection_checklist_items table
CREATE TABLE IF NOT EXISTS inspection_checklist_items (
    id VARCHAR(36) PRIMARY KEY,
    category_id VARCHAR(36) NOT NULL,
    item_code VARCHAR(50) NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    description TEXT,
    compliance_requirement TEXT,
    points INT DEFAULT 1,
    is_critical TINYINT(1) DEFAULT 0,
    sort_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES inspection_checklist_categories(id) ON DELETE CASCADE
);

-- Create inspection_checklist_responses table
CREATE TABLE IF NOT EXISTS inspection_checklist_responses (
    id VARCHAR(36) PRIMARY KEY,
    inspection_id VARCHAR(36) NOT NULL,
    checklist_item_id VARCHAR(36) NOT NULL,
    inspector_id VARCHAR(36) NOT NULL,
    compliance_status ENUM('compliant', 'needs_improvement', 'non_compliant', 'not_applicable') NOT NULL,
    score INT DEFAULT 0,
    notes TEXT,
    photo_evidence VARCHAR(255),
    corrective_action TEXT,
    deadline DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (inspection_id) REFERENCES inspections(id) ON DELETE CASCADE,
    FOREIGN KEY (checklist_item_id) REFERENCES inspection_checklist_items(id) ON DELETE CASCADE,
    FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inspection_item (inspection_id, checklist_item_id)
);

-- Create business_checklist_evidence table for business owner uploads
CREATE TABLE IF NOT EXISTS business_checklist_evidence (
    id VARCHAR(36) PRIMARY KEY,
    business_id VARCHAR(36) NOT NULL,
    checklist_item_id VARCHAR(36) NOT NULL,
    inspection_id VARCHAR(36) DEFAULT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INT NOT NULL,
    notes TEXT,
    status ENUM('pending', 'reviewed', 'approved', 'rejected') DEFAULT 'pending',
    uploaded_by VARCHAR(36) NOT NULL,
    reviewed_by VARCHAR(36) DEFAULT NULL,
    reviewed_at DATETIME DEFAULT NULL,
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_id) REFERENCES businesses(id) ON DELETE CASCADE,
    FOREIGN KEY (checklist_item_id) REFERENCES inspection_checklist_items(id) ON DELETE CASCADE,
    FOREIGN KEY (inspection_id) REFERENCES inspections(id) ON DELETE SET NULL,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample checklist categories based on Bacoor inspection standards
INSERT INTO inspection_checklist_categories (id, name, description, weight, sort_order) VALUES
(UUID(), 'General Safety', 'Basic safety requirements and protocols', 2.0, 1),
(UUID(), 'Fire Safety', 'Fire prevention and emergency response measures', 2.5, 2),
(UUID(), 'Health and Sanitation', 'Health standards and sanitation practices', 2.0, 3),
(UUID(), 'Environmental Compliance', 'Environmental protection and waste management', 1.5, 4),
(UUID(), 'Documentation', 'Required permits and documentation', 1.5, 5),
(UUID(), 'Emergency Preparedness', 'Emergency response and preparedness measures', 2.0, 6);

-- Insert sample checklist items for General Safety
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'General Safety'), 'GS001', 'Safety Signage', 'Proper safety signs and warnings displayed', 'All required safety signs must be visible and in good condition', 5, 1, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'General Safety'), 'GS002', 'First Aid Kit', 'Complete and accessible first aid kit', 'First aid kit must be complete, accessible, and regularly maintained', 5, 1, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'General Safety'), 'GS003', 'Safety Officer', 'Designated safety officer present', 'At least one trained safety officer must be designated and present', 3, 0, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'General Safety'), 'GS004', 'Personal Protective Equipment', 'PPE available and used properly', 'Appropriate PPE must be available and used by employees', 4, 1, 4);

-- Insert sample checklist items for Fire Safety
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Fire Safety'), 'FS001', 'Fire Extinguishers', 'Functional fire extinguishers properly placed', 'Fire extinguishers must be functional, properly placed, and regularly inspected', 5, 1, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Fire Safety'), 'FS002', 'Fire Exit Routes', 'Clear and marked fire exit routes', 'Fire exits must be clearly marked, unobstructed, and accessible', 5, 1, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Fire Safety'), 'FS003', 'Fire Safety Certificate', 'Valid fire safety certificate', 'Current fire safety certificate from Bureau of Fire Protection', 3, 0, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Fire Safety'), 'FS004', 'Emergency Lighting', 'Functional emergency lighting system', 'Emergency lighting must be functional and regularly tested', 3, 0, 4);

-- Insert sample checklist items for Health and Sanitation
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Health and Sanitation'), 'HS001', 'Sanitary Facilities', 'Clean and functional sanitary facilities', 'Restrooms and washing facilities must be clean and functional', 4, 1, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Health and Sanitation'), 'HS002', 'Waste Segregation', 'Proper waste segregation system', 'Waste must be properly segregated according to local guidelines', 3, 0, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Health and Sanitation'), 'HS003', 'Pest Control', 'Effective pest control measures', 'Evidence of regular pest control and prevention measures', 3, 0, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Health and Sanitation'), 'HS004', 'Food Safety', 'Food handling and storage compliance', 'Proper food handling, storage, and preparation practices (if applicable)', 4, 1, 4);

-- Insert sample checklist items for Environmental Compliance
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Environmental Compliance'), 'EC001', 'Environmental Permit', 'Valid environmental compliance certificate', 'Current environmental compliance certificate from DENR', 3, 0, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Environmental Compliance'), 'EC002', 'Waste Management', 'Proper waste disposal and management', 'Waste disposal must comply with environmental regulations', 4, 1, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Environmental Compliance'), 'EC003', 'Air Quality', 'Air quality monitoring and control', 'Measures to monitor and control air quality emissions', 3, 0, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Environmental Compliance'), 'EC004', 'Water Management', 'Water usage and wastewater management', 'Proper water usage and wastewater treatment practices', 3, 0, 4);

-- Insert sample checklist items for Documentation
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Documentation'), 'DOC001', 'Business Permit', 'Valid business permit', 'Current business permit from local government', 3, 0, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Documentation'), 'DOC002', 'Sanitary Permit', 'Valid sanitary permit', 'Current sanitary permit from health department', 3, 0, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Documentation'), 'DOC003', 'Safety Training Records', 'Employee safety training documentation', 'Records of safety training for all employees', 2, 0, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Documentation'), 'DOC004', 'Incident Reports', 'Incident reporting and documentation system', 'System for reporting and documenting workplace incidents', 2, 0, 4);

-- Insert sample checklist items for Emergency Preparedness
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Emergency Preparedness'), 'EP001', 'Emergency Plan', 'Written emergency response plan', 'Comprehensive emergency response plan documented and accessible', 4, 1, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Emergency Preparedness'), 'EP002', 'Emergency Drills', 'Regular emergency drill conduct', 'Evidence of regular emergency drills and training', 3, 0, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Emergency Preparedness'), 'EP003', 'Emergency Contacts', 'Emergency contact information posted', 'Emergency contact numbers clearly posted and accessible', 2, 0, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Emergency Preparedness'), 'EP004', 'Communication System', 'Emergency communication system', 'Functional communication system for emergencies', 3, 0, 4);

-- Create indexes for better performance
CREATE INDEX idx_checklist_items_category ON inspection_checklist_items(category_id);
CREATE INDEX idx_checklist_responses_inspection ON inspection_checklist_responses(inspection_id);
CREATE INDEX idx_checklist_responses_item ON inspection_checklist_responses(checklist_item_id);
CREATE INDEX idx_business_evidence_business ON business_checklist_evidence(business_id);
CREATE INDEX idx_business_evidence_item ON business_checklist_evidence(checklist_item_id);
CREATE INDEX idx_business_evidence_inspection ON business_checklist_evidence(inspection_id);
