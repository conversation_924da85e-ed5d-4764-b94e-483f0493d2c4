-- Update checklist items to match Bacoor City inspection standards (Based on Bacoor-Inspection-Monitoring_1.xlsx)
-- This replaces the generic checklist with actual Bacoor inspection requirements

-- Clear existing checklist data
DELETE FROM inspection_checklist_responses;
DELETE FROM inspection_checklist_items;
DELETE FROM inspection_checklist_categories;

-- Insert Bacoor-specific checklist categories based on actual inspection form
INSERT INTO inspection_checklist_categories (id, name, description, weight, sort_order) VALUES
(UUID(), 'Business Documentation', 'Required business permits and documentation', 3.0, 1),
(UUID(), 'Safety & Emergency Preparedness', 'Safety equipment and emergency response measures', 2.5, 2),
(UUID(), 'Health & Sanitation Compliance', 'Health standards and sanitation requirements', 2.0, 3),
(UUID(), 'Security & Monitoring', 'Security systems and monitoring requirements', 1.5, 4),
(UUID(), 'Environmental & Waste Management', 'Environmental compliance and waste management', 1.5, 5);

-- Business Documentation (Based on actual Bacoor inspection form)
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Business Documentation'), 'BD001', 'Business Permit', 'Valid business permit from Bacoor City', 'Current and valid business permit', 5, 1, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Business Documentation'), 'BD002', 'Sanitary Permit', 'Valid sanitary permit from City Health Office', 'Current sanitary permit for business operation', 5, 1, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Business Documentation'), 'BD003', 'Fire Safety Inspection', 'Fire Safety Inspection Certificate', 'Valid FSIC from Bureau of Fire Protection', 5, 1, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Business Documentation'), 'BD004', 'Work Permit', 'Work permit for foreign employees (if applicable)', 'Valid work permits for foreign workers', 3, 0, 4);

-- Safety & Emergency Preparedness (Based on actual Bacoor inspection form)
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Safety & Emergency Preparedness'), 'SE001', 'Safety Officer', 'Designated safety officer for workplace', 'Trained safety officer (if required by business size)', 3, 0, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Safety & Emergency Preparedness'), 'SE002', 'Safety Signage', 'Proper safety signs and warnings displayed', 'Appropriate safety signage throughout premises', 4, 1, 2),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Safety & Emergency Preparedness'), 'SE003', 'First-Aid Kit', 'Complete and accessible first aid kit', 'Well-stocked and accessible first aid kit', 5, 1, 3),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Safety & Emergency Preparedness'), 'SE004', 'Fire Extinguisher', 'Functional fire extinguisher properly placed', 'Working fire extinguisher in accessible location', 5, 1, 4),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Safety & Emergency Preparedness'), 'SE005', 'Emergency Contact', 'Emergency contact information posted', 'Emergency contact numbers clearly displayed', 3, 0, 5);

-- Health & Sanitation Compliance (Based on actual Bacoor inspection form)
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Health & Sanitation Compliance'), 'HS001', 'Clinic/Treatment Room/Lactation Room', 'Medical facility or lactation room (if required)', 'Proper medical/lactation facilities for applicable businesses', 3, 0, 1);

-- Security & Monitoring (Based on actual Bacoor inspection form)
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Security & Monitoring'), 'SM001', 'Monitoring and Security System (CCTV)', 'CCTV or security monitoring system', 'Security cameras or monitoring system (if required)', 2, 0, 1);

-- Environmental & Waste Management (Based on actual Bacoor inspection form)
INSERT INTO inspection_checklist_items (id, category_id, item_code, item_name, description, compliance_requirement, points, is_critical, sort_order) VALUES
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Environmental & Waste Management'), 'EW001', 'Waste Segregation', 'Proper waste segregation system', 'Segregated waste containers and proper disposal practices', 4, 1, 1),
(UUID(), (SELECT id FROM inspection_checklist_categories WHERE name = 'Environmental & Waste Management'), 'EW002', 'Others', 'Other environmental compliance requirements', 'Additional environmental or safety requirements as applicable', 2, 0, 2);
