<?php

namespace App\Services;

use App\Models\Business;
use App\Models\Inspection;
use App\Models\User;
use App\Models\InspectionChecklistResponse;
use App\Models\BusinessChecklistEvidence;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportService
{
    /**
     * Generate inspection report data
     */
    public function generateInspectionReport($filters = [])
    {
        $query = Inspection::with(['business.barangay.district', 'inspector', 'assignedBy']);

        // Apply filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('scheduled_date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('scheduled_date', '<=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['inspector_id'])) {
            $query->where('inspector_id', $filters['inspector_id']);
        }

        if (!empty($filters['district_id'])) {
            $query->whereHas('business.barangay', function($q) use ($filters) {
                $q->where('district_id', $filters['district_id']);
            });
        }

        if (!empty($filters['barangay_id'])) {
            $query->whereHas('business', function($q) use ($filters) {
                $q->where('barangay_id', $filters['barangay_id']);
            });
        }

        $inspections = $query->get();

        // Calculate statistics
        $stats = [
            'total_inspections' => $inspections->count(),
            'completed_inspections' => $inspections->where('status', 'completed')->count(),
            'pending_inspections' => $inspections->where('status', 'scheduled')->count(),
            'in_progress_inspections' => $inspections->where('status', 'in_progress')->count(),
            'average_score' => $inspections->where('status', 'completed')->avg('score'),
            'compliance_rate' => $this->calculateComplianceRate($inspections),
        ];

        // Group by status
        $byStatus = $inspections->groupBy('status')->map(function($group) {
            return $group->count();
        });

        // Group by inspector
        $byInspector = $inspections->groupBy('inspector.full_name')->map(function($group) {
            return [
                'count' => $group->count(),
                'completed' => $group->where('status', 'completed')->count(),
                'average_score' => $group->where('status', 'completed')->avg('score'),
            ];
        });

        // Group by district
        $byDistrict = $inspections->groupBy('business.barangay.district.name')->map(function($group) {
            return [
                'count' => $group->count(),
                'completed' => $group->where('status', 'completed')->count(),
                'compliance_rate' => $this->calculateComplianceRate($group),
            ];
        });

        return [
            'inspections' => $inspections,
            'stats' => $stats,
            'by_status' => $byStatus,
            'by_inspector' => $byInspector,
            'by_district' => $byDistrict,
            'filters' => $filters,
        ];
    }

    /**
     * Generate compliance report data
     */
    public function generateComplianceReport($filters = [])
    {
        $query = Business::with(['category', 'barangay.district', 'inspections', 'checklistEvidence']);

        // Apply filters
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['compliance_status'])) {
            $query->where('compliance_status', $filters['compliance_status']);
        }

        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['district_id'])) {
            $query->whereHas('barangay', function($q) use ($filters) {
                $q->where('district_id', $filters['district_id']);
            });
        }

        $businesses = $query->get();

        // Calculate statistics
        $stats = [
            'total_businesses' => $businesses->count(),
            'compliant_businesses' => $businesses->where('compliance_status', 'compliant')->count(),
            'non_compliant_businesses' => $businesses->where('compliance_status', 'non_compliant')->count(),
            'pending_review_businesses' => $businesses->where('compliance_status', 'pending_review')->count(),
            'overall_compliance_rate' => $this->calculateOverallComplianceRate($businesses),
        ];

        // Group by compliance status
        $byComplianceStatus = $businesses->groupBy('compliance_status')->map(function($group) {
            return $group->count();
        });

        // Group by category
        $byCategory = $businesses->groupBy('category.name')->map(function($group) {
            return [
                'count' => $group->count(),
                'compliant' => $group->where('compliance_status', 'compliant')->count(),
                'compliance_rate' => $group->count() > 0 
                    ? round(($group->where('compliance_status', 'compliant')->count() / $group->count()) * 100, 1)
                    : 0,
            ];
        });

        // Group by district
        $byDistrict = $businesses->groupBy('barangay.district.name')->map(function($group) {
            return [
                'count' => $group->count(),
                'compliant' => $group->where('compliance_status', 'compliant')->count(),
                'compliance_rate' => $group->count() > 0 
                    ? round(($group->where('compliance_status', 'compliant')->count() / $group->count()) * 100, 1)
                    : 0,
            ];
        });

        return [
            'businesses' => $businesses,
            'stats' => $stats,
            'by_compliance_status' => $byComplianceStatus,
            'by_category' => $byCategory,
            'by_district' => $byDistrict,
            'filters' => $filters,
        ];
    }

    /**
     * Generate inspector performance report
     */
    public function generateInspectorReport($filters = [])
    {
        $query = User::where('role', 'inspector')->with(['assignedInspections', 'districtAssignments.district']);

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $inspectors = $query->get();

        $inspectorStats = $inspectors->map(function($inspector) use ($filters) {
            $inspectionsQuery = $inspector->assignedInspections();

            // Apply date filters
            if (!empty($filters['date_from'])) {
                $inspectionsQuery->whereDate('scheduled_date', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $inspectionsQuery->whereDate('scheduled_date', '<=', $filters['date_to']);
            }

            $inspections = $inspectionsQuery->get();

            return [
                'inspector' => $inspector,
                'total_assigned' => $inspections->count(),
                'completed' => $inspections->where('status', 'completed')->count(),
                'pending' => $inspections->where('status', 'scheduled')->count(),
                'in_progress' => $inspections->where('status', 'in_progress')->count(),
                'completion_rate' => $inspections->count() > 0 
                    ? round(($inspections->where('status', 'completed')->count() / $inspections->count()) * 100, 1)
                    : 0,
                'average_score' => $inspections->where('status', 'completed')->avg('score'),
                'assigned_districts' => $inspector->districtAssignments->pluck('district.name')->implode(', '),
            ];
        });

        return [
            'inspector_stats' => $inspectorStats,
            'filters' => $filters,
        ];
    }

    /**
     * Generate monthly analytics data
     */
    public function generateMonthlyAnalytics($year = null)
    {
        $year = $year ?? Carbon::now()->year;
        
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[] = Carbon::create($year, $i, 1)->format('M');
        }

        // Monthly inspections
        $monthlyInspections = DB::table('inspections')
            ->select(
                DB::raw('MONTH(scheduled_date) as month'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed')
            )
            ->whereYear('scheduled_date', $year)
            ->groupBy(DB::raw('MONTH(scheduled_date)'))
            ->get()
            ->keyBy('month');

        // Monthly business registrations
        $monthlyBusinesses = DB::table('businesses')
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as total')
            )
            ->whereYear('created_at', $year)
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->get()
            ->keyBy('month');

        // Prepare data arrays
        $inspectionTotals = [];
        $inspectionCompleted = [];
        $businessRegistrations = [];

        for ($i = 1; $i <= 12; $i++) {
            $inspectionTotals[] = $monthlyInspections->get($i)->total ?? 0;
            $inspectionCompleted[] = $monthlyInspections->get($i)->completed ?? 0;
            $businessRegistrations[] = $monthlyBusinesses->get($i)->total ?? 0;
        }

        return [
            'year' => $year,
            'months' => $months,
            'inspection_totals' => $inspectionTotals,
            'inspection_completed' => $inspectionCompleted,
            'business_registrations' => $businessRegistrations,
        ];
    }

    /**
     * Calculate compliance rate for inspections
     */
    private function calculateComplianceRate($inspections)
    {
        $completedInspections = $inspections->where('status', 'completed');
        
        if ($completedInspections->count() === 0) {
            return 0;
        }

        $compliantInspections = $completedInspections->filter(function($inspection) {
            return in_array($inspection->compliance_rating, ['A', 'B']);
        });

        return round(($compliantInspections->count() / $completedInspections->count()) * 100, 1);
    }

    /**
     * Calculate overall compliance rate for businesses
     */
    private function calculateOverallComplianceRate($businesses)
    {
        if ($businesses->count() === 0) {
            return 0;
        }

        $compliantBusinesses = $businesses->where('compliance_status', 'compliant');

        return round(($compliantBusinesses->count() / $businesses->count()) * 100, 1);
    }

    /**
     * Export report data to array format (for Excel/PDF export)
     */
    public function exportInspectionReport($filters = [])
    {
        $reportData = $this->generateInspectionReport($filters);
        
        $exportData = [];
        foreach ($reportData['inspections'] as $inspection) {
            $exportData[] = [
                'Date' => $inspection->scheduled_date->format('Y-m-d'),
                'Business Name' => $inspection->business->name,
                'Business Owner' => $inspection->business->owner_name,
                'District' => $inspection->business->barangay->district->name,
                'Barangay' => $inspection->business->barangay->name,
                'Inspector' => $inspection->inspector->full_name,
                'Status' => ucfirst($inspection->status),
                'Score' => $inspection->score ?? 'N/A',
                'Compliance Rating' => $inspection->compliance_rating ?? 'N/A',
                'Verification Status' => ucfirst($inspection->verification_status),
            ];
        }

        return $exportData;
    }
}
