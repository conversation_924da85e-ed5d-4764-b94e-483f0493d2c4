<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-robot text-primary me-2"></i>Chatbot Management
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/chatbot/responses" class="btn btn-primary">
                <i class="fas fa-comments"></i> Manage Responses
            </a>
            <a href="<?= BASE_URL ?>admin/chatbot/knowledge" class="btn btn-outline-primary">
                <i class="fas fa-brain"></i> Knowledge Base
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Chatbot Status Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chatbot Status
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php 
                                $isEnabled = false;
                                foreach ($settings as $setting) {
                                    if ($setting['setting_name'] === 'chatbot_enabled') {
                                        $isEnabled = $setting['setting_value'] === 'true';
                                        break;
                                    }
                                }
                                echo $isEnabled ? '<span class="text-success">Enabled</span>' : '<span class="text-danger">Disabled</span>';
                                ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-robot fa-2x <?= $isEnabled ? 'text-success' : 'text-gray-300' ?>"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Conversations -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Conversations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $analytics['total_conversations'] ?? 0 ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comments fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Escalations -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Escalations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $analytics['total_escalations'] ?? 0 ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Confidence -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Avg Confidence
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format(($analytics['avg_confidence'] ?? 0) * 100, 1) ?>%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Chatbot Settings -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>Chatbot Settings
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>admin/chatbot/settings" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="chatbot_enabled" name="chatbot_enabled" 
                                           <?= $isEnabled ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="chatbot_enabled">
                                        <strong>Enable Chatbot</strong>
                                    </label>
                                    <div class="form-text">Turn the chatbot system on or off globally</div>
                                </div>

                                <div class="form-check form-switch mb-3">
                                    <?php 
                                    $autoRespond = false;
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'auto_respond_when_offline') {
                                            $autoRespond = $setting['setting_value'] === 'true';
                                            break;
                                        }
                                    }
                                    ?>
                                    <input class="form-check-input" type="checkbox" id="auto_respond_when_offline" name="auto_respond_when_offline" 
                                           <?= $autoRespond ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="auto_respond_when_offline">
                                        <strong>Auto-respond when offline</strong>
                                    </label>
                                    <div class="form-text">Automatically activate chatbot when no admins are online</div>
                                </div>

                                <div class="mb-3">
                                    <label for="response_delay" class="form-label">Response Delay (seconds)</label>
                                    <?php 
                                    $responseDelay = '2';
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'response_delay') {
                                            $responseDelay = $setting['setting_value'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <input type="number" class="form-control" id="response_delay" name="response_delay" 
                                           value="<?= htmlspecialchars($responseDelay) ?>" min="0" max="10">
                                    <div class="form-text">Delay before bot responds (simulates typing)</div>
                                </div>

                                <div class="mb-3">
                                    <label for="escalation_threshold" class="form-label">Escalation Threshold</label>
                                    <?php 
                                    $escalationThreshold = '3';
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'escalation_threshold') {
                                            $escalationThreshold = $setting['setting_value'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <input type="number" class="form-control" id="escalation_threshold" name="escalation_threshold" 
                                           value="<?= htmlspecialchars($escalationThreshold) ?>" min="1" max="10">
                                    <div class="form-text">Number of unresolved queries before escalating</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="greeting_message" class="form-label">Greeting Message</label>
                                    <?php 
                                    $greetingMessage = '';
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'greeting_message') {
                                            $greetingMessage = $setting['setting_value'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <textarea class="form-control" id="greeting_message" name="greeting_message" rows="2"><?= htmlspecialchars($greetingMessage) ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="offline_message" class="form-label">Offline Message</label>
                                    <?php 
                                    $offlineMessage = '';
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'offline_message') {
                                            $offlineMessage = $setting['setting_value'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <textarea class="form-control" id="offline_message" name="offline_message" rows="2"><?= htmlspecialchars($offlineMessage) ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="escalation_message" class="form-label">Escalation Message</label>
                                    <?php 
                                    $escalationMessage = '';
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'escalation_message') {
                                            $escalationMessage = $setting['setting_value'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <textarea class="form-control" id="escalation_message" name="escalation_message" rows="2"><?= htmlspecialchars($escalationMessage) ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="fallback_message" class="form-label">Fallback Message</label>
                                    <?php 
                                    $fallbackMessage = '';
                                    foreach ($settings as $setting) {
                                        if ($setting['setting_name'] === 'fallback_message') {
                                            $fallbackMessage = $setting['setting_value'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <textarea class="form-control" id="fallback_message" name="fallback_message" rows="2"><?= htmlspecialchars($fallbackMessage) ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Top Responses -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Top Responses
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($analytics['top_responses'])): ?>
                        <?php foreach ($analytics['top_responses'] as $response): ?>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-sm"><?= htmlspecialchars(substr($response['trigger_keywords'], 0, 30)) ?>...</span>
                                    <span class="badge bg-primary"><?= $response['usage_count'] ?></span>
                                </div>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" style="width: <?= min(100, ($response['usage_count'] / max(1, $analytics['top_responses'][0]['usage_count'])) * 100) ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center">No usage data available yet.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>
