<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Admin Dashboard</h1>
        <div>
            <a href="<?= BASE_URL ?>admin/businesses/create" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2"></i>New Business
            </a>
            <a href="<?= BASE_URL ?>admin/users/create" class="btn btn-outline-primary">
                <i class="fas fa-user-plus me-2"></i>New User
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-primary bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-building text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['business_count'] ?></h3>
                            <p class="text-muted mb-0">Total Businesses</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>admin/businesses" class="btn btn-primary w-100">View Details →</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-warning bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-chart-line text-warning"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['compliant_businesses'] ?></h3>
                            <p class="text-muted mb-0">Compliant Businesses</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>admin/businesses?compliance=compliant" class="btn btn-primary w-100">View Details →</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-success bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-clipboard-check text-success"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['inspection_count'] ?></h3>
                            <p class="text-muted mb-0">Completed Inspections</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>admin/compliance" class="btn btn-primary w-100">View Results →</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-info bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-users text-info"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['user_count'] ?></h3>
                            <p class="text-muted mb-0">Total Users</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>admin/users" class="btn btn-primary w-100">View Details →</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inspection Management Card -->
        <div class="col-lg-3 col-md-6">
            <div class="card border-start border-success border-4 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h3 class="h2 mb-1">
                                <i class="fas fa-calendar-check text-success me-2"></i>
                                Schedule
                            </h3>
                            <p class="text-muted mb-0">Inspection Management</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-success w-100">Integrated Management →</a>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Recent Businesses -->
    <div class="card border-0 rounded-3 shadow-sm mb-4">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-building text-primary me-2"></i>
                    <h5 class="mb-0">Recent Businesses</h5>
                </div>
                <a href="<?= BASE_URL ?>admin/businesses" class="btn btn-primary btn-sm">View All</a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="ps-4">Business Name</th>
                            <th>Owner</th>
                            <th>Status</th>
                            <th class="pe-4">Registered</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($stats['recent_businesses'])): ?>
                        <tr>
                            <td colspan="4" class="text-center py-4">No businesses found</td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($stats['recent_businesses'] as $business): ?>
                            <tr>
                                <td class="ps-4"><?= htmlspecialchars($business['name']) ?></td>
                                <td><?= htmlspecialchars($business['owner_name']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $business['status'] === 'active' ? 'success' : 'warning' ?>">
                                        <?= ucfirst($business['status']) ?>
                                    </span>
                                </td>
                                <td class="pe-4"><?= date('M d, Y', strtotime($business['created_at'])) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>



    <!-- Upcoming Inspections -->
    <div class="card border-0 rounded-3 shadow-sm">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clipboard-check text-success me-2"></i>
                    <h5 class="mb-0">Upcoming Inspections</h5>
                </div>
                <a href="<?= BASE_URL ?>admin/compliance" class="btn btn-primary btn-sm">View Results</a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="ps-4">Business</th>
                            <th>Inspector</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th class="pe-4">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($stats['upcoming_inspections'])): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">No upcoming inspections</td>
                        </tr>
                          <?php else: ?>
                              <?php foreach ($stats['upcoming_inspections'] as $inspection): ?>
                              <tr>
                                  <td class="ps-4">
                                      <div>
                                          <div class="fw-medium"><?= htmlspecialchars($inspection['business_name']) ?></div>
                                          <small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                      </div>
                                  </td>
                                  <td>
                                      <div>
                                          <div class="fw-medium"><?= htmlspecialchars($inspection['inspector_name'] ?? 'Unassigned') ?></div>
                                          <small class="text-muted"><?= htmlspecialchars($inspection['inspector_email'] ?? 'N/A') ?></small>
                                      </div>
                                  </td>
                                  <td>
                                      <span class="badge bg-light text-dark">
                                          <?= ucfirst($inspection['inspection_type'] ?? 'routine') ?>
                                      </span>
</td>
                                <td>
                                    <div>
                                        <div class="fw-medium"><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></div>
                                        <small class="text-muted"><?= date('h:i A', strtotime($inspection['scheduled_date'])) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= ucfirst($inspection['status']) ?>
                                    </span>
                                </td>
                                <td class="pe-4">
                                    <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    background: #fff;
}
.table > :not(caption) > * > * {
    padding: 1rem;
    background: none;
}
.table {
    margin: 0;
}
.badge {
    font-weight: 500;
}
.btn-primary {
    background: #0d6efd;
    border-color: #0d6efd;
}
.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
}
.btn-outline-primary:hover {
    background: #0d6efd;
    color: #fff;
}
  .table-light {
      background: #f8f9fa !important;
  }
  </style>
  <?php $this->endSection(); ?>
