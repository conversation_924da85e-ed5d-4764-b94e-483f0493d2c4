# 🚀 OHS System Laravel Deployment Guide

## **📋 Pre-Deployment Checklist**

### **✅ System Requirements**
- PHP 8.1 or higher
- MySQL 8.0 or higher
- Composer 2.x
- Node.js 16+ and NPM
- Web server (Apache/Nginx)
- SSL certificate (recommended)

### **✅ Required PHP Extensions**
```bash
php -m | grep -E "(pdo|mysql|gd|zip|xml|mbstring|curl|openssl|tokenizer|fileinfo|json)"
```

---

## **🔧 Step 1: Environment Setup**

### **1.1 Create Laravel Project**
```bash
# Create new Laravel project
composer create-project laravel/laravel ohs-laravel "^10.0"
cd ohs-laravel

# Set permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### **1.2 Copy Migration Files**
```bash
# Copy all Laravel migration files
cp -r laravel-migrations/* database/migrations/
cp -r laravel-models/* app/Models/
cp -r laravel-controllers/* app/Http/Controllers/
cp -r laravel-middleware/* app/Http/Middleware/
cp -r laravel-requests/* app/Http/Requests/
cp -r laravel-services/* app/Services/
cp -r laravel-views/* resources/views/
cp laravel-routes/web.php routes/web.php
```

### **1.3 Environment Configuration**
```bash
# Copy and configure environment
cp .env.example .env
php artisan key:generate
```

**Edit .env file:**
```env
APP_NAME="OHS System"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ohs_laravel
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="OHS System"

FILESYSTEM_DISK=public
```

---

## **🗄️ Step 2: Database Setup**

### **2.1 Create Database**
```sql
CREATE DATABASE ohs_laravel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ohs_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON ohs_laravel.* TO 'ohs_user'@'localhost';
FLUSH PRIVILEGES;
```

### **2.2 Run Migrations**
```bash
# Run all migrations
php artisan migrate

# Create storage link
php artisan storage:link

# Clear caches
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### **2.3 Seed Initial Data**
```bash
# Create admin user
php artisan tinker
```
```php
// In tinker
use App\Models\User;
use Illuminate\Support\Facades\Hash;

User::create([
    'email' => '<EMAIL>',
    'password' => Hash::make('admin123'),
    'full_name' => 'System Administrator',
    'role' => 'admin',
    'status' => 'active',
]);
```

---

## **📦 Step 3: Dependencies & Assets**

### **3.1 Install PHP Dependencies**
```bash
# Install production dependencies
composer install --optimize-autoloader --no-dev

# Install additional packages
composer require intervention/image
composer require laravel/sanctum
```

### **3.2 Install Frontend Dependencies**
```bash
# Install Node.js dependencies
npm install

# Build assets for production
npm run build
```

### **3.3 Configure Middleware**
**Edit `app/Http/Kernel.php`:**
```php
protected $routeMiddleware = [
    // ... existing middleware
    'role' => \App\Http\Middleware\RoleMiddleware::class,
];
```

---

## **🔄 Step 4: Data Migration (Optional)**

### **4.1 Configure Old Database Connection**
**Add to `config/database.php`:**
```php
'mysql_old' => [
    'driver' => 'mysql',
    'host' => env('DB_OLD_HOST', '127.0.0.1'),
    'port' => env('DB_OLD_PORT', '3306'),
    'database' => env('DB_OLD_DATABASE', 'old_ohs'),
    'username' => env('DB_OLD_USERNAME', 'root'),
    'password' => env('DB_OLD_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
],
```

### **4.2 Run Data Migration**
```bash
# Create migration command
php artisan make:command MigrateOldData

# Run migration
php artisan migrate:old-data
```

---

## **🌐 Step 5: Web Server Configuration**

### **5.1 Apache Configuration**
**Create `/etc/apache2/sites-available/ohs-system.conf`:**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/ohs-laravel/public
    
    <Directory /var/www/ohs-laravel/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/ohs_error.log
    CustomLog ${APACHE_LOG_DIR}/ohs_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /var/www/ohs-laravel/public
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    <Directory /var/www/ohs-laravel/public>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

### **5.2 Nginx Configuration**
**Create `/etc/nginx/sites-available/ohs-system`:**
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name your-domain.com;
    root /var/www/ohs-laravel/public;
    index index.php index.html;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
```

---

## **🔒 Step 6: Security Configuration**

### **6.1 File Permissions**
```bash
# Set proper permissions
find /var/www/ohs-laravel -type f -exec chmod 644 {} \;
find /var/www/ohs-laravel -type d -exec chmod 755 {} \;
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data /var/www/ohs-laravel
```

### **6.2 Environment Security**
```bash
# Secure .env file
chmod 600 .env
chown www-data:www-data .env

# Hide sensitive files
echo "deny from all" > .env.htaccess
```

### **6.3 Database Security**
```sql
-- Remove test databases and users
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
FLUSH PRIVILEGES;
```

---

## **📧 Step 7: Email Configuration**

### **7.1 Gmail SMTP Setup**
1. Enable 2-factor authentication
2. Generate app-specific password
3. Update .env with credentials

### **7.2 Test Email Configuration**
```bash
php artisan tinker
```
```php
// Test email
use App\Services\EmailService;
$emailService = new EmailService();
$emailService->testEmailConfiguration('<EMAIL>');
```

---

## **🔍 Step 8: Testing & Validation**

### **8.1 System Health Check**
```bash
# Check system status
php artisan about

# Test database connection
php artisan migrate:status

# Check file permissions
ls -la storage/ bootstrap/cache/
```

### **8.2 Functional Testing**
1. **Admin Login**: Test admin dashboard access
2. **Business Registration**: Create test business
3. **Inspector Assignment**: Assign inspector to business
4. **Inspection Process**: Complete full inspection workflow
5. **File Upload**: Test evidence upload functionality
6. **Email Notifications**: Verify email delivery

### **8.3 Performance Testing**
```bash
# Optimize for production
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Test page load times
curl -w "@curl-format.txt" -o /dev/null -s "https://your-domain.com"
```

---

## **🚀 Step 9: Go Live**

### **9.1 Final Checklist**
- [ ] SSL certificate installed and working
- [ ] Database backups configured
- [ ] Email notifications working
- [ ] File uploads working
- [ ] All user roles can login
- [ ] Inspection workflow complete
- [ ] Error logging configured
- [ ] Monitoring setup

### **9.2 Launch Commands**
```bash
# Final optimization
php artisan optimize:clear
php artisan optimize

# Set production mode
php artisan down --message="System maintenance in progress"
php artisan up
```

---

## **📊 Step 10: Monitoring & Maintenance**

### **10.1 Log Monitoring**
```bash
# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Monitor web server logs
tail -f /var/log/apache2/ohs_error.log
tail -f /var/log/nginx/error.log
```

### **10.2 Backup Strategy**
```bash
# Database backup script
#!/bin/bash
mysqldump -u ohs_user -p ohs_laravel > backup_$(date +%Y%m%d_%H%M%S).sql

# File backup
tar -czf files_backup_$(date +%Y%m%d_%H%M%S).tar.gz storage/app/public/
```

### **10.3 Update Procedure**
```bash
# Update application
git pull origin main
composer install --no-dev
php artisan migrate
php artisan optimize:clear
php artisan optimize
```

---

## **🆘 Troubleshooting**

### **Common Issues:**
1. **500 Error**: Check storage permissions and .env configuration
2. **Database Connection**: Verify credentials and MySQL service
3. **File Upload Issues**: Check storage link and permissions
4. **Email Not Sending**: Verify SMTP credentials and firewall
5. **CSS/JS Not Loading**: Run `npm run build` and check asset paths

### **Support Contacts:**
- **Technical Issues**: Check Laravel documentation
- **System Administration**: Review server logs
- **Database Issues**: Check MySQL error logs

---

## **🎉 Deployment Complete!**

Your OHS System Laravel application is now ready for production use! 

**Default Admin Credentials:**
- Email: <EMAIL>
- Password: admin123

**Remember to change the default password immediately after first login!**
