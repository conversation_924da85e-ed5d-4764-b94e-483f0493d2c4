@extends('layouts.inspector')

@section('title', 'Inspection Checklist')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800">Inspection Checklist</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('inspector.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ route('inspector.inspections.index') }}">Inspections</a></li>
                <li class="breadcrumb-item"><a href="{{ route('inspector.inspections.show', $inspection) }}">{{ $inspection->business->name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Checklist</li>
            </ol>
        </nav>
    </div>
    <div class="d-none d-sm-inline-block">
        <span class="badge badge-{{ $inspection->status === 'completed' ? 'success' : 'warning' }} badge-lg">
            {{ ucfirst($inspection->status) }}
        </span>
    </div>
</div>

<!-- Inspection Info -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Inspection Information</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-2"><strong>Business:</strong> {{ $inspection->business->name }}</div>
                <div class="mb-2"><strong>Owner:</strong> {{ $inspection->business->owner_name }}</div>
                <div class="mb-2"><strong>Category:</strong> {{ $inspection->business->category->name }}</div>
                <div class="mb-2"><strong>Location:</strong> {{ $inspection->business->barangay->name }}, {{ $inspection->business->barangay->district->name }}</div>
            </div>
            <div class="col-md-6">
                <div class="mb-2"><strong>Scheduled Date:</strong> {{ $inspection->scheduled_date->format('M j, Y g:i A') }}</div>
                <div class="mb-2"><strong>Type:</strong> {{ ucfirst($inspection->inspection_type) }}</div>
                <div class="mb-2"><strong>Priority:</strong> 
                    <span class="badge badge-{{ $inspection->priority === 'high' ? 'danger' : ($inspection->priority === 'medium' ? 'warning' : 'info') }}">
                        {{ ucfirst($inspection->priority) }}
                    </span>
                </div>
                <div class="mb-2"><strong>Progress:</strong> 
                    <span id="progress-text">{{ $existingResponses->count() }}/{{ $categories->sum(fn($cat) => $cat->activeChecklistItems->count()) }} items completed</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Checklist Form -->
<form id="checklistForm" action="{{ route('inspector.inspections.checklist.submit', $inspection) }}" method="POST" enctype="multipart/form-data">
    @csrf
    
    <!-- Progress Bar -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-sm font-weight-bold">Checklist Progress</span>
                <span id="progress-percentage">{{ $categories->sum(fn($cat) => $cat->activeChecklistItems->count()) > 0 ? round(($existingResponses->count() / $categories->sum(fn($cat) => $cat->activeChecklistItems->count())) * 100, 1) : 0 }}%</span>
            </div>
            <div class="progress">
                <div id="progress-bar" class="progress-bar bg-success" role="progressbar" 
                     style="width: {{ $categories->sum(fn($cat) => $cat->activeChecklistItems->count()) > 0 ? round(($existingResponses->count() / $categories->sum(fn($cat) => $cat->activeChecklistItems->count())) * 100, 1) : 0 }}%" 
                     aria-valuenow="{{ $existingResponses->count() }}" 
                     aria-valuemin="0" 
                     aria-valuemax="{{ $categories->sum(fn($cat) => $cat->activeChecklistItems->count()) }}"></div>
            </div>
        </div>
    </div>

    <!-- Checklist Categories -->
    @foreach($categories as $category)
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{{ $category->name }}</h6>
            <div class="text-xs text-gray-500">
                Weight: {{ $category->weight }} | {{ $category->activeChecklistItems->count() }} items
            </div>
        </div>
        <div class="card-body">
            @if($category->description)
                <p class="text-muted mb-4">{{ $category->description }}</p>
            @endif
            
            @foreach($category->activeChecklistItems as $item)
                @php
                    $existingResponse = $existingResponses->get($item->id);
                    $businessEvidence = $businessEvidence->get($item->id, collect());
                @endphp
                
                <div class="checklist-item border rounded p-3 mb-3" data-item-id="{{ $item->id }}">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="d-flex align-items-start mb-2">
                                <h6 class="font-weight-bold text-dark mb-1">
                                    {{ $item->item_code }}: {{ $item->item_name }}
                                    @if($item->is_critical)
                                        <span class="badge badge-danger badge-sm ml-1">Critical</span>
                                    @endif
                                    <span class="badge badge-info badge-sm ml-1">{{ $item->points }} pts</span>
                                </h6>
                            </div>
                            
                            @if($item->description)
                                <p class="text-muted mb-2">{{ $item->description }}</p>
                            @endif
                            
                            @if($item->compliance_requirement)
                                <div class="alert alert-info alert-sm mb-3">
                                    <strong>Requirement:</strong> {{ $item->compliance_requirement }}
                                </div>
                            @endif
                            
                            <!-- Business Evidence -->
                            @if($businessEvidence->count() > 0)
                                <div class="mb-3">
                                    <h6 class="text-sm font-weight-bold text-success">Business Evidence:</h6>
                                    @foreach($businessEvidence as $evidence)
                                        <div class="d-flex align-items-center border rounded p-2 mb-2 bg-light">
                                            @if($evidence->isImage())
                                                <i class="fas fa-image text-primary mr-2"></i>
                                            @else
                                                <i class="fas fa-file text-secondary mr-2"></i>
                                            @endif
                                            <div class="flex-grow-1">
                                                <div class="font-weight-bold">{{ $evidence->file_name }}</div>
                                                <div class="text-xs text-muted">
                                                    {{ $evidence->getFormattedFileSizeAttribute() }} • 
                                                    {{ $evidence->created_at->format('M j, Y') }}
                                                    @if($evidence->notes)
                                                        • {{ $evidence->notes }}
                                                    @endif
                                                </div>
                                            </div>
                                            @if($evidence->isImage())
                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                        onclick="viewEvidence('{{ $evidence->getFileUrlAttribute() }}', '{{ $evidence->file_name }}')">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                            @else
                                                <a href="{{ $evidence->getFileUrlAttribute() }}" target="_blank" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Compliance Status -->
                            <div class="form-group">
                                <label class="form-label font-weight-bold">Compliance Status <span class="text-danger">*</span></label>
                                <div class="form-check">
                                    <input class="form-check-input compliance-radio" type="radio" 
                                           name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][compliance_status]" 
                                           id="compliant_{{ $item->id }}" value="compliant"
                                           {{ $existingResponse && $existingResponse->compliance_status === 'compliant' ? 'checked' : '' }}
                                           data-points="{{ $item->points }}">
                                    <label class="form-check-label text-success" for="compliant_{{ $item->id }}">
                                        <i class="fas fa-check-circle"></i> Compliant
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input compliance-radio" type="radio" 
                                           name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][compliance_status]" 
                                           id="needs_improvement_{{ $item->id }}" value="needs_improvement"
                                           {{ $existingResponse && $existingResponse->compliance_status === 'needs_improvement' ? 'checked' : '' }}
                                           data-points="{{ round($item->points * 0.7) }}">
                                    <label class="form-check-label text-warning" for="needs_improvement_{{ $item->id }}">
                                        <i class="fas fa-exclamation-triangle"></i> Needs Improvement
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input compliance-radio" type="radio" 
                                           name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][compliance_status]" 
                                           id="non_compliant_{{ $item->id }}" value="non_compliant"
                                           {{ $existingResponse && $existingResponse->compliance_status === 'non_compliant' ? 'checked' : '' }}
                                           data-points="0">
                                    <label class="form-check-label text-danger" for="non_compliant_{{ $item->id }}">
                                        <i class="fas fa-times-circle"></i> Non-Compliant
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input compliance-radio" type="radio" 
                                           name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][compliance_status]" 
                                           id="not_applicable_{{ $item->id }}" value="not_applicable"
                                           {{ $existingResponse && $existingResponse->compliance_status === 'not_applicable' ? 'checked' : '' }}
                                           data-points="{{ $item->points }}">
                                    <label class="form-check-label text-secondary" for="not_applicable_{{ $item->id }}">
                                        <i class="fas fa-minus-circle"></i> Not Applicable
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Photo Evidence -->
                            <div class="form-group">
                                <label for="photo_{{ $item->id }}" class="form-label">Photo Evidence</label>
                                <input type="file" class="form-control-file" 
                                       id="photo_{{ $item->id }}" 
                                       name="photo_evidence[{{ $item->id }}]" 
                                       accept="image/*">
                                <small class="form-text text-muted">Optional inspection photo</small>
                            </div>
                            
                            <!-- Notes -->
                            <div class="form-group">
                                <label for="notes_{{ $item->id }}" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes_{{ $item->id }}" 
                                          name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][notes]" 
                                          rows="3" placeholder="Inspection notes...">{{ $existingResponse ? $existingResponse->notes : '' }}</textarea>
                            </div>
                            
                            <!-- Corrective Action (for non-compliant items) -->
                            <div class="form-group corrective-action" style="display: {{ $existingResponse && in_array($existingResponse->compliance_status, ['non_compliant', 'needs_improvement']) ? 'block' : 'none' }};">
                                <label for="corrective_action_{{ $item->id }}" class="form-label">Corrective Action Required</label>
                                <textarea class="form-control" id="corrective_action_{{ $item->id }}" 
                                          name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][corrective_action]" 
                                          rows="2" placeholder="Required corrective actions...">{{ $existingResponse ? $existingResponse->corrective_action : '' }}</textarea>
                            </div>
                            
                            <!-- Deadline (for non-compliant items) -->
                            <div class="form-group deadline-field" style="display: {{ $existingResponse && in_array($existingResponse->compliance_status, ['non_compliant', 'needs_improvement']) ? 'block' : 'none' }};">
                                <label for="deadline_{{ $item->id }}" class="form-label">Deadline</label>
                                <input type="date" class="form-control" id="deadline_{{ $item->id }}" 
                                       name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][deadline]" 
                                       min="{{ now()->addDay()->format('Y-m-d') }}"
                                       value="{{ $existingResponse && $existingResponse->deadline ? $existingResponse->deadline->format('Y-m-d') : '' }}">
                            </div>
                            
                            <!-- Hidden field for checklist item ID -->
                            <input type="hidden" name="responses[{{ $loop->parent->index }}_{{ $loop->index }}][checklist_item_id]" value="{{ $item->id }}">
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endforeach
    
    <!-- Action Buttons -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="text-muted">Total Score: </span>
                    <span id="total-score" class="font-weight-bold text-primary">{{ $existingResponses->sum('score') }}</span>
                    <span class="text-muted">/ <span id="max-score">{{ $categories->sum(fn($cat) => $cat->activeChecklistItems->sum('points')) }}</span></span>
                    <span class="ml-2">(<span id="score-percentage">{{ $categories->sum(fn($cat) => $cat->activeChecklistItems->sum('points')) > 0 ? round(($existingResponses->sum('score') / $categories->sum(fn($cat) => $cat->activeChecklistItems->sum('points'))) * 100, 1) : 0 }}</span>%)</span>
                </div>
                <div>
                    <a href="{{ route('inspector.inspections.show', $inspection) }}" class="btn btn-secondary mr-2">
                        <i class="fas fa-arrow-left"></i> Back to Inspection
                    </a>
                    <button type="submit" class="btn btn-primary" id="saveChecklistBtn">
                        <i class="fas fa-save"></i> Save Progress
                    </button>
                    @if($inspection->status !== 'completed')
                        <button type="button" class="btn btn-success ml-2" id="completeInspectionBtn" 
                                onclick="window.location.href='{{ route('inspector.inspections.show', $inspection) }}#complete-section'">
                            <i class="fas fa-check"></i> Complete Inspection
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Evidence Viewer Modal -->
<div class="modal fade" id="evidenceViewerModal" tabindex="-1" role="dialog" aria-labelledby="evidenceViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="evidenceViewerModalLabel">Business Evidence</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalEvidence" src="" alt="Evidence" class="img-fluid" style="max-height: 70vh;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Handle compliance status changes
    $('.compliance-radio').on('change', function() {
        const $item = $(this).closest('.checklist-item');
        const status = $(this).val();
        const $correctiveAction = $item.find('.corrective-action');
        const $deadlineField = $item.find('.deadline-field');
        
        // Show/hide corrective action and deadline fields
        if (status === 'non_compliant' || status === 'needs_improvement') {
            $correctiveAction.show();
            $deadlineField.show();
        } else {
            $correctiveAction.hide();
            $deadlineField.hide();
        }
        
        updateProgress();
        updateScore();
    });
    
    // Auto-save functionality
    let autoSaveTimeout;
    $('#checklistForm input, #checklistForm textarea').on('change', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            saveProgress();
        }, 2000);
    });
    
    // Form submission
    $('#checklistForm').on('submit', function(e) {
        e.preventDefault();
        saveProgress();
    });
});

function updateProgress() {
    const totalItems = $('.checklist-item').length;
    const completedItems = $('.compliance-radio:checked').length;
    const percentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
    
    $('#progress-text').text(completedItems + '/' + totalItems + ' items completed');
    $('#progress-percentage').text(percentage + '%');
    $('#progress-bar').css('width', percentage + '%').attr('aria-valuenow', completedItems);
}

function updateScore() {
    let totalScore = 0;
    $('.compliance-radio:checked').each(function() {
        totalScore += parseInt($(this).data('points'));
    });
    
    $('#total-score').text(totalScore);
    
    const maxScore = parseInt($('#max-score').text());
    const scorePercentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    $('#score-percentage').text(scorePercentage);
}

function saveProgress() {
    const $btn = $('#saveChecklistBtn');
    const originalText = $btn.html();
    
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');
    
    $.ajax({
        url: $('#checklistForm').attr('action'),
        method: 'POST',
        data: new FormData($('#checklistForm')[0]),
        processData: false,
        contentType: false,
        success: function(response) {
            $btn.removeClass('btn-primary').addClass('btn-success')
                .html('<i class="fas fa-check"></i> Saved');
            
            setTimeout(function() {
                $btn.removeClass('btn-success').addClass('btn-primary')
                    .html(originalText).prop('disabled', false);
            }, 2000);
        },
        error: function(xhr) {
            $btn.removeClass('btn-primary').addClass('btn-danger')
                .html('<i class="fas fa-times"></i> Error');
            
            setTimeout(function() {
                $btn.removeClass('btn-danger').addClass('btn-primary')
                    .html(originalText).prop('disabled', false);
            }, 2000);
            
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                let errorMessage = 'Validation errors:\n';
                Object.keys(xhr.responseJSON.errors).forEach(function(key) {
                    errorMessage += '- ' + xhr.responseJSON.errors[key][0] + '\n';
                });
                alert(errorMessage);
            }
        }
    });
}

function viewEvidence(evidenceUrl, fileName) {
    $('#modalEvidence').attr('src', evidenceUrl);
    $('#evidenceViewerModalLabel').text(fileName);
    $('#evidenceViewerModal').modal('show');
}

// Initialize progress and score on page load
updateProgress();
updateScore();
</script>
@endpush
