<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InspectionAssignment extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'inspection_id',
        'inspector_id',
        'assigned_by',
        'role',
        'status',
        'assigned_at',
        'responded_at',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'assigned_at' => 'datetime',
            'responded_at' => 'datetime',
        ];
    }

    /**
     * Get the inspection this assignment is for
     */
    public function inspection()
    {
        return $this->belongsTo(Inspection::class);
    }

    /**
     * Get the assigned inspector
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Get the admin who made the assignment
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Check if assignment is accepted
     */
    public function isAccepted(): bool
    {
        return $this->status === 'accepted';
    }

    /**
     * Check if assignment is declined
     */
    public function isDeclined(): bool
    {
        return $this->status === 'declined';
    }

    /**
     * Check if assignment is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if assignment is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'assigned';
    }

    /**
     * Check if inspector is primary
     */
    public function isPrimary(): bool
    {
        return $this->role === 'primary';
    }

    /**
     * Check if inspector is secondary
     */
    public function isSecondary(): bool
    {
        return $this->role === 'secondary';
    }

    /**
     * Check if inspector is observer
     */
    public function isObserver(): bool
    {
        return $this->role === 'observer';
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'accepted' => 'badge-success',
            'declined' => 'badge-danger',
            'completed' => 'badge-primary',
            'assigned' => 'badge-warning',
            default => 'badge-light'
        };
    }

    /**
     * Get role badge class for UI
     */
    public function getRoleBadgeClassAttribute(): string
    {
        return match($this->role) {
            'primary' => 'badge-primary',
            'secondary' => 'badge-info',
            'observer' => 'badge-secondary',
            default => 'badge-light'
        };
    }
}
