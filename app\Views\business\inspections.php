<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6><?= $title ?></h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <?php if (empty($inspections)): ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No inspections found.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive p-0">
                            <table class="table align-items-center mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Inspector</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Scheduled Date</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inspections as $inspection): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex px-3 py-1">
                                                <div class="d-flex flex-column justify-content-center">
                                                    <h6 class="mb-0 text-sm"><?= htmlspecialchars($inspection['inspector_name']) ?></h6>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <p class="text-sm font-weight-bold mb-0">
                                                <?= date('M j, Y', strtotime($inspection['scheduled_date'])) ?>
                                            </p>
                                        </td>
                                        <td>
                                            <span class="badge badge-sm bg-<?= $inspection['status'] === 'completed' ? 'success' : ($inspection['status'] === 'cancelled' ? 'danger' : 'warning') ?>">
                                                <?= ucfirst($inspection['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <p class="text-sm text-secondary mb-0">
                                                <?= htmlspecialchars($inspection['notes'] ?? 'No notes') ?>
                                            </p>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?> 