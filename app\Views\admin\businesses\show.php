<?php
require_once __DIR__ . '/../../../layouts/header.php';
?>

<div class="container">
    <h1><?= htmlspecialchars($business['name']); ?></h1>
    
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success"><?= htmlspecialchars($_SESSION['success']); ?></div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($_SESSION['error']); ?></div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Business Details</h3>
                </div>
                <div class="card-body">
                    <p><strong>Owner:</strong> <?= htmlspecialchars($business['owner_name']); ?> (<?= htmlspecialchars($business['owner_email']); ?>)</p>
                    <p><strong>Registration Number:</strong> <?= htmlspecialchars($business['registration_number']); ?></p>
                    <p><strong>Category:</strong> <?= htmlspecialchars($business['category_name']); ?></p>
                    <p><strong>District:</strong> <?= htmlspecialchars($business['district_name']); ?></p>
                    <p><strong>Address:</strong> <?= htmlspecialchars($business['address']); ?></p>
                    <p><strong>Contact Email:</strong> <?= htmlspecialchars($business['contact_email']); ?></p>
                    <p><strong>Contact Phone:</strong> <?= htmlspecialchars($business['contact_phone']); ?></p>
                    <p><strong>Employees:</strong> <?= htmlspecialchars($business['employee_count']); ?></p>
                    <p><strong>Status:</strong> 
                        <span class="badge badge-<?= 
                            $business['compliance_status'] === 'COMPLIANT' ? 'success' :
                            ($business['compliance_status'] === 'NON_COMPLIANT' ? 'danger' :
                            ($business['compliance_status'] === 'WARNING' ? 'warning' : 'info'))
                        ?>">
                            <?= htmlspecialchars($business['compliance_status']); ?>
                        </span>
                    </p>
                    <?php if ($business['compliance_status'] === 'GRACE_PERIOD' && $business['grace_period_end']): ?>
                        <p><strong>Grace Period Ends:</strong> <?= date('M d, Y', strtotime($business['grace_period_end'])); ?></p>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="/admin/businesses/<?= htmlspecialchars($business['id']); ?>/edit" class="btn btn-primary">Edit Business</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Compliance Status</h3>
                </div>
                <div class="card-body">
                    <form action="/admin/businesses/<?= htmlspecialchars($business['id']); ?>/compliance" method="POST">
                        <div class="form-group">
                            <label for="compliance_status">Status</label>
                            <select id="compliance_status" name="compliance_status" class="form-control" required>
                                <option value="COMPLIANT" <?= $business['compliance_status'] === 'COMPLIANT' ? 'selected' : ''; ?>>Compliant</option>
                                <option value="NON_COMPLIANT" <?= $business['compliance_status'] === 'NON_COMPLIANT' ? 'selected' : ''; ?>>Non-Compliant</option>
                                <option value="WARNING" <?= $business['compliance_status'] === 'WARNING' ? 'selected' : ''; ?>>Warning</option>
                                <option value="GRACE_PERIOD" <?= $business['compliance_status'] === 'GRACE_PERIOD' ? 'selected' : ''; ?>>Grace Period</option>
                            </select>
                        </div>
                        
                        <div class="form-group" id="gracePeriodGroup" style="<?= $business['compliance_status'] === 'GRACE_PERIOD' ? '' : 'display: none;'; ?>">
                            <label for="grace_period_end">Grace Period End Date</label>
                            <input type="date" id="grace_period_end" name="grace_period_end" 
                                   class="form-control" 
                                   value="<?= $business['grace_period_end'] ? htmlspecialchars($business['grace_period_end']) : ''; ?>">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3>Documents</h3>
                </div>
                <div class="card-body">
                    <p>Document management will be implemented in the next phase.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('compliance_status').addEventListener('change', function() {
    const gracePeriodGroup = document.getElementById('gracePeriodGroup');
    if (this.value === 'GRACE_PERIOD') {
        gracePeriodGroup.style.display = 'block';
    } else {
        gracePeriodGroup.style.display = 'none';
    }
});
</script>

<?php
require_once __DIR__ . '/../../../layouts/footer.php';