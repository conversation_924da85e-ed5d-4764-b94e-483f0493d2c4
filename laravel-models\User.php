<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'password',
        'full_name',
        'role',
        'status',
        'last_login',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is inspector
     */
    public function isInspector(): bool
    {
        return $this->role === 'inspector';
    }

    /**
     * Check if user is business owner
     */
    public function isBusinessOwner(): bool
    {
        return $this->role === 'business_owner';
    }

    /**
     * Get businesses owned by this user
     */
    public function ownedBusinesses()
    {
        return $this->hasMany(Business::class, 'owner_id');
    }

    /**
     * Get inspections assigned to this inspector
     */
    public function assignedInspections()
    {
        return $this->hasMany(Inspection::class, 'inspector_id');
    }

    /**
     * Get inspections created by this admin
     */
    public function createdInspections()
    {
        return $this->hasMany(Inspection::class, 'assigned_by');
    }

    /**
     * Get district assignments for this inspector
     */
    public function districtAssignments()
    {
        return $this->hasMany(InspectorDistrictAssignment::class, 'inspector_id');
    }

    /**
     * Get barangay assignments for this inspector
     */
    public function barangayAssignments()
    {
        return $this->hasMany(InspectorBarangayAssignment::class, 'inspector_id');
    }

    /**
     * Get notifications for this user
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get chat messages sent by this user
     */
    public function sentMessages()
    {
        return $this->hasMany(ChatMessage::class, 'sender_id');
    }
}
