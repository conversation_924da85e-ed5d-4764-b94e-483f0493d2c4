<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-clipboard-check text-warning"></i> Pending Inspection Verification
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspections" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Inspections
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= count($pending_inspections) ?></h4>
                            <div class="small">Pending Inspections</div>
                        </div>
                        <i class="fas fa-clipboard-check fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Pending Inspections Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> Completed Inspections Awaiting Verification
            </h6>
        </div>
        <div class="card-body">
            <?php if (empty($pending_inspections)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-muted">All Inspections Verified</h5>
                    <p class="text-muted">There are no completed inspections pending verification at this time.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="verificationsTable">
                        <thead class="table-light">
                            <tr>
                                <th>Business</th>
                                <th>Inspector</th>
                                <th>Completed Date</th>
                                <th>Compliance Rating</th>
                                <th>Score</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pending_inspections as $inspection): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                        <?php if (!empty($inspection['business_address'])): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <i class="fas fa-user-tie me-1"></i>
                                        <?= htmlspecialchars($inspection['inspector_name']) ?>
                                    </td>
                                    <td>
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php if ($inspection['completed_date']): ?>
                                            <?= date('M j, Y', strtotime($inspection['completed_date'])) ?>
                                            <br><small class="text-muted"><?= date('g:i A', strtotime($inspection['completed_date'])) ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">Not completed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($inspection['compliance_rating']): ?>
                                            <?php
                                            $ratingClass = '';
                                            switch ($inspection['compliance_rating']) {
                                                case 'A': $ratingClass = 'success'; break;
                                                case 'B': $ratingClass = 'info'; break;
                                                case 'C': $ratingClass = 'warning'; break;
                                                case 'D': $ratingClass = 'danger'; break;
                                                case 'F': $ratingClass = 'dark'; break;
                                            }
                                            ?>
                                            <span class="badge bg-<?= $ratingClass ?> fs-6">
                                                Grade <?= htmlspecialchars($inspection['compliance_rating']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">Not rated</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($inspection['score']): ?>
                                            <strong><?= htmlspecialchars($inspection['score']) ?>%</strong>
                                        <?php else: ?>
                                            <span class="text-muted">No score</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-clock me-1"></i>Pending Verification
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= BASE_URL ?>admin/inspections/verify/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-primary" title="Review Inspection">
                                                <i class="fas fa-clipboard-check"></i> Review
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Information Card -->
    <div class="card shadow mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Checklist Evidence Review Process
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h6><i class="fas fa-lightbulb me-2"></i>How Evidence Review Works</h6>
                <p class="mb-2">Business owners upload evidence through their checklist interface. Inspectors review this evidence during scheduled inspections as part of the checklist process.</p>
                <ul class="mb-0">
                    <li>Evidence is uploaded by business owners for specific checklist items</li>
                    <li>Inspectors review evidence during on-site inspections</li>
                    <li>Evidence verification is integrated into the inspection checklist</li>
                    <li>Admin can view results through the Inspection Results dashboard</li>
                </ul>
            </div>
            <div class="text-center">
                <a href="<?= BASE_URL ?>admin/compliance" class="btn btn-primary me-2">
                    <i class="fas fa-chart-line me-1"></i>View Inspection Results
                </a>
                <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-outline-primary">
                    <i class="fas fa-calendar-check me-1"></i>Manage Inspections
                </a>
            </div>
        </div>
    </div>
</div>

<!-- DataTables CSS and JS -->
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize inspections table
    $('#verificationsTable').DataTable({
        order: [[2, 'desc']], // Sort by completed date by default
        pageLength: 25,
        responsive: true,
        language: {
            search: '',
            searchPlaceholder: 'Search inspections...'
        },
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting for actions column
        ]
    });


});
</script>

<?php $this->endSection(); ?>
