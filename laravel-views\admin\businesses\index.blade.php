@extends('layouts.admin')

@section('title', 'Manage Businesses')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Manage Businesses</h1>
    <a href="{{ route('admin.businesses.create') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
        <i class="fas fa-plus fa-sm text-white-50"></i> Register New Business
    </a>
</div>

<!-- Filters Card -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.businesses.index') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="search">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Business name, owner, email...">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="compliance_status">Compliance</label>
                        <select class="form-control" id="compliance_status" name="compliance_status">
                            <option value="">All Compliance</option>
                            <option value="compliant" {{ request('compliance_status') === 'compliant' ? 'selected' : '' }}>Compliant</option>
                            <option value="non_compliant" {{ request('compliance_status') === 'non_compliant' ? 'selected' : '' }}>Non-Compliant</option>
                            <option value="pending_review" {{ request('compliance_status') === 'pending_review' ? 'selected' : '' }}>Pending Review</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="category_id">Category</label>
                        <select class="form-control" id="category_id" name="category_id">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ request('category_id') === $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="district_id">District</label>
                        <select class="form-control" id="district_id" name="district_id">
                            <option value="">All Districts</option>
                            @foreach($districts as $district)
                                <option value="{{ $district->id }}" {{ request('district_id') === $district->id ? 'selected' : '' }}>
                                    {{ $district->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Businesses Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Businesses List</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">Actions:</div>
                <a class="dropdown-item" href="{{ route('admin.businesses.export') }}">
                    <i class="fas fa-download fa-sm fa-fw mr-2 text-gray-400"></i>
                    Export Data
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($businesses->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered" id="businessesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Business Name</th>
                            <th>Owner</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Compliance</th>
                            <th>Last Inspection</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($businesses as $business)
                            <tr>
                                <td>
                                    <div class="font-weight-bold">{{ $business->name }}</div>
                                    <div class="small text-gray-500">{{ $business->registration_number }}</div>
                                </td>
                                <td>
                                    <div>{{ $business->owner_name }}</div>
                                    <div class="small text-gray-500">{{ $business->email }}</div>
                                </td>
                                <td>{{ $business->category->name }}</td>
                                <td>
                                    <div>{{ $business->barangay->name }}</div>
                                    <div class="small text-gray-500">{{ $business->barangay->district->name }}</div>
                                </td>
                                <td>
                                    <span class="badge badge-{{ $business->status === 'active' ? 'success' : ($business->status === 'pending' ? 'warning' : 'danger') }}">
                                        {{ ucfirst($business->status) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-{{ $business->compliance_status === 'compliant' ? 'success' : ($business->compliance_status === 'pending_review' ? 'warning' : 'danger') }}">
                                        {{ ucfirst(str_replace('_', ' ', $business->compliance_status)) }}
                                    </span>
                                </td>
                                <td>
                                    @if($business->last_inspection_date)
                                        {{ $business->last_inspection_date->format('M j, Y') }}
                                    @else
                                        <span class="text-muted">Never</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.businesses.show', $business) }}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.businesses.edit', $business) }}" 
                                           class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete('{{ $business->id }}', '{{ $business->name }}')" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                    Showing {{ $businesses->firstItem() }} to {{ $businesses->lastItem() }} of {{ $businesses->total() }} results
                </div>
                {{ $businesses->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-500">No businesses found</h5>
                <p class="text-gray-400">Try adjusting your search criteria or register a new business.</p>
                <a href="{{ route('admin.businesses.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>Register New Business
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the business "<span id="businessName"></span>"? 
                This action cannot be undone and will also delete the associated business owner account.
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button class="btn btn-danger" type="submit">Delete Business</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#businessesTable').DataTable({
        "paging": false,
        "searching": false,
        "ordering": true,
        "info": false,
        "columnDefs": [
            { "orderable": false, "targets": [7] } // Actions column
        ]
    });
});

function confirmDelete(businessId, businessName) {
    $('#businessName').text(businessName);
    $('#deleteForm').attr('action', '{{ route("admin.businesses.index") }}/' + businessId);
    $('#deleteModal').modal('show');
}
</script>
@endpush
