<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-check"></i> Inspection Details
        </h1>
        <div>
            <?php if (isset($inspection_score) && $inspection_score): ?>
                <a href="<?= BASE_URL ?>admin/inspections/report/<?= $inspection['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-file-alt"></i> View Report
                </a>
            <?php endif; ?>
            <a href="<?= BASE_URL ?>admin/inspections" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Inspection Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Business</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['name']) ?></p>
                            
                            <h6 class="text-muted">Business Address</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['address']) ?></p>
                            
                            <h6 class="text-muted">Inspector</h6>
                            <p class="mb-3"><?= htmlspecialchars($inspector['full_name']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Scheduled Date</h6>
                            <p class="mb-3">
                                <?php if ($inspection['scheduled_date']): ?>
                                    <?= date('F d, Y', strtotime($inspection['scheduled_date'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">Not scheduled</span>
                                <?php endif; ?>
                            </p>

                            <?php if (!empty($inspection['scheduled_time'])): ?>
                            <h6 class="text-muted">Scheduled Time</h6>
                            <p class="mb-3"><?= date('h:i A', strtotime($inspection['scheduled_time'])) ?></p>
                            <?php endif; ?>
                            
                            <h6 class="text-muted">Status</h6>
                            <p class="mb-3">
                                <?php
                                $status = $inspection['status'] ?? 'pending';
                                $statusClasses = [
                                    'completed' => 'success',
                                    'scheduled' => 'primary',
                                    'confirmed' => 'info',
                                    'cancelled' => 'danger',
                                    'in_progress' => 'warning',
                                    'pending' => 'warning'
                                ];
                                $statusClass = $statusClasses[$status] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?= $statusClass ?>">
                                    <?= ucfirst(str_replace('_', ' ', $status)) ?>
                                </span>
                            </p>

                            <h6 class="text-muted">Inspection Type</h6>
                            <p class="mb-3"><?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'] ?? 'routine')) ?></p>

                            <h6 class="text-muted">District</h6>
                            <p class="mb-3"><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($inspection['notes'])): ?>
                    <div class="mt-4">
                        <h6 class="text-muted">Notes</h6>
                        <p><?= nl2br(htmlspecialchars($inspection['notes'])) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Checklist Progress -->
            <?php if (isset($completion_status) && $completion_status['total_items'] > 0): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Checklist Progress</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar bg-info" role="progressbar"
                                     style="width: <?= $completion_status['completion_percentage'] ?>%">
                                    <?= $completion_status['completion_percentage'] ?>%
                                </div>
                            </div>
                            <small class="text-muted">
                                <?= $completion_status['completed_items'] ?> of <?= $completion_status['total_items'] ?> items completed
                            </small>
                        </div>
                        <?php if (isset($inspection_score) && $inspection_score): ?>
                        <div class="col-md-6">
                            <h4 class="mb-1 text-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>">
                                <?= $inspection_score['percentage'] ?>% (Grade <?= $inspection_score['grade'] ?>)
                            </h4>
                            <small class="text-muted">Compliance Score</small>
                            <?php if ($inspection_score['critical_violations'] > 0): ?>
                                <br><small class="text-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?= $inspection_score['critical_violations'] ?> critical violations
                                </small>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($status == 'completed' && isset($inspection_score)): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Inspection Results</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Completed Date</h6>
                            <p class="mb-3">
                                <?php if ($inspection['completed_date']): ?>
                                    <?= date('F d, Y', strtotime($inspection['completed_date'])) ?>
                                <?php elseif ($inspection['updated_at']): ?>
                                    <?= date('F d, Y', strtotime($inspection['updated_at'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">Not completed</span>
                                <?php endif; ?>
                            </p>

                            <h6 class="text-muted">Compliance Status</h6>
                            <span class="badge bg-<?= $inspection_score['status'] === 'passed' ? 'success' : 'danger' ?> fs-6">
                                <?= ucfirst($inspection_score['status']) ?>
                            </span>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Final Score</h6>
                            <p class="mb-3"><?= $inspection_score['percentage'] ?>% (<?= $inspection_score['score'] ?>/<?= $inspection_score['total_points'] ?> points)</p>

                            <h6 class="text-muted">Grade</h6>
                            <span class="badge bg-<?=
                                $inspection_score['grade'] === 'A' ? 'success' :
                                (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger')
                            ?> fs-6">
                                Grade <?= $inspection_score['grade'] ?>
                            </span>
                        </div>
                    </div>

                    <?php if ($inspection_score['critical_violations'] > 0): ?>
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Critical Violations:</strong> <?= $inspection_score['critical_violations'] ?> critical violation(s) found that require immediate attention.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($status != 'completed'): ?>
                        <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>" class="btn btn-success">
                            <i class="fas fa-clipboard-check"></i> Start Checklist
                        </a>
                        <?php endif; ?>

                        <?php if (isset($inspection_score) && $inspection_score): ?>
                        <a href="<?= BASE_URL ?>admin/inspections/report/<?= $inspection['id'] ?>" class="btn btn-info">
                            <i class="fas fa-file-alt"></i> View Full Report
                        </a>
                        <?php endif; ?>
                        
                        <form method="POST" action="<?= BASE_URL ?>admin/inspections/delete/<?= $inspection['id'] ?>" 
                              onsubmit="return confirm('Are you sure you want to delete this inspection?')">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash"></i> Delete Inspection
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Inspection Scheduled</h6>
                                <small class="text-muted">
                                    <?php if ($inspection['created_at']): ?>
                                        <?= date('M d, Y', strtotime($inspection['created_at'])) ?>
                                    <?php else: ?>
                                        Unknown date
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>

                        <?php if ($status == 'completed' && !empty($inspection['completed_date'])): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Inspection Completed</h6>
                                <small class="text-muted"><?= date('M d, Y', strtotime($inspection['completed_date'])) ?></small>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    background: #f8f9fc;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #4e73df;
}
</style>
<?php $this->endSection(); ?>
