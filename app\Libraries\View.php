<?php
namespace App\Libraries;

class View {
    private $sections = [];
    private $currentSection = null;
    private $data = [];
    private $layout = null;

    public function __construct(array $data = []) {
        $this->data = $data;
    }

    public function render(string $template, array $data = []): void {
        try {
            $this->data = array_merge($this->data, $data);

            $templatePath = __DIR__ . '/../Views/' . $template . '.php';

            if (!file_exists($templatePath)) {
                throw new \Exception("Template file not found: " . $templatePath);
            }

            // Reset layout and sections for each render
            $this->layout = null;
            $this->sections = [];
            $this->currentSection = null;

            // Extract data for template
            extract($this->data);

            // Start output buffering and include template
            ob_start();
            include $templatePath;
            $content = ob_get_clean();

            // If template set a layout, render with layout
            if ($this->layout) {
                $layoutPath = __DIR__ . '/../Views/' . $this->layout . '.php';

                if (!file_exists($layoutPath)) {
                    throw new \Exception("Layout file not found: " . $layoutPath);
                }

                // Extract data for layout
                extract($this->data);

                // Include layout (which will call renderSection)
                include $layoutPath;
            } else {
                // No layout, just output content
                echo $content;
            }
        } catch (\Exception $e) {
            error_log("Error in View::render: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            if (defined('APP_DEBUG') && APP_DEBUG) {
                echo "<h1>View Error</h1>";
                echo "<pre>" . $e->getMessage() . "\n\n" . $e->getTraceAsString() . "</pre>";
            } else {
                echo "Internal Server Error";
            }
        }
    }

    public function extend(string $layout): void {
        $this->layout = $layout;
    }

    public function section(string $name): void {
        if ($this->currentSection) {
            $this->endSection();
        }

        $this->currentSection = $name;
        ob_start();
    }

    public function endSection(): void {
        if ($this->currentSection) {
            $this->sections[$this->currentSection] = ob_get_clean();
            $this->currentSection = null;
        }
    }

    public function getSection(string $name): string {
        return $this->sections[$name] ?? '';
    }

    public function renderSection(string $name): string {
        return $this->getSection($name);
    }

    public function __get($name) {
        return $this->data[$name] ?? null;
    }
} 