<?php
namespace App\Controllers;

use App\Models\User;
use App\Libraries\Auth;
use App\Libraries\View;
use App\Core\Controller;

class AuthController extends Controller {
    private $userModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->userModel = new User();
    }

    public function showLogin() {
        if ($this->auth->isLoggedIn()) {
            $this->redirectToDashboard();
            return;
        }

        return $this->render('auth/login', [
            'title' => 'Login'
        ]);
    }

    public function login() {
        if ($this->auth->isLoggedIn()) {
            $this->redirectToDashboard();
            return;
        }

        if (!$this->isPost()) {
            return $this->render('auth/login', [
                'title' => 'Login'
            ]);
        }

        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($email) || empty($password)) {
            $_SESSION['error'] = 'Please enter both email and password.';
            $this->redirect('login');
            return;
        }

        try {
            if ($this->auth->login($email, $password)) {
                $this->redirectToDashboard();
            } else {
                $_SESSION['error'] = 'Invalid email or password.';
                $this->redirect('login');
            }
        } catch (\Exception $e) {
            error_log("Error in AuthController::login: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred during login.';
            $this->redirect('login');
        }
    }

    public function showRegister() {
        if ($this->auth->isLoggedIn()) {
            $this->redirectBasedOnRole();
            exit();
        }
        
        return $this->render('auth/register', [
            'title' => 'Register'
        ]);
    }

    public function register() {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('dashboard');
            return;
        }

        if (!$this->isPost()) {
            return $this->render('auth/register', [
                'title' => 'Register'
            ]);
        }

        $data = [
            'email' => $_POST['email'] ?? '',
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'full_name' => $_POST['full_name'] ?? '',
            'role' => 'business_owner',
            'status' => 'active'
        ];

        foreach ($data as $key => $value) {
            if (empty($value) && $key !== 'status') {
                $_SESSION['error'] = ucfirst(str_replace('_', ' ', $key)) . ' is required.';
                $this->redirect('register');
                return;
            }
        }

        // Check if passwords match
        if ($data['password'] !== $data['confirm_password']) {
            $_SESSION['error'] = 'Passwords do not match.';
            $this->redirect('register');
            return;
        }

        // Remove confirm_password from data before creating user
        unset($data['confirm_password']);

        try {
            if ($this->userModel->create($data)) {
                $_SESSION['success'] = 'Registration successful. Please login.';
                $this->redirect('login');
            } else {
                $_SESSION['error'] = 'Failed to create account.';
                $this->redirect('register');
            }
        } catch (\Exception $e) {
            error_log("Error in AuthController::register: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred during registration.';
            $this->redirect('register');
        }
    }

    public function logout() {
        $this->auth->logout();
        $this->redirect('login');
    }

    private function redirectToDashboard() {
        $user = $this->auth->getUser();
        switch ($user['role']) {
            case 'admin':
                $this->redirect('admin/dashboard');
                break;
            case 'inspector':
                $this->redirect('inspector/dashboard');
                break;
            case 'business_owner':
                $this->redirect('business/dashboard');
                break;
            default:
                $this->redirect('dashboard');
        }
    }

    private function redirectBasedOnRole() {
        $user = $this->auth->getUser();
        if (!$user) {
            header('Location: ' . BASE_URL . 'login');
            exit();
        }

        $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $basePath = parse_url(BASE_URL, PHP_URL_PATH);
        
        if ($basePath && strpos($currentPath, $basePath) === 0) {
            $currentPath = substr($currentPath, strlen($basePath));
        }
        
        $role = strtoupper($user['role']);
        $currentDashboard = $role === 'ADMIN' ? 'admin/dashboard' : 
                          ($role === 'INSPECTOR' ? 'inspector/dashboard' : 
                          ($role === 'BUSINESS_OWNER' ? 'business/dashboard' : 'dashboard'));
        
        if ($currentPath === $currentDashboard) {
            return;
        }

        switch ($role) {
            case 'ADMIN':
                header('Location: ' . BASE_URL . 'admin/dashboard');
                break;
            case 'INSPECTOR':
                header('Location: ' . BASE_URL . 'inspector/dashboard');
                break;
            case 'BUSINESS_OWNER':
                header('Location: ' . BASE_URL . 'business/dashboard');
                break;
            default:
                header('Location: ' . BASE_URL . 'dashboard');
        }
        exit();
    }
}