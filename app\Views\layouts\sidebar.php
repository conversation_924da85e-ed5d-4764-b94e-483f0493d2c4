<?php
$auth = \App\Libraries\Auth::getInstance();
$user = $auth->getUser();
$currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$basePath = parse_url(BASE_URL, PHP_URL_PATH);
if ($basePath && strpos($currentPath, $basePath) === 0) {
    $currentPath = substr($currentPath, strlen($basePath));
}
$currentPath = '/' . trim($currentPath, '/');

function isActive($path) {
    global $currentPath;
    return strpos($currentPath, $path) === 0 ? 'active' : '';
}
?>

<nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <?php if ($auth->isAdmin()): ?>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/admin/dashboard') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>admin/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/admin/users') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>admin/users">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/admin/businesses') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>admin/businesses">
                        <i class="fas fa-building me-2"></i>Businesses
                    </a>
                </li>
                <?php /* Removed Documents link as requested */ ?>
                <?php /*
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/admin/documents') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>admin/documents">
                        <i class="fas fa-file-alt me-2"></i>Documents
                    </a>
                </li>
                */ ?>

            <?php elseif ($auth->isInspector()): ?>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/inspector/dashboard') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>inspector/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/inspector/schedule') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>inspector/schedule">
                        <i class="fas fa-calendar-alt me-2"></i>Schedule
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/inspector/inspections') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>inspector/inspections">
                        <i class="fas fa-clipboard-list me-2"></i>My Inspections
                    </a>
                </li>
            <?php elseif ($auth->isBusinessOwner()): ?>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/business/dashboard') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>business/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/business') === strlen(BASE_URL) ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>business">
                        <i class="fas fa-building me-2"></i>My Businesses
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/business/inspections') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>business/inspections">
                        <i class="fas fa-clipboard-check me-2"></i>Inspections
                    </a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/business/compliance') !== false ? 'active' : '' ?>"
                       href="<?= BASE_URL ?>business/compliance">
                        <i class="fas fa-file-upload me-2"></i>Compliance Evidence
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </div>
</nav>
