<?php
namespace App\Models;

use App\Config\Database;
use PDO;

class Chat {
    private $db;

    public function __construct() {
        $this->db = (new Database())->getConnection();
    }
    
    public function createRoom($data) {
        $roomId = $this->generateUUID();

        $query = "INSERT INTO chat_rooms (
                    id, business_id, business_owner_id, admin_id, inspector_id,
                    status, subject, created_at
                 ) VALUES (
                    :id, :business_id, :business_owner_id, :admin_id, :inspector_id,
                    :status, :subject, NOW()
                 )";

        $stmt = $this->db->prepare($query);
        $result = $stmt->execute([
            ':id' => $roomId,
            ':business_id' => $data['business_id'],
            ':business_owner_id' => $data['business_owner_id'],
            ':admin_id' => $data['admin_id'] ?? null,
            ':inspector_id' => $data['inspector_id'] ?? null,
            ':status' => $data['status'] ?? 'active',
            ':subject' => $data['subject'] ?? null
        ]);

        return $result ? $roomId : false;
    }

    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    public function getRoomsByUser($userId, $userRole) {
        $sql = "SELECT cr.*, 
                       b.name as business_name,
                       bo.full_name as business_owner_name,
                       a.full_name as admin_name,
                       i.full_name as inspector_name,
                       (SELECT COUNT(*) FROM chat_messages cm 
                        WHERE cm.chat_room_id = cr.id 
                        AND cm.sender_id != ? 
                        AND cm.is_read = 0) as unread_count,
                       (SELECT cm.message FROM chat_messages cm 
                        WHERE cm.chat_room_id = cr.id 
                        ORDER BY cm.created_at DESC LIMIT 1) as last_message,
                       (SELECT cm.created_at FROM chat_messages cm 
                        WHERE cm.chat_room_id = cr.id 
                        ORDER BY cm.created_at DESC LIMIT 1) as last_message_time
                FROM chat_rooms cr
                LEFT JOIN businesses b ON cr.business_id = b.id
                LEFT JOIN users bo ON cr.business_owner_id = bo.id
                LEFT JOIN users a ON cr.admin_id = a.id
                LEFT JOIN users i ON cr.inspector_id = i.id
                WHERE ";
        
        $params = [$userId];
        
        switch ($userRole) {
            case 'admin':
                $sql .= "(cr.admin_id = ? OR cr.admin_id IS NULL)";
                $params[] = $userId;
                break;
            case 'inspector':
                $sql .= "(cr.inspector_id = ? OR cr.inspector_id IS NULL)";
                $params[] = $userId;
                break;
            case 'business_owner':
                $sql .= "cr.business_owner_id = ?";
                $params[] = $userId;
                break;
        }
        
        $sql .= " ORDER BY last_message_time DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getRoomById($roomId) {
        $sql = "SELECT cr.*, 
                       b.name as business_name,
                       bo.full_name as business_owner_name,
                       bo.email as business_owner_email,
                       a.full_name as admin_name,
                       a.email as admin_email,
                       i.full_name as inspector_name,
                       i.email as inspector_email
                FROM chat_rooms cr
                LEFT JOIN businesses b ON cr.business_id = b.id
                LEFT JOIN users bo ON cr.business_owner_id = bo.id
                LEFT JOIN users a ON cr.admin_id = a.id
                LEFT JOIN users i ON cr.inspector_id = i.id
                WHERE cr.id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$roomId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getRoomByBusiness($businessId, $businessOwnerId) {
        $sql = "SELECT * FROM chat_rooms 
                WHERE business_id = ? AND business_owner_id = ? 
                AND status = 'active'
                ORDER BY created_at DESC LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$businessId, $businessOwnerId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function assignAdminToRoom($roomId, $adminId) {
        $query = "UPDATE chat_rooms SET admin_id = :admin_id WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':admin_id' => $adminId, ':id' => $roomId]);
    }

    public function assignInspectorToRoom($roomId, $inspectorId) {
        $query = "UPDATE chat_rooms SET inspector_id = :inspector_id WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':inspector_id' => $inspectorId, ':id' => $roomId]);
    }

    public function closeRoom($roomId) {
        $query = "UPDATE chat_rooms SET status = 'closed' WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $roomId]);
    }
    
    public function getMessages($roomId, $limit = 50, $offset = 0) {
        $sql = "SELECT cm.*,
                       u.full_name as sender_name,
                       u.role as sender_role
                FROM chat_messages cm
                LEFT JOIN users u ON cm.sender_id = u.id
                WHERE cm.chat_room_id = ?
                ORDER BY cm.created_at DESC
                LIMIT " . (int)$limit . " OFFSET " . (int)$offset;

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$roomId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function sendMessage($data) {
        $messageId = $this->generateUUID();

        $sql = "INSERT INTO chat_messages (id, chat_room_id, sender_id, message, message_type, file_path, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $messageId,
            $data['chat_room_id'],
            $data['sender_id'],
            $data['message'],
            $data['message_type'] ?? 'text',
            $data['file_path'] ?? null
        ]);
    }
    
    public function markMessagesAsRead($roomId, $userId) {
        $sql = "UPDATE chat_messages
                SET is_read = 1
                WHERE chat_room_id = ? AND sender_id != ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$roomId, $userId]);
    }
    
    public function getUnreadCount($userId, $userRole) {
        $sql = "SELECT COUNT(*) as count
                FROM chat_messages cm
                JOIN chat_rooms cr ON cm.chat_room_id = cr.id
                WHERE cm.sender_id != ? AND cm.is_read = 0 AND ";
        
        $params = [$userId];
        
        switch ($userRole) {
            case 'admin':
                $sql .= "(cr.admin_id = ? OR cr.admin_id IS NULL)";
                $params[] = $userId;
                break;
            case 'inspector':
                $sql .= "(cr.inspector_id = ? OR cr.inspector_id IS NULL)";
                $params[] = $userId;
                break;
            case 'business_owner':
                $sql .= "cr.business_owner_id = ?";
                $params[] = $userId;
                break;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['count'] : 0;
    }
    
    public function getActiveRoomsForAdmin() {
        $sql = "SELECT cr.*, 
                       b.name as business_name,
                       bo.full_name as business_owner_name,
                       (SELECT COUNT(*) FROM chat_messages cm 
                        WHERE cm.chat_room_id = cr.id 
                        AND cm.is_read = 0) as unread_count
                FROM chat_rooms cr
                LEFT JOIN businesses b ON cr.business_id = b.id
                LEFT JOIN users bo ON cr.business_owner_id = bo.id
                WHERE cr.status = 'active' AND cr.admin_id IS NULL
                ORDER BY cr.created_at DESC";
        
        $stmt = $this->db->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
