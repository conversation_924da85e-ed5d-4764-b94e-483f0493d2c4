<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ChatMessage extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'chat_room_id',
        'sender_id',
        'message',
        'message_type',
        'file_path',
        'is_read',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_read' => 'boolean',
        ];
    }

    /**
     * Get the chat room this message belongs to
     */
    public function chatRoom()
    {
        return $this->belongsTo(ChatRoom::class);
    }

    /**
     * Get the sender of this message
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Check if message is a text message
     */
    public function isText(): bool
    {
        return $this->message_type === 'text';
    }

    /**
     * Check if message is a file
     */
    public function isFile(): bool
    {
        return $this->message_type === 'file';
    }

    /**
     * Check if message is an image
     */
    public function isImage(): bool
    {
        return $this->message_type === 'image';
    }

    /**
     * Check if message is a system message
     */
    public function isSystem(): bool
    {
        return $this->message_type === 'system';
    }

    /**
     * Get file URL if message has a file
     */
    public function getFileUrlAttribute(): ?string
    {
        return $this->file_path ? Storage::url($this->file_path) : null;
    }

    /**
     * Get formatted timestamp
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    /**
     * Get time ago format
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for messages by sender
     */
    public function scopeBySender($query, User $sender)
    {
        return $query->where('sender_id', $sender->id);
    }

    /**
     * Scope for messages not by sender
     */
    public function scopeNotBySender($query, User $sender)
    {
        return $query->where('sender_id', '!=', $sender->id);
    }
}
