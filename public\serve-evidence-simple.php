<?php
// Ultra-simple file serving for compliance evidence
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get the filename from the URL parameter
$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    die('No file specified');
}

// Sanitize filename to prevent directory traversal
$filename = basename($filename);
$filePath = dirname(__DIR__) . '/public/uploads/compliance_evidence/' . $filename;

// Check if file exists
if (!file_exists($filePath)) {
    http_response_code(404);
    die('File not found: ' . htmlspecialchars($filename));
}

// Get file info
$fileInfo = pathinfo($filePath);
$extension = strtolower($fileInfo['extension']);

// Set appropriate content type
$contentTypes = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

$contentType = $contentTypes[$extension] ?? 'application/octet-stream';

// Set headers
header('Content-Type: ' . $contentType);
header('Content-Length: ' . filesize($filePath));
header('Content-Disposition: inline; filename="' . $filename . '"');
header('Cache-Control: private, max-age=3600');

// Output file
readfile($filePath);
exit;
?>
