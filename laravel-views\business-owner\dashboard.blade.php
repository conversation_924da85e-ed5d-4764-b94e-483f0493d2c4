@extends('layouts.business-owner')

@section('title', 'Business Dashboard')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Business Dashboard</h1>
    <div class="d-none d-sm-inline-block">
        <span class="text-gray-600">{{ $business->name }}</span>
    </div>
</div>

<!-- Business Info Card -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Business Information</h6>
        <a href="{{ route('business-owner.business.edit') }}" class="btn btn-sm btn-primary">
            <i class="fas fa-edit"></i> Edit Business Info
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <strong>Business Name:</strong> {{ $business->name }}
                </div>
                <div class="mb-3">
                    <strong>Registration Number:</strong> {{ $business->registration_number }}
                </div>
                <div class="mb-3">
                    <strong>Category:</strong> {{ $business->category->name }}
                </div>
                <div class="mb-3">
                    <strong>Location:</strong> {{ $business->barangay->name }}, {{ $business->barangay->district->name }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <strong>Status:</strong> 
                    <span class="badge badge-{{ $business->status === 'active' ? 'success' : ($business->status === 'pending' ? 'warning' : 'danger') }}">
                        {{ ucfirst($business->status) }}
                    </span>
                </div>
                <div class="mb-3">
                    <strong>Compliance Status:</strong> 
                    <span class="badge badge-{{ $business->compliance_status === 'compliant' ? 'success' : ($business->compliance_status === 'pending_review' ? 'warning' : 'danger') }}">
                        {{ ucfirst(str_replace('_', ' ', $business->compliance_status)) }}
                    </span>
                </div>
                <div class="mb-3">
                    <strong>Employee Count:</strong> {{ $business->employee_count }}
                </div>
                <div class="mb-3">
                    <strong>Last Inspection:</strong> 
                    @if($business->last_inspection_date)
                        {{ $business->last_inspection_date->format('M j, Y') }}
                    @else
                        <span class="text-muted">Never inspected</span>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Compliance Progress Card -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Overall Compliance
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{ $stats['compliance_percentage'] }}%</div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-primary" role="progressbar" 
                                         style="width: {{ $stats['compliance_percentage'] }}%" 
                                         aria-valuenow="{{ $stats['compliance_percentage'] }}" 
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Evidence Progress Card -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Evidence Uploaded
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{ $stats['evidence_completion_rate'] }}%</div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ $stats['evidence_completion_rate'] }}%" 
                                         aria-valuenow="{{ $stats['evidence_completion_rate'] }}" 
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                        <div class="small text-gray-500">{{ $stats['uploaded_evidence'] }} of {{ $stats['total_evidence_items'] }} items</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-upload fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Latest Score Card -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Latest Inspection Score
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            @if($stats['latest_score'])
                                {{ $stats['latest_score'] }}%
                            @else
                                N/A
                            @endif
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Upcoming Inspections -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Upcoming Inspections</h6>
                <a href="{{ route('business-owner.inspections.index') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if($upcomingInspections->count() > 0)
                    @foreach($upcomingInspections as $inspection)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-warning">
                                    <i class="fas fa-calendar-alt text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">{{ $inspection->scheduled_date->format('M j, Y g:i A') }}</div>
                                <div class="small text-gray-500">Inspector: {{ $inspection->inspector->full_name }}</div>
                                <div class="small">Type: {{ ucfirst($inspection->inspection_type) }}</div>
                            </div>
                            <div>
                                <span class="badge badge-warning">{{ ucfirst($inspection->status) }}</span>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-gray-500">No upcoming inspections</h5>
                        <p class="text-gray-400">You have no scheduled inspections at this time.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Recent Inspections -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Inspections</h6>
            </div>
            <div class="card-body">
                @if($recentInspections->count() > 0)
                    @foreach($recentInspections as $inspection)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-{{ $inspection->status === 'completed' ? 'success' : 'info' }}">
                                    <i class="fas fa-clipboard-check text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">{{ $inspection->scheduled_date->format('M j, Y') }}</div>
                                <div class="small text-gray-500">Inspector: {{ $inspection->inspector->full_name }}</div>
                                @if($inspection->status === 'completed' && $inspection->getScorePercentage())
                                    <div class="small">Score: {{ $inspection->getScorePercentage() }}%</div>
                                @endif
                            </div>
                            <div>
                                <span class="badge badge-{{ $inspection->status === 'completed' ? 'success' : ($inspection->status === 'scheduled' ? 'warning' : 'info') }}">
                                    {{ ucfirst($inspection->status) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-check fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-gray-500">No inspections yet</h5>
                        <p class="text-gray-400">Your business hasn't been inspected yet.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Compliance Progress by Category -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Compliance Progress by Category</h6>
        <a href="{{ route('business-owner.checklist.index') }}" class="btn btn-sm btn-primary">
            <i class="fas fa-upload"></i> Upload Evidence
        </a>
    </div>
    <div class="card-body">
        @if(count($complianceProgress) > 0)
            <div class="row">
                @foreach($complianceProgress as $category)
                    <div class="col-md-6 mb-4">
                        <div class="card border-left-primary">
                            <div class="card-body">
                                <h6 class="font-weight-bold text-primary">{{ $category['name'] }}</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Evidence Uploaded: {{ $category['uploaded_items'] }}/{{ $category['total_items'] }}</small>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar bg-info" role="progressbar" 
                                             style="width: {{ $category['uploaded_percentage'] }}%" 
                                             aria-valuenow="{{ $category['uploaded_percentage'] }}" 
                                             aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Evidence Approved: {{ $category['approved_items'] }}/{{ $category['total_items'] }}</small>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ $category['approved_percentage'] }}%" 
                                             aria-valuenow="{{ $category['approved_percentage'] }}" 
                                             aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="badge badge-{{ $category['approved_percentage'] >= 80 ? 'success' : ($category['approved_percentage'] >= 50 ? 'warning' : 'danger') }}">
                                        {{ $category['approved_percentage'] }}% Complete
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-4">
                <i class="fas fa-list-check fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-500">No compliance categories available</h5>
                <p class="text-gray-400">Compliance categories will appear here once they are configured.</p>
            </div>
        @endif
    </div>
</div>

<!-- Pending Evidence Items -->
@if($pendingEvidenceItems->count() > 0)
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-warning">Pending Evidence Upload</h6>
    </div>
    <div class="card-body">
        <p class="text-muted mb-3">The following items require evidence upload to maintain compliance:</p>
        <div class="row">
            @foreach($pendingEvidenceItems->take(6) as $item)
                <div class="col-md-6 mb-3">
                    <div class="card border-left-warning">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="font-weight-bold">{{ $item->item_name }}</div>
                                    <div class="small text-muted">{{ $item->category->name }}</div>
                                </div>
                                <a href="{{ route('business-owner.checklist.index') }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-upload"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        @if($pendingEvidenceItems->count() > 6)
            <div class="text-center">
                <a href="{{ route('business-owner.checklist.index') }}" class="btn btn-warning">
                    View All {{ $pendingEvidenceItems->count() }} Pending Items
                </a>
            </div>
        @endif
    </div>
</div>
@endif

@endsection

@push('scripts')
<script src="{{ asset('vendor/chart.js/Chart.min.js') }}"></script>
<script>
// Auto-refresh dashboard data every 5 minutes
setInterval(function() {
    // You can implement AJAX refresh here if needed
}, 300000);
</script>
@endpush
