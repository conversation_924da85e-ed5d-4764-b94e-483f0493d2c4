<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ContactMessage extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'status',
        'replied_by',
        'replied_at',
        'reply_notes',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'replied_at' => 'datetime',
    ];

    /**
     * Get the user who replied to this message
     */
    public function repliedBy()
    {
        return $this->belongsTo(User::class, 'replied_by');
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('status', 'unread');
    }

    /**
     * Scope for read messages
     */
    public function scopeRead($query)
    {
        return $query->where('status', 'read');
    }

    /**
     * Scope for replied messages
     */
    public function scopeReplied($query)
    {
        return $query->where('status', 'replied');
    }

    /**
     * Scope for recent messages
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get status label
     */
    public function getStatusLabel()
    {
        return match($this->status) {
            'unread' => 'Unread',
            'read' => 'Read',
            'replied' => 'Replied',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor()
    {
        return match($this->status) {
            'unread' => 'primary',
            'read' => 'warning',
            'replied' => 'success',
            default => 'secondary'
        };
    }

    /**
     * Check if message is unread
     */
    public function isUnread()
    {
        return $this->status === 'unread';
    }

    /**
     * Check if message is read
     */
    public function isRead()
    {
        return $this->status === 'read';
    }

    /**
     * Check if message is replied
     */
    public function isReplied()
    {
        return $this->status === 'replied';
    }

    /**
     * Mark message as read
     */
    public function markAsRead()
    {
        if ($this->status === 'unread') {
            $this->update(['status' => 'read']);
        }
    }

    /**
     * Mark message as replied
     */
    public function markAsReplied($userId = null, $notes = null)
    {
        $this->update([
            'status' => 'replied',
            'replied_by' => $userId,
            'replied_at' => now(),
            'reply_notes' => $notes,
        ]);
    }

    /**
     * Get formatted phone number
     */
    public function getFormattedPhone()
    {
        if (empty($this->phone)) {
            return 'Not provided';
        }

        // Basic phone formatting for Philippine numbers
        $phone = preg_replace('/[^0-9]/', '', $this->phone);
        
        if (strlen($phone) === 11 && str_starts_with($phone, '09')) {
            return substr($phone, 0, 4) . '-' . substr($phone, 4, 3) . '-' . substr($phone, 7);
        }

        return $this->phone;
    }

    /**
     * Get message excerpt
     */
    public function getExcerpt($length = 100)
    {
        return strlen($this->message) > $length 
            ? substr($this->message, 0, $length) . '...'
            : $this->message;
    }

    /**
     * Get time ago
     */
    public function getTimeAgo()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get formatted created date
     */
    public function getFormattedDate()
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    /**
     * Get recent unread messages count
     */
    public static function getUnreadCount()
    {
        return static::unread()->count();
    }

    /**
     * Get recent messages for dashboard
     */
    public static function getRecentMessages($limit = 5)
    {
        return static::latest()->limit($limit)->get();
    }

    /**
     * Get messages statistics
     */
    public static function getStatistics()
    {
        return [
            'total' => static::count(),
            'unread' => static::unread()->count(),
            'read' => static::read()->count(),
            'replied' => static::replied()->count(),
            'today' => static::whereDate('created_at', today())->count(),
            'this_week' => static::where('created_at', '>=', now()->startOfWeek())->count(),
            'this_month' => static::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * Search messages
     */
    public static function search($query)
    {
        return static::where(function($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('email', 'like', "%{$query}%")
              ->orWhere('subject', 'like', "%{$query}%")
              ->orWhere('message', 'like', "%{$query}%");
        });
    }

    /**
     * Get messages by status
     */
    public static function getByStatus($status)
    {
        return static::where('status', $status)->latest()->get();
    }

    /**
     * Bulk mark as read
     */
    public static function bulkMarkAsRead($ids)
    {
        return static::whereIn('id', $ids)
            ->where('status', 'unread')
            ->update(['status' => 'read']);
    }

    /**
     * Bulk mark as replied
     */
    public static function bulkMarkAsReplied($ids, $userId = null, $notes = null)
    {
        return static::whereIn('id', $ids)
            ->whereIn('status', ['unread', 'read'])
            ->update([
                'status' => 'replied',
                'replied_by' => $userId,
                'replied_at' => now(),
                'reply_notes' => $notes,
            ]);
    }

    /**
     * Get contact form statistics for admin dashboard
     */
    public static function getDashboardStats()
    {
        return [
            'total_messages' => static::count(),
            'unread_messages' => static::unread()->count(),
            'messages_today' => static::whereDate('created_at', today())->count(),
            'messages_this_week' => static::where('created_at', '>=', now()->startOfWeek())->count(),
            'response_rate' => static::count() > 0 
                ? round((static::replied()->count() / static::count()) * 100, 1)
                : 0,
        ];
    }

    /**
     * Auto-cleanup old messages
     */
    public static function cleanupOldMessages($days = 365)
    {
        return static::where('created_at', '<', now()->subDays($days))
            ->where('status', 'replied')
            ->delete();
    }
}
