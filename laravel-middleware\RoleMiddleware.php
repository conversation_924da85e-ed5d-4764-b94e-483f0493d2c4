<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user has the required role
        if ($user->role !== $role) {
            // Redirect to appropriate dashboard based on user's actual role
            return $this->redirectToUserDashboard($user->role);
        }

        // Check if user account is active
        if ($user->status !== 'active') {
            Auth::logout();
            return redirect()->route('login')
                ->withErrors(['account' => 'Your account is not active. Please contact the administrator.']);
        }

        return $next($request);
    }

    /**
     * Redirect user to their appropriate dashboard
     */
    private function redirectToUserDashboard(string $role)
    {
        return match($role) {
            'admin' => redirect()->route('admin.dashboard')
                ->withErrors(['access' => 'You do not have permission to access this area.']),
            'inspector' => redirect()->route('inspector.dashboard')
                ->withErrors(['access' => 'You do not have permission to access this area.']),
            'business_owner' => redirect()->route('business-owner.dashboard')
                ->withErrors(['access' => 'You do not have permission to access this area.']),
            default => redirect()->route('login')
                ->withErrors(['role' => 'Invalid user role.'])
        };
    }
}
