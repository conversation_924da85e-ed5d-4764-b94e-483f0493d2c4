<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Business;
use App\Models\ComplianceEvidence;
use App\Models\Inspection;
use App\Models\User;

class AdminController extends Controller {
    private $userModel;
    private $businessModel;
    private $inspectionModel;
    private $evidenceModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->auth->requireAdmin();

        $this->userModel = new User();
        $this->businessModel = new Business();
        $this->inspectionModel = new Inspection();
        $this->evidenceModel = new ComplianceEvidence();
    }

    public function dashboard() {
        $businessCount = $this->businessModel->countAll();
        $userCount = $this->userModel->countAll();
        // Count completed inspections (includes both approved and rejected) to match the Inspection Results page
        $inspectionCount = $this->inspectionModel->countByStatus('completed');

        $recentBusinesses = $this->businessModel->getRecent(5);
        $recentUsers = $this->userModel->getRecent(5);

        // Get upcoming inspections
        $upcomingInspections = $this->inspectionModel->getUpcomingInspections(5);

        $compliantBusinesses = $this->businessModel->countByComplianceStatus('compliant');
        $nonCompliantBusinesses = $this->businessModel->countByComplianceStatus('non_compliant');
        $pendingReviewBusinesses = $this->businessModel->countByComplianceStatus('pending_review');
        $complianceTypes = $this->evidenceModel->getComplianceTypes();

        $stats = [
            'business_count' => $businessCount,
            'user_count' => $userCount,
            'inspection_count' => $inspectionCount,
            'recent_businesses' => $recentBusinesses,
            'recent_users' => $recentUsers,
            'upcoming_inspections' => $upcomingInspections,
            'compliant_businesses' => $compliantBusinesses,
            'non_compliant_businesses' => $nonCompliantBusinesses,
            'pending_review_businesses' => $pendingReviewBusinesses
        ];

        return $this->render('admin/dashboard', [
            'title' => 'Admin Dashboard',
            'active_page' => 'dashboard',
            'stats' => $stats,
            'user' => $this->auth->getUser()
        ]);
    }

    public function businesses() {
        $businesses = $this->businessModel->getAll();
        return $this->render('admin/businesses/index', [
            'title' => 'Manage Businesses',
            'active_page' => 'businesses',
            'businesses' => $businesses,
            'user' => $this->auth->getUser()
        ]);
    }

    public function createBusiness() {
        $users = $this->userModel->getByRole('business_owner');

        if ($this->isPost()) {
            $owner_id = $_POST['owner_id'] ?? '';
            $owner = $this->userModel->find($owner_id);
            $owner_name = $owner ? $owner['full_name'] : '';
            $name = $_POST['name'] ?? '';
            $registration_number = $_POST['registration_number'] ?? '';
            $email = $_POST['email'] ?? '';
            $contact_number = $_POST['contact_number'] ?? '';
            $address = $_POST['address'] ?? '';
            $category_id = $_POST['category_id'] ?? '';
            $barangay_id = $_POST['barangay_id'] ?? '';
            $employee_count = $_POST['employee_count'] ?? 1;
            $date_established = $_POST['date_established'] ?? null;
            $status = $_POST['status'] ?? 'pending';

            if (empty($owner_id) || empty($name) || empty($registration_number) || empty($email) ||
                empty($contact_number) || empty($address) || empty($category_id) || empty($barangay_id)) {
                $_SESSION['error'] = 'All required fields must be filled out.';
                $this->redirect('admin/businesses/create');
                return;
            }

            $data = [
                'owner_id' => $owner_id,
                'owner_name' => $owner_name,
                'name' => $name,
                'registration_number' => $registration_number,
                'email' => $email,
                'contact_number' => $contact_number,
                'address' => $address,
                'category_id' => $category_id,
                'barangay_id' => $barangay_id,
                'employee_count' => $employee_count,
                'date_established' => $date_established,
                'status' => $status
            ];

            if ($this->businessModel->create($data)) {
                $_SESSION['success'] = 'Business created successfully.';
                $this->redirect('admin/businesses');
            } else {
                $_SESSION['error'] = 'Failed to create business.';
                $this->redirect('admin/businesses/create');
            }
            return;
        }

        $categories = $this->businessModel->getCategories();
        $districts = $this->businessModel->getDistricts();
        $barangays = $this->businessModel->getBarangays();

        return $this->render('admin/businesses/create', [
            'title' => 'Create Business',
            'active_page' => 'businesses',
            'users' => $users,
            'categories' => $categories,
            'districts' => $districts,
            'barangays' => $barangays,
            'user' => $this->auth->getUser()
        ]);
    }

    public function viewBusiness($id) {
        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('admin/businesses');
            return;
        }

        // Get related data
        $inspections = $this->inspectionModel->getByBusiness($id);
        $evidence = $this->evidenceModel->getByBusinessId($id);

        return $this->render('admin/businesses/view', [
            'title' => 'Business Details',
            'active_page' => 'businesses',
            'business' => $business,
            'inspections' => $inspections,
            'evidence' => $evidence,
            'user' => $this->auth->getUser()
        ]);
    }

    public function editBusiness($id) {
        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('admin/businesses');
            return;
        }

        if ($this->isPost()) {
            $data = [
                'name' => $_POST['name'] ?? '',
                'registration_number' => $_POST['registration_number'] ?? '',
                'email' => $_POST['email'] ?? '',
                'contact_number' => $_POST['contact_number'] ?? '',
                'address' => $_POST['address'] ?? '',
                'category_id' => $_POST['category_id'] ?? '',
                'barangay_id' => $_POST['barangay_id'] ?? '',
                'employee_count' => $_POST['employee_count'] ?? 1,
                'date_established' => $_POST['date_established'] ?? null,
                'status' => $_POST['status'] ?? 'pending'
            ];

            if (empty($data['name']) || empty($data['registration_number']) || empty($data['email']) ||
                empty($data['contact_number']) || empty($data['address']) || empty($data['category_id']) || empty($data['barangay_id'])) {
                $_SESSION['error'] = 'All required fields must be filled out.';
                $this->redirect("admin/businesses/edit/$id");
                return;
            }

            if ($this->businessModel->update($id, $data)) {
                $_SESSION['success'] = 'Business updated successfully.';
                $this->redirect("admin/businesses/view/$id");
            } else {
                $_SESSION['error'] = 'Failed to update business.';
                $this->redirect("admin/businesses/edit/$id");
            }
            return;
        }

        $users = $this->userModel->getByRole('business_owner');
        $categories = $this->businessModel->getCategories();
        $districts = $this->businessModel->getDistricts();
        $barangays = $this->businessModel->getBarangays();

        return $this->render('admin/businesses/edit', [
            'title' => 'Edit Business',
            'active_page' => 'businesses',
            'business' => $business,
            'users' => $users,
            'categories' => $categories,
            'districts' => $districts,
            'barangays' => $barangays,
            'user' => $this->auth->getUser()
        ]);
    }

    public function deleteBusiness($id) {
        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/businesses');
            return;
        }

        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('admin/businesses');
            return;
        }

        if ($this->businessModel->delete($id)) {
            $_SESSION['success'] = 'Business deleted successfully.';
        } else {
            $_SESSION['error'] = 'Failed to delete business.';
        }

        $this->redirect('admin/businesses');
    }

    public function activateBusiness($id) {
        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('admin/businesses');
            return;
        }

        if ($this->businessModel->updateStatus($id, 'active')) {
            $_SESSION['success'] = 'Business activated successfully.';
        } else {
            $_SESSION['error'] = 'Failed to activate business.';
        }

        $this->redirect('admin/businesses');
    }

    public function suspendBusiness($id) {
        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('admin/businesses');
            return;
        }

        if ($this->businessModel->updateStatus($id, 'suspended')) {
            $_SESSION['success'] = 'Business suspended successfully.';
        } else {
            $_SESSION['error'] = 'Failed to suspend business.';
        }

        $this->redirect('admin/businesses');
    }

    public function updateBusinessCompliance($id) {
        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('admin/businesses');
            return;
        }

        if ($this->isPost()) {
            $data = [
                'business_permit' => isset($_POST['business_permit']) ? 1 : 0,
                'sanitary_permit' => isset($_POST['sanitary_permit']) ? 1 : 0,
                'fire_safety_certificate' => isset($_POST['fire_safety_certificate']) ? 1 : 0,
                'environmental_permit' => isset($_POST['environmental_permit']) ? 1 : 0,
                'safety_officer_count' => $_POST['safety_officer_count'] ?? 0,
                'has_safety_signage' => isset($_POST['has_safety_signage']) ? 1 : 0,
                'has_first_aid' => isset($_POST['has_first_aid']) ? 1 : 0,
                'has_fire_extinguishers' => isset($_POST['has_fire_extinguishers']) ? 1 : 0,
                'has_cctv' => isset($_POST['has_cctv']) ? 1 : 0,
                'has_waste_segregation' => isset($_POST['has_waste_segregation']) ? 1 : 0,
                'compliance_status' => $_POST['compliance_status'] ?? 'pending_review'
            ];

            if ($this->businessModel->updateComplianceChecks($id, $data)) {
                $_SESSION['success'] = 'Compliance status updated successfully.';
                $this->checkAndSetComplianceStatus($id);
                $this->redirect("admin/businesses/compliance/$id");
            } else {
                $_SESSION['error'] = 'Failed to update compliance status.';
                $this->redirect("admin/businesses/compliance/$id");
            }
            return;
        }

        $complianceRequirements = $this->businessModel->getComplianceRequirements();
        $evidence = $this->evidenceModel->getByBusinessId($id);

        return $this->render('admin/businesses/compliance', [
            'title' => 'Update Compliance Status',
            'active_page' => 'businesses',
            'business' => $business,
            'compliance_requirements' => $complianceRequirements,
            'evidence' => $evidence,
            'compliance_types' => $this->evidenceModel->getComplianceTypes(),
            'user' => $this->auth->getUser()
        ]);
    }



    public function profile() {
        $user = $this->auth->getUser();
        return $this->render('admin/profile', [
            'title' => 'My Profile',
            'active_page' => 'profile',
            'user' => $user
        ]);
    }

    public function updateProfile() {
        if (!$this->isPost()) {
            return $this->redirect('admin/profile');
        }

        $userId = $this->auth->getUserId();
        $data = [
            'full_name' => $_POST['full_name'] ?? '',
            'email' => $_POST['email'] ?? ''
        ];

        // Validate required fields
        if (empty($data['full_name']) || empty($data['email'])) {
            $_SESSION['error'] = 'Full name and email are required.';
            return $this->redirect('admin/profile');
        }

        // Update password if provided
        if (!empty($_POST['current_password']) && !empty($_POST['new_password'])) {
            $currentUser = $this->userModel->find($userId);

            if (!password_verify($_POST['current_password'], $currentUser['password_hash'])) {
                $_SESSION['error'] = 'Current password is incorrect.';
                return $this->redirect('admin/profile');
            }

            if ($_POST['new_password'] !== $_POST['confirm_password']) {
                $_SESSION['error'] = 'New passwords do not match.';
                return $this->redirect('admin/profile');
            }

            $data['password_hash'] = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
        }

        try {
            if ($this->userModel->update($userId, $data)) {
                $_SESSION['success'] = 'Profile updated successfully.';
            } else {
                $_SESSION['error'] = 'Failed to update profile.';
            }
        } catch (\Exception $e) {
            error_log("Error updating admin profile: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred while updating your profile.';
        }

        return $this->redirect('admin/profile');
    }

    public function settings() {
        $user = $this->auth->getUser();
        return $this->render('admin/settings', [
            'title' => 'Settings',
            'active_page' => 'settings',
            'user' => $user
        ]);
    }

    public function updateSettings() {
        if (!$this->isPost()) {
            return $this->redirect('admin/settings');
        }

        // Handle settings updates here
        // This could include notification preferences, system settings, etc.

        $_SESSION['success'] = 'Settings updated successfully.';
        return $this->redirect('admin/settings');
    }

    private function checkAndSetComplianceStatus($businessId) {
        return $this->businessModel->checkAndSetComplianceStatus($businessId);
    }

    public function getBarangaysByDistrict($districtId) {
        // For AJAX requests, we'll be more lenient with auth
        header('Content-Type: application/json');

        if (!$districtId) {
            echo json_encode(['success' => false, 'message' => 'District ID is required']);
            return;
        }

        try {
            $barangays = $this->businessModel->getBarangaysByDistrict($districtId);
            echo json_encode(['success' => true, 'data' => $barangays]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error fetching barangays: ' . $e->getMessage()]);
        }
    }
}