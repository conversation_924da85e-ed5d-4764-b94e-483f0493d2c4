<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-map-marker-alt text-primary me-2"></i>
            Assign Inspectors to <?= htmlspecialchars($district['name']) ?>
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspector-assignments" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Assignments
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Assignment Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-plus me-2"></i>Assign New Inspectors
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($available_inspectors)): ?>
                        <form method="POST" action="<?= BASE_URL ?>admin/inspector-assignments/assign/<?= $district['id'] ?>">
                            <div class="mb-3">
                                <label for="inspector_ids" class="form-label">Select Inspectors <span class="text-danger">*</span></label>
                                <div class="row">
                                    <?php foreach ($available_inspectors as $inspector): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       name="inspector_ids[]" 
                                                       value="<?= $inspector['id'] ?>" 
                                                       id="inspector_<?= $inspector['id'] ?>">
                                                <label class="form-check-label" for="inspector_<?= $inspector['id'] ?>">
                                                    <strong><?= htmlspecialchars($inspector['full_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($inspector['email']) ?></small>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="form-text">Select one or more inspectors to assign to this district.</div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Assignment Notes</label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Optional notes about this assignment..."></textarea>
                                <div class="form-text">Add any special instructions or notes for the assigned inspectors.</div>
                            </div>

                            <div class="d-flex justify-content-end gap-2">
                                <a href="<?= BASE_URL ?>admin/inspector-assignments" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> Assign Selected Inspectors
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Available Inspectors</h5>
                            <p class="text-muted">All inspectors are already assigned to this district or there are no active inspectors.</p>
                            <a href="<?= BASE_URL ?>admin/users/create?role=inspector" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Add New Inspector
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- District Info & Current Inspectors -->
        <div class="col-lg-4">
            <!-- District Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>District Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted">District Name</h6>
                        <p class="mb-0"><?= htmlspecialchars($district['name']) ?></p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Current Inspectors</h6>
                        <p class="mb-0">
                            <span class="badge bg-primary fs-6"><?= count($current_inspectors) ?></span>
                        </p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Available Inspectors</h6>
                        <p class="mb-0">
                            <span class="badge bg-success fs-6"><?= count($available_inspectors) ?></span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Currently Assigned Inspectors -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-users me-2"></i>Currently Assigned
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($current_inspectors)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($current_inspectors as $inspector): ?>
                                <div class="list-group-item px-0 py-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <strong><?= htmlspecialchars($inspector['inspector_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspector['inspector_email']) ?></small>
                                            <?php if ($inspector['notes']): ?>
                                                <br><small class="text-info">
                                                    <i class="fas fa-sticky-note"></i> 
                                                    <?= htmlspecialchars(substr($inspector['notes'], 0, 50)) ?>
                                                    <?= strlen($inspector['notes']) > 50 ? '...' : '' ?>
                                                </small>
                                            <?php endif; ?>
                                            <br><small class="text-muted">
                                                Assigned: <?= date('M d, Y', strtotime($inspector['assigned_at'])) ?>
                                            </small>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="removeInspector('<?= $inspector['inspector_id'] ?>', '<?= htmlspecialchars($inspector['inspector_name']) ?>')"
                                                    title="Remove from district">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-user-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No inspectors assigned yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Inspector Modal -->
<div class="modal fade" id="removeInspectorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Inspector</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove <strong id="removeInspectorName"></strong> from <?= htmlspecialchars($district['name']) ?>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="removeInspectorForm" method="POST" action="<?= BASE_URL ?>admin/inspector-assignments/remove" style="display: inline;">
                    <input type="hidden" name="inspector_id" id="removeInspectorIdInput">
                    <input type="hidden" name="district_id" value="<?= $district['id'] ?>">
                    <button type="submit" class="btn btn-danger">Remove Inspector</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function removeInspector(inspectorId, inspectorName) {
    document.getElementById('removeInspectorName').textContent = inspectorName;
    document.getElementById('removeInspectorIdInput').value = inspectorId;
    
    new bootstrap.Modal(document.getElementById('removeInspectorModal')).show();
}

// Select/Deselect all functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add select all button
    const checkboxContainer = document.querySelector('.row');
    if (checkboxContainer && checkboxContainer.children.length > 0) {
        const selectAllDiv = document.createElement('div');
        selectAllDiv.className = 'col-12 mb-3';
        selectAllDiv.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="selectAll">
                <label class="form-check-label" for="selectAll">
                    <strong>Select All Inspectors</strong>
                </label>
            </div>
        `;
        checkboxContainer.insertBefore(selectAllDiv, checkboxContainer.firstChild);
        
        // Add functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="inspector_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // Update select all when individual checkboxes change
        document.querySelectorAll('input[name="inspector_ids[]"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('input[name="inspector_ids[]"]');
                const checkedCheckboxes = document.querySelectorAll('input[name="inspector_ids[]"]:checked');
                const selectAllCheckbox = document.getElementById('selectAll');
                
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            });
        });
    }
});
</script>
<?php $this->endSection() ?>
