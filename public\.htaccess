<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /ohs-system/
    
    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    
    # Remove /public/ from URLs if present
    RewriteCond %{THE_REQUEST} /public/([^\s?]*) [NC]
    RewriteRule ^ %1 [L,NE,R=302]
    
    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]
    
    # Handle assets - redirect /assets/ to /public/assets/
    RewriteCond %{REQUEST_URI} ^/ohs-system/assets/(.*)$ [NC]
    RewriteRule ^ public/assets/%1 [L]

    # Allow direct access to other static files in public directory
    RewriteCond %{REQUEST_URI} \.(css|js|jpg|jpeg|png|gif|ico|woff|woff2|ttf|svg|eot)$ [NC]
    RewriteRule ^ public/%{REQUEST_URI} [L]

    # Handle file serving routes before general routing
    RewriteCond %{REQUEST_URI} ^/ohs-system/files/compliance-evidence/(.+)$ [NC]
    RewriteRule ^ public/serve-evidence-simple.php?file=%1 [L,QSA]
    
    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ public/index.php [QSA,L]
</IfModule>

# Prevent directory listing
Options -Indexes

# Handle errors
ErrorDocument 404 /ohs-system/public/index.php
ErrorDocument 500 /ohs-system/public/index.php 