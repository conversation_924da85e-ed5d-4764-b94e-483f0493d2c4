<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\WebsiteSettings;
use App\Models\HomepageContent;

class WebsiteSettingsController extends Controller {
    protected $auth;
    protected $settingsModel;
    protected $homepageModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->auth->requireAdmin();
        $this->settingsModel = new WebsiteSettings();
        $this->homepageModel = new HomepageContent();
    }

    /**
     * Display website settings dashboard
     */
    public function index() {
        $settings = $this->settingsModel->getAllSettings();
        $colorSchemes = $this->settingsModel->getColorSchemes();

        return $this->render('admin/website-settings/index', [
            'title' => 'Website Settings',
            'active_page' => 'website-settings',
            'settings' => $settings,
            'color_schemes' => $colorSchemes,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Update general settings
     */
    public function updateGeneral() {
        if (!$this->isPost()) {
            return $this->redirect('admin/website-settings');
        }

        $settings = [
            'site_name' => $_POST['site_name'] ?? '',
            'site_tagline' => $_POST['site_tagline'] ?? '',
            'site_description' => $_POST['site_description'] ?? '',
            'contact_email' => $_POST['contact_email'] ?? '',
            'contact_phone' => $_POST['contact_phone'] ?? '',
            'office_address' => $_POST['office_address'] ?? '',
            'office_hours' => $_POST['office_hours'] ?? '',
            'facebook_url' => $_POST['facebook_url'] ?? '',
            'twitter_url' => $_POST['twitter_url'] ?? '',
            'youtube_url' => $_POST['youtube_url'] ?? ''
        ];

        try {
            foreach ($settings as $key => $value) {
                $this->settingsModel->updateSetting($key, $value);
            }
            
            $_SESSION['success'] = 'General settings updated successfully.';
        } catch (\Exception $e) {
            error_log("Error updating general settings: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to update general settings.';
        }

        return $this->redirect('admin/website-settings');
    }

    /**
     * Update color scheme
     */
    public function updateColors() {
        if (!$this->isPost()) {
            return $this->redirect('admin/website-settings');
        }

        $colors = [
            'primary_color' => $_POST['primary_color'] ?? '#2563eb',
            'secondary_color' => $_POST['secondary_color'] ?? '#1e40af',
            'accent_color' => $_POST['accent_color'] ?? '#3b82f6',
            'success_color' => $_POST['success_color'] ?? '#059669',
            'warning_color' => $_POST['warning_color'] ?? '#d97706',
            'danger_color' => $_POST['danger_color'] ?? '#dc2626',
            'dark_color' => $_POST['dark_color'] ?? '#1f2937',
            'light_color' => $_POST['light_color'] ?? '#f8fafc'
        ];

        try {
            foreach ($colors as $key => $value) {
                $this->settingsModel->updateSetting($key, $value);
            }
            
            // Generate CSS file with new colors
            $this->generateCustomCSS($colors);

            // Clear any potential cache
            $this->clearCache();

            $_SESSION['success'] = 'Color scheme updated successfully.';
        } catch (\Exception $e) {
            error_log("Error updating color scheme: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to update color scheme.';
        }

        return $this->redirect('admin/website-settings');
    }

    /**
     * Apply predefined color scheme
     */
    public function applyColorScheme($schemeId) {
        if (!$this->isPost()) {
            return $this->redirect('admin/website-settings');
        }

        $schemes = $this->settingsModel->getColorSchemes();
        
        if (!isset($schemes[$schemeId])) {
            $_SESSION['error'] = 'Invalid color scheme selected.';
            return $this->redirect('admin/website-settings');
        }

        $scheme = $schemes[$schemeId];

        try {
            foreach ($scheme['colors'] as $key => $value) {
                $this->settingsModel->updateSetting($key, $value);
            }
            
            // Generate CSS file with new colors
            $this->generateCustomCSS($scheme['colors']);

            // Clear any potential cache
            $this->clearCache();

            $_SESSION['success'] = "Applied {$scheme['name']} color scheme successfully.";
        } catch (\Exception $e) {
            error_log("Error applying color scheme: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to apply color scheme.';
        }

        return $this->redirect('admin/website-settings');
    }

    /**
     * Upload and manage images
     */
    public function uploadImage() {
        if (!$this->isPost()) {
            return $this->redirect('admin/website-settings');
        }

        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            $_SESSION['error'] = 'Please select a valid image file.';
            return $this->redirect('admin/website-settings');
        }

        $imageType = $_POST['image_type'] ?? '';
        $allowedTypes = ['hero_bg', 'logo', 'about_image', 'services_image', 'contact_image'];
        
        if (!in_array($imageType, $allowedTypes)) {
            $_SESSION['error'] = 'Invalid image type selected.';
            return $this->redirect('admin/website-settings');
        }

        try {
            $uploadResult = $this->handleImageUpload($_FILES['image'], $imageType);
            
            if ($uploadResult['success']) {
                // Update setting with new image path
                $this->settingsModel->updateSetting($imageType, $uploadResult['path']);
                $_SESSION['success'] = 'Image uploaded successfully.';
            } else {
                $_SESSION['error'] = $uploadResult['message'];
            }
        } catch (\Exception $e) {
            error_log("Error uploading image: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to upload image.';
        }

        return $this->redirect('admin/website-settings');
    }

    /**
     * Handle image upload
     */
    private function handleImageUpload($file, $type) {
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $maxFileSize = 5 * 1024 * 1024; // 5MB
        
        $fileName = $file['name'];
        $fileSize = $file['size'];
        $fileTmp = $file['tmp_name'];
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Validate file extension
        if (!in_array($fileExt, $allowedExtensions)) {
            return ['success' => false, 'message' => 'Invalid file type. Allowed: ' . implode(', ', $allowedExtensions)];
        }
        
        // Validate file size
        if ($fileSize > $maxFileSize) {
            return ['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.'];
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = ROOT_PATH . '/assets/images/uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $newFileName = $type . '_' . time() . '.' . $fileExt;
        $uploadPath = $uploadDir . $newFileName;
        $webPath = '/assets/images/uploads/' . $newFileName;
        
        // Move uploaded file
        if (move_uploaded_file($fileTmp, $uploadPath)) {
            return ['success' => true, 'path' => $webPath];
        } else {
            return ['success' => false, 'message' => 'Failed to move uploaded file.'];
        }
    }

    /**
     * Generate custom CSS file with current colors
     */
    private function generateCustomCSS($colors) {
        $cssContent = ":root {\n";
        foreach ($colors as $key => $value) {
            $cssVar = '--' . str_replace('_', '-', $key);
            $cssContent .= "    $cssVar: $value;\n";
        }
        $cssContent .= "}\n\n";

        // Override existing CSS variables in layouts
        $cssContent .= "body {\n";
        foreach ($colors as $key => $value) {
            $cssVar = '--' . str_replace('_', '-', $key);
            $cssContent .= "    $cssVar: $value !important;\n";
        }
        $cssContent .= "}\n\n";

        // Add comprehensive CSS rules
        $cssContent .= $this->getComprehensiveCSS();

        // Add public page specific CSS
        $cssContent .= $this->getPublicPageCSS();

        // Save CSS file
        $cssDir = ROOT_PATH . '/assets/css/';
        if (!file_exists($cssDir)) {
            mkdir($cssDir, 0755, true);
        }

        $cssFile = $cssDir . 'custom-theme.css';
        file_put_contents($cssFile, $cssContent);
    }

    /**
     * Get comprehensive CSS rules for theming
     */
    private function getComprehensiveCSS() {
        return "
/* Primary elements */
.btn-primary, .bg-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Secondary elements */
.btn-secondary, .bg-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

/* Success elements */
.btn-success, .bg-success, .alert-success {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

/* Warning elements */
.btn-warning, .bg-warning, .alert-warning {
    background-color: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
}

/* Danger elements */
.btn-danger, .bg-danger, .alert-danger {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

/* Navigation */
.navbar-custom {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Feature icons */
.feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* Cards and buttons */
.btn-hero {
    color: var(--primary-color) !important;
}

.btn-hero:hover {
    color: var(--primary-color) !important;
}

/* Stats and highlights */
.stat-number {
    color: var(--primary-color) !important;
}

/* Homepage specific overrides */
.hero-section, .hero-section .bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.navbar-brand, .navbar-nav .nav-link {
    color: white !important;
}

/* Admin Dashboard specific overrides */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .nav-link:hover, .sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
    color: white !important;
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

/* Dashboard cards */
.dashboard-card {
    border-left: 4px solid var(--primary-color) !important;
}

.dashboard-card .card-body {
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(139, 92, 246, 0.05)) !important;
}

/* Table styling */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(124, 58, 237, 0.05) !important;
}

.table th {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--secondary-color) !important;
}

/* Badge styling */
.badge-success {
    background-color: var(--success-color) !important;
}

.badge-warning {
    background-color: var(--warning-color) !important;
}

.badge-danger {
    background-color: var(--danger-color) !important;
}

/* Form controls */
.form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25) !important;
}

/* Links */
a {
    color: var(--primary-color) !important;
}

a:hover {
    color: var(--secondary-color) !important;
}

/* Progress bars */
.progress-bar {
    background-color: var(--primary-color) !important;
}

/* Alerts */
.alert-info {
    background-color: rgba(124, 58, 237, 0.1) !important;
    border-color: var(--primary-color) !important;
    color: var(--secondary-color) !important;
}

/* Force override for specific elements */
.navbar-dark.bg-primary, .bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.navbar-custom, .navbar-dark {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Hero section override */
.hero-section, .hero-section * {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Sidebar override for admin */
#sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Button overrides */
.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

/* Stats cards */
.card-primary .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
    color: white !important;
}

/* Force override all primary colors */
html body .navbar-custom,
html body .hero-section,
html body .bg-primary,
html body .btn-primary,
html body #sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    background-color: var(--primary-color) !important;
}

/* Text colors */
html body .text-primary {
    color: var(--primary-color) !important;
}

/* Border colors */
html body .border-primary {
    border-color: var(--primary-color) !important;
}

/* Ensure navbar brand and links are white */
html body .navbar-brand,
html body .navbar-nav .nav-link {
    color: white !important;
}
";
    }

    /**
     * Reset to default settings
     */
    public function resetToDefaults() {
        if (!$this->isPost()) {
            return $this->redirect('admin/website-settings');
        }

        try {
            $this->settingsModel->resetToDefaults();
            
            // Generate default CSS
            $defaultColors = [
                'primary_color' => '#2563eb',
                'secondary_color' => '#1e40af',
                'accent_color' => '#3b82f6',
                'success_color' => '#059669',
                'warning_color' => '#d97706',
                'danger_color' => '#dc2626',
                'dark_color' => '#1f2937',
                'light_color' => '#f8fafc'
            ];
            $this->generateCustomCSS($defaultColors);
            
            $_SESSION['success'] = 'Website settings reset to defaults successfully.';
        } catch (\Exception $e) {
            error_log("Error resetting settings: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to reset settings.';
        }

        return $this->redirect('admin/website-settings');
    }

    /**
     * Preview changes
     */
    public function preview() {
        $settings = $this->settingsModel->getAllSettings();

        return $this->render('admin/website-settings/preview', [
            'title' => 'Website Preview',
            'settings' => $settings
        ]);
    }

    /**
     * Clear cache to ensure immediate color updates
     */
    private function clearCache() {
        // Clear PHP opcache if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }

        // Add cache-busting headers
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Update CSS file timestamp
        $cssFile = ROOT_PATH . '/assets/css/custom-theme.css';
        if (file_exists($cssFile)) {
            touch($cssFile);
        }
    }

    /**
     * Get public page specific CSS rules
     */
    private function getPublicPageCSS() {
        return "
/* Public Page Headers - Dynamic Colors */
.page-header,
section[style*='background: linear-gradient'] {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Feature Icons - Dynamic Colors */
.feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* Process Steps - Dynamic Colors */
.process-step {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* Call to Action Sections */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Contact Icons */
.contact-icon i {
    color: var(--primary-color) !important;
}

/* Service Cards */
.service-card .feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* News Cards */
.news-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* About Page Elements */
.about-section .highlight {
    color: var(--primary-color) !important;
}

/* Override any hardcoded blue gradients */
[style*='#2563eb'],
[style*='#1e40af'],
[style*='linear-gradient(135deg, #2563eb'],
[style*='linear-gradient(135deg, #1e40af'] {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Ensure all primary text uses dynamic colors */
.text-primary,
.btn-primary,
.bg-primary {
    color: var(--primary-color) !important;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Public page specific button styles */
.btn-hero {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-hero:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}
";
    }
}
?>
