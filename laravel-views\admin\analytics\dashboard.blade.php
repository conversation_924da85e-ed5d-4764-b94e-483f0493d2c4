@extends('layouts.admin')

@section('title', 'Analytics Dashboard')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Analytics Dashboard</h1>
    <div class="d-none d-sm-inline-block">
        <button class="btn btn-sm btn-primary" onclick="refreshAnalytics()">
            <i class="fas fa-sync-alt"></i> Refresh Data
        </button>
        <button class="btn btn-sm btn-success" onclick="exportAnalytics()">
            <i class="fas fa-download"></i> Export Report
        </button>
    </div>
</div>

<!-- Date Range Filter -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form id="analyticsFilter" class="row align-items-end">
            <div class="col-md-3">
                <label for="date_from">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ request('date_from', now()->subMonths(6)->format('Y-m-d')) }}">
            </div>
            <div class="col-md-3">
                <label for="date_to">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ request('date_to', now()->format('Y-m-d')) }}">
            </div>
            <div class="col-md-2">
                <label for="district_filter">District</label>
                <select class="form-control" id="district_filter" name="district_id">
                    <option value="">All Districts</option>
                    @foreach($districts as $district)
                        <option value="{{ $district->id }}">{{ $district->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="category_filter">Category</label>
                <select class="form-control" id="category_filter" name="category_id">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-filter"></i> Apply Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Key Metrics Row -->
<div class="row">
    <!-- Total Inspections -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Inspections
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-inspections">
                            {{ $analytics['overview']['total_inspections'] }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completion Rate -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Completion Rate
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800" id="completion-rate">
                                    {{ round(($analytics['overview']['completed_inspections'] / max($analytics['overview']['total_inspections'], 1)) * 100, 1) }}%
                                </div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ round(($analytics['overview']['completed_inspections'] / max($analytics['overview']['total_inspections'], 1)) * 100, 1) }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Rate -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Compliance Rate
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800" id="compliance-rate">
                                    {{ $analytics['overview']['compliance_rate'] }}%
                                </div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: {{ $analytics['overview']['compliance_rate'] }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Average Response Time -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Avg Response Time
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="response-time">
                            {{ $analytics['response_times']['verification_time_hours'] ?? 0 }}h
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Monthly Trends Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Monthly Inspection Trends</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="downloadChart('monthlyTrendsChart')">Download Chart</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="monthlyTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- District Comparison -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">District Comparison</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="districtComparisonChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    @foreach($analytics['district_comparison'] as $district)
                        <span class="mr-2">
                            <i class="fas fa-circle" style="color: {{ $loop->index == 0 ? '#4e73df' : '#1cc88a' }}"></i>
                            {{ $district['name'] }}
                        </span>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Performance & Inspector Workload -->
<div class="row">
    <!-- Category Performance -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Category Performance</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="categoryPerformanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Inspector Workload -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Inspector Workload Distribution</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="inspectorWorkloadChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Violations & Compliance Trends -->
<div class="row">
    <!-- Top Violations -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Top Violations</h6>
            </div>
            <div class="card-body">
                @if(count($analytics['top_violations']) > 0)
                    @foreach($analytics['top_violations'] as $violation)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-danger">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">{{ $violation['item_name'] }}</div>
                                <div class="small text-gray-500">{{ $violation['violation_count'] }} violations</div>
                            </div>
                            <div>
                                <span class="badge badge-danger">{{ $violation['violation_count'] }}</span>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center text-gray-500">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <p>No violations found</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Compliance Trends -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Compliance Trends</h6>
            </div>
            <div class="card-body">
                <div class="chart-line">
                    <canvas id="complianceTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Updates -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Real-time Activity Feed</h6>
    </div>
    <div class="card-body">
        <div id="activity-feed" style="max-height: 300px; overflow-y: auto;">
            <!-- Activity items will be loaded here -->
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="{{ asset('vendor/chart.js/Chart.min.js') }}"></script>
<script>
// Chart.js default configuration
Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
Chart.defaults.global.defaultFontColor = '#858796';

// Analytics data from backend
const analyticsData = @json($analytics);

// Monthly Trends Chart
const monthlyTrendsCtx = document.getElementById("monthlyTrendsChart");
const monthlyTrendsChart = new Chart(monthlyTrendsCtx, {
    type: 'line',
    data: {
        labels: analyticsData.monthly_trends.map(item => 
            new Date(item.year, item.month - 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
        ),
        datasets: [{
            label: "Total Inspections",
            lineTension: 0.3,
            backgroundColor: "rgba(78, 115, 223, 0.05)",
            borderColor: "rgba(78, 115, 223, 1)",
            pointRadius: 3,
            pointBackgroundColor: "rgba(78, 115, 223, 1)",
            pointBorderColor: "rgba(78, 115, 223, 1)",
            pointHoverRadius: 3,
            pointHoverBackgroundColor: "rgba(78, 115, 223, 1)",
            pointHoverBorderColor: "rgba(78, 115, 223, 1)",
            pointHitRadius: 10,
            pointBorderWidth: 2,
            data: analyticsData.monthly_trends.map(item => item.total),
        }, {
            label: "Completed Inspections",
            lineTension: 0.3,
            backgroundColor: "rgba(28, 200, 138, 0.05)",
            borderColor: "rgba(28, 200, 138, 1)",
            pointRadius: 3,
            pointBackgroundColor: "rgba(28, 200, 138, 1)",
            pointBorderColor: "rgba(28, 200, 138, 1)",
            pointHoverRadius: 3,
            pointHoverBackgroundColor: "rgba(28, 200, 138, 1)",
            pointHoverBorderColor: "rgba(28, 200, 138, 1)",
            pointHitRadius: 10,
            pointBorderWidth: 2,
            data: analyticsData.monthly_trends.map(item => item.completed),
        }]
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            xAxes: [{
                gridLines: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 7
                }
            }],
            yAxes: [{
                ticks: {
                    maxTicksLimit: 5,
                    padding: 10,
                },
                gridLines: {
                    color: "rgb(234, 236, 244)",
                    zeroLineColor: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                }
            }],
        },
        legend: {
            display: true
        },
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            titleMarginBottom: 10,
            titleFontColor: '#6e707e',
            titleFontSize: 14,
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            intersect: false,
            mode: 'index',
            caretPadding: 10,
        }
    }
});

// District Comparison Chart
const districtComparisonCtx = document.getElementById("districtComparisonChart");
const districtComparisonChart = new Chart(districtComparisonCtx, {
    type: 'doughnut',
    data: {
        labels: analyticsData.district_comparison.map(item => item.name),
        datasets: [{
            data: analyticsData.district_comparison.map(item => item.total_businesses),
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: false
        },
        cutoutPercentage: 80,
    },
});

// Category Performance Chart
const categoryPerformanceCtx = document.getElementById("categoryPerformanceChart");
const categoryPerformanceChart = new Chart(categoryPerformanceCtx, {
    type: 'bar',
    data: {
        labels: analyticsData.category_performance.map(item => item.name),
        datasets: [{
            label: "Compliant Businesses",
            backgroundColor: "#4e73df",
            hoverBackgroundColor: "#2e59d9",
            borderColor: "#4e73df",
            data: analyticsData.category_performance.map(item => item.businesses_count),
        }],
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            xAxes: [{
                gridLines: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 6
                },
                maxBarThickness: 25,
            }],
            yAxes: [{
                ticks: {
                    min: 0,
                    maxTicksLimit: 5,
                    padding: 10,
                },
                gridLines: {
                    color: "rgb(234, 236, 244)",
                    zeroLineColor: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                }
            }],
        },
        legend: {
            display: false
        },
        tooltips: {
            titleMarginBottom: 10,
            titleFontColor: '#6e707e',
            titleFontSize: 14,
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
    }
});

// Inspector Workload Chart
const inspectorWorkloadCtx = document.getElementById("inspectorWorkloadChart");
const inspectorWorkloadChart = new Chart(inspectorWorkloadCtx, {
    type: 'horizontalBar',
    data: {
        labels: analyticsData.inspector_workload.map(item => item.name),
        datasets: [{
            label: "Inspections",
            backgroundColor: "#1cc88a",
            hoverBackgroundColor: "#17a673",
            borderColor: "#1cc88a",
            data: analyticsData.inspector_workload.map(item => item.inspection_count),
        }],
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            xAxes: [{
                gridLines: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 6
                },
                maxBarThickness: 25,
            }],
            yAxes: [{
                ticks: {
                    min: 0,
                    maxTicksLimit: 5,
                    padding: 10,
                },
                gridLines: {
                    color: "rgb(234, 236, 244)",
                    zeroLineColor: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                }
            }],
        },
        legend: {
            display: false
        },
        tooltips: {
            titleMarginBottom: 10,
            titleFontColor: '#6e707e',
            titleFontSize: 14,
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
    }
});

// Functions
function refreshAnalytics() {
    // Show loading state
    const refreshBtn = document.querySelector('button[onclick="refreshAnalytics()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;

    // Reload page with current filters
    const form = document.getElementById('analyticsFilter');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    window.location.href = window.location.pathname + '?' + params.toString();
}

function exportAnalytics() {
    // Implement export functionality
    window.open('/admin/analytics/export?' + new URLSearchParams(new FormData(document.getElementById('analyticsFilter'))).toString());
}

function downloadChart(chartId) {
    const canvas = document.getElementById(chartId);
    const url = canvas.toDataURL('image/png');
    const a = document.createElement('a');
    a.href = url;
    a.download = chartId + '.png';
    a.click();
}

// Auto-refresh every 5 minutes
setInterval(function() {
    loadActivityFeed();
}, 300000);

// Load activity feed
function loadActivityFeed() {
    fetch('/admin/analytics/activity-feed')
        .then(response => response.json())
        .then(data => {
            const feed = document.getElementById('activity-feed');
            feed.innerHTML = data.activities.map(activity => `
                <div class="d-flex align-items-center mb-2">
                    <div class="mr-3">
                        <i class="fas fa-${activity.icon} text-${activity.color}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="small">${activity.message}</div>
                        <div class="text-xs text-gray-500">${activity.time_ago}</div>
                    </div>
                </div>
            `).join('');
        })
        .catch(error => console.error('Error loading activity feed:', error));
}

// Initial load
document.addEventListener('DOMContentLoaded', function() {
    loadActivityFeed();
});

// Form submission
document.getElementById('analyticsFilter').addEventListener('submit', function(e) {
    e.preventDefault();
    refreshAnalytics();
});
</script>
@endpush
