<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\HomepageContent;

class HomeController extends Controller {
    protected $homepageModel;

    public function __construct() {
        parent::__construct();
        $this->homepageModel = new HomepageContent();
    }

    /**
     * Display the homepage
     */
    public function index() {
        // Get homepage content
        $content = $this->homepageModel->getContent();
        
        // Get latest news/announcements (if any)
        $announcements = $this->homepageModel->getAnnouncements(3);
        
        // Get statistics for homepage
        $stats = $this->homepageModel->getSystemStats();
        
        return $this->render('public/home', [
            'title' => 'Bacoor Occupational Health & Safety System',
            'content' => $content,
            'announcements' => $announcements,
            'stats' => $stats,
            'show_navbar' => true
        ]);
    }

    /**
     * About page
     */
    public function about() {
        $content = $this->homepageModel->getPageContent('about');
        
        return $this->render('public/about', [
            'title' => 'About Us - Bacoor OHS',
            'content' => $content,
            'show_navbar' => true
        ]);
    }

    /**
     * Contact page
     */
    public function contact() {
        $content = $this->homepageModel->getPageContent('contact');
        
        return $this->render('public/contact', [
            'title' => 'Contact Us - Bacoor OHS',
            'content' => $content,
            'show_navbar' => true
        ]);
    }

    /**
     * Services page
     */
    public function services() {
        $content = $this->homepageModel->getPageContent('services');
        
        return $this->render('public/services', [
            'title' => 'Our Services - Bacoor OHS',
            'content' => $content,
            'show_navbar' => true
        ]);
    }

    /**
     * News/Announcements page
     */
    public function news() {
        $announcements = $this->homepageModel->getAnnouncements();
        
        return $this->render('public/news', [
            'title' => 'News & Announcements - Bacoor OHS',
            'announcements' => $announcements,
            'show_navbar' => true
        ]);
    }

    /**
     * Handle contact form submission
     */
    public function submitContact() {
        if (!$this->isPost()) {
            return $this->redirect('/contact');
        }

        $data = [
            'name' => $_POST['name'] ?? '',
            'email' => $_POST['email'] ?? '',
            'subject' => $_POST['subject'] ?? '',
            'message' => $_POST['message'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Validate required fields
        if (empty($data['name']) || empty($data['email']) || empty($data['message'])) {
            $_SESSION['error'] = 'Please fill in all required fields.';
            return $this->redirect('/contact');
        }

        // Validate email
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $_SESSION['error'] = 'Please enter a valid email address.';
            return $this->redirect('/contact');
        }

        try {
            if ($this->homepageModel->saveContactMessage($data)) {
                $_SESSION['success'] = 'Thank you for your message. We will get back to you soon.';
            } else {
                $_SESSION['error'] = 'Failed to send message. Please try again.';
            }
        } catch (\Exception $e) {
            error_log("Contact form error: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred. Please try again later.';
        }

        return $this->redirect('/contact');
    }
}
?>
