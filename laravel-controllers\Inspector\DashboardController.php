<?php

namespace App\Http\Controllers\Inspector;

use App\Http\Controllers\Controller;
use App\Models\Inspection;
use App\Models\Business;
use App\Models\InspectionChecklistResponse;
use App\Models\ChatRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show inspector dashboard
     */
    public function index()
    {
        $inspector = Auth::user();
        
        // Get inspector statistics
        $stats = $this->getInspectorStats($inspector);
        
        // Get upcoming inspections
        $upcomingInspections = Inspection::with(['business.barangay.district'])
            ->where('inspector_id', $inspector->id)
            ->where('status', 'scheduled')
            ->where('scheduled_date', '>=', now())
            ->orderBy('scheduled_date')
            ->limit(5)
            ->get();
            
        // Get recent completed inspections
        $recentInspections = Inspection::with(['business.barangay.district'])
            ->where('inspector_id', $inspector->id)
            ->where('status', 'completed')
            ->latest('completed_date')
            ->limit(5)
            ->get();
            
        // Get assigned businesses
        $assignedBusinesses = Business::whereHas('barangay', function($query) use ($inspector) {
                $query->whereHas('assignedInspectors', function($q) use ($inspector) {
                    $q->where('inspector_id', $inspector->id)
                      ->where('status', 'active');
                });
            })
            ->with(['barangay.district', 'category'])
            ->limit(10)
            ->get();
            
        // Get active chat rooms
        $activeChatRooms = ChatRoom::where('inspector_id', $inspector->id)
            ->where('status', 'active')
            ->with(['business', 'businessOwner'])
            ->latest()
            ->limit(5)
            ->get();

        return view('inspector.dashboard', compact(
            'stats',
            'upcomingInspections',
            'recentInspections',
            'assignedBusinesses',
            'activeChatRooms'
        ));
    }

    /**
     * Get inspector statistics
     */
    private function getInspectorStats($inspector): array
    {
        $totalInspections = Inspection::where('inspector_id', $inspector->id)->count();
        $completedInspections = Inspection::where('inspector_id', $inspector->id)
            ->where('status', 'completed')->count();
        $scheduledInspections = Inspection::where('inspector_id', $inspector->id)
            ->where('status', 'scheduled')->count();
        $pendingVerification = Inspection::where('inspector_id', $inspector->id)
            ->where('status', 'completed')
            ->where('verification_status', 'pending')->count();

        // Get assigned businesses count
        $assignedBusinesses = Business::whereHas('barangay', function($query) use ($inspector) {
            $query->whereHas('assignedInspectors', function($q) use ($inspector) {
                $q->where('inspector_id', $inspector->id)
                  ->where('status', 'active');
            });
        })->count();

        // Get assigned districts/barangays
        $assignedDistricts = $inspector->districtAssignments()
            ->where('is_active', true)->count();
        $assignedBarangays = $inspector->barangayAssignments()
            ->where('status', 'active')->count();

        // Calculate completion rate
        $completionRate = $totalInspections > 0 
            ? round(($completedInspections / $totalInspections) * 100, 2) 
            : 0;

        // Get monthly inspection data
        $monthlyInspections = $this->getMonthlyInspectionData($inspector);

        return [
            'total_inspections' => $totalInspections,
            'completed_inspections' => $completedInspections,
            'scheduled_inspections' => $scheduledInspections,
            'pending_verification' => $pendingVerification,
            'assigned_businesses' => $assignedBusinesses,
            'assigned_districts' => $assignedDistricts,
            'assigned_barangays' => $assignedBarangays,
            'completion_rate' => $completionRate,
            'monthly_inspections' => $monthlyInspections,
            'today_inspections' => $this->getTodayInspections($inspector),
            'this_week_inspections' => $this->getThisWeekInspections($inspector),
            'average_score' => $this->getAverageInspectionScore($inspector),
        ];
    }

    /**
     * Get monthly inspection data for charts
     */
    private function getMonthlyInspectionData($inspector): array
    {
        $monthlyData = Inspection::where('inspector_id', $inspector->id)
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $months = [];
        $totals = [];
        $completed = [];

        for ($i = 1; $i <= 12; $i++) {
            $monthData = $monthlyData->firstWhere('month', $i);
            $months[] = date('M', mktime(0, 0, 0, $i, 1));
            $totals[] = $monthData ? $monthData->total : 0;
            $completed[] = $monthData ? $monthData->completed : 0;
        }

        return [
            'months' => $months,
            'totals' => $totals,
            'completed' => $completed,
        ];
    }

    /**
     * Get today's inspections count
     */
    private function getTodayInspections($inspector): int
    {
        return Inspection::where('inspector_id', $inspector->id)
            ->whereDate('scheduled_date', today())
            ->count();
    }

    /**
     * Get this week's inspections count
     */
    private function getThisWeekInspections($inspector): int
    {
        return Inspection::where('inspector_id', $inspector->id)
            ->whereBetween('scheduled_date', [now()->startOfWeek(), now()->endOfWeek()])
            ->count();
    }

    /**
     * Get average inspection score
     */
    private function getAverageInspectionScore($inspector): float
    {
        $averageScore = Inspection::where('inspector_id', $inspector->id)
            ->where('status', 'completed')
            ->whereNotNull('score')
            ->avg('score');

        return $averageScore ? round($averageScore, 2) : 0;
    }

    /**
     * Get dashboard data for AJAX requests
     */
    public function getData(Request $request)
    {
        $inspector = Auth::user();
        $type = $request->get('type');

        return match($type) {
            'stats' => response()->json($this->getInspectorStats($inspector)),
            'upcoming_inspections' => response()->json(
                Inspection::with(['business.barangay.district'])
                    ->where('inspector_id', $inspector->id)
                    ->where('status', 'scheduled')
                    ->where('scheduled_date', '>=', now())
                    ->orderBy('scheduled_date')
                    ->limit(10)
                    ->get()
            ),
            'recent_inspections' => response()->json(
                Inspection::with(['business.barangay.district'])
                    ->where('inspector_id', $inspector->id)
                    ->where('status', 'completed')
                    ->latest('completed_date')
                    ->limit(10)
                    ->get()
            ),
            default => response()->json(['error' => 'Invalid data type'], 400)
        };
    }
}
