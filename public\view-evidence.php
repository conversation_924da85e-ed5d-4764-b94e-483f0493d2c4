<?php
// Minimal file serving script for compliance evidence
session_start();

// Get the filename from the URL parameter
$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    echo 'No file specified';
    exit;
}

// Basic authentication check (check if user is logged in)
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo 'Authentication required. Please log in.';
    exit;
}

// Sanitize filename to prevent directory traversal
$filename = basename($filename);
$filePath = __DIR__ . '/uploads/compliance_evidence/' . $filename;

// Check if file exists
if (!file_exists($filePath)) {
    http_response_code(404);
    echo 'File not found: ' . htmlspecialchars($filename);
    exit;
}

// Get file info
$fileInfo = pathinfo($filePath);
$extension = strtolower($fileInfo['extension']);

// Set appropriate content type
$contentTypes = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

$contentType = $contentTypes[$extension] ?? 'application/octet-stream';

// Set headers
header('Content-Type: ' . $contentType);
header('Content-Length: ' . filesize($filePath));
header('Content-Disposition: inline; filename="' . $filename . '"');
header('Cache-Control: private, max-age=3600');

// Output file
readfile($filePath);
exit;
?>
