<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HomepageContent;
use App\Models\Announcement;
use App\Models\ContactMessage;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class HomepageController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Display homepage content management dashboard
     */
    public function index()
    {
        $content = HomepageContent::ordered()->get();
        $announcements = Announcement::latest()->limit(10)->get();
        $recentMessages = ContactMessage::latest()->limit(5)->get();
        
        $stats = [
            'total_content_sections' => HomepageContent::count(),
            'active_content_sections' => HomepageContent::where('is_active', true)->count(),
            'total_announcements' => Announcement::count(),
            'active_announcements' => Announcement::where('status', 'active')->count(),
            'unread_messages' => ContactMessage::where('status', 'unread')->count(),
            'total_messages' => ContactMessage::count(),
        ];

        return view('admin.homepage.index', compact('content', 'announcements', 'recentMessages', 'stats'));
    }

    /**
     * Show form to create new content section
     */
    public function createContent()
    {
        return view('admin.homepage.create-content');
    }

    /**
     * Store new content section
     */
    public function storeContent(Request $request)
    {
        $validated = $request->validate([
            'page' => 'required|in:home,about,services,contact',
            'section_type' => 'required|in:hero,content,feature,testimonial,cta',
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url|max:255',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $uploadResult = $this->fileUploadService->uploadFile(
                $request->file('image'),
                'homepage-content',
                ['section_type' => $validated['section_type']]
            );
            $validated['image_url'] = $uploadResult['path'];
        }

        $validated['created_by'] = Auth::id();

        HomepageContent::create($validated);

        return redirect()->route('admin.homepage.index')
            ->with('success', 'Content section created successfully.');
    }

    /**
     * Show form to edit content section
     */
    public function editContent(HomepageContent $content)
    {
        return view('admin.homepage.edit-content', compact('content'));
    }

    /**
     * Update content section
     */
    public function updateContent(Request $request, HomepageContent $content)
    {
        $validated = $request->validate([
            'page' => 'required|in:home,about,services,contact',
            'section_type' => 'required|in:hero,content,feature,testimonial,cta',
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url|max:255',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($content->image_url && Storage::disk('public')->exists($content->image_url)) {
                Storage::disk('public')->delete($content->image_url);
            }

            $uploadResult = $this->fileUploadService->uploadFile(
                $request->file('image'),
                'homepage-content',
                ['section_type' => $validated['section_type']]
            );
            $validated['image_url'] = $uploadResult['path'];
        }

        $content->update($validated);

        return redirect()->route('admin.homepage.index')
            ->with('success', 'Content section updated successfully.');
    }

    /**
     * Delete content section
     */
    public function destroyContent(HomepageContent $content)
    {
        // Delete associated image
        if ($content->image_url && Storage::disk('public')->exists($content->image_url)) {
            Storage::disk('public')->delete($content->image_url);
        }

        $content->delete();

        return redirect()->route('admin.homepage.index')
            ->with('success', 'Content section deleted successfully.');
    }

    /**
     * Display announcements management
     */
    public function announcements()
    {
        $announcements = Announcement::with('author')->latest()->paginate(15);

        return view('admin.homepage.announcements', compact('announcements'));
    }

    /**
     * Show form to create new announcement
     */
    public function createAnnouncement()
    {
        return view('admin.homepage.create-announcement');
    }

    /**
     * Store new announcement
     */
    public function storeAnnouncement(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
            'publish_date' => 'nullable|date|after_or_equal:today',
            'expire_date' => 'nullable|date|after:publish_date',
            'status' => 'required|in:active,inactive,scheduled',
            'is_featured' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $uploadResult = $this->fileUploadService->uploadFile(
                $request->file('image'),
                'announcements'
            );
            $validated['image_url'] = $uploadResult['path'];
        }

        $validated['author_id'] = Auth::id();

        Announcement::create($validated);

        return redirect()->route('admin.homepage.announcements')
            ->with('success', 'Announcement created successfully.');
    }

    /**
     * Show form to edit announcement
     */
    public function editAnnouncement(Announcement $announcement)
    {
        return view('admin.homepage.edit-announcement', compact('announcement'));
    }

    /**
     * Update announcement
     */
    public function updateAnnouncement(Request $request, Announcement $announcement)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
            'publish_date' => 'nullable|date',
            'expire_date' => 'nullable|date|after:publish_date',
            'status' => 'required|in:active,inactive,scheduled',
            'is_featured' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($announcement->image_url && Storage::disk('public')->exists($announcement->image_url)) {
                Storage::disk('public')->delete($announcement->image_url);
            }

            $uploadResult = $this->fileUploadService->uploadFile(
                $request->file('image'),
                'announcements'
            );
            $validated['image_url'] = $uploadResult['path'];
        }

        $announcement->update($validated);

        return redirect()->route('admin.homepage.announcements')
            ->with('success', 'Announcement updated successfully.');
    }

    /**
     * Delete announcement
     */
    public function destroyAnnouncement(Announcement $announcement)
    {
        // Delete associated image
        if ($announcement->image_url && Storage::disk('public')->exists($announcement->image_url)) {
            Storage::disk('public')->delete($announcement->image_url);
        }

        $announcement->delete();

        return redirect()->route('admin.homepage.announcements')
            ->with('success', 'Announcement deleted successfully.');
    }

    /**
     * Display contact messages
     */
    public function contactMessages()
    {
        $messages = ContactMessage::latest()->paginate(20);

        return view('admin.homepage.contact-messages', compact('messages'));
    }

    /**
     * Mark contact message as read
     */
    public function markMessageRead(ContactMessage $message)
    {
        $message->update(['status' => 'read']);

        return response()->json(['success' => true, 'message' => 'Message marked as read.']);
    }

    /**
     * Mark contact message as replied
     */
    public function markMessageReplied(Request $request, ContactMessage $message)
    {
        $validated = $request->validate([
            'reply_notes' => 'nullable|string|max:1000',
        ]);

        $message->update([
            'status' => 'replied',
            'replied_by' => Auth::id(),
            'replied_at' => now(),
            'reply_notes' => $validated['reply_notes'],
        ]);

        return response()->json(['success' => true, 'message' => 'Message marked as replied.']);
    }

    /**
     * Delete contact message
     */
    public function destroyContactMessage(ContactMessage $message)
    {
        $message->delete();

        return redirect()->route('admin.homepage.contact-messages')
            ->with('success', 'Contact message deleted successfully.');
    }

    /**
     * Bulk actions for content sections
     */
    public function bulkContentActions(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'content_ids' => 'required|array',
            'content_ids.*' => 'exists:homepage_contents,id',
        ]);

        $contents = HomepageContent::whereIn('id', $validated['content_ids']);

        switch ($validated['action']) {
            case 'activate':
                $contents->update(['is_active' => true]);
                $message = 'Selected content sections activated successfully.';
                break;

            case 'deactivate':
                $contents->update(['is_active' => false]);
                $message = 'Selected content sections deactivated successfully.';
                break;

            case 'delete':
                // Delete associated images
                foreach ($contents->get() as $content) {
                    if ($content->image_url && Storage::disk('public')->exists($content->image_url)) {
                        Storage::disk('public')->delete($content->image_url);
                    }
                }
                $contents->delete();
                $message = 'Selected content sections deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Reorder content sections
     */
    public function reorderContent(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:homepage_contents,id',
            'items.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['items'] as $item) {
            HomepageContent::where('id', $item['id'])
                ->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json(['success' => true, 'message' => 'Content sections reordered successfully.']);
    }
}
