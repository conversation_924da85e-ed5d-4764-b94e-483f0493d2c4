<?php $this->extend('layouts/app'); ?>

<?php $this->section('styles'); ?>
<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css" rel="stylesheet">
<?php $this->endSection(); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Manage Users</h1>
        <a href="<?= BASE_URL ?>admin/users/create" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>Create New User
        </a>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="usersTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary bg-opacity-10 text-primary me-3">
                                        <?= strtoupper(substr($user['full_name'], 0, 2)) ?>
                                    </div>
                                    <div>
                                        <div class="fw-medium"><?= htmlspecialchars($user['full_name']) ?></div>
                                        <small class="text-muted">ID: <?= $user['id'] ?></small>
                                    </div>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td>
                                <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'staff' ? 'info' : 'secondary') ?>">
                                    <?= ucfirst($user['role']) ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                                    <?= ucfirst($user['status']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if (!empty($user['last_login'])): ?>
                                    <div><?= date('M d, Y', strtotime($user['last_login'])) ?></div>
                                    <small class="text-muted"><?= date('h:i A', strtotime($user['last_login'])) ?></small>
                                <?php else: ?>
                                    <span class="text-muted">Never</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <?php if ($user['role'] === 'inspector'): ?>
                                        <a href="<?= BASE_URL ?>admin/inspector-profile/<?= $user['id'] ?>"
                                           class="btn btn-sm btn-outline-info" title="View Inspector Profile">
                                            <i class="fas fa-user-shield"></i>
                                        </a>
                                    <?php elseif ($user['role'] === 'business_owner'): ?>
                                        <a href="<?= BASE_URL ?>admin/business-owner-profile/<?= $user['id'] ?>"
                                           class="btn btn-sm btn-outline-info" title="View Business Owner Profile">
                                            <i class="fas fa-user-tie"></i>
                                        </a>
                                    <?php else: ?>
                                        <a href="<?= BASE_URL ?>admin/users/<?= $user['id'] ?>/view"
                                           class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?= BASE_URL ?>admin/users/<?= $user['id'] ?>/edit"
                                       class="btn btn-sm btn-outline-primary" title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </a>

                                    <button type="button"
                                            class="btn btn-sm btn-outline-success"
                                            title="Send New Password"
                                            onclick="confirmSendPassword('<?= $user['id'] ?>', '<?= htmlspecialchars($user['full_name']) ?>', '<?= htmlspecialchars($user['email']) ?>')">
                                        <i class="fas fa-key"></i>
                                    </button>

                                    <button type="button"
                                            class="btn btn-sm btn-outline-info"
                                            title="Resend Welcome Email"
                                            onclick="confirmResendWelcome('<?= $user['id'] ?>', '<?= htmlspecialchars($user['full_name']) ?>', '<?= htmlspecialchars($user['email']) ?>')">
                                        <i class="fas fa-envelope"></i>
                                    </button>

                                    <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            title="Delete User"
                                            onclick="confirmDelete('<?= $user['id'] ?>', '<?= htmlspecialchars($user['full_name']) ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete user <strong id="deleteUserName"></strong>?
                This action cannot be undone.
            </div>
            <div class="modal-footer">
                <form id="deleteForm" action="" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Send Password Modal -->
<div class="modal fade" id="sendPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send New Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Generate a new random password for <strong id="passwordUserName"></strong> and send it to:</p>
                <p class="text-primary"><i class="fas fa-envelope me-2"></i><strong id="passwordUserEmail"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This will replace the user's current password and send the new one via email.
                </div>
            </div>
            <div class="modal-footer">
                <form id="sendPasswordForm" action="" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-key me-2"></i>Generate & Send Password
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Resend Welcome Modal -->
<div class="modal fade" id="resendWelcomeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resend Welcome Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Send a welcome email with new login credentials to <strong id="welcomeUserName"></strong>:</p>
                <p class="text-primary"><i class="fas fa-envelope me-2"></i><strong id="welcomeUserEmail"></strong></p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This will generate a new password and send a complete welcome email with login instructions.
                </div>
            </div>
            <div class="modal-footer">
                <form id="resendWelcomeForm" action="" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-envelope me-2"></i>Send Welcome Email
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

.btn-group .btn i {
    width: 1rem;
    text-align: center;
}
</style>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = `<?= BASE_URL ?>admin/users/${userId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function confirmSendPassword(userId, userName, userEmail) {
    document.getElementById('passwordUserName').textContent = userName;
    document.getElementById('passwordUserEmail').textContent = userEmail;
    document.getElementById('sendPasswordForm').action = `<?= BASE_URL ?>admin/users/${userId}/send-password`;
    new bootstrap.Modal(document.getElementById('sendPasswordModal')).show();
}

function confirmResendWelcome(userId, userName, userEmail) {
    document.getElementById('welcomeUserName').textContent = userName;
    document.getElementById('welcomeUserEmail').textContent = userEmail;
    document.getElementById('resendWelcomeForm').action = `<?= BASE_URL ?>admin/users/${userId}/resend-welcome`;
    new bootstrap.Modal(document.getElementById('resendWelcomeModal')).show();
}

$(document).ready(function() {
    $('#usersTable').DataTable({
        order: [[0, 'asc']],
        pageLength: 25,
        responsive: true,
        language: {
            search: '<i class="fas fa-search"></i>',
            searchPlaceholder: 'Search users...'
        }
    });
});
</script>

<?php $this->endSection(); ?> 