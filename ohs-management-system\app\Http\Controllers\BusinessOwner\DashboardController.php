<?php

namespace App\Http\Controllers\BusinessOwner;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\Inspection;
use App\Models\BusinessChecklistEvidence;
use App\Models\InspectionChecklistItem;
use App\Models\ChatRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show business owner dashboard
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get user's business
        $business = Business::where('owner_id', $user->id)
            ->with(['category', 'barangay.district'])
            ->first();

        if (!$business) {
            return view('business-owner.no-business');
        }

        // Get business statistics
        $stats = $this->getBusinessStats($business);
        
        // Get recent inspections
        $recentInspections = Inspection::where('business_id', $business->id)
            ->with(['inspector', 'verifiedBy'])
            ->latest('scheduled_date')
            ->limit(5)
            ->get();
            
        // Get upcoming inspections
        $upcomingInspections = Inspection::where('business_id', $business->id)
            ->where('status', 'scheduled')
            ->where('scheduled_date', '>=', now())
            ->with(['inspector'])
            ->orderBy('scheduled_date')
            ->limit(3)
            ->get();
            
        // Get pending evidence items
        $pendingEvidenceItems = InspectionChecklistItem::active()
            ->whereDoesntHave('businessEvidence', function($query) use ($business) {
                $query->where('business_id', $business->id)
                      ->where('status', '!=', 'rejected');
            })
            ->with('category')
            ->limit(10)
            ->get();
            
        // Get active chat rooms
        $activeChatRooms = ChatRoom::where('business_id', $business->id)
            ->where('status', 'active')
            ->with(['admin', 'inspector'])
            ->latest()
            ->limit(5)
            ->get();

        // Get compliance progress
        $complianceProgress = $this->getComplianceProgress($business);

        return view('business-owner.dashboard', compact(
            'business',
            'stats',
            'recentInspections',
            'upcomingInspections',
            'pendingEvidenceItems',
            'activeChatRooms',
            'complianceProgress'
        ));
    }

    /**
     * Get business statistics
     */
    private function getBusinessStats(Business $business): array
    {
        $totalInspections = Inspection::where('business_id', $business->id)->count();
        $completedInspections = Inspection::where('business_id', $business->id)
            ->where('status', 'completed')->count();
        $approvedInspections = Inspection::where('business_id', $business->id)
            ->where('verification_status', 'approved')->count();
        $pendingInspections = Inspection::where('business_id', $business->id)
            ->where('status', 'scheduled')->count();

        // Get evidence statistics
        $totalEvidenceItems = InspectionChecklistItem::active()->count();
        $uploadedEvidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->where('status', '!=', 'rejected')
            ->distinct('checklist_item_id')
            ->count();
        $approvedEvidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->where('status', 'approved')
            ->distinct('checklist_item_id')
            ->count();
        $pendingEvidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->where('status', 'pending')
            ->count();

        // Calculate compliance percentage
        $compliancePercentage = $business->getCompliancePercentage();

        // Get latest inspection score
        $latestInspection = Inspection::where('business_id', $business->id)
            ->where('status', 'completed')
            ->latest('completed_date')
            ->first();

        $latestScore = $latestInspection ? $latestInspection->getScorePercentage() : null;

        // Get monthly inspection data
        $monthlyInspections = $this->getMonthlyInspectionData($business);

        return [
            'total_inspections' => $totalInspections,
            'completed_inspections' => $completedInspections,
            'approved_inspections' => $approvedInspections,
            'pending_inspections' => $pendingInspections,
            'total_evidence_items' => $totalEvidenceItems,
            'uploaded_evidence' => $uploadedEvidence,
            'approved_evidence' => $approvedEvidence,
            'pending_evidence' => $pendingEvidence,
            'compliance_percentage' => $compliancePercentage,
            'latest_score' => $latestScore,
            'monthly_inspections' => $monthlyInspections,
            'evidence_completion_rate' => $totalEvidenceItems > 0 
                ? round(($uploadedEvidence / $totalEvidenceItems) * 100, 2) 
                : 0,
        ];
    }

    /**
     * Get monthly inspection data for charts
     */
    private function getMonthlyInspectionData(Business $business): array
    {
        $monthlyData = Inspection::where('business_id', $business->id)
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed'),
                DB::raw('AVG(CASE WHEN score IS NOT NULL THEN score ELSE 0 END) as avg_score')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $months = [];
        $totals = [];
        $completed = [];
        $scores = [];

        for ($i = 1; $i <= 12; $i++) {
            $monthData = $monthlyData->firstWhere('month', $i);
            $months[] = date('M', mktime(0, 0, 0, $i, 1));
            $totals[] = $monthData ? $monthData->total : 0;
            $completed[] = $monthData ? $monthData->completed : 0;
            $scores[] = $monthData ? round($monthData->avg_score, 2) : 0;
        }

        return [
            'months' => $months,
            'totals' => $totals,
            'completed' => $completed,
            'scores' => $scores,
        ];
    }

    /**
     * Get compliance progress by category
     */
    private function getComplianceProgress(Business $business): array
    {
        $categories = DB::table('inspection_checklist_categories')
            ->select(
                'inspection_checklist_categories.name',
                DB::raw('COUNT(inspection_checklist_items.id) as total_items'),
                DB::raw('COUNT(business_checklist_evidence.id) as uploaded_items'),
                DB::raw('COUNT(CASE WHEN business_checklist_evidence.status = "approved" THEN 1 END) as approved_items')
            )
            ->leftJoin('inspection_checklist_items', 'inspection_checklist_categories.id', '=', 'inspection_checklist_items.category_id')
            ->leftJoin('business_checklist_evidence', function($join) use ($business) {
                $join->on('inspection_checklist_items.id', '=', 'business_checklist_evidence.checklist_item_id')
                     ->where('business_checklist_evidence.business_id', '=', $business->id)
                     ->where('business_checklist_evidence.status', '!=', 'rejected');
            })
            ->where('inspection_checklist_categories.is_active', true)
            ->where('inspection_checklist_items.is_active', true)
            ->groupBy('inspection_checklist_categories.id', 'inspection_checklist_categories.name')
            ->get();

        return $categories->map(function($category) {
            $uploadedPercentage = $category->total_items > 0 
                ? round(($category->uploaded_items / $category->total_items) * 100, 2) 
                : 0;
            $approvedPercentage = $category->total_items > 0 
                ? round(($category->approved_items / $category->total_items) * 100, 2) 
                : 0;

            return [
                'name' => $category->name,
                'total_items' => $category->total_items,
                'uploaded_items' => $category->uploaded_items,
                'approved_items' => $category->approved_items,
                'uploaded_percentage' => $uploadedPercentage,
                'approved_percentage' => $approvedPercentage,
            ];
        })->toArray();
    }

    /**
     * Get dashboard data for AJAX requests
     */
    public function getData(Request $request)
    {
        $user = Auth::user();
        $business = Business::where('owner_id', $user->id)->first();
        
        if (!$business) {
            return response()->json(['error' => 'No business found'], 404);
        }

        $type = $request->get('type');

        return match($type) {
            'stats' => response()->json($this->getBusinessStats($business)),
            'recent_inspections' => response()->json(
                Inspection::where('business_id', $business->id)
                    ->with(['inspector', 'verifiedBy'])
                    ->latest('scheduled_date')
                    ->limit(10)
                    ->get()
            ),
            'compliance_progress' => response()->json($this->getComplianceProgress($business)),
            'upcoming_inspections' => response()->json(
                Inspection::where('business_id', $business->id)
                    ->where('status', 'scheduled')
                    ->where('scheduled_date', '>=', now())
                    ->with(['inspector'])
                    ->orderBy('scheduled_date')
                    ->limit(10)
                    ->get()
            ),
            default => response()->json(['error' => 'Invalid data type'], 400)
        };
    }
}
