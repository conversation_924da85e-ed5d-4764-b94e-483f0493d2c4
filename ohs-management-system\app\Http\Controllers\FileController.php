<?php

namespace App\Http\Controllers;

use App\Models\BusinessChecklistEvidence;
use App\Models\InspectionChecklistResponse;
use App\Models\HomepageContent;
use App\Models\Announcement;
use App\Models\WebsiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FileController extends Controller
{
    /**
     * Serve evidence files securely
     */
    public function serveEvidence($filename)
    {
        $user = Auth::user();
        
        // Find the evidence file
        $evidence = BusinessChecklistEvidence::where('file_path', 'like', "%{$filename}")
            ->first();

        if (!$evidence) {
            abort(404, 'File not found.');
        }

        // Check permissions
        if (!$this->canAccessEvidence($user, $evidence)) {
            abort(403, 'Unauthorized access to file.');
        }

        return $this->serveFile($evidence->file_path, $evidence->file_name);
    }

    /**
     * Serve inspection evidence photos securely
     */
    public function serveInspectionEvidence($filename)
    {
        $user = Auth::user();
        
        // Find the inspection response with evidence
        $response = InspectionChecklistResponse::where('evidence_photo', 'like', "%{$filename}")
            ->with(['inspection.business', 'inspection.inspector'])
            ->first();

        if (!$response) {
            abort(404, 'File not found.');
        }

        // Check permissions
        if (!$this->canAccessInspectionEvidence($user, $response)) {
            abort(403, 'Unauthorized access to file.');
        }

        return $this->serveFile($response->evidence_photo);
    }

    /**
     * Serve homepage content images
     */
    public function serveHomepageImage($filename)
    {
        // Homepage images are public, but we still validate they exist
        $content = HomepageContent::where('image_url', 'like', "%{$filename}")
            ->first();

        if (!$content) {
            abort(404, 'Image not found.');
        }

        return $this->serveFile($content->image_url);
    }

    /**
     * Serve announcement images
     */
    public function serveAnnouncementImage($filename)
    {
        // Announcement images are public, but we validate they exist
        $announcement = Announcement::where('image_url', 'like', "%{$filename}")
            ->first();

        if (!$announcement) {
            abort(404, 'Image not found.');
        }

        return $this->serveFile($announcement->image_url);
    }

    /**
     * Serve website setting images (logo, favicon, etc.)
     */
    public function serveWebsiteImage($type, $filename)
    {
        // Website images are public
        $setting = WebsiteSetting::where('key', $type)
            ->where('value', 'like', "%{$filename}")
            ->first();

        if (!$setting) {
            abort(404, 'Image not found.');
        }

        return $this->serveFile($setting->value);
    }

    /**
     * Download evidence file
     */
    public function downloadEvidence($evidenceId)
    {
        $user = Auth::user();
        $evidence = BusinessChecklistEvidence::findOrFail($evidenceId);

        // Check permissions
        if (!$this->canAccessEvidence($user, $evidence)) {
            abort(403, 'Unauthorized access to file.');
        }

        if (!Storage::disk('public')->exists($evidence->file_path)) {
            abort(404, 'File not found on server.');
        }

        return Storage::disk('public')->download($evidence->file_path, $evidence->file_name);
    }

    /**
     * Download inspection evidence
     */
    public function downloadInspectionEvidence($responseId)
    {
        $user = Auth::user();
        $response = InspectionChecklistResponse::with(['inspection.business', 'inspection.inspector'])
            ->findOrFail($responseId);

        // Check permissions
        if (!$this->canAccessInspectionEvidence($user, $response)) {
            abort(403, 'Unauthorized access to file.');
        }

        if (!$response->evidence_photo || !Storage::disk('public')->exists($response->evidence_photo)) {
            abort(404, 'File not found on server.');
        }

        $filename = 'inspection_evidence_' . $response->id . '.' . pathinfo($response->evidence_photo, PATHINFO_EXTENSION);
        
        return Storage::disk('public')->download($response->evidence_photo, $filename);
    }

    /**
     * Get file info (for AJAX requests)
     */
    public function getFileInfo(Request $request)
    {
        $validated = $request->validate([
            'file_type' => 'required|in:evidence,inspection_evidence,homepage_image,announcement_image',
            'file_id' => 'required|string',
        ]);

        $user = Auth::user();
        $fileInfo = null;

        switch ($validated['file_type']) {
            case 'evidence':
                $evidence = BusinessChecklistEvidence::find($validated['file_id']);
                if ($evidence && $this->canAccessEvidence($user, $evidence)) {
                    $fileInfo = [
                        'name' => $evidence->file_name,
                        'size' => $evidence->file_size,
                        'type' => $evidence->file_type,
                        'uploaded_at' => $evidence->created_at->format('M j, Y g:i A'),
                        'status' => $evidence->status,
                        'url' => route('files.serve-evidence', basename($evidence->file_path)),
                        'download_url' => route('files.download-evidence', $evidence->id),
                    ];
                }
                break;

            case 'inspection_evidence':
                $response = InspectionChecklistResponse::find($validated['file_id']);
                if ($response && $this->canAccessInspectionEvidence($user, $response)) {
                    $fileInfo = [
                        'name' => basename($response->evidence_photo),
                        'uploaded_at' => $response->created_at->format('M j, Y g:i A'),
                        'url' => route('files.serve-inspection-evidence', basename($response->evidence_photo)),
                        'download_url' => route('files.download-inspection-evidence', $response->id),
                    ];
                }
                break;
        }

        if (!$fileInfo) {
            return response()->json(['error' => 'File not found or access denied.'], 404);
        }

        return response()->json($fileInfo);
    }

    /**
     * Validate file upload
     */
    public function validateUpload(Request $request)
    {
        $validated = $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'file_type' => 'required|in:evidence,inspection_evidence,image',
        ]);

        $file = $request->file('file');
        $fileType = $validated['file_type'];

        // Define allowed types based on file type
        $allowedTypes = [
            'evidence' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
            'inspection_evidence' => ['jpg', 'jpeg', 'png'],
            'image' => ['jpg', 'jpeg', 'png', 'webp'],
        ];

        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($extension, $allowedTypes[$fileType])) {
            return response()->json([
                'valid' => false,
                'message' => 'Invalid file type. Allowed types: ' . implode(', ', $allowedTypes[$fileType])
            ]);
        }

        // Additional validation
        $maxSizes = [
            'evidence' => 5120, // 5MB
            'inspection_evidence' => 5120, // 5MB
            'image' => 2048, // 2MB
        ];

        if ($file->getSize() > $maxSizes[$fileType] * 1024) {
            return response()->json([
                'valid' => false,
                'message' => 'File too large. Maximum size: ' . ($maxSizes[$fileType] / 1024) . 'MB'
            ]);
        }

        return response()->json([
            'valid' => true,
            'file_info' => [
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
                'extension' => $extension,
            ]
        ]);
    }

    /**
     * Clean up orphaned files
     */
    public function cleanupOrphanedFiles()
    {
        if (!Auth::user() || Auth::user()->role !== 'admin') {
            abort(403, 'Unauthorized.');
        }

        $deletedCount = 0;
        $directories = [
            'business-evidence',
            'inspection-evidence',
            'homepage-content',
            'announcements',
            'website-images',
        ];

        foreach ($directories as $directory) {
            $files = Storage::disk('public')->files($directory);
            
            foreach ($files as $file) {
                $filename = basename($file);
                $isOrphaned = true;

                // Check if file is referenced in database
                switch ($directory) {
                    case 'business-evidence':
                        $isOrphaned = !BusinessChecklistEvidence::where('file_path', $file)->exists();
                        break;
                    case 'inspection-evidence':
                        $isOrphaned = !InspectionChecklistResponse::where('evidence_photo', $file)->exists();
                        break;
                    case 'homepage-content':
                        $isOrphaned = !HomepageContent::where('image_url', $file)->exists();
                        break;
                    case 'announcements':
                        $isOrphaned = !Announcement::where('image_url', $file)->exists();
                        break;
                    case 'website-images':
                        $isOrphaned = !WebsiteSetting::where('value', $file)->exists();
                        break;
                }

                if ($isOrphaned) {
                    Storage::disk('public')->delete($file);
                    $deletedCount++;
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Cleaned up {$deletedCount} orphaned files."
        ]);
    }

    /**
     * Serve file with proper headers
     */
    private function serveFile($filePath, $originalName = null)
    {
        if (!Storage::disk('public')->exists($filePath)) {
            abort(404, 'File not found on server.');
        }

        $fullPath = Storage::disk('public')->path($filePath);
        $mimeType = Storage::disk('public')->mimeType($filePath);
        $filename = $originalName ?: basename($filePath);

        return Response::file($fullPath, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
            'Cache-Control' => 'private, max-age=3600',
        ]);
    }

    /**
     * Check if user can access evidence file
     */
    private function canAccessEvidence($user, $evidence)
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'inspector':
                // Inspector can access if they're assigned to the business's area
                return $user->districtAssignments()
                    ->where('status', 'active')
                    ->whereHas('district.barangays', function($q) use ($evidence) {
                        $q->where('id', $evidence->business->barangay_id);
                    })
                    ->exists();
            case 'business_owner':
                // Business owner can only access their own evidence
                return $evidence->business->owner_id === $user->id;
            default:
                return false;
        }
    }

    /**
     * Check if user can access inspection evidence
     */
    private function canAccessInspectionEvidence($user, $response)
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'inspector':
                // Inspector can access if it's their inspection
                return $response->inspection->inspector_id === $user->id;
            case 'business_owner':
                // Business owner can access their business's inspection evidence
                return $response->inspection->business->owner_id === $user->id;
            default:
                return false;
        }
    }
}
