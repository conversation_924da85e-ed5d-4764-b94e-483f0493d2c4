<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display notifications for current user
     */
    public function index(Request $request)
    {
        $query = Notification::where('user_id', Auth::id());

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('is_read')) {
            $query->where('is_read', $request->is_read);
        }

        $notifications = $query->latest()->paginate(20);

        // Get notification counts
        $counts = [
            'total' => Notification::where('user_id', Auth::id())->count(),
            'unread' => Notification::where('user_id', Auth::id())->where('is_read', false)->count(),
            'by_type' => Notification::where('user_id', Auth::id())
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
        ];

        return view('notifications.index', compact('notifications', 'counts'));
    }

    /**
     * Get unread notifications count (AJAX)
     */
    public function getUnreadCount()
    {
        $count = Notification::where('user_id', Auth::id())
            ->where('is_read', false)
            ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Get recent notifications (AJAX)
     */
    public function getRecent(Request $request)
    {
        $limit = $request->get('limit', 10);
        
        $notifications = Notification::where('user_id', Auth::id())
            ->latest()
            ->limit($limit)
            ->get();

        $formattedNotifications = $notifications->map(function ($notification) {
            return [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'is_read' => $notification->is_read,
                'created_at' => $notification->created_at->diffForHumans(),
                'action_url' => $notification->action_url,
                'icon' => $this->getNotificationIcon($notification->type),
                'color' => $this->getNotificationColor($notification->type),
            ];
        });

        return response()->json([
            'notifications' => $formattedNotifications,
            'unread_count' => Notification::where('user_id', Auth::id())->where('is_read', false)->count()
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification)
    {
        if ($notification->user_id !== Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized.']);
        }

        $notification->update(['is_read' => true]);

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        Notification::where('user_id', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return response()->json(['success' => true, 'message' => 'All notifications marked as read.']);
    }

    /**
     * Delete notification
     */
    public function destroy(Notification $notification)
    {
        if ($notification->user_id !== Auth::id()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized.']);
        }

        $notification->delete();

        return response()->json(['success' => true, 'message' => 'Notification deleted.']);
    }

    /**
     * Delete all read notifications
     */
    public function deleteAllRead()
    {
        $deleted = Notification::where('user_id', Auth::id())
            ->where('is_read', true)
            ->delete();

        return response()->json([
            'success' => true, 
            'message' => "Deleted {$deleted} read notifications."
        ]);
    }

    /**
     * Create notification (for system use)
     */
    public static function createNotification($userId, $title, $message, $type = 'info', $relatedId = null, $relatedType = null, $actionUrl = null)
    {
        return Notification::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'related_id' => $relatedId,
            'related_type' => $relatedType,
            'action_url' => $actionUrl,
        ]);
    }

    /**
     * Send notification to multiple users
     */
    public static function sendToMultipleUsers($userIds, $title, $message, $type = 'info', $relatedId = null, $relatedType = null, $actionUrl = null)
    {
        $notifications = [];
        
        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'related_id' => $relatedId,
                'related_type' => $relatedType,
                'action_url' => $actionUrl,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        Notification::insert($notifications);
    }

    /**
     * Send notification to all users with specific role
     */
    public static function sendToRole($role, $title, $message, $type = 'info', $relatedId = null, $relatedType = null, $actionUrl = null)
    {
        $userIds = User::where('role', $role)
            ->where('status', 'active')
            ->pluck('id')
            ->toArray();

        if (!empty($userIds)) {
            self::sendToMultipleUsers($userIds, $title, $message, $type, $relatedId, $relatedType, $actionUrl);
        }
    }

    /**
     * Send inspection-related notifications
     */
    public static function sendInspectionNotification($inspection, $type, $additionalRecipients = [])
    {
        $notifications = [];

        switch ($type) {
            case 'scheduled':
                // Notify business owner
                $notifications[] = [
                    'user_id' => $inspection->business->owner_id,
                    'title' => 'Inspection Scheduled',
                    'message' => "An inspection has been scheduled for {$inspection->business->name} on {$inspection->scheduled_date->format('M j, Y')}",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('business-owner.inspections.show', $inspection),
                ];

                // Notify inspector
                $notifications[] = [
                    'user_id' => $inspection->inspector_id,
                    'title' => 'New Inspection Assignment',
                    'message' => "You have been assigned to inspect {$inspection->business->name} on {$inspection->scheduled_date->format('M j, Y')}",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('inspector.inspections.show', $inspection),
                ];
                break;

            case 'completed':
                // Notify admin who assigned
                $notifications[] = [
                    'user_id' => $inspection->assigned_by,
                    'title' => 'Inspection Completed',
                    'message' => "Inspection of {$inspection->business->name} has been completed and is pending verification",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('admin.inspections.show', $inspection),
                ];

                // Notify business owner
                $notifications[] = [
                    'user_id' => $inspection->business->owner_id,
                    'title' => 'Inspection Completed',
                    'message' => "Your business inspection has been completed. Results are pending verification.",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('business-owner.inspections.show', $inspection),
                ];
                break;

            case 'verified':
                $status = $inspection->verification_status === 'approved' ? 'approved' : 'rejected';
                
                // Notify business owner
                $notifications[] = [
                    'user_id' => $inspection->business->owner_id,
                    'title' => "Inspection Results {$status}",
                    'message' => "Your inspection results have been {$status}. Check your dashboard for details.",
                    'type' => $inspection->verification_status === 'approved' ? 'success' : 'warning',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('business-owner.inspections.show', $inspection),
                ];

                // Notify inspector
                $notifications[] = [
                    'user_id' => $inspection->inspector_id,
                    'title' => "Inspection {$status}",
                    'message' => "Your inspection of {$inspection->business->name} has been {$status}",
                    'type' => $inspection->verification_status === 'approved' ? 'success' : 'warning',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('inspector.inspections.show', $inspection),
                ];
                break;
        }

        // Add additional recipients
        foreach ($additionalRecipients as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'title' => $notifications[0]['title'] ?? 'Inspection Update',
                'message' => $notifications[0]['message'] ?? 'An inspection has been updated',
                'type' => 'inspection',
                'related_id' => $inspection->id,
                'related_type' => 'inspection',
                'action_url' => route('admin.inspections.show', $inspection),
            ];
        }

        // Insert all notifications
        foreach ($notifications as &$notification) {
            $notification['created_at'] = now();
            $notification['updated_at'] = now();
        }

        Notification::insert($notifications);
    }

    /**
     * Get notification icon based on type
     */
    private function getNotificationIcon($type)
    {
        return match($type) {
            'inspection' => 'fas fa-clipboard-check',
            'compliance' => 'fas fa-shield-alt',
            'chat' => 'fas fa-comments',
            'success' => 'fas fa-check-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'error' => 'fas fa-times-circle',
            default => 'fas fa-info-circle'
        };
    }

    /**
     * Get notification color based on type
     */
    private function getNotificationColor($type)
    {
        return match($type) {
            'inspection' => 'primary',
            'compliance' => 'warning',
            'chat' => 'info',
            'success' => 'success',
            'warning' => 'warning',
            'error' => 'danger',
            default => 'secondary'
        };
    }
}
