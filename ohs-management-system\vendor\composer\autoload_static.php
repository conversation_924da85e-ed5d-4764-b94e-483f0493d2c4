<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit
{
    public static $prefixLengthsPsr4 = array (
        'I' =>
        array (
            'Illuminate\\' => 11,
        ),
        'A' =>
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Illuminate\\' =>
        array (
            0 => __DIR__ . '/../../vendor/laravel/framework/src/Illuminate',
        ),
        'App\\' =>
        array (
            0 => __DIR__ . '/../../app',
        ),
    );

    public static $classMap = array (
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit::$classMap;

        }, null, ClassLoader::class);
    }
}
