<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatRoom extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'business_owner_id',
        'admin_id',
        'inspector_id',
        'status',
        'subject',
    ];

    /**
     * Get the business this chat room is for
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the business owner
     */
    public function businessOwner()
    {
        return $this->belongsTo(User::class, 'business_owner_id');
    }

    /**
     * Get the admin participant
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Get the inspector participant
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Get all messages in this chat room
     */
    public function messages()
    {
        return $this->hasMany(ChatMessage::class)->orderBy('created_at');
    }

    /**
     * Get latest message
     */
    public function latestMessage()
    {
        return $this->hasOne(ChatMessage::class)->latest();
    }

    /**
     * Get unread messages for a specific user
     */
    public function unreadMessagesFor(User $user)
    {
        return $this->messages()
            ->where('sender_id', '!=', $user->id)
            ->where('is_read', false);
    }

    /**
     * Get unread count for a specific user
     */
    public function getUnreadCountFor(User $user): int
    {
        return $this->unreadMessagesFor($user)->count();
    }

    /**
     * Mark all messages as read for a specific user
     */
    public function markAsReadFor(User $user): void
    {
        $this->messages()
            ->where('sender_id', '!=', $user->id)
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    /**
     * Check if chat room is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Get all participants
     */
    public function getParticipantsAttribute(): array
    {
        $participants = [];
        
        if ($this->business_owner_id) {
            $participants[] = $this->businessOwner;
        }
        if ($this->admin_id) {
            $participants[] = $this->admin;
        }
        if ($this->inspector_id) {
            $participants[] = $this->inspector;
        }
        
        return array_filter($participants);
    }

    /**
     * Check if user is participant
     */
    public function hasParticipant(User $user): bool
    {
        return in_array($user->id, [
            $this->business_owner_id,
            $this->admin_id,
            $this->inspector_id
        ]);
    }
}
