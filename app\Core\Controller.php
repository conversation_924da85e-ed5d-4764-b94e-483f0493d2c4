<?php
namespace App\Core;

use App\Libraries\View;
use App\Libraries\Auth;

/**
 * Enhanced Base Controller Class
 *
 * Provides common functionality for all controllers including:
 * - View rendering
 * - Authentication management
 * - Request handling
 * - Response helpers
 * - Flash messaging
 * - Input validation
 */
class Controller {
    protected $view;
    protected $auth;

    public function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->view = new View();
        $this->auth = Auth::getInstance();
    }

    // ==================== View & Response Methods ====================

    protected function render($name, $data = []) {
        $view = new View($data);
        return $view->render($name);
    }

    protected function redirect($path) {
        header('Location: ' . BASE_URL . $path);
        exit();
    }

    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit();
    }

    protected function jsonResponse($data, $statusCode = 200) {
        return $this->json($data, $statusCode);
    }

    protected function jsonSuccess($message = 'Success', $data = []) {
        return $this->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }

    protected function jsonError($message = 'Error', $statusCode = 400, $errors = []) {
        return $this->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }

    // ==================== Request Methods ====================

    protected function isPost() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }

    protected function isGet() {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }

    protected function isPut() {
        return $_SERVER['REQUEST_METHOD'] === 'PUT';
    }

    protected function isDelete() {
        return $_SERVER['REQUEST_METHOD'] === 'DELETE';
    }

    protected function input($key = null, $default = null) {
        $data = array_merge($_GET, $_POST);
        if ($key === null) {
            return $data;
        }
        return isset($data[$key]) ? $data[$key] : $default;
    }

    protected function get($key = null, $default = null) {
        if ($key === null) {
            return $_GET;
        }
        return isset($_GET[$key]) ? $_GET[$key] : $default;
    }

    protected function post($key = null, $default = null) {
        if ($key === null) {
            return $_POST;
        }
        return isset($_POST[$key]) ? $_POST[$key] : $default;
    }

    protected function files($key = null) {
        if ($key === null) {
            return $_FILES;
        }
        return isset($_FILES[$key]) ? $_FILES[$key] : null;
    }

    // ==================== Flash Message Methods ====================

    protected function setFlash($type, $message) {
        $_SESSION[$type] = $message;
    }

    protected function getFlash($type) {
        $message = $_SESSION[$type] ?? null;
        unset($_SESSION[$type]);
        return $message;
    }

    protected function setSuccess($message) {
        $this->setFlash('success', $message);
    }

    protected function setError($message) {
        $this->setFlash('error', $message);
    }

    protected function setWarning($message) {
        $this->setFlash('warning', $message);
    }

    protected function setInfo($message) {
        $this->setFlash('info', $message);
    }

    // ==================== Authentication Methods ====================

    protected function requireAuth() {
        if (!$this->auth->isLoggedIn()) {
            $this->setError('Please login to continue');
            $this->redirect('login');
        }
    }

    protected function requireAdmin() {
        $this->requireAuth();
        if (!$this->auth->isAdmin()) {
            $this->setError('Access denied');
            $this->redirect('dashboard');
        }
    }

    protected function requireInspector() {
        $this->requireAuth();
        if (!$this->auth->isInspector()) {
            $this->setError('Access denied');
            $this->redirect('dashboard');
        }
    }

    protected function requireBusinessOwner() {
        $this->requireAuth();
        if (!$this->auth->isBusinessOwner()) {
            $this->setError('Access denied');
            $this->redirect('dashboard');
        }
    }

    protected function requireRole($role) {
        $this->requireAuth();
        $user = $this->auth->getUser();
        if ($user['role'] !== $role) {
            $this->setError('Access denied');
            $this->redirect('dashboard');
        }
    }

    protected function requireRoles(array $roles) {
        $this->requireAuth();
        $user = $this->auth->getUser();
        if (!in_array($user['role'], $roles)) {
            $this->setError('Access denied');
            $this->redirect('dashboard');
        }
    }

    // ==================== Validation Methods ====================

    protected function validate(array $data, array $rules) {
        $errors = [];

        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = is_string($rule) ? explode('|', $rule) : $rule;

            foreach ($ruleList as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }

        return $errors;
    }

    private function validateField($field, $value, $rule) {
        if ($rule === 'required' && empty($value)) {
            return ucfirst($field) . ' is required';
        }

        if ($rule === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return ucfirst($field) . ' must be a valid email';
        }

        if (strpos($rule, 'min:') === 0) {
            $min = (int) substr($rule, 4);
            if (strlen($value) < $min) {
                return ucfirst($field) . " must be at least {$min} characters";
            }
        }

        if (strpos($rule, 'max:') === 0) {
            $max = (int) substr($rule, 4);
            if (strlen($value) > $max) {
                return ucfirst($field) . " must not exceed {$max} characters";
            }
        }

        return null;
    }

    // ==================== Utility Methods ====================

    protected function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    protected function getCurrentUrl() {
        return $_SERVER['REQUEST_URI'];
    }

    protected function getBaseUrl() {
        return BASE_URL;
    }

    protected function abort($statusCode = 404, $message = null) {
        http_response_code($statusCode);
        if ($message) {
            echo $message;
        }
        exit();
    }

    protected function hasFlash($type) {
        return isset($_SESSION[$type]);
    }
}