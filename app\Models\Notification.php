<?php
namespace App\Models;

use App\Config\Database;
use PDO;

class Notification {
    private $db;

    public function __construct() {
        $this->db = (new Database())->getConnection();
    }

    public function create($data) {
        $notificationId = $this->generateUUID();

        $sql = "INSERT INTO notifications (id, user_id, title, message, type, related_id, related_type, action_url, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $notificationId,
            $data['user_id'],
            $data['title'],
            $data['message'],
            $data['type'] ?? 'info',
            $data['related_id'] ?? null,
            $data['related_type'] ?? null,
            $data['action_url'] ?? null
        ]);
    }

    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    public function getByUser($userId, $limit = 20, $offset = 0) {
        $sql = "SELECT * FROM notifications
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT " . (int)$limit . " OFFSET " . (int)$offset;

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getUnreadByUser($userId) {
        $sql = "SELECT * FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getUnreadCount($userId) {
        $sql = "SELECT COUNT(*) as count FROM notifications
                WHERE user_id = ? AND is_read = 0";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['count'] : 0;
    }

    public function markAsRead($notificationId) {
        $sql = "UPDATE notifications SET is_read = 1 WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$notificationId]);
    }

    public function markAllAsRead($userId) {
        $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId]);
    }

    public function createInspectionNotification($inspectionId, $inspectorId, $businessName, $scheduledDate) {
        return $this->create([
            'user_id' => $inspectorId,
            'title' => 'New Inspection Assigned',
            'message' => "You have been assigned to inspect {$businessName} on " . date('M d, Y', strtotime($scheduledDate)),
            'type' => 'inspection',
            'related_id' => $inspectionId,
            'related_type' => 'inspection',
            'action_url' => 'inspector/inspection/' . $inspectionId
        ]);
    }

    public function createComplianceNotification($businessOwnerId, $evidenceType, $status) {
        $title = $status === 'verified' ? 'Compliance Evidence Approved' : 'Compliance Evidence Rejected';
        $message = "Your {$evidenceType} evidence has been {$status}.";

        return $this->create([
            'user_id' => $businessOwnerId,
            'title' => $title,
            'message' => $message,
            'type' => 'compliance',
            'related_type' => 'compliance_evidence',
            'action_url' => 'business/compliance'
        ]);
    }

    public function createChatNotification($userId, $senderName, $businessName, $roomId) {
        return $this->create([
            'user_id' => $userId,
            'title' => 'New Chat Message',
            'message' => "{$senderName} sent you a message about {$businessName}",
            'type' => 'chat',
            'related_id' => $roomId,
            'related_type' => 'chat_room',
            'action_url' => 'chat/room/' . $roomId
        ]);
    }
}