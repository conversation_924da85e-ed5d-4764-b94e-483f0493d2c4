<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">User Management</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Users</li>
    </ol>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-users me-1"></i>
                All Users
            </div>
            <a href="/admin/users/create" class="btn btn-primary btn-sm">
                <i class="fas fa-plus me-1"></i> Add New User
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="usersTable">
                    <thead>
                        <tr>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= htmlspecialchars($user['full_name']) ?></td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td>
                                <span class="badge bg-<?= 
                                    $user['role'] === 'ADMIN' ? 'danger' : 
                                    ($user['role'] === 'INSPECTOR' ? 'primary' : 
                                    ($user['role'] === 'SAFETY_OFFICER' ? 'success' : 'info')) 
                                ?>">
                                    <?= str_replace('_', ' ', $user['role']) ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                    <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td><?= date('M d, Y', strtotime($user['created_at'])) ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="/admin/users/<?= $user['id'] ?>/edit" 
                                       class="btn btn-warning btn-sm" title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($user['is_active']): ?>
                                    <a href="/admin/users/<?= $user['id'] ?>/deactivate" 
                                       class="btn btn-danger btn-sm" title="Deactivate User"
                                       onclick="return confirm('Are you sure you want to deactivate this user?')">
                                        <i class="fas fa-user-times"></i>
                                    </a>
                                    <?php else: ?>
                                    <a href="/admin/users/<?= $user['id'] ?>/activate" 
                                       class="btn btn-success btn-sm" title="Activate User"
                                       onclick="return confirm('Are you sure you want to activate this user?')">
                                        <i class="fas fa-user-check"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#usersTable').DataTable({
        order: [[0, 'asc']],
        pageLength: 25,
        responsive: true
    });
});
</script>
<?php $this->endSection(); ?> 