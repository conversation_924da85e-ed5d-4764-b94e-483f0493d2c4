<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">Document Details</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= BASE_URL ?>admin/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= BASE_URL ?>admin/documents">Documents</a></li>
        <li class="breadcrumb-item active">Document Details</li>
    </ol>

    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Document Information
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Business Name:</th>
                            <td><?= htmlspecialchars($document['business_name']) ?></td>
                        </tr>
                        <tr>
                            <th>Owner:</th>
                            <td><?= htmlspecialchars($document['owner_name'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <th>Document Type:</th>
                            <td><?= htmlspecialchars($document['type']) ?></td>
                        </tr>
                        <tr>
                            <th>Submission Date:</th>
                            <td><?= date('Y-m-d', strtotime($document['created_at'])) ?></td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <span class="badge bg-warning">Pending</span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-file me-1"></i>
                    Document Preview
                </div>
                <div class="card-body">
                    <?php
                    $fileExtension = pathinfo($document['file_path'], PATHINFO_EXTENSION);
                    if (in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif'])): ?>
                        <img src="<?= BASE_URL . $document['file_path'] ?>" class="img-fluid" alt="Document Preview">
                    <?php elseif (strtolower($fileExtension) === 'pdf'): ?>
                        <embed src="<?= BASE_URL . $document['file_path'] ?>" type="application/pdf" width="100%" height="600px">
                    <?php else: ?>
                        <p class="text-muted">Preview not available for this file type.</p>
                        <a href="<?= BASE_URL . $document['file_path'] ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i> Download File
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-check-circle me-1"></i>
            Verification Action
        </div>
        <div class="card-body">
            <form action="<?= BASE_URL ?>admin/documents/<?= $document['id'] ?>/verify" method="POST">
                <div class="mb-3">
                    <label for="status" class="form-label">Verification Status</label>
                    <select name="status" id="status" class="form-select" required>
                        <option value="">Select Status</option>
                        <option value="verified">Verify</option>
                        <option value="rejected">Reject</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Enter verification notes..."></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Submit Verification</button>
                <a href="<?= BASE_URL ?>admin/documents" class="btn btn-secondary">Back to List</a>
            </form>
        </div>
    </div>
</div>
<?php $this->endSection() ?>