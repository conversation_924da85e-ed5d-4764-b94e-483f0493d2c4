<?php

namespace App\Http\Controllers;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\Business;
use App\Models\User;
use App\Models\Notification;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ChatController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Display chat rooms for current user
     */
    public function index()
    {
        $user = Auth::user();
        $query = ChatRoom::with(['business', 'businessOwner', 'admin', 'inspector']);

        // Filter based on user role
        switch ($user->role) {
            case 'admin':
                // Admin can see all chat rooms
                break;
            case 'inspector':
                $query->where('inspector_id', $user->id);
                break;
            case 'business_owner':
                $query->where('business_owner_id', $user->id);
                break;
        }

        $chatRooms = $query->latest()->paginate(15);

        return view('chat.index', compact('chatRooms'));
    }

    /**
     * Display specific chat room
     */
    public function room(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user has access to this chat room
        if (!$this->hasAccessToRoom($chatRoom, $user)) {
            abort(403, 'You do not have access to this chat room.');
        }

        // Load chat room with relationships
        $chatRoom->load(['business', 'businessOwner', 'admin', 'inspector']);

        // Get messages
        $messages = ChatMessage::where('chat_room_id', $chatRoom->id)
            ->with('sender')
            ->orderBy('created_at', 'asc')
            ->get();

        // Mark messages as read for current user
        ChatMessage::where('chat_room_id', $chatRoom->id)
            ->where('sender_id', '!=', $user->id)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        // Check if chatbot is active (when admin is offline)
        $chatbotActive = $this->isChatbotActive($chatRoom);

        return view('chat.room', compact('chatRoom', 'messages', 'chatbotActive'));
    }

    /**
     * Send message to chat room
     */
    public function sendMessage(Request $request, ChatRoom $chatRoom)
    {
        $user = Auth::user();

        if (!$this->hasAccessToRoom($chatRoom, $user)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized access.']);
        }

        $validated = $request->validate([
            'message' => 'required_without:file|string|max:2000',
            'file' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:5120',
            'message_type' => 'in:text,file,image'
        ]);

        DB::transaction(function () use ($validated, $chatRoom, $user, $request) {
            $messageData = [
                'chat_room_id' => $chatRoom->id,
                'sender_id' => $user->id,
                'message' => $validated['message'] ?? '',
                'message_type' => $validated['message_type'] ?? 'text',
                'is_read' => false,
            ];

            // Handle file upload
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $uploadResult = $this->fileUploadService->uploadFile(
                    $file,
                    'chat-files',
                    ['chat_room_id' => $chatRoom->id, 'sender_id' => $user->id]
                );

                $messageData['file_path'] = $uploadResult['path'];
                $messageData['message_type'] = str_contains($file->getMimeType(), 'image') ? 'image' : 'file';
                
                if (empty($messageData['message'])) {
                    $messageData['message'] = 'Shared a file: ' . $uploadResult['original_name'];
                }
            }

            // Create message
            $message = ChatMessage::create($messageData);

            // Send notifications to other participants
            $this->sendChatNotifications($chatRoom, $user, $message);
        });

        return response()->json(['success' => true, 'message' => 'Message sent successfully.']);
    }

    /**
     * Get messages for chat room (AJAX)
     */
    public function getMessages(ChatRoom $chatRoom, Request $request)
    {
        $user = Auth::user();

        if (!$this->hasAccessToRoom($chatRoom, $user)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized access.']);
        }

        $lastMessageId = $request->get('last_message_id', 0);

        $messages = ChatMessage::where('chat_room_id', $chatRoom->id)
            ->where('id', '>', $lastMessageId)
            ->with('sender')
            ->orderBy('created_at', 'asc')
            ->get();

        // Mark new messages as read
        ChatMessage::where('chat_room_id', $chatRoom->id)
            ->where('sender_id', '!=', $user->id)
            ->where('id', '>', $lastMessageId)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        $formattedMessages = $messages->map(function ($message) {
            return [
                'id' => $message->id,
                'message' => $message->message,
                'message_type' => $message->message_type,
                'file_path' => $message->file_path ? asset('storage/' . $message->file_path) : null,
                'sender_name' => $message->sender->full_name,
                'sender_id' => $message->sender_id,
                'created_at' => $message->created_at->format('M j, Y g:i A'),
                'is_own_message' => $message->sender_id === Auth::id(),
            ];
        });

        return response()->json([
            'success' => true,
            'messages' => $formattedMessages
        ]);
    }

    /**
     * Create or get chat room for business
     */
    public function createOrGetRoom(Request $request)
    {
        $user = Auth::user();

        if ($user->role !== 'business_owner') {
            return response()->json(['success' => false, 'message' => 'Only business owners can create chat rooms.']);
        }

        $business = $user->businesses()->first();

        if (!$business) {
            return response()->json(['success' => false, 'message' => 'No business profile found.']);
        }

        // Check if chat room already exists
        $chatRoom = ChatRoom::where('business_id', $business->id)
            ->where('business_owner_id', $user->id)
            ->where('status', 'active')
            ->first();

        if (!$chatRoom) {
            // Create new chat room
            $chatRoom = ChatRoom::create([
                'business_id' => $business->id,
                'business_owner_id' => $user->id,
                'subject' => $request->get('subject', 'General Inquiry'),
                'status' => 'active',
            ]);

            // Send system message
            ChatMessage::create([
                'chat_room_id' => $chatRoom->id,
                'sender_id' => $user->id,
                'message' => 'Chat room created. How can we help you today?',
                'message_type' => 'system',
            ]);

            // Notify admins about new chat room
            $this->notifyAdminsNewChatRoom($chatRoom);
        }

        return response()->json([
            'success' => true,
            'chat_room_id' => $chatRoom->id,
            'redirect_url' => route('chat.room', $chatRoom)
        ]);
    }

    /**
     * Close chat room
     */
    public function closeRoom(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        if (!$this->hasAccessToRoom($chatRoom, $user)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized access.']);
        }

        $chatRoom->update(['status' => 'closed']);

        // Send system message
        ChatMessage::create([
            'chat_room_id' => $chatRoom->id,
            'sender_id' => $user->id,
            'message' => 'Chat room has been closed by ' . $user->full_name,
            'message_type' => 'system',
        ]);

        return response()->json(['success' => true, 'message' => 'Chat room closed successfully.']);
    }

    /**
     * Assign inspector to chat room
     */
    public function assignInspector(Request $request, ChatRoom $chatRoom)
    {
        $validated = $request->validate([
            'inspector_id' => 'required|exists:users,id'
        ]);

        $inspector = User::where('id', $validated['inspector_id'])
            ->where('role', 'inspector')
            ->where('status', 'active')
            ->first();

        if (!$inspector) {
            return response()->json(['success' => false, 'message' => 'Invalid inspector selected.']);
        }

        $chatRoom->update(['inspector_id' => $inspector->id]);

        // Send system message
        ChatMessage::create([
            'chat_room_id' => $chatRoom->id,
            'sender_id' => Auth::id(),
            'message' => $inspector->full_name . ' has been assigned to this chat.',
            'message_type' => 'system',
        ]);

        // Notify inspector
        Notification::create([
            'user_id' => $inspector->id,
            'title' => 'New Chat Assignment',
            'message' => 'You have been assigned to a chat with ' . $chatRoom->business->name,
            'type' => 'chat',
            'related_id' => $chatRoom->id,
            'related_type' => 'chat_room',
            'action_url' => route('chat.room', $chatRoom),
        ]);

        return response()->json(['success' => true, 'message' => 'Inspector assigned successfully.']);
    }

    /**
     * Check if user has access to chat room
     */
    private function hasAccessToRoom(ChatRoom $chatRoom, User $user)
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'inspector':
                return $chatRoom->inspector_id === $user->id;
            case 'business_owner':
                return $chatRoom->business_owner_id === $user->id;
            default:
                return false;
        }
    }

    /**
     * Check if chatbot should be active
     */
    private function isChatbotActive(ChatRoom $chatRoom)
    {
        // Chatbot is active when no admin is assigned or admin is offline
        if (!$chatRoom->admin_id) {
            return true;
        }

        $admin = User::find($chatRoom->admin_id);
        
        // Check if admin was last active more than 30 minutes ago
        return !$admin || $admin->last_activity_at < now()->subMinutes(30);
    }

    /**
     * Send notifications to chat participants
     */
    private function sendChatNotifications(ChatRoom $chatRoom, User $sender, ChatMessage $message)
    {
        $participants = collect();

        if ($chatRoom->business_owner_id && $chatRoom->business_owner_id !== $sender->id) {
            $participants->push($chatRoom->business_owner_id);
        }

        if ($chatRoom->admin_id && $chatRoom->admin_id !== $sender->id) {
            $participants->push($chatRoom->admin_id);
        }

        if ($chatRoom->inspector_id && $chatRoom->inspector_id !== $sender->id) {
            $participants->push($chatRoom->inspector_id);
        }

        foreach ($participants as $userId) {
            Notification::create([
                'user_id' => $userId,
                'title' => 'New Chat Message',
                'message' => $sender->full_name . ' sent a message in ' . $chatRoom->business->name . ' chat',
                'type' => 'chat',
                'related_id' => $chatRoom->id,
                'related_type' => 'chat_room',
                'action_url' => route('chat.room', $chatRoom),
            ]);
        }
    }

    /**
     * Notify admins about new chat room
     */
    private function notifyAdminsNewChatRoom(ChatRoom $chatRoom)
    {
        $admins = User::where('role', 'admin')->where('status', 'active')->get();

        foreach ($admins as $admin) {
            Notification::create([
                'user_id' => $admin->id,
                'title' => 'New Chat Room Created',
                'message' => $chatRoom->business->name . ' has started a new chat conversation',
                'type' => 'chat',
                'related_id' => $chatRoom->id,
                'related_type' => 'chat_room',
                'action_url' => route('chat.room', $chatRoom),
            ]);
        }
    }
}
