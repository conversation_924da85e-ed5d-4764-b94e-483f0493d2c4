<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-check"></i> Inspection Details
        </h1>
        <div>
            <a href="<?= BASE_URL ?>business/inspections" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Inspections
            </a>
            <?php if ($inspection_score): ?>
                <a href="<?= BASE_URL ?>business/inspection-report/<?= $inspection['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-file-alt"></i> View Full Report
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Inspection Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-building"></i> Inspection Information
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= htmlspecialchars($business['name']) ?></h5>
                    <p class="text-muted mb-1">
                        <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($business['address']) ?>
                    </p>
                    <p class="text-muted mb-1">
                        <i class="fas fa-map"></i> District: <?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?>
                    </p>
                    <p class="mb-1">
                        <strong>Inspector:</strong> <?= htmlspecialchars($inspection['inspector_name'] ?? 'Not assigned') ?>
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1">
                        <strong>Inspection Date:</strong> <?= date('M d, Y g:i A', strtotime($inspection['scheduled_date'])) ?>
                    </p>
                    <p class="mb-1">
                        <strong>Type:</strong> <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'] ?? 'routine')) ?>
                    </p>
                    <p class="mb-1">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : ($inspection['status'] === 'in_progress' ? 'warning' : 'secondary') ?>">
                            <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                        </span>
                    </p>
                    <?php if ($inspection['notes']): ?>
                        <p class="mb-1">
                            <strong>Notes:</strong> <?= htmlspecialchars($inspection['notes']) ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress and Score Card -->
    <?php if ($completion_status['total_items'] > 0): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-chart-line"></i> Inspection Progress
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar bg-info" role="progressbar" 
                                     style="width: <?= $completion_status['completion_percentage'] ?>%">
                                    <?= $completion_status['completion_percentage'] ?>%
                                </div>
                            </div>
                            <small class="text-muted">
                                <?= $completion_status['completed_items'] ?> of <?= $completion_status['total_items'] ?> items completed
                            </small>
                        </div>
                    </div>
                    <?php if ($inspection_score): ?>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="mb-1 text-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>">
                                    <?= $inspection_score['percentage'] ?>%
                                </h3>
                                <small class="text-muted">Compliance Score</small>
                                <br>
                                <span class="badge bg-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?> fs-6">
                                    Grade: <?= $inspection_score['grade'] ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="mb-1 text-<?= $inspection_score['critical_violations'] > 0 ? 'danger' : 'success' ?>">
                                    <?= $inspection_score['critical_violations'] ?>
                                </h3>
                                <small class="text-muted">Critical Violations</small>
                                <?php if ($inspection_score['critical_violations'] > 0): ?>
                                    <br>
                                    <small class="text-danger">
                                        <i class="fas fa-exclamation-triangle"></i> Requires immediate attention
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($inspection_score): ?>
                    <div class="mt-3">
                        <strong>Overall Status:</strong> 
                        <span class="badge bg-<?= $inspection_score['status'] === 'passed' ? 'success' : 'danger' ?> fs-6">
                            <?= ucfirst($inspection_score['status']) ?>
                        </span>
                        
                        <?php if ($inspection_score['critical_violations'] > 0): ?>
                            <div class="alert alert-danger mt-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Critical Violations Detected:</strong> Your business has <?= $inspection_score['critical_violations'] ?> critical violation(s) that require immediate attention.
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Checklist Summary -->
    <?php if (!empty($checklist_responses)): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list-check"></i> Inspection Checklist Summary
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Status</th>
                                <th>Score</th>
                                <th>Notes</th>
                                <th>Corrective Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($checklist_responses as $response): ?>
                                <tr class="<?= $response['compliance_status'] === 'non_compliant' ? 'table-danger' : ($response['compliance_status'] === 'needs_improvement' ? 'table-warning' : ($response['compliance_status'] === 'compliant' ? 'table-success' : '')) ?>">
                                    <td>
                                        <strong><?= htmlspecialchars($response['item_name']) ?></strong>
                                        <?php if ($response['is_critical']): ?>
                                            <span class="badge bg-danger ms-1">CRITICAL</span>
                                        <?php endif; ?>
                                        <br><small class="text-muted"><?= htmlspecialchars($response['category_name']) ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $statusIcons = [
                                            'compliant' => ['✅', 'success'],
                                            'needs_improvement' => ['⚠️', 'warning'],
                                            'non_compliant' => ['❌', 'danger'],
                                            'not_applicable' => ['➖', 'secondary']
                                        ];
                                        $icon = $statusIcons[$response['compliance_status']][0] ?? '❓';
                                        $badgeClass = $statusIcons[$response['compliance_status']][1] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $badgeClass ?>">
                                            <?= $icon ?> <?= ucfirst(str_replace('_', ' ', $response['compliance_status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <strong><?= $response['score'] ?>/<?= $response['points'] ?></strong>
                                    </td>
                                    <td>
                                        <?= $response['notes'] ? htmlspecialchars($response['notes']) : '<em class="text-muted">No notes</em>' ?>
                                    </td>
                                    <td>
                                        <?php if ($response['corrective_action']): ?>
                                            <div>
                                                <strong>Action:</strong> <?= htmlspecialchars($response['corrective_action']) ?>
                                                <?php if ($response['deadline']): ?>
                                                    <br><strong>Deadline:</strong> 
                                                    <span class="badge bg-<?= strtotime($response['deadline']) < time() ? 'danger' : 'info' ?>">
                                                        <?= date('M d, Y', strtotime($response['deadline'])) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <em class="text-muted">No action required</em>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="card shadow mb-4">
            <div class="card-body text-center py-5">
                <i class="fas fa-clipboard fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Inspection Not Started</h5>
                <p class="text-muted">The inspection checklist has not been started yet. Please wait for the inspector to begin the inspection process.</p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Next Steps -->
    <?php if ($inspection_score): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-tasks"></i> Next Steps
                </h6>
            </div>
            <div class="card-body">
                <h6>Recommended Actions:</h6>
                <ul>
                    <?php if ($inspection_score['critical_violations'] > 0): ?>
                        <li class="text-danger"><strong>Address all critical violations immediately</strong></li>
                    <?php endif; ?>
                    <li>Review and implement corrective actions for non-compliant items</li>
                    <li>Maintain documentation of improvements made</li>
                    <li>Ensure staff training on compliance requirements</li>
                    <li>Prepare for follow-up inspection in <?= $inspection_score['percentage'] >= 80 ? '6 months' : ($inspection_score['percentage'] >= 60 ? '3 months' : '1 month') ?></li>
                </ul>
                
                <div class="mt-3">
                    <strong>Need Help?</strong> Contact the City Health Office for guidance on compliance requirements.
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php $this->endSection(); ?>
