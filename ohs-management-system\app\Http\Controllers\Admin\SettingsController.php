<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WebsiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class SettingsController extends Controller
{
    /**
     * Display website settings dashboard
     */
    public function index()
    {
        $settings = WebsiteSetting::getAllSettings();
        $colorSchemes = $this->getColorSchemes();

        return view('admin.settings.index', compact('settings', 'colorSchemes'));
    }

    /**
     * Update general settings
     */
    public function updateGeneral(Request $request)
    {
        $validated = $request->validate([
            'site_name' => 'required|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'site_description' => 'nullable|string',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:50',
            'office_address' => 'nullable|string',
            'office_hours' => 'nullable|string',
            'facebook_url' => 'nullable|url',
            'twitter_url' => 'nullable|url',
            'youtube_url' => 'nullable|url',
        ]);

        foreach ($validated as $key => $value) {
            WebsiteSetting::updateSetting($key, $value);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'General settings updated successfully.');
    }

    /**
     * Update color scheme
     */
    public function updateColors(Request $request)
    {
        $validated = $request->validate([
            'primary_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'secondary_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'accent_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'success_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'warning_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'danger_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'dark_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'light_color' => 'required|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
        ]);

        foreach ($validated as $key => $value) {
            WebsiteSetting::updateSetting($key, $value);
        }

        // Generate CSS file with new colors
        $this->generateCustomCSS($validated);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Color scheme updated successfully.');
    }

    /**
     * Apply predefined color scheme
     */
    public function applyColorScheme(Request $request)
    {
        $schemeId = $request->input('scheme_id');
        $schemes = $this->getColorSchemes();

        if (!isset($schemes[$schemeId])) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'Invalid color scheme selected.');
        }

        $scheme = $schemes[$schemeId];

        foreach ($scheme['colors'] as $key => $value) {
            WebsiteSetting::updateSetting($key, $value);
        }

        // Generate CSS file with new colors
        $this->generateCustomCSS($scheme['colors']);

        return redirect()->route('admin.settings.index')
            ->with('success', "Applied {$scheme['name']} color scheme successfully.");
    }

    /**
     * Upload website images
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120',
            'image_type' => 'required|in:logo,hero_bg,about_image,services_image,contact_image'
        ]);

        $imageType = $request->input('image_type');
        $image = $request->file('image');

        // Store the image
        $path = $image->store('website-images', 'public');

        // Update setting with new image path
        WebsiteSetting::updateSetting($imageType, '/storage/' . $path);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Image uploaded successfully.');
    }

    /**
     * Reset to default settings
     */
    public function resetToDefaults()
    {
        WebsiteSetting::resetToDefaults();

        // Generate default CSS
        $defaultColors = [
            'primary_color' => '#2563eb',
            'secondary_color' => '#1e40af',
            'accent_color' => '#3b82f6',
            'success_color' => '#059669',
            'warning_color' => '#d97706',
            'danger_color' => '#dc2626',
            'dark_color' => '#1f2937',
            'light_color' => '#f8fafc'
        ];
        $this->generateCustomCSS($defaultColors);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Website settings reset to defaults successfully.');
    }

    /**
     * Generate custom CSS file with current colors
     */
    private function generateCustomCSS($colors)
    {
        $cssContent = ":root {\n";
        foreach ($colors as $key => $value) {
            $cssVar = '--' . str_replace('_', '-', $key);
            $cssContent .= "    $cssVar: $value;\n";
        }
        $cssContent .= "}\n\n";

        // Add comprehensive CSS rules
        $cssContent .= $this->getComprehensiveCSS();

        // Save CSS file to public directory
        $cssPath = public_path('css/custom-theme.css');
        File::put($cssPath, $cssContent);
    }

    /**
     * Get comprehensive CSS rules for theming
     */
    private function getComprehensiveCSS()
    {
        return "
/* Primary elements */
.btn-primary, .bg-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Secondary elements */
.btn-secondary, .bg-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

/* Success elements */
.btn-success, .bg-success {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

/* Warning elements */
.btn-warning, .bg-warning {
    background-color: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

/* Danger elements */
.btn-danger, .bg-danger {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* Sidebar customization */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 10%, var(--secondary-color) 100%) !important;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar .nav-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Card customization */
.card-header {
    background-color: var(--light-color) !important;
    border-bottom: 1px solid var(--primary-color) !important;
}

/* Progress bars */
.progress-bar {
    background-color: var(--primary-color) !important;
}

/* Links */
a {
    color: var(--primary-color) !important;
}

a:hover {
    color: var(--secondary-color) !important;
}
";
    }

    /**
     * Get predefined color schemes
     */
    private function getColorSchemes()
    {
        return [
            'default' => [
                'name' => 'Default Blue',
                'description' => 'Professional blue theme',
                'colors' => [
                    'primary_color' => '#2563eb',
                    'secondary_color' => '#1e40af',
                    'accent_color' => '#3b82f6',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#dc2626',
                    'dark_color' => '#1f2937',
                    'light_color' => '#f8fafc'
                ]
            ],
            'green' => [
                'name' => 'Government Green',
                'description' => 'Professional green theme',
                'colors' => [
                    'primary_color' => '#059669',
                    'secondary_color' => '#047857',
                    'accent_color' => '#10b981',
                    'success_color' => '#22c55e',
                    'warning_color' => '#f59e0b',
                    'danger_color' => '#ef4444',
                    'dark_color' => '#1f2937',
                    'light_color' => '#f0fdf4'
                ]
            ],
            'red' => [
                'name' => 'Professional Red',
                'description' => 'Bold red theme',
                'colors' => [
                    'primary_color' => '#dc2626',
                    'secondary_color' => '#b91c1c',
                    'accent_color' => '#ef4444',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#991b1b',
                    'dark_color' => '#1f2937',
                    'light_color' => '#fef2f2'
                ]
            ],
            'purple' => [
                'name' => 'Royal Purple',
                'description' => 'Elegant purple theme',
                'colors' => [
                    'primary_color' => '#7c3aed',
                    'secondary_color' => '#6d28d9',
                    'accent_color' => '#8b5cf6',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#dc2626',
                    'dark_color' => '#1f2937',
                    'light_color' => '#faf5ff'
                ]
            ],
            'dark' => [
                'name' => 'Dark Mode',
                'description' => 'Professional dark theme',
                'colors' => [
                    'primary_color' => '#3b82f6',
                    'secondary_color' => '#1d4ed8',
                    'accent_color' => '#60a5fa',
                    'success_color' => '#10b981',
                    'warning_color' => '#f59e0b',
                    'danger_color' => '#ef4444',
                    'dark_color' => '#111827',
                    'light_color' => '#1f2937'
                ]
            ]
        ];
    }
}
