-- Create business_checklist_evidence table for business owner uploads
CREATE TABLE IF NOT EXISTS business_checklist_evidence (
    id VARCHAR(36) PRIMARY KEY,
    business_id VARCHAR(36) NOT NULL,
    checklist_item_id VARCHAR(36) NOT NULL,
    inspection_id VARCHAR(36) DEFAULT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INT NOT NULL,
    notes TEXT,
    status ENUM('pending', 'reviewed', 'approved', 'rejected') DEFAULT 'pending',
    uploaded_by VARCHAR(36) NOT NULL,
    reviewed_by VARCHAR(36) DEFAULT NULL,
    reviewed_at DATETIME DEFAULT NULL,
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_id) REFERENCES businesses(id) ON DELETE CASCADE,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (checklist_item_id) REFERENCES inspection_checklist_items(id) ON DELETE CASCADE,
    FOREIGN KEY (inspection_id) REFERENCES inspections(id) ON DELETE SET NULL,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_evidence_business ON business_checklist_evidence(business_id);
CREATE INDEX IF NOT EXISTS idx_business_evidence_item ON business_checklist_evidence(checklist_item_id);
CREATE INDEX IF NOT EXISTS idx_business_evidence_inspection ON business_checklist_evidence(inspection_id);
