<?php
namespace App\Helpers;

/**
 * Response Helper
 * 
 * Standardized response handling for JSON, redirects, and flash messages
 */
class ResponseHelper {
    
    /**
     * Send JSON response
     */
    public static function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit();
    }
    
    /**
     * Send success JSON response
     */
    public static function jsonSuccess($message = 'Success', $data = [], $statusCode = 200) {
        return self::json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $statusCode);
    }
    
    /**
     * Send error JSON response
     */
    public static function jsonError($message = 'Error', $errors = [], $statusCode = 400) {
        return self::json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
    
    /**
     * Send validation error JSON response
     */
    public static function jsonValidationError($errors, $message = 'Validation failed') {
        return self::jsonError($message, $errors, 422);
    }
    
    /**
     * Send not found JSON response
     */
    public static function jsonNotFound($message = 'Resource not found') {
        return self::jsonError($message, [], 404);
    }
    
    /**
     * Send unauthorized JSON response
     */
    public static function jsonUnauthorized($message = 'Unauthorized access') {
        return self::jsonError($message, [], 401);
    }
    
    /**
     * Send forbidden JSON response
     */
    public static function jsonForbidden($message = 'Access forbidden') {
        return self::jsonError($message, [], 403);
    }
    
    /**
     * Send server error JSON response
     */
    public static function jsonServerError($message = 'Internal server error') {
        return self::jsonError($message, [], 500);
    }
    
    /**
     * Redirect to URL
     */
    public static function redirect($url, $statusCode = 302) {
        // Ensure URL starts with base URL if it's a relative path
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            $url = BASE_URL . ltrim($url, '/');
        }
        
        http_response_code($statusCode);
        header('Location: ' . $url);
        exit();
    }
    
    /**
     * Redirect back to previous page
     */
    public static function redirectBack($fallback = '') {
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        
        if ($referer && strpos($referer, BASE_URL) === 0) {
            self::redirect($referer);
        } else {
            self::redirect($fallback ?: 'dashboard');
        }
    }
    
    /**
     * Redirect with success message
     */
    public static function redirectWithSuccess($url, $message) {
        self::setFlash('success', $message);
        self::redirect($url);
    }
    
    /**
     * Redirect with error message
     */
    public static function redirectWithError($url, $message) {
        self::setFlash('error', $message);
        self::redirect($url);
    }
    
    /**
     * Redirect with warning message
     */
    public static function redirectWithWarning($url, $message) {
        self::setFlash('warning', $message);
        self::redirect($url);
    }
    
    /**
     * Redirect with info message
     */
    public static function redirectWithInfo($url, $message) {
        self::setFlash('info', $message);
        self::redirect($url);
    }
    
    /**
     * Set flash message
     */
    public static function setFlash($type, $message) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['flash'][$type] = $message;
    }
    
    /**
     * Get flash message
     */
    public static function getFlash($type) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $message = $_SESSION['flash'][$type] ?? null;
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    
    /**
     * Get all flash messages
     */
    public static function getAllFlash() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $messages = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $messages;
    }
    
    /**
     * Check if flash message exists
     */
    public static function hasFlash($type) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION['flash'][$type]);
    }
    
    /**
     * Set success flash message
     */
    public static function setSuccess($message) {
        self::setFlash('success', $message);
    }
    
    /**
     * Set error flash message
     */
    public static function setError($message) {
        self::setFlash('error', $message);
    }
    
    /**
     * Set warning flash message
     */
    public static function setWarning($message) {
        self::setFlash('warning', $message);
    }
    
    /**
     * Set info flash message
     */
    public static function setInfo($message) {
        self::setFlash('info', $message);
    }
    
    /**
     * Abort with HTTP status code
     */
    public static function abort($statusCode = 404, $message = null) {
        http_response_code($statusCode);
        
        if ($message) {
            echo $message;
        } else {
            $messages = [
                400 => 'Bad Request',
                401 => 'Unauthorized',
                403 => 'Forbidden',
                404 => 'Not Found',
                405 => 'Method Not Allowed',
                422 => 'Unprocessable Entity',
                500 => 'Internal Server Error',
                503 => 'Service Unavailable'
            ];
            
            echo $messages[$statusCode] ?? 'Error';
        }
        
        exit();
    }
    
    /**
     * Download file response
     */
    public static function download($filePath, $fileName = null, $mimeType = null) {
        if (!file_exists($filePath)) {
            self::abort(404, 'File not found');
        }
        
        $fileName = $fileName ?: basename($filePath);
        $mimeType = $mimeType ?: mime_content_type($filePath);
        
        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');
        
        readfile($filePath);
        exit();
    }
    
    /**
     * Stream file response (for viewing in browser)
     */
    public static function stream($filePath, $mimeType = null) {
        if (!file_exists($filePath)) {
            self::abort(404, 'File not found');
        }
        
        $mimeType = $mimeType ?: mime_content_type($filePath);
        
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: public, max-age=3600');
        
        readfile($filePath);
        exit();
    }
    
    /**
     * Send CSV response
     */
    public static function csv($data, $filename = 'export.csv', $headers = []) {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');
        
        $output = fopen('php://output', 'w');
        
        // Add headers if provided
        if (!empty($headers)) {
            fputcsv($output, $headers);
        }
        
        // Add data rows
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit();
    }
    
    /**
     * Send XML response
     */
    public static function xml($data, $rootElement = 'response') {
        header('Content-Type: application/xml');
        
        $xml = new \SimpleXMLElement('<' . $rootElement . '/>');
        self::arrayToXml($data, $xml);
        
        echo $xml->asXML();
        exit();
    }
    
    /**
     * Convert array to XML
     */
    private static function arrayToXml($data, &$xml) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if (is_numeric($key)) {
                    $key = 'item';
                }
                $subnode = $xml->addChild($key);
                self::arrayToXml($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }
    
    /**
     * Get current URL
     */
    public static function getCurrentUrl() {
        return $_SERVER['REQUEST_URI'];
    }
    
    /**
     * Get base URL
     */
    public static function getBaseUrl() {
        return BASE_URL;
    }
    
    /**
     * Check if request is AJAX
     */
    public static function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get request method
     */
    public static function getRequestMethod() {
        return $_SERVER['REQUEST_METHOD'];
    }
    
    /**
     * Check if request method is POST
     */
    public static function isPost() {
        return self::getRequestMethod() === 'POST';
    }
    
    /**
     * Check if request method is GET
     */
    public static function isGet() {
        return self::getRequestMethod() === 'GET';
    }
}
