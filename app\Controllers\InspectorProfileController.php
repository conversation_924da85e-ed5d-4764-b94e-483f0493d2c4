<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\User;
use App\Models\Inspection;

class InspectorProfileController extends Controller {
    protected $auth;
    protected $userModel;
    protected $inspectionModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->auth->requireAdmin();

        $this->userModel = new User();
        $this->inspectionModel = new Inspection();
    }

    public function view($id) {
        $user = $this->userModel->find($id);

        if (!$user || $user['role'] !== 'inspector') {
            $this->setFlash('error', 'Inspector not found.');
            return $this->redirect('admin/users');
        }

        // Get barangay assignments
        $barangays = $this->getInspectorBarangays($id);
        
        // Get workload statistics
        $workload = $this->getInspectorWorkload($id);
        
        // Get recent inspections
        $recentInspections = $this->inspectionModel->getByInspector($id);

        return $this->render('admin/inspector_profile/view', [
            'title' => 'Inspector Profile - ' . $user['full_name'],
            'user' => $user,
            'barangays' => $barangays,
            'workload' => $workload,
            'recent_inspections' => array_slice($recentInspections, 0, 10),
            'active_page' => 'users'
        ]);
    }

    private function getInspectorBarangays($inspectorId) {
        try {
            $db = (new \App\Config\Database())->getConnection();
            $query = "SELECT iba.*, b.name as barangay_name, d.name as district_name, 
                             u.full_name as assigned_by_name
                      FROM inspector_barangay_assignments iba
                      LEFT JOIN barangays b ON iba.barangay_id = b.id
                      LEFT JOIN districts d ON b.district_id = d.id
                      LEFT JOIN users u ON iba.assigned_by = u.id
                      WHERE iba.inspector_id = ? AND iba.status = 'active'
                      ORDER BY d.name ASC, b.name ASC";
            
            $stmt = $db->prepare($query);
            $stmt->execute([$inspectorId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\Exception $e) {
            error_log("Error getting inspector barangays: " . $e->getMessage());
            return [];
        }
    }

    private function getInspectorWorkload($inspectorId) {
        try {
            $db = (new \App\Config\Database())->getConnection();
            $query = "SELECT
                        COUNT(DISTINCT iba.barangay_id) as assigned_barangays,
                        COUNT(DISTINCT b.id) as businesses_in_barangays,
                        COUNT(DISTINCT i.id) as total_inspections,
                        COUNT(DISTINCT CASE WHEN i.status IN ('scheduled', 'confirmed') THEN i.id END) as pending_inspections,
                        COUNT(DISTINCT CASE WHEN i.status = 'completed' THEN i.id END) as completed_inspections
                      FROM inspector_barangay_assignments iba
                      LEFT JOIN businesses b ON iba.barangay_id = b.barangay_id
                      LEFT JOIN inspections i ON b.id = i.business_id AND i.inspector_id = ?
                      WHERE iba.inspector_id = ? AND iba.status = 'active'";

            $stmt = $db->prepare($query);
            $stmt->execute([$inspectorId, $inspectorId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            return $result ?: [
                'assigned_barangays' => 0,
                'businesses_in_barangays' => 0,
                'total_inspections' => 0,
                'pending_inspections' => 0,
                'completed_inspections' => 0
            ];
        } catch (\Exception $e) {
            error_log("Error getting inspector workload: " . $e->getMessage());
            return [
                'assigned_barangays' => 0,
                'businesses_in_barangays' => 0,
                'total_inspections' => 0,
                'pending_inspections' => 0,
                'completed_inspections' => 0
            ];
        }
    }
}
