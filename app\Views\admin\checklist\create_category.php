<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus"></i> Create Checklist Category
        </h1>
        <a href="<?= BASE_URL ?>admin/checklist" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Checklist
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Category Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= BASE_URL ?>admin/checklist/categories/create">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="e.g., Safety & Emergency Preparedness">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Weight</label>
                                    <input type="number" class="form-control" id="weight" name="weight" 
                                           step="0.1" min="0" max="10" value="1.0"
                                           placeholder="1.0">
                                    <div class="form-text">Category importance weight (0-10)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Brief description of this category..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           min="0" value="0"
                                           placeholder="0">
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            Active Category
                                        </label>
                                        <div class="form-text">Only active categories are used in inspections</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= BASE_URL ?>admin/checklist" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Category Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Category Name</h6>
                    <p class="small text-muted mb-3">
                        Use clear, descriptive names that group related inspection items. 
                        Examples: "Safety & Emergency", "Documentation", "Health & Sanitation"
                    </p>

                    <h6 class="text-primary">Weight</h6>
                    <p class="small text-muted mb-3">
                        Higher weights make this category more important in overall compliance scoring. 
                        Critical categories like "Safety" might have weight 3.0, while "Documentation" might be 2.0.
                    </p>

                    <h6 class="text-primary">Sort Order</h6>
                    <p class="small text-muted mb-3">
                        Controls the display order in checklists. Lower numbers appear first. 
                        Typically: Documentation (1), Safety (2), Health (3), etc.
                    </p>

                    <h6 class="text-primary">Status</h6>
                    <p class="small text-muted">
                        Inactive categories and their items won't appear in new inspections, 
                        but existing inspection data is preserved.
                    </p>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-lightbulb"></i> Best Practices
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="small text-muted mb-0">
                        <li>Keep category names concise but descriptive</li>
                        <li>Group related inspection items together</li>
                        <li>Use consistent naming conventions</li>
                        <li>Order categories by inspection workflow</li>
                        <li>Consider regulatory requirements when setting weights</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>
