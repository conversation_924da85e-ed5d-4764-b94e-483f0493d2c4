<?php
namespace App\Models;

use App\Core\Model;

class Barangay extends Model {
    protected $table = 'barangays';
    protected $fillable = ['district_id', 'name', 'code'];

    public function getAll() {
        return $this->all('name', 'ASC');
    }

    public function getByDistrict($districtId) {
        $query = "SELECT * FROM barangays WHERE district_id = ? ORDER BY name";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getWithDistrict() {
        $query = "SELECT b.*,
                         COALESCE(d.name, 'No District') as district_name,
                         COALESCE(d.code, '') as district_code
                 FROM barangays b
                 LEFT JOIN districts d ON b.district_id = d.id
                 ORDER BY COALESCE(d.name, 'ZZZ'), b.name";
        $stmt = $this->query($query);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getBusinesses($barangayId) {
        $query = "SELECT b.*, u.full_name as owner_name 
                 FROM businesses b 
                 LEFT JOIN users u ON b.owner_id = u.id 
                 WHERE b.barangay_id = ? 
                 ORDER BY b.name";
        $stmt = $this->query($query, [$barangayId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getInspectors($barangayId) {
        $query = "SELECT u.*, iba.assigned_at, iba.status 
                 FROM users u 
                 INNER JOIN inspector_barangay_assignments iba ON u.id = iba.inspector_id 
                 WHERE iba.barangay_id = ? AND iba.status = 'active' AND u.role = 'inspector'
                 ORDER BY u.full_name";
        $stmt = $this->query($query, [$barangayId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getDistrictId($barangayId) {
        $query = "SELECT district_id FROM barangays WHERE id = ?";
        $stmt = $this->query($query, [$barangayId]);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        return $result ? $result['district_id'] : null;
    }

    public function getDistrictInfo($barangayId) {
        $query = "SELECT d.* FROM districts d 
                 INNER JOIN barangays b ON d.id = b.district_id 
                 WHERE b.id = ?";
        $stmt = $this->query($query, [$barangayId]);
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }
}
