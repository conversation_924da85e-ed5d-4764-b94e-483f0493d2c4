<?php
namespace App\Models;

use App\Core\Model;

class District extends Model {
    protected $table = 'districts';
    protected $fillable = ['id', 'name', 'code', 'description'];
    protected $timestamps = true;

    public function getAll() {
        return $this->all('name', 'ASC');
    }

    public function getWithBarangayCount() {
        $query = "SELECT d.*, COUNT(b.id) as barangay_count
                 FROM districts d
                 LEFT JOIN barangays b ON d.id = b.district_id
                 GROUP BY d.id
                 ORDER BY d.name";
        $stmt = $this->query($query);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getBarangays($districtId) {
        $query = "SELECT * FROM barangays WHERE district_id = ? ORDER BY name";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getInspectors($districtId) {
        $query = "SELECT u.*, ida.assigned_at, ida.status
                 FROM users u
                 INNER JOIN inspector_district_assignments ida ON u.id = ida.inspector_id
                 WHERE ida.district_id = ? AND ida.status = 'active' AND u.role = 'inspector'
                 ORDER BY u.full_name";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getBusinesses($districtId) {
        $query = "SELECT b.*, brg.name as barangay_name
                 FROM businesses b
                 INNER JOIN barangays brg ON b.barangay_id = brg.id
                 WHERE brg.district_id = ?
                 ORDER BY b.name";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}