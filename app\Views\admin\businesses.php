<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">Manage Businesses</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/admin/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Businesses</li>
    </ol>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-building me-1"></i>
                All Businesses
            </div>
            <a href="/admin/businesses/create" class="btn btn-primary btn-sm">
                <i class="fas fa-plus me-1"></i> Add Business
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="businessesTable">
                    <thead>
                        <tr>
                            <th>Business Name</th>
                            <th>Owner</th>
                            <th>Contact Person</th>
                            <th>Contact Number</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($businesses as $business): ?>
                        <tr>
                            <td><?= htmlspecialchars($business['name'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($business['owner_name'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($business['contact_person'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($business['contact_number'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($business['email'] ?? 'N/A') ?></td>
                            <td>
                                <span class="badge bg-<?= ($business['status'] ?? 'pending') === 'active' ? 'success' : 
                                    (($business['status'] ?? 'pending') === 'pending' ? 'warning' : 'danger') ?>">
                                    <?= ucfirst($business['status'] ?? 'pending') ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="/business/<?= $business['id'] ?>/documents" 
                                       class="btn btn-info btn-sm" title="View Documents">
                                        <i class="fas fa-file-alt"></i>
                                    </a>
                                    <a href="/business/<?= $business['id'] ?>/inspections" 
                                       class="btn btn-primary btn-sm" title="View Inspections">
                                        <i class="fas fa-clipboard-check"></i>
                                    </a>
                                    <a href="/admin/businesses/edit/<?= $business['id'] ?>"
                                       class="btn btn-warning btn-sm" title="Edit Business">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if (($business['status'] ?? 'pending') !== 'active'): ?>
                                    <button type="button"
                                            class="btn btn-success btn-sm"
                                            title="Activate Business"
                                            onclick="showActivateModal('<?= $business['id'] ?>', '<?= htmlspecialchars($business['name']) ?>')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <?php else: ?>
                                    <button type="button"
                                            class="btn btn-danger btn-sm"
                                            title="Deactivate Business"
                                            onclick="showDeactivateModal('<?= $business['id'] ?>', '<?= htmlspecialchars($business['name']) ?>')">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Activate Business Modal -->
<div class="modal fade" id="activateModal" tabindex="-1" role="dialog" aria-labelledby="activateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="activateModalLabel">
                    <i class="fas fa-check-circle me-2"></i>Activate Business
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center mb-3">Are you sure you want to activate this business?</p>
                <div class="alert alert-info">
                    <strong>Business:</strong> <span id="activateBusinessName"></span>
                </div>
                <p class="text-muted small">This action will make the business active and allow them to access all system features.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <form id="activateForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>Yes, Activate
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Deactivate Business Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1" role="dialog" aria-labelledby="deactivateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deactivateModalLabel">
                    <i class="fas fa-ban me-2"></i>Deactivate Business
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center mb-3">Are you sure you want to deactivate this business?</p>
                <div class="alert alert-warning">
                    <strong>Business:</strong> <span id="deactivateBusinessName"></span>
                </div>
                <p class="text-muted small">This action will suspend the business and restrict their access to system features.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <form id="deactivateForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-ban me-1"></i>Yes, Deactivate
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#businessesTable').DataTable({
        order: [[0, 'asc']],
        pageLength: 25,
        responsive: true
    });
});

function showActivateModal(businessId, businessName) {
    $('#activateBusinessName').text(businessName);
    $('#activateForm').attr('action', '/admin/businesses/' + businessId + '/activate');
    $('#activateModal').modal('show');
}

function showDeactivateModal(businessId, businessName) {
    $('#deactivateBusinessName').text(businessName);
    $('#deactivateForm').attr('action', '/admin/businesses/' + businessId + '/suspend');
    $('#deactivateModal').modal('show');
}
</script>
<?php $this->endSection(); ?>