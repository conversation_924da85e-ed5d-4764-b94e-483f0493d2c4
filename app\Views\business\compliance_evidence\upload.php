<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= $title ?></h1>
        <a href="<?= BASE_URL ?>business/compliance/<?= $business['id'] ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Evidence
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="<?= BASE_URL ?>business/compliance/<?= $business['id'] ?>/upload" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="compliance_type" class="form-label">Compliance Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="compliance_type" name="compliance_type" required>
                                <option value="">Select Compliance Type</option>
                                <?php foreach ($compliance_types as $key => $type): ?>
                                    <option value="<?= $key ?>"><?= htmlspecialchars($type) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a compliance type.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="evidence_photo" class="form-label">Evidence File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="evidence_photo" name="evidence_photo"
                                   accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" required>
                            <div class="invalid-feedback">
                                Please upload a valid file as evidence.
                            </div>
                            <small class="text-muted">Supported formats: JPG, PNG, PDF, DOC, DOCX (Max: 5MB)</small>
                        </div>

                        <div class="mb-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Upload Guidelines:</h6>
                                <ul class="mb-0 small">
                                    <li>Ensure files are clear and readable</li>
                                    <li>Documents should be current and valid</li>
                                    <li>Evidence will be reviewed by inspectors</li>
                                    <li>You'll be notified once verified</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload Evidence
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('evidence_photo');
    const form = document.querySelector('.needs-validation');

    // File validation
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (5MB = 5 * 1024 * 1024 bytes)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB');
                this.value = '';
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf',
                                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            if (!allowedTypes.includes(file.type)) {
                alert('Only JPG, PNG, PDF, DOC, and DOCX files are allowed');
                this.value = '';
                return;
            }

            // Show file info
            const fileInfo = document.createElement('div');
            fileInfo.className = 'mt-2 text-success small';
            fileInfo.innerHTML = `<i class="fas fa-check-circle"></i> Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;

            // Remove existing file info
            const existingInfo = this.parentNode.querySelector('.text-success');
            if (existingInfo) {
                existingInfo.remove();
            }

            this.parentNode.appendChild(fileInfo);
        }
    });

    // Form validation
    form.addEventListener('submit', function(event) {
        const complianceType = document.getElementById('compliance_type').value;
        const file = fileInput.files[0];

        if (!complianceType) {
            event.preventDefault();
            alert('Please select a compliance type');
            return;
        }

        if (!file) {
            event.preventDefault();
            alert('Please select a file to upload');
            return;
        }

        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
        }

        form.classList.add('was-validated');
    });
});
</script>
<?php $this->endSection(); ?>