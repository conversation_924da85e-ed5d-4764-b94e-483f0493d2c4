<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3 text-gray-800">Create Business</h1>
        <a href="<?= BASE_URL ?>admin/businesses" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="<?= BASE_URL ?>admin/businesses/create" method="POST" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="owner_id" class="form-label">Business Owner <span class="text-danger">*</span></label>
                            <select class="form-select" id="owner_id" name="owner_id" required>
                                <option value="">Select Business Owner</option>
                                <?php foreach ($users as $owner): ?>
                                    <option value="<?= $owner['id'] ?>">
                                        <?= htmlspecialchars($owner['full_name']) ?> (<?= htmlspecialchars($owner['email']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a business owner.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label">Business Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                Please enter the business name.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Business Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_number" class="form-label">Contact Number <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="contact_number" name="contact_number" required>
                            <div class="invalid-feedback">
                                Please enter a contact number.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="category_id" class="form-label">Business Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>">
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a business category.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="district_id" class="form-label">District <span class="text-danger">*</span></label>
                            <select class="form-select" id="district_id" onchange="loadBarangays()">
                                <option value="">Select District First</option>
                                <?php foreach ($districts as $district): ?>
                                    <option value="<?= $district['id'] ?>">
                                        <?= htmlspecialchars($district['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="barangay_id" class="form-label">Barangay <span class="text-danger">*</span></label>
                            <select class="form-select" id="barangay_id" name="barangay_id" required>
                                <option value="">Select District First</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a barangay.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Business Address <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                            <div class="invalid-feedback">
                                Please enter the business address.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="registration_number" class="form-label">Registration Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number" required>
                            <div class="invalid-feedback">
                                Please enter the registration number.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="employee_count" class="form-label">Number of Employees</label>
                            <input type="number" class="form-control" id="employee_count" name="employee_count" min="1" value="1">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="date_established" class="form-label">Date Established</label>
                            <input type="date" class="form-control" id="date_established" name="date_established">
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="pending">Pending</option>
                                <option value="active">Active</option>
                                <option value="suspended">Suspended</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Create Business
                    </button>
                    <a href="<?= BASE_URL ?>admin/businesses" class="btn btn-secondary ms-2">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Store barangays data for JavaScript use
const barangaysData = <?= json_encode($barangays) ?>;

function loadBarangays() {
    const districtId = document.getElementById('district_id').value;
    const barangaySelect = document.getElementById('barangay_id');

    // Clear existing options
    barangaySelect.innerHTML = '<option value="">Select Barangay</option>';

    if (!districtId) {
        barangaySelect.innerHTML = '<option value="">Select District First</option>';
        return;
    }

    // Filter barangays by district
    const districtBarangays = barangaysData.filter(barangay => barangay.district_id == districtId);

    // Add barangay options
    districtBarangays.forEach(barangay => {
        const option = document.createElement('option');
        option.value = barangay.id;
        option.textContent = barangay.name;
        barangaySelect.appendChild(option);
    });
}

// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
<?php $this->endSection(); ?> 