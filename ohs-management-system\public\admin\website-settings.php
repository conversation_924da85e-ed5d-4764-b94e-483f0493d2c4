<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed.');
}

// Handle form submission
if ($_POST) {
    $success = false;
    $error = '';
    
    try {
        // Create website_settings table if it doesn't exist
        $pdo->exec("CREATE TABLE IF NOT EXISTS website_settings (
            id CHAR(36) PRIMARY KEY,
            setting_key VARCHAR(255) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type VARCHAR(50) DEFAULT 'text',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // Update settings
        $settings = [
            'site_name' => $_POST['site_name'] ?? 'OHS Management System',
            'site_description' => $_POST['site_description'] ?? 'Occupational Health & Safety Management System',
            'contact_email' => $_POST['contact_email'] ?? '<EMAIL>',
            'contact_phone' => $_POST['contact_phone'] ?? '+63 ************',
            'contact_address' => $_POST['contact_address'] ?? 'Bacoor City Hall, Cavite',
            'facebook_url' => $_POST['facebook_url'] ?? '',
            'twitter_url' => $_POST['twitter_url'] ?? '',
            'instagram_url' => $_POST['instagram_url'] ?? '',
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? '1' : '0',
            'allow_registration' => isset($_POST['allow_registration']) ? '1' : '0',
            'email_notifications' => isset($_POST['email_notifications']) ? '1' : '0',
        ];
        
        foreach ($settings as $key => $value) {
            $stmt = $pdo->prepare("INSERT INTO website_settings (id, setting_key, setting_value) VALUES (?, ?, ?) 
                                   ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $stmt->execute([bin2hex(random_bytes(16)), $key, $value]);
        }
        
        $success = true;
    } catch (Exception $e) {
        $error = 'Error updating settings: ' . $e->getMessage();
    }
}

// Get current settings
$current_settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM website_settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // Table might not exist yet
}

// Default values
$defaults = [
    'site_name' => 'OHS Management System',
    'site_description' => 'Occupational Health & Safety Management System',
    'contact_email' => '<EMAIL>',
    'contact_phone' => '+63 ************',
    'contact_address' => 'Bacoor City Hall, Cavite',
    'facebook_url' => '',
    'twitter_url' => '',
    'instagram_url' => '',
    'maintenance_mode' => '0',
    'allow_registration' => '1',
    'email_notifications' => '1',
];

$settings = array_merge($defaults, $current_settings);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Settings - OHS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 280px;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover { background: rgba(255,255,255,0.1); color: white; }
        .sidebar .nav-link.active { background: rgba(255,255,255,0.2); color: white; }
        .main-content { margin-left: 280px; padding: 2rem; }
        .settings-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="p-3">
                    <h4><i class="fas fa-shield-alt me-2"></i>OHS Admin</h4>
                    <hr class="text-white">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="businesses.php">
                            <i class="fas fa-building me-2"></i>Businesses
                        </a>
                        <a class="nav-link" href="inspections.php">
                            <i class="fas fa-clipboard-check me-2"></i>Inspections
                        </a>
                        <a class="nav-link" href="chat.php">
                            <i class="fas fa-comments me-2"></i>Live Chat
                        </a>
                        <a class="nav-link active" href="website-settings.php">
                            <i class="fas fa-cog me-2"></i>Website Settings
                        </a>
                        <a class="nav-link" href="website-customization.php">
                            <i class="fas fa-palette me-2"></i>Website Customization
                        </a>
                        <hr class="text-white">
                        <a class="nav-link" href="../index.php?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-cog me-2"></i>Website Settings</h2>
                        <p class="text-muted mb-0">Configure your website settings and preferences</p>
                    </div>
                </div>
                
                <?php if (isset($success) && $success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>Settings updated successfully!
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error) && $error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <form method="POST">
                    <div class="row">
                        <!-- General Settings -->
                        <div class="col-md-6 mb-4">
                            <div class="card settings-card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-globe me-2"></i>General Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" 
                                               value="<?= htmlspecialchars($settings['site_name']) ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="site_description" class="form-label">Site Description</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?= htmlspecialchars($settings['site_description']) ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="<?= htmlspecialchars($settings['contact_email']) ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="<?= htmlspecialchars($settings['contact_phone']) ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="contact_address" class="form-label">Contact Address</label>
                                        <textarea class="form-control" id="contact_address" name="contact_address" rows="2"><?= htmlspecialchars($settings['contact_address']) ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Social Media & System Settings -->
                        <div class="col-md-6 mb-4">
                            <div class="card settings-card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>Social Media Links</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="facebook_url" class="form-label">Facebook URL</label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                               value="<?= htmlspecialchars($settings['facebook_url']) ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="twitter_url" class="form-label">Twitter URL</label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                               value="<?= htmlspecialchars($settings['twitter_url']) ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="instagram_url" class="form-label">Instagram URL</label>
                                        <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                               value="<?= htmlspecialchars($settings['instagram_url']) ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card settings-card">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0"><i class="fas fa-tools me-2"></i>System Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                               <?= $settings['maintenance_mode'] == '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="maintenance_mode">
                                            Maintenance Mode
                                        </label>
                                        <small class="form-text text-muted d-block">Enable to put the site in maintenance mode</small>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="allow_registration" name="allow_registration" 
                                               <?= $settings['allow_registration'] == '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="allow_registration">
                                            Allow User Registration
                                        </label>
                                        <small class="form-text text-muted d-block">Allow new users to register</small>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                               <?= $settings['email_notifications'] == '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="email_notifications">
                                            Email Notifications
                                        </label>
                                        <small class="form-text text-muted d-block">Enable system email notifications</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card settings-card">
                                <div class="card-body text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5">
                                        <i class="fas fa-save me-2"></i>Save Settings
                                    </button>
                                    <a href="dashboard.php" class="btn btn-secondary btn-lg px-5 ms-3">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
