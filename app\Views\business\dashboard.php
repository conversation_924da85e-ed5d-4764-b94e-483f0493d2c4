<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid px-4">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-tachometer-alt text-primary me-2"></i>
            Business Owner Dashboard
        </h1>
        <a href="<?= BASE_URL ?>business/create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Register New Business
        </a>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-6 col-xl-4">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-primary bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-building text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $business_count ?></h3>
                            <p class="text-muted mb-0">My Businesses</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>business" class="btn btn-primary w-100">View All →</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-4">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-success bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-clipboard-check text-success"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $inspection_count ?></h3>
                            <p class="text-muted mb-0">Total Inspections</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>business/inspections" class="btn btn-success w-100">View Details →</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-4">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-warning bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-clock text-warning"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $upcoming_inspections ?></h3>
                            <p class="text-muted mb-0">Upcoming Inspections</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="<?= BASE_URL ?>business/inspections" class="btn btn-warning w-100">View Schedule →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row g-4">
        <!-- Recent Inspections -->
        <div class="col-lg-7">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-header bg-transparent border-0 pt-4 pb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-list text-primary me-2"></i>
                            Recent Inspections
                        </h5>
                        <a href="<?= BASE_URL ?>business/inspections" class="btn btn-sm btn-outline-primary">
                            View All <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <?php if (!empty($recent_inspections)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Business</th>
                                        <th>Inspector</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_inspections as $inspection): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                        <i class="fas fa-building text-primary"></i>
                                                    </div>
                                                    <span class="fw-medium"><?= htmlspecialchars($inspection['business_name']) ?></span>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($inspection['inspector_name'] ?? 'Not Assigned') ?></td>
                                            <td><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                            <td>
                                                <span class="badge bg-<?=
                                                    $inspection['status'] === 'completed' ? 'success' :
                                                    ($inspection['status'] === 'in_progress' ? 'warning' : 'secondary')
                                                ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= BASE_URL ?>business/inspection/<?= $inspection['id'] ?>"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list text-muted mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mb-2">No Inspections Yet</h6>
                            <p class="text-muted">Your inspection history will appear here once inspections are scheduled.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Business Overview -->
        <div class="col-lg-5">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-header bg-transparent border-0 pt-4 pb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-building text-success me-2"></i>
                        My Businesses
                    </h5>
                </div>
                <div class="card-body pt-0">
                    <?php if (!empty($businesses)): ?>
                        <div class="space-y-3">
                            <?php foreach (array_slice($businesses, 0, 5) as $business): ?>
                                <div class="border rounded-3 p-3 mb-3 bg-light">
                                    <div class="d-flex align-items-start justify-content-between">
                                        <div class="d-flex align-items-center flex-grow-1">
                                            <div class="avatar avatar-sm bg-success bg-opacity-10 rounded-circle me-3 flex-shrink-0">
                                                <i class="fas fa-store text-success"></i>
                                            </div>
                                            <div class="flex-grow-1 min-width-0">
                                                <h6 class="mb-1 text-truncate"><?= htmlspecialchars($business['name']) ?></h6>
                                                <small class="text-muted d-block text-truncate"><?= htmlspecialchars($business['category_name'] ?? 'N/A') ?></small>
                                                <span class="badge bg-<?= $business['status'] === 'active' ? 'success' : 'warning' ?> mt-1">
                                                    <?= ucfirst($business['status']) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ms-3 flex-shrink-0">
                                            <div class="d-flex flex-column gap-1">
                                                <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>"
                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>"
                                                   class="btn btn-sm btn-outline-success" title="Update Checklist">
                                                    <i class="fas fa-clipboard-check"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if (count($businesses) > 5): ?>
                            <div class="text-center mt-3">
                                <a href="<?= BASE_URL ?>business" class="btn btn-outline-primary">
                                    View All <?= count($businesses) ?> Businesses
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-building text-muted mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mb-2">No Businesses Registered</h6>
                            <p class="text-muted mb-3">Start by registering your first business to access all features.</p>
                            <a href="<?= BASE_URL ?>business/create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Register Business
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>