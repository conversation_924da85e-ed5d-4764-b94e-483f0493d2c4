<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\InspectorAssignment;
use App\Models\User;
use App\Models\Business;
use App\Models\District;
use App\Models\Notification;

class InspectorAssignmentController extends Controller {
    private $assignmentModel;
    private $userModel;
    private $businessModel;
    private $districtModel;
    private $notificationModel;
    private $db;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->assignmentModel = new InspectorAssignment();
        $this->userModel = new User();
        $this->businessModel = new Business();
        $this->districtModel = new District();
        $this->notificationModel = new Notification();

        // Get database connection for direct queries
        $database = new \App\Config\Database();
        $this->db = $database->getConnection();
    }

    public function index() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $assignments = $this->assignmentModel->getAllAssignments();
        $stats = $this->assignmentModel->getAssignmentStats();
        $unassignedInspectors = $this->assignmentModel->getUnassignedInspectors();
        $districts = $this->businessModel->getDistricts();

        return $this->render('admin/inspector_assignments/index', [
            'title' => 'Inspector District Assignments',
            'active_page' => 'inspector_assignments',
            'assignments' => $assignments,
            'stats' => $stats,
            'districts' => $districts,
            'unassigned_inspectors' => $unassignedInspectors,
            'user' => $this->auth->getUser()
        ]);
    }

    public function assignToDistrict($districtId = null) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$districtId) {
            $_SESSION['error'] = 'District ID is required.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        $district = $this->districtModel->find($districtId);
        if (!$district) {
            $_SESSION['error'] = 'District not found.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        if ($this->isPost()) {
            $inspectorIds = $_POST['inspector_ids'] ?? [];
            $notes = $_POST['notes'] ?? '';

            if (empty($inspectorIds)) {
                $_SESSION['error'] = 'Please select at least one inspector.';
                $this->redirect('admin/inspector-assignments/assign/' . $districtId);
                return;
            }

            if (!is_array($inspectorIds)) {
                $inspectorIds = [$inspectorIds];
            }

            $assignedBy = $this->auth->getUserId();
            $successCount = 0;

            foreach ($inspectorIds as $inspectorId) {
                if ($this->assignmentModel->assignInspectorToDistrict($inspectorId, $districtId, $assignedBy, $notes)) {
                    $successCount++;
                    
                    // Create notification for inspector
                    $this->notificationModel->create([
                        'user_id' => $inspectorId,
                        'title' => 'District Assignment',
                        'message' => "You have been assigned to {$district['name']} for inspections.",
                        'type' => 'info',
                        'related_id' => $districtId,
                        'related_type' => 'district',
                        'action_url' => 'inspector/assignments'
                    ]);
                }
            }

            if ($successCount > 0) {
                $_SESSION['success'] = "Successfully assigned {$successCount} inspector(s) to {$district['name']}.";
            } else {
                $_SESSION['error'] = 'Failed to assign inspectors.';
            }

            $this->redirect('admin/inspector-assignments');
            return;
        }

        $availableInspectors = $this->assignmentModel->getAvailableInspectorsForDistrict($districtId);
        $currentInspectors = $this->assignmentModel->getDistrictInspectors($districtId);

        return $this->render('admin/inspector_assignments/assign', [
            'title' => 'Assign Inspectors to ' . $district['name'],
            'active_page' => 'inspector_assignments',
            'district' => $district,
            'available_inspectors' => $availableInspectors,
            'current_inspectors' => $currentInspectors,
            'user' => $this->auth->getUser()
        ]);
    }

    public function removeFromDistrict() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        $inspectorId = $_POST['inspector_id'] ?? '';
        $districtId = $_POST['district_id'] ?? '';

        if (empty($inspectorId) || empty($districtId)) {
            $_SESSION['error'] = 'Inspector and district are required.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        if ($this->assignmentModel->removeInspectorFromDistrict($inspectorId, $districtId)) {
            // Get inspector and district names for notification
            $inspector = $this->userModel->find($inspectorId);
            $district = $this->districtModel->find($districtId);

            if ($inspector && $district) {
                // Create notification for inspector
                $this->notificationModel->create([
                    'user_id' => $inspectorId,
                    'title' => 'District Assignment Removed',
                    'message' => "Your assignment to {$district['name']} has been removed.",
                    'type' => 'warning',
                    'related_id' => $districtId,
                    'related_type' => 'district'
                ]);
            }

            $_SESSION['success'] = 'Inspector removed from district successfully.';
        } else {
            $_SESSION['error'] = 'Failed to remove inspector from district.';
        }

        $this->redirect('admin/inspector-assignments');
    }

    public function viewDistrict($districtId) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $district = $this->districtModel->find($districtId);
        if (!$district) {
            $_SESSION['error'] = 'District not found.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        $inspectors = $this->assignmentModel->getDistrictInspectors($districtId);
        $businesses = $this->businessModel->getByDistrict($districtId);

        return $this->render('admin/inspector_assignments/district', [
            'title' => $district['name'] . ' - Inspector Assignments',
            'active_page' => 'inspector_assignments',
            'district' => $district,
            'inspectors' => $inspectors,
            'businesses' => $businesses,
            'user' => $this->auth->getUser()
        ]);
    }

    public function viewInspector($inspectorId) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $inspector = $this->userModel->find($inspectorId);
        if (!$inspector || $inspector['role'] !== 'inspector') {
            $_SESSION['error'] = 'Inspector not found.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        $districts = $this->assignmentModel->getInspectorDistricts($inspectorId);
        $workload = $this->assignmentModel->getInspectorWorkload($inspectorId);

        return $this->render('admin/inspector_assignments/inspector', [
            'title' => $inspector['full_name'] . ' - District Assignments',
            'active_page' => 'inspector_assignments',
            'inspector' => $inspector,
            'districts' => $districts,
            'workload' => $workload,
            'user' => $this->auth->getUser()
        ]);
    }



    public function integratedView() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        // Get all districts with error handling
        try {
            $districts = $this->districtModel->getAll();
            if (empty($districts)) {
                // Create default districts if none exist
                $this->createDefaultDistricts();
                $districts = $this->districtModel->getAll();
            }
        } catch (Exception $e) {
            error_log("Error getting districts: " . $e->getMessage());
            $districts = [];
        }

        // Get barangays - prioritize direct database query for reliability
        $barangays = [];
        $barangayLoadMethod = 'unknown';

        // Strategy 1: Direct database query (most reliable)
        try {
            // Use the business model's database connection
            $db = $this->businessModel->getDb();
            $query = "SELECT b.id, b.name, b.district_id,
                            COALESCE(d.name, 'No District') as district_name
                     FROM barangays b
                     LEFT JOIN districts d ON b.district_id = d.id
                     ORDER BY COALESCE(d.name, 'ZZZ'), b.name";
            $stmt = $db->query($query);
            $barangays = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            $barangayLoadMethod = 'Direct database query';

            // Log success for debugging
            error_log("Successfully loaded " . count($barangays) . " barangays via direct query");
        } catch (Exception $e) {
            error_log("Direct database query failed: " . $e->getMessage());
        }

        // Strategy 2: Try Barangay model if direct query failed
        if (empty($barangays)) {
            try {
                $barangayModel = new \App\Models\Barangay();
                $barangays = $barangayModel->getWithDistrict();
                $barangayLoadMethod = 'Barangay->getWithDistrict()';

                // If empty, try without district join
                if (empty($barangays)) {
                    $barangays = $barangayModel->getAll();
                    $barangayLoadMethod = 'Barangay->getAll() + manual district lookup';

                    // Add district info manually
                    foreach ($barangays as &$barangay) {
                        if (isset($barangay['district_id']) && $barangay['district_id']) {
                            $district = $this->districtModel->find($barangay['district_id']);
                            $barangay['district_name'] = $district ? $district['name'] : 'Unknown District';
                        } else {
                            $barangay['district_name'] = 'No District';
                            $barangay['district_id'] = null;
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("Barangay model failed: " . $e->getMessage());
            }
        }

        // Strategy 3: Try Business model if still empty
        if (empty($barangays)) {
            try {
                $barangays = $this->businessModel->getBarangays();
                $barangayLoadMethod = 'Business->getBarangays()';
            } catch (Exception $e) {
                error_log("Business model getBarangays failed: " . $e->getMessage());
            }
        }

        // Strategy 4: Use districts as barangays ONLY if absolutely nothing else works
        if (empty($barangays)) {
            error_log("WARNING: Falling back to districts as barangays - this should not happen if database is properly set up");
            foreach ($districts as $district) {
                $barangays[] = [
                    'id' => $district['id'],
                    'name' => $district['name'],
                    'district_id' => $district['id'],
                    'district_name' => $district['name']
                ];
            }
            $barangayLoadMethod = 'Districts as barangays fallback';
        }

        // Get all inspectors
        $allInspectors = $this->userModel->getByRole('inspector');

        // Get all scheduled inspections with details
        $inspectionModel = new \App\Models\Inspection();
        $allInspections = $inspectionModel->getAllWithDetails();

        // Get inspector assignments by district
        $inspectorAssignments = [];
        foreach ($districts as $district) {
            $inspectorAssignments[$district['id']] = $this->assignmentModel->getDistrictInspectors($district['id']);
        }

        // Get businesses by barangay for quick reference
        $businessesByBarangay = [];
        foreach ($barangays as $barangay) {
            $businessesByBarangay[$barangay['id']] = $this->businessModel->getByBarangay($barangay['id']);
        }

        // Get statistics
        $stats = [
            'total_inspectors' => count($allInspectors),
            'total_barangays' => count($barangays),
            'total_businesses' => $this->businessModel->countAll(),
            'pending_inspections' => $inspectionModel->countByStatus('scheduled'),
            'ongoing_inspections' => $inspectionModel->countByStatus('in_progress'),
            'completed_inspections' => $inspectionModel->countByStatus('completed')
        ];



        return $this->render('admin/inspector_assignments/integrated', [
            'title' => 'Integrated Inspector & Inspection Management',
            'active_page' => 'inspector_assignments',
            'districts' => $districts,
            'barangays' => $barangays,
            'inspectors' => $allInspectors,
            'inspections' => $allInspections,
            'inspector_assignments' => $inspectorAssignments,
            'businesses_by_barangay' => $businessesByBarangay,
            'stats' => $stats,
            'barangay_load_method' => $barangayLoadMethod,
            'user' => $this->auth->getUser()
        ]);
    }

    public function scheduleInspection() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        $businessId = $_POST['business_id'] ?? '';
        $inspectorId = $_POST['inspector_id'] ?? '';
        $scheduledDate = $_POST['scheduled_date'] ?? '';
        $inspectionType = $_POST['inspection_type'] ?? 'routine';
        $notes = $_POST['notes'] ?? '';

        if (empty($businessId) || empty($inspectorId) || empty($scheduledDate)) {
            $_SESSION['error'] = 'Business, inspector, and date are required.';
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        // Check for scheduling conflicts
        $conflicts = $this->checkSingleInspectionConflicts($businessId, $inspectorId, $scheduledDate);
        if (!empty($conflicts)) {
            $_SESSION['error'] = 'Scheduling conflict detected: ' . implode(', ', $conflicts);
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        // Create inspection
        $inspectionModel = new \App\Models\Inspection();
        $data = [
            'business_id' => $businessId,
            'inspector_id' => $inspectorId,
            'assigned_by' => $this->auth->getUserId(),
            'scheduled_date' => $scheduledDate,
            'inspection_type' => $inspectionType,
            'notes' => $notes,
            'status' => 'scheduled'
        ];

        if ($inspectionModel->create($data)) {
            // Update queue status if this business was in the queue
            $queueModel = new \App\Models\BusinessInspectionQueue();
            $queueModel->markBusinessAsScheduled($businessId);

            $_SESSION['success'] = 'Inspection scheduled successfully.';
        } else {
            $_SESSION['error'] = 'Failed to schedule inspection.';
        }

        $this->redirect('admin/inspector-assignments/integrated');
    }

    public function scheduleBarangayInspections() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        $barangayIds = $_POST['barangay_ids'] ?? [];
        $inspectorIds = $_POST['inspector_ids'] ?? [];
        $scheduledDate = $_POST['scheduled_date'] ?? '';
        $inspectionType = $_POST['inspection_type'] ?? 'routine';
        $notes = $_POST['notes'] ?? '';

        // Validation
        if (empty($barangayIds) || empty($inspectorIds) || empty($scheduledDate)) {
            $_SESSION['error'] = 'Barangays, inspectors, and scheduled date are required.';
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        // Check for conflicts
        $conflicts = $this->checkSchedulingConflicts($barangayIds, $inspectorIds, $scheduledDate);
        if (!empty($conflicts)) {
            $_SESSION['error'] = 'Scheduling conflicts detected: ' . implode(', ', $conflicts);
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        $inspectionModel = new \App\Models\Inspection();
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        // Get all businesses in selected barangays
        $allBusinesses = [];
        foreach ($barangayIds as $barangayId) {
            $businesses = $this->businessModel->getByBarangay($barangayId);
            $allBusinesses = array_merge($allBusinesses, $businesses);
        }

        if (empty($allBusinesses)) {
            $_SESSION['error'] = 'No businesses found in selected barangays.';
            $this->redirect('admin/inspector-assignments/integrated');
            return;
        }

        // Distribute businesses among inspectors
        $businessesPerInspector = $this->distributeBusinessesAmongInspectors($allBusinesses, $inspectorIds);

        foreach ($businessesPerInspector as $inspectorId => $businesses) {
            foreach ($businesses as $business) {
                try {
                    $data = [
                        'business_id' => $business['id'],
                        'inspector_id' => $inspectorId,
                        'assigned_by' => $this->auth->getUserId(),
                        'scheduled_date' => $scheduledDate,
                        'inspection_type' => $inspectionType,
                        'notes' => $notes,
                        'status' => 'scheduled'
                    ];

                    if ($inspectionModel->create($data)) {
                        $successCount++;

                        // Update queue status if this business was in the queue
                        $queueModel = new \App\Models\BusinessInspectionQueue();
                        $queueModel->markBusinessAsScheduled($business['id']);

                        // Create notification for inspector
                        $this->notificationModel->create([
                            'user_id' => $inspectorId,
                            'title' => 'New Inspection Scheduled',
                            'message' => "Inspection scheduled for {$business['name']} on {$scheduledDate}",
                            'type' => 'info',
                            'related_id' => $business['id'],
                            'related_type' => 'inspection',
                            'action_url' => 'inspector/inspections'
                        ]);
                    } else {
                        $errorCount++;
                        $errors[] = "Failed to schedule inspection for {$business['name']}";
                    }
                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "Error scheduling inspection for {$business['name']}: " . $e->getMessage();
                }
            }
        }

        // Set success/error messages
        if ($successCount > 0) {
            $_SESSION['success'] = "Successfully scheduled {$successCount} inspection(s) across " . count($barangayIds) . " barangay(s).";
            if ($errorCount > 0) {
                $_SESSION['success'] .= " {$errorCount} inspection(s) failed to schedule.";
            }
        } else {
            $_SESSION['error'] = "Failed to schedule any inspections. " . implode('; ', array_slice($errors, 0, 3));
        }

        $this->redirect('admin/inspector-assignments/integrated');
    }

    public function bulkScheduleInspections() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspector-assignments');
            return;
        }

        $districtId = $_POST['district_id'] ?? '';
        $businessIds = $_POST['business_ids'] ?? [];
        $inspectionType = $_POST['inspection_type'] ?? 'routine';
        $scheduledDate = $_POST['scheduled_date'] ?? '';
        $bulkNotes = $_POST['bulk_notes'] ?? '';
        $assignmentStrategy = $_POST['assignment_strategy'] ?? 'auto';
        $inspectorAssignments = $_POST['inspector_assignments'] ?? [];

        if (empty($districtId) || empty($businessIds) || empty($scheduledDate)) {
            $_SESSION['error'] = 'District, businesses, and scheduled date are required.';
            $this->redirect('admin/inspector-assignments/district/' . $districtId);
            return;
        }

        // Check for conflicts before proceeding
        $conflicts = $this->checkBulkSchedulingConflicts($businessIds, $inspectorAssignments, $scheduledDate, $assignmentStrategy);
        if (!empty($conflicts)) {
            $_SESSION['error'] = 'Scheduling conflicts detected: ' . implode('; ', array_slice($conflicts, 0, 3)) . (count($conflicts) > 3 ? ' and ' . (count($conflicts) - 3) . ' more...' : '');
            $this->redirect('admin/inspector-assignments/district/' . $districtId);
            return;
        }

        // Get assigned inspectors for this district
        $assignedInspectors = $this->assignmentModel->getDistrictInspectors($districtId);

        if (empty($assignedInspectors)) {
            $_SESSION['error'] = 'No inspectors assigned to this district. Please assign inspectors first.';
            $this->redirect('admin/inspector-assignments/district/' . $districtId);
            return;
        }

        $inspectionModel = new \App\Models\Inspection();
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        foreach ($businessIds as $businessId) {
            try {
                // Determine inspector assignment
                $inspectorId = null;

                if ($assignmentStrategy === 'manual' && isset($inspectorAssignments[$businessId]) && !empty($inspectorAssignments[$businessId])) {
                    $inspectorId = $inspectorAssignments[$businessId];
                } else {
                    // Auto-assign: get optimal inspector for this business
                    $optimalInspector = $this->assignmentModel->getOptimalInspectorForBusiness($businessId);
                    $inspectorId = $optimalInspector ? $optimalInspector['id'] : $assignedInspectors[0]['inspector_id'];
                }

                // Create inspection
                $data = [
                    'business_id' => $businessId,
                    'inspector_id' => $inspectorId,
                    'assigned_by' => $this->auth->getUserId(),
                    'scheduled_date' => $scheduledDate,
                    'inspection_type' => $inspectionType,
                    'notes' => $bulkNotes,
                    'status' => 'scheduled'
                ];

                if ($inspectionModel->create($data)) {
                    $successCount++;

                    // Update queue status if this business was in the queue
                    $queueModel = new \App\Models\BusinessInspectionQueue();
                    $queueModel->markBusinessAsScheduled($businessId);
                } else {
                    $errorCount++;
                    $errors[] = "Failed to schedule inspection for business ID: $businessId";
                }
            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "Error scheduling inspection for business ID $businessId: " . $e->getMessage();
                error_log("Bulk scheduling error: " . $e->getMessage());
            }
        }

        // Set success/error messages
        if ($successCount > 0) {
            $_SESSION['success'] = "Successfully scheduled $successCount inspection(s).";
            if ($errorCount > 0) {
                $_SESSION['success'] .= " $errorCount inspection(s) failed to schedule.";
            }
        } else {
            $_SESSION['error'] = "Failed to schedule any inspections. " . implode('; ', $errors);
        }

        $this->redirect('admin/inspector-assignments/district/' . $districtId);
    }





    private function checkSingleInspectionConflicts($businessId, $inspectorId, $scheduledDate) {
        $conflicts = [];
        $inspectionModel = new \App\Models\Inspection();

        // Check if inspector is already scheduled on this date
        $existingInspections = $inspectionModel->getByInspectorAndDate($inspectorId, $scheduledDate);
        if (!empty($existingInspections)) {
            $inspector = $this->userModel->find($inspectorId);
            $conflicts[] = "Inspector {$inspector['full_name']} already has " . count($existingInspections) . " inspection(s) scheduled on " . date('M d, Y', strtotime($scheduledDate));
        }

        // Check if business already has inspection scheduled on this date
        $existingBusinessInspections = $inspectionModel->getByBusinessAndDate($businessId, $scheduledDate);
        if (!empty($existingBusinessInspections)) {
            $business = $this->businessModel->getById($businessId);
            $conflicts[] = "Business '{$business['name']}' already has an inspection scheduled on " . date('M d, Y', strtotime($scheduledDate));
        }

        // Check for exact duplicate (same business + same inspector + same date)
        $exactDuplicates = $inspectionModel->getByBusinessInspectorAndDate($businessId, $inspectorId, $scheduledDate);
        if (!empty($exactDuplicates)) {
            $business = $this->businessModel->getById($businessId);
            $inspector = $this->userModel->find($inspectorId);
            $conflicts[] = "DUPLICATE DETECTED: Inspector {$inspector['full_name']} is already assigned to inspect '{$business['name']}' on " . date('M d, Y', strtotime($scheduledDate));
        }

        return $conflicts;
    }

    private function checkSchedulingConflicts($barangayIds, $inspectorIds, $scheduledDate) {
        $conflicts = [];
        $inspectionModel = new \App\Models\Inspection();

        // Check if inspectors are already scheduled on this date
        foreach ($inspectorIds as $inspectorId) {
            $existingInspections = $inspectionModel->getByInspectorAndDate($inspectorId, $scheduledDate);
            if (!empty($existingInspections)) {
                $inspector = $this->userModel->find($inspectorId);
                $conflicts[] = "Inspector {$inspector['full_name']} already has " . count($existingInspections) . " inspection(s) on " . date('M d, Y', strtotime($scheduledDate));
            }
        }

        // Check if businesses in barangays already have inspections scheduled
        foreach ($barangayIds as $barangayId) {
            $businesses = $this->businessModel->getByBarangay($barangayId);
            foreach ($businesses as $business) {
                $existingInspections = $inspectionModel->getByBusinessAndDate($business['id'], $scheduledDate);
                if (!empty($existingInspections)) {
                    $barangay = $this->businessModel->getBarangayById($barangayId);
                    $conflicts[] = "Business {$business['name']} in {$barangay['name']} already has inspection scheduled on " . date('M d, Y', strtotime($scheduledDate));
                    break; // Only report one conflict per barangay to avoid spam
                }
            }
        }

        return $conflicts;
    }

    private function checkBulkSchedulingConflicts($businessIds, $inspectorAssignments, $scheduledDate, $assignmentStrategy) {
        $conflicts = [];
        $inspectionModel = new \App\Models\Inspection();

        // Get unique inspector IDs based on assignment strategy
        $inspectorIds = [];
        if ($assignmentStrategy === 'manual') {
            $inspectorIds = array_unique(array_values($inspectorAssignments));
        } else {
            // For auto assignment, get all available inspectors in the district
            $districtInspectors = $this->assignmentModel->getDistrictInspectors($businessIds[0] ?? '');
            $inspectorIds = array_column($districtInspectors, 'inspector_id');
        }

        // Check inspector conflicts
        foreach ($inspectorIds as $inspectorId) {
            if (empty($inspectorId)) continue;

            $existingInspections = $inspectionModel->getByInspectorAndDate($inspectorId, $scheduledDate);
            if (!empty($existingInspections)) {
                $inspector = $this->userModel->find($inspectorId);
                $conflicts[] = "Inspector {$inspector['full_name']} already has " . count($existingInspections) . " inspection(s) on " . date('M d, Y', strtotime($scheduledDate));
            }
        }

        // Check business conflicts
        foreach ($businessIds as $businessId) {
            $existingInspections = $inspectionModel->getByBusinessAndDate($businessId, $scheduledDate);
            if (!empty($existingInspections)) {
                $business = $this->businessModel->getById($businessId);
                $conflicts[] = "Business '{$business['name']}' already has an inspection scheduled on " . date('M d, Y', strtotime($scheduledDate));
            }
        }

        return $conflicts;
    }

    public function checkConflicts() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $inspectorId = $_POST['inspector_id'] ?? '';
        $businessId = $_POST['business_id'] ?? '';
        $scheduledDate = $_POST['scheduled_date'] ?? '';

        if (empty($inspectorId) || empty($scheduledDate)) {
            echo json_encode(['error' => 'Inspector and date are required']);
            return;
        }

        $conflicts = [];
        $inspectionModel = new \App\Models\Inspection();

        // Check inspector conflicts
        $existingInspections = $inspectionModel->getByInspectorAndDate($inspectorId, $scheduledDate);
        if (!empty($existingInspections)) {
            $inspector = $this->userModel->find($inspectorId);
            $conflicts[] = [
                'type' => 'inspector',
                'message' => "Inspector {$inspector['full_name']} already has " . count($existingInspections) . " inspection(s) on " . date('M d, Y', strtotime($scheduledDate)),
                'count' => count($existingInspections)
            ];
        }

        // Check business conflicts if business ID is provided
        if (!empty($businessId)) {
            $existingBusinessInspections = $inspectionModel->getByBusinessAndDate($businessId, $scheduledDate);
            if (!empty($existingBusinessInspections)) {
                $business = $this->businessModel->getById($businessId);
                $conflicts[] = [
                    'type' => 'business',
                    'message' => "Business '{$business['name']}' already has an inspection scheduled on " . date('M d, Y', strtotime($scheduledDate)),
                    'count' => count($existingBusinessInspections)
                ];
            }
        }

        header('Content-Type: application/json');
        echo json_encode([
            'conflicts' => $conflicts,
            'hasConflicts' => !empty($conflicts)
        ]);
    }

    private function distributeBusinessesAmongInspectors($businesses, $inspectorIds) {
        $distribution = [];
        $inspectorCount = count($inspectorIds);

        // Initialize arrays for each inspector
        foreach ($inspectorIds as $inspectorId) {
            $distribution[$inspectorId] = [];
        }

        // Distribute businesses evenly
        foreach ($businesses as $index => $business) {
            $inspectorIndex = $index % $inspectorCount;
            $inspectorId = $inspectorIds[$inspectorIndex];
            $distribution[$inspectorId][] = $business;
        }

        return $distribution;
    }

    private function createDefaultDistricts() {
        try {
            // Create default districts for Bacoor City
            $defaultDistricts = [
                [
                    'id' => $this->generateUUID(),
                    'name' => 'District 1 - Bacoor West',
                    'code' => 'D1',
                    'description' => 'First Legislative District of Bacoor (73 barangays)'
                ],
                [
                    'id' => $this->generateUUID(),
                    'name' => 'District 2 - Bacoor East',
                    'code' => 'D2',
                    'description' => 'Second Legislative District of Bacoor (3 barangays)'
                ]
            ];

            foreach ($defaultDistricts as $district) {
                $this->districtModel->create($district);
            }

            $_SESSION['info'] = 'Default districts have been created for the system.';
        } catch (Exception $e) {
            error_log("Error creating default districts: " . $e->getMessage());
        }
    }

    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    public function getBarangayBusinesses($barangayId) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        header('Content-Type: application/json');

        if (!$barangayId) {
            echo json_encode(['success' => false, 'message' => 'Barangay ID is required']);
            return;
        }

        try {
            $businesses = $this->businessModel->getByBarangay($barangayId);
            echo json_encode(['success' => true, 'data' => $businesses]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error fetching businesses: ' . $e->getMessage()]);
        }
    }

    public function inspectionAssignments() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $inspectionModel = new \App\Models\Inspection();

        // Get all inspections with assignment details
        $inspections = $inspectionModel->getAllWithDetails();

        // Get all inspectors for assignment options
        $inspectors = $this->userModel->getByRole('inspector');

        // Get inspector workload statistics
        $inspectorWorkloads = [];
        foreach ($inspectors as $inspector) {
            $workload = $inspectionModel->getInspectorWorkload($inspector['id']);
            $inspectorWorkloads[$inspector['id']] = $workload;
        }

        // Get unassigned inspections
        $unassignedInspections = $inspectionModel->getUnassignedInspections();

        // Get overdue inspections
        $overdueInspections = $inspectionModel->getOverdueInspections();

        // Get assignment statistics
        $stats = [
            'total_inspections' => count($inspections),
            'assigned_inspections' => count(array_filter($inspections, fn($i) => !empty($i['inspector_id']))),
            'unassigned_inspections' => count($unassignedInspections),
            'overdue_inspections' => count($overdueInspections),
            'total_inspectors' => count($inspectors),
            'active_inspectors' => count(array_filter($inspectors, fn($i) => $i['status'] === 'active'))
        ];

        return $this->render('admin/inspector_assignments/inspection_assignments', [
            'title' => 'Inspection Assignments',
            'active_page' => 'inspection_assignments',
            'inspections' => $inspections,
            'inspectors' => $inspectors,
            'inspector_workloads' => $inspectorWorkloads,
            'unassigned_inspections' => $unassignedInspections,
            'overdue_inspections' => $overdueInspections,
            'stats' => $stats,
            'user' => $this->auth->getUser()
        ]);
    }

    public function reassignInspection() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspection-assignments');
            return;
        }

        $inspectionId = $_POST['inspection_id'] ?? '';
        $newInspectorId = $_POST['new_inspector_id'] ?? '';
        $reason = $_POST['reason'] ?? '';

        if (empty($inspectionId) || empty($newInspectorId)) {
            $_SESSION['error'] = 'Inspection ID and new inspector are required.';
            $this->redirect('admin/inspection-assignments');
            return;
        }

        $inspectionModel = new \App\Models\Inspection();

        // Get current inspection details
        $inspection = $inspectionModel->getById($inspectionId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspection-assignments');
            return;
        }

        // Get old and new inspector details
        $oldInspector = $inspection['inspector_id'] ? $this->userModel->find($inspection['inspector_id']) : null;
        $newInspector = $this->userModel->find($newInspectorId);

        if (!$newInspector) {
            $_SESSION['error'] = 'New inspector not found.';
            $this->redirect('admin/inspection-assignments');
            return;
        }

        try {
            // Update inspection assignment
            $updateData = [
                'inspector_id' => $newInspectorId,
                'assigned_by' => $this->auth->getUserId(),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($inspectionModel->update($inspectionId, $updateData)) {
                // Log the reassignment
                $this->logReassignment($inspectionId, $inspection['inspector_id'], $newInspectorId, $reason);

                // Notify old inspector (if exists)
                if ($oldInspector) {
                    $this->notificationModel->create([
                        'user_id' => $oldInspector['id'],
                        'title' => 'Inspection Reassigned',
                        'message' => "Inspection for {$inspection['business_name']} has been reassigned to another inspector.",
                        'type' => 'warning',
                        'related_id' => $inspectionId,
                        'related_type' => 'inspection'
                    ]);
                }

                // Notify new inspector
                $this->notificationModel->create([
                    'user_id' => $newInspectorId,
                    'title' => 'New Inspection Assignment',
                    'message' => "You have been assigned to inspect {$inspection['business_name']} on {$inspection['scheduled_date']}.",
                    'type' => 'info',
                    'related_id' => $inspectionId,
                    'related_type' => 'inspection',
                    'action_url' => 'inspector/inspections'
                ]);

                $_SESSION['success'] = "Inspection successfully reassigned to {$newInspector['full_name']}.";
            } else {
                $_SESSION['error'] = 'Failed to reassign inspection.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error reassigning inspection: ' . $e->getMessage();
        }

        $this->redirect('admin/inspection-assignments');
    }

    private function logReassignment($inspectionId, $oldInspectorId, $newInspectorId, $reason) {
        $db = (new \App\Config\Database())->getConnection();

        $query = "INSERT INTO inspection_assignment_logs
                  (inspection_id, old_inspector_id, new_inspector_id, reassigned_by, reason, created_at)
                  VALUES (?, ?, ?, ?, ?, NOW())";

        try {
            $stmt = $db->prepare($query);
            $stmt->execute([$inspectionId, $oldInspectorId, $newInspectorId, $this->auth->getUserId(), $reason]);
        } catch (Exception $e) {
            // Log error but don't fail the reassignment
            error_log("Failed to log reassignment: " . $e->getMessage());
        }
    }
}
