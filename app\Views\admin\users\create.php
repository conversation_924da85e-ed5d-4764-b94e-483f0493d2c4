<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Create New User</h1>
        <a href="<?= BASE_URL ?>admin/users" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="<?= BASE_URL ?>admin/users/store" method="POST" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= isset($_SESSION['old']['email']) ? htmlspecialchars($_SESSION['old']['email']) : '' ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generate_password" name="generate_password" value="1">
                                <label class="form-check-label" for="generate_password">
                                    <strong>Generate Random Password</strong>
                                    <small class="text-muted d-block">System will create a secure random password</small>
                                </label>
                            </div>
                        </div>

                        <div id="manual_password_section">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="invalid-feedback">
                                    Please enter a password.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password_confirm" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password_confirm" required>
                                <div class="invalid-feedback">
                                    Passwords do not match.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?= isset($_SESSION['old']['full_name']) ? htmlspecialchars($_SESSION['old']['full_name']) : '' ?>" 
                                   required>
                            <div class="invalid-feedback">
                                Please enter the full name.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="admin" <?= isset($_SESSION['old']['role']) && $_SESSION['old']['role'] === 'admin' ? 'selected' : '' ?>>Admin</option>
                                <option value="inspector" <?= isset($_SESSION['old']['role']) && $_SESSION['old']['role'] === 'inspector' ? 'selected' : '' ?>>Inspector</option>
                                <option value="business_owner" <?= isset($_SESSION['old']['role']) && $_SESSION['old']['role'] === 'business_owner' ? 'selected' : '' ?>>Business Owner</option>
                            </select>
                            <div class="invalid-feedback">
                                Please select a role.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Notification Section -->
                <div class="row g-3 mt-3">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-envelope me-2"></i>Email Notification Options
                                </h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1" checked>
                                    <label class="form-check-label" for="send_email">
                                        <strong>Send Welcome Email with Login Credentials</strong>
                                        <small class="text-muted d-block">
                                            User will receive an email with their login details and instructions.
                                            <br><strong>Note:</strong> This will send the password via email, so ensure the email address is correct.
                                        </small>
                                    </label>
                                </div>

                                <div class="alert alert-info mt-3 mb-0" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Recommended Workflow:</strong>
                                    <ol class="mb-0 mt-2">
                                        <li>Check "Generate Random Password" for security</li>
                                        <li>Check "Send Welcome Email" to notify the user</li>
                                        <li>User will receive login credentials via email</li>
                                        <li>User should change password on first login</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Create User
                    </button>
                    <a href="<?= BASE_URL ?>admin/users" class="btn btn-secondary ms-2">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Password generation toggle
document.getElementById('generate_password').addEventListener('change', function() {
    var manualPasswordSection = document.getElementById('manual_password_section');
    var passwordField = document.getElementById('password');
    var confirmField = document.getElementById('password_confirm');
    var sendEmailCheckbox = document.getElementById('send_email');

    if (this.checked) {
        // Hide manual password fields
        manualPasswordSection.style.display = 'none';
        passwordField.required = false;
        confirmField.required = false;
        passwordField.value = '';
        confirmField.value = '';

        // Auto-check send email option
        sendEmailCheckbox.checked = true;

        // Show info message
        if (!document.getElementById('password_info')) {
            var infoDiv = document.createElement('div');
            infoDiv.id = 'password_info';
            infoDiv.className = 'alert alert-success mt-2';
            infoDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>A secure random password will be generated automatically.';
            manualPasswordSection.parentNode.insertBefore(infoDiv, manualPasswordSection.nextSibling);
        }
    } else {
        // Show manual password fields
        manualPasswordSection.style.display = 'block';
        passwordField.required = true;
        confirmField.required = true;

        // Remove info message
        var infoDiv = document.getElementById('password_info');
        if (infoDiv) {
            infoDiv.remove();
        }
    }
});

// Form validation
(function() {
    'use strict';
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            var generatePassword = document.getElementById('generate_password').checked;

            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            // Check if passwords match (only if not generating password)
            if (!generatePassword) {
                var password = document.getElementById('password');
                var confirm = document.getElementById('password_confirm');
                if (password.value !== confirm.value) {
                    confirm.setCustomValidity('Passwords do not match');
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    confirm.setCustomValidity('');
                }
            }

            form.classList.add('was-validated');
        }, false);
    });
})();

// Password confirmation validation
document.getElementById('password_confirm').addEventListener('input', function() {
    var password = document.getElementById('password');
    var confirm = document.getElementById('password_confirm');
    if (password.value !== confirm.value) {
        confirm.setCustomValidity('Passwords do not match');
    } else {
        confirm.setCustomValidity('');
    }
});

// Email notification info
document.getElementById('send_email').addEventListener('change', function() {
    if (!this.checked) {
        if (confirm('Are you sure you don\'t want to send a welcome email? The user won\'t receive their login credentials.')) {
            // User confirmed
        } else {
            this.checked = true;
        }
    }
});
</script>

<?php 
// Clear old form data
unset($_SESSION['old']); 
$this->endSection(); 
?> 