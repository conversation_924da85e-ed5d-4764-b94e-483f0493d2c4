<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-tie text-primary me-2"></i>
            Business Owner Profile - <?= htmlspecialchars($user['full_name']) ?>
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/users/<?= $user['id'] ?>/edit" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit User
            </a>
            <a href="<?= BASE_URL ?>admin/users" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Business Owner Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Owner Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-lg bg-success bg-opacity-10 text-success mx-auto mb-3">
                            <?= strtoupper(substr($user['full_name'], 0, 2)) ?>
                        </div>
                        <h5 class="mb-1"><?= htmlspecialchars($user['full_name']) ?></h5>
                        <p class="text-muted mb-0"><?= htmlspecialchars($user['email']) ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">User ID</h6>
                        <p class="mb-0"><code><?= htmlspecialchars($user['id']) ?></code></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Role</h6>
                        <span class="badge bg-success">Business Owner</span>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                            <?= ucfirst($user['status']) ?>
                        </span>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Member Since</h6>
                        <p class="mb-0"><?= date('M d, Y', strtotime($user['created_at'])) ?></p>
                    </div>

                    <?php if (!empty($user['last_login'])): ?>
                    <div class="mb-3">
                        <h6 class="text-muted">Last Login</h6>
                        <p class="mb-0"><?= date('M d, Y h:i A', strtotime($user['last_login'])) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Business Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-pie me-2"></i>Business Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body py-2">
                                    <div class="text-white-50 small">Total Businesses</div>
                                    <div class="h5 font-weight-bold"><?= $business_stats['total_businesses'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body py-2">
                                    <div class="text-white-50 small">Active</div>
                                    <div class="h5 font-weight-bold"><?= $business_stats['active_businesses'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body py-2">
                                    <div class="text-white-50 small">Compliant</div>
                                    <div class="h5 font-weight-bold"><?= $business_stats['compliant_businesses'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body py-2">
                                    <div class="text-white-50 small">Inspections</div>
                                    <div class="h5 font-weight-bold"><?= $business_stats['total_inspections'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Owner Details -->
        <div class="col-lg-8">
            <!-- Owned Businesses -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-building me-2"></i>Owned Businesses
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($businesses)): ?>
                        <div class="row">
                            <?php foreach ($businesses as $business): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-info h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title text-info mb-1">
                                                    <i class="fas fa-store me-1"></i>
                                                    <?= htmlspecialchars($business['name']) ?>
                                                </h6>
                                                <span class="badge bg-<?= $business['status'] === 'active' ? 'success' : 'warning' ?>">
                                                    <?= ucfirst($business['status']) ?>
                                                </span>
                                            </div>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?= htmlspecialchars($business['barangay_name'] ?? 'N/A') ?>, 
                                                <?= htmlspecialchars($business['district_name'] ?? 'N/A') ?>
                                            </p>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-tag me-1"></i>
                                                <?= htmlspecialchars($business['category_name'] ?? 'N/A') ?>
                                            </p>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                Registered: <?= date('M d, Y', strtotime($business['created_at'])) ?>
                                            </small>
                                            <div class="mt-2">
                                                <a href="<?= BASE_URL ?>admin/businesses/view/<?= $business['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Businesses Registered</h5>
                            <p class="text-muted mb-3">This owner hasn't registered any businesses yet.</p>
                            <a href="<?= BASE_URL ?>admin/businesses/create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Register Business
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Inspections -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-list me-2"></i>Recent Inspections
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_inspections)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Business</th>
                                        <th>Inspector</th>
                                        <th>Location</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Score</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($recent_inspections, 0, 5) as $inspection): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($inspection['business_name']) ?></td>
                                            <td><?= htmlspecialchars($inspection['inspector_name'] ?? 'Not assigned') ?></td>
                                            <td><?= htmlspecialchars($inspection['barangay_name'] ?? 'N/A') ?></td>
                                            <td><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                                                    <?= ucfirst($inspection['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($inspection['score']): ?>
                                                    <?= $inspection['score'] ?>%
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="<?= BASE_URL ?>admin/inspections?owner=<?= $user['id'] ?>" class="btn btn-outline-primary">
                                View All Inspections
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No inspections found for this business owner.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.5rem;
}

.card.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
</style>
<?php $this->endSection() ?>
