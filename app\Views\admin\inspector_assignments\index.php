<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Inspector District Assignments</h1>
        <div>
            <a href="<?= BASE_URL ?>admin" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Admin
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <?php if (!empty($districts)): ?>
            <?php foreach ($districts as $district): ?>
                <?php
                // Count inspectors for this district
                $inspectorCount = 0;
                if (!empty($assignments)) {
                    foreach ($assignments as $assignment) {
                        if (isset($assignment['district_id']) && $assignment['district_id'] === $district['id']) {
                            $inspectorCount++;
                        }
                    }
                }
                ?>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        <?= htmlspecialchars($district['name']) ?>
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?= $inspectorCount ?> Inspector(s)
                                    </div>
                                    <div class="text-xs text-muted">
                                        District ID: <?= htmlspecialchars($district['id']) ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                            <div class="mt-2">
                                <a href="<?= BASE_URL ?>admin/inspector-assignments/assign/<?= $district['id'] ?>"
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Assign
                                </a>
                                <a href="<?= BASE_URL ?>admin/inspector-assignments/district/<?= $district['id'] ?>"
                                   class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No districts found. Please create districts first.
                </div>
            </div>
        <?php endif; ?>
    </div>

    <div class="row">
        <!-- Current Assignments -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>Current Assignments
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($assignments)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="assignmentsTable">
                                <thead>
                                    <tr>
                                        <th>Inspector</th>
                                        <th>District</th>
                                        <th>Assigned By</th>
                                        <th>Assigned Date</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assignments as $assignment): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-user-check text-info me-2"></i>
                                                    <div>
                                                        <strong><?= htmlspecialchars($assignment['inspector_name']) ?></strong>
                                                        <br><small class="text-muted"><?= htmlspecialchars($assignment['inspector_email']) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?= htmlspecialchars($assignment['district_name']) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($assignment['assigned_by_name']) ?></td>
                                            <td><?= date('M d, Y', strtotime($assignment['assigned_at'])) ?></td>
                                            <td>
                                                <?php if ($assignment['notes']): ?>
                                                    <span class="text-muted" title="<?= htmlspecialchars($assignment['notes']) ?>">
                                                        <?= htmlspecialchars(substr($assignment['notes'], 0, 30)) ?>
                                                        <?= strlen($assignment['notes']) > 30 ? '...' : '' ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= BASE_URL ?>admin/inspector-assignments/inspector/<?= $assignment['inspector_id'] ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View Inspector">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= BASE_URL ?>admin/inspector-assignments/district/<?= $assignment['district_id'] ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="View District">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="removeAssignment('<?= $assignment['inspector_id'] ?>', '<?= $assignment['district_id'] ?>', '<?= htmlspecialchars($assignment['inspector_name']) ?>', '<?= htmlspecialchars($assignment['district_name']) ?>')"
                                                            title="Remove Assignment">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Assignments Yet</h5>
                            <p class="text-muted">Start by assigning inspectors to districts.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Unassigned Inspectors -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-user-clock me-2"></i>Unassigned Inspectors
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($unassigned_inspectors)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($unassigned_inspectors as $inspector): ?>
                                <div class="list-group-item px-0 py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= htmlspecialchars($inspector['full_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspector['email']) ?></small>
                                        </div>
                                        <div>
                                            <a href="<?= BASE_URL ?>admin/inspector-assignments/inspector/<?= $inspector['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-plus"></i> Assign
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">All inspectors are assigned!</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/users/create?role=inspector" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> Add New Inspector
                        </a>
                        <a href="<?= BASE_URL ?>admin/inspections" class="btn btn-primary">
                            <i class="fas fa-clipboard-list"></i> Manage Inspections
                        </a>
                        <a href="<?= BASE_URL ?>admin/businesses" class="btn btn-info">
                            <i class="fas fa-building"></i> View Businesses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Assignment Modal -->
<div class="modal fade" id="removeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Assignment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove <strong id="inspectorName"></strong> from <strong id="districtName"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="removeForm" method="POST" action="<?= BASE_URL ?>admin/inspector-assignments/remove" style="display: inline;">
                    <input type="hidden" id="removeInspectorId" name="inspector_id">
                    <input type="hidden" id="removeDistrictId" name="district_id">
                    <button type="submit" class="btn btn-danger">Remove Assignment</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function removeAssignment(inspectorId, districtId, inspectorName, districtName) {
    document.getElementById('inspectorName').textContent = inspectorName;
    document.getElementById('districtName').textContent = districtName;
    document.getElementById('removeInspectorId').value = inspectorId;
    document.getElementById('removeDistrictId').value = districtId;
    
    new bootstrap.Modal(document.getElementById('removeModal')).show();
}

// Initialize DataTable
$(document).ready(function() {
    $('#assignmentsTable').DataTable({
        "pageLength": 10,
        "order": [[ 3, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 5 }
        ]
    });
});
</script>
<?php $this->endSection() ?>
