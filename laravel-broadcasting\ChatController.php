<?php

namespace App\Http\Controllers;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\Business;
use App\Models\User;
use App\Events\MessageSent;
use App\Events\UserTyping;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;

class ChatController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Get or create chat room
     */
    public function getOrCreateChatRoom(Request $request)
    {
        $validated = $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'participant_id' => 'nullable|exists:users,id',
        ]);

        $user = Auth::user();
        $business = Business::findOrFail($validated['business_id']);

        // Check permissions
        if ($user->role === 'business_owner' && $business->owner_id !== $user->id) {
            abort(403, 'You can only access chat for your own business.');
        }

        // Find existing chat room or create new one
        $chatRoom = ChatRoom::where('business_id', $business->id)
            ->where(function($query) use ($user) {
                $query->where('business_owner_id', $user->id)
                      ->orWhere('admin_id', $user->id)
                      ->orWhere('inspector_id', $user->id);
            })
            ->first();

        if (!$chatRoom) {
            $chatRoom = $this->createChatRoom($business, $user, $validated['participant_id'] ?? null);
        }

        return response()->json([
            'chat_room' => $chatRoom->load(['business', 'businessOwner', 'admin', 'inspector']),
            'channel' => "chat.{$chatRoom->id}",
        ]);
    }

    /**
     * Send message
     */
    public function sendMessage(Request $request, ChatRoom $chatRoom)
    {
        $validated = $request->validate([
            'message' => 'required_without:attachment|string|max:1000',
            'attachment' => 'nullable|file|max:10240', // 10MB max
            'message_type' => 'in:text,file,image,system',
        ]);

        $user = Auth::user();

        // Check if user is participant
        if (!$chatRoom->hasParticipant($user)) {
            abort(403, 'You are not a participant in this chat room.');
        }

        $messageData = [
            'chat_room_id' => $chatRoom->id,
            'sender_id' => $user->id,
            'message_type' => $validated['message_type'] ?? 'text',
            'is_read' => false,
        ];

        // Handle file attachment
        if ($request->hasFile('attachment')) {
            $uploadResult = $this->fileUploadService->uploadChatAttachment(
                $request->file('attachment'),
                $chatRoom->id
            );

            $messageData['file_path'] = $uploadResult['file_path'];
            $messageData['message'] = $uploadResult['file_name'];
            $messageData['message_type'] = $this->fileUploadService->isImage($request->file('attachment')) ? 'image' : 'file';
        } else {
            $messageData['message'] = $validated['message'];
        }

        $message = ChatMessage::create($messageData);
        $message->load('sender');

        // Broadcast message to all participants
        broadcast(new MessageSent($message))->toOthers();

        // Mark chat room as active
        $chatRoom->update(['updated_at' => now()]);

        return response()->json([
            'message' => $message,
            'success' => true,
        ]);
    }

    /**
     * Get chat messages
     */
    public function getMessages(ChatRoom $chatRoom, Request $request)
    {
        $user = Auth::user();

        // Check if user is participant
        if (!$chatRoom->hasParticipant($user)) {
            abort(403, 'You are not a participant in this chat room.');
        }

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 50);

        $messages = ChatMessage::where('chat_room_id', $chatRoom->id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->paginate($limit, ['*'], 'page', $page);

        // Mark messages as read for current user
        ChatMessage::where('chat_room_id', $chatRoom->id)
            ->where('sender_id', '!=', $user->id)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return response()->json([
            'messages' => $messages->items(),
            'pagination' => [
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
                'total' => $messages->total(),
                'has_more' => $messages->hasMorePages(),
            ],
        ]);
    }

    /**
     * Handle typing indicator
     */
    public function typing(Request $request, ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user is participant
        if (!$chatRoom->hasParticipant($user)) {
            abort(403, 'You are not a participant in this chat room.');
        }

        $validated = $request->validate([
            'is_typing' => 'required|boolean',
        ]);

        // Broadcast typing status
        broadcast(new UserTyping($chatRoom->id, $user, $validated['is_typing']))->toOthers();

        return response()->json(['success' => true]);
    }

    /**
     * Get chat rooms for user
     */
    public function getChatRooms(Request $request)
    {
        $user = Auth::user();
        $query = ChatRoom::with(['business', 'businessOwner', 'admin', 'inspector', 'latestMessage.sender']);

        // Filter based on user role
        switch ($user->role) {
            case 'admin':
                $query->where('admin_id', $user->id);
                break;
            case 'inspector':
                $query->where('inspector_id', $user->id);
                break;
            case 'business_owner':
                $query->where('business_owner_id', $user->id);
                break;
        }

        $chatRooms = $query->where('status', 'active')
            ->orderBy('updated_at', 'desc')
            ->get();

        // Add unread count for each chat room
        $chatRooms->each(function ($chatRoom) use ($user) {
            $chatRoom->unread_count = $chatRoom->getUnreadCountFor($user);
        });

        return response()->json(['chat_rooms' => $chatRooms]);
    }

    /**
     * Mark chat room as read
     */
    public function markAsRead(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user is participant
        if (!$chatRoom->hasParticipant($user)) {
            abort(403, 'You are not a participant in this chat room.');
        }

        $chatRoom->markAsReadFor($user);

        return response()->json(['success' => true]);
    }

    /**
     * Get online users
     */
    public function getOnlineUsers(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Check if user is participant
        if (!$chatRoom->hasParticipant($user)) {
            abort(403, 'You are not a participant in this chat room.');
        }

        // Get participants
        $participants = collect($chatRoom->getParticipantsAttribute())
            ->filter()
            ->map(function ($participant) {
                return [
                    'id' => $participant->id,
                    'name' => $participant->full_name,
                    'role' => $participant->role,
                    'online' => $this->isUserOnline($participant->id),
                    'last_seen' => $participant->last_login,
                ];
            });

        return response()->json(['participants' => $participants]);
    }

    /**
     * Close chat room
     */
    public function closeChatRoom(ChatRoom $chatRoom)
    {
        $user = Auth::user();

        // Only admin can close chat rooms
        if ($user->role !== 'admin') {
            abort(403, 'Only administrators can close chat rooms.');
        }

        $chatRoom->update(['status' => 'closed']);

        // Send system message
        ChatMessage::create([
            'chat_room_id' => $chatRoom->id,
            'sender_id' => $user->id,
            'message' => 'Chat room has been closed by administrator.',
            'message_type' => 'system',
            'is_read' => false,
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Create new chat room
     */
    private function createChatRoom(Business $business, User $initiator, ?string $participantId = null): ChatRoom
    {
        $chatRoomData = [
            'business_id' => $business->id,
            'status' => 'active',
            'subject' => "Support for {$business->name}",
        ];

        // Set participants based on initiator role
        switch ($initiator->role) {
            case 'business_owner':
                $chatRoomData['business_owner_id'] = $initiator->id;
                // Auto-assign admin if specified
                if ($participantId) {
                    $participant = User::find($participantId);
                    if ($participant && $participant->role === 'admin') {
                        $chatRoomData['admin_id'] = $participant->id;
                    }
                }
                break;

            case 'admin':
                $chatRoomData['admin_id'] = $initiator->id;
                $chatRoomData['business_owner_id'] = $business->owner_id;
                break;

            case 'inspector':
                $chatRoomData['inspector_id'] = $initiator->id;
                $chatRoomData['business_owner_id'] = $business->owner_id;
                break;
        }

        $chatRoom = ChatRoom::create($chatRoomData);

        // Send welcome message
        ChatMessage::create([
            'chat_room_id' => $chatRoom->id,
            'sender_id' => $initiator->id,
            'message' => 'Chat room created. How can we help you today?',
            'message_type' => 'system',
            'is_read' => false,
        ]);

        return $chatRoom;
    }

    /**
     * Check if user is online (simplified implementation)
     */
    private function isUserOnline(string $userId): bool
    {
        // This would typically check Redis or another cache store
        // For now, we'll check if user was active in last 5 minutes
        $user = User::find($userId);
        return $user && $user->last_login && $user->last_login->diffInMinutes(now()) < 5;
    }

    /**
     * Get chat statistics for admin
     */
    public function getChatStatistics()
    {
        $user = Auth::user();

        if ($user->role !== 'admin') {
            abort(403, 'Only administrators can view chat statistics.');
        }

        $stats = [
            'total_chat_rooms' => ChatRoom::count(),
            'active_chat_rooms' => ChatRoom::where('status', 'active')->count(),
            'total_messages' => ChatMessage::count(),
            'messages_today' => ChatMessage::whereDate('created_at', today())->count(),
            'average_response_time' => $this->calculateAverageResponseTime(),
            'busiest_hours' => $this->getBusiestHours(),
        ];

        return response()->json(['statistics' => $stats]);
    }

    /**
     * Calculate average response time
     */
    private function calculateAverageResponseTime(): float
    {
        // Simplified calculation - would need more complex logic for accurate results
        $recentMessages = ChatMessage::where('created_at', '>=', now()->subDays(7))
            ->orderBy('chat_room_id')
            ->orderBy('created_at')
            ->get();

        $responseTimes = [];
        $lastMessage = null;

        foreach ($recentMessages as $message) {
            if ($lastMessage && 
                $lastMessage->chat_room_id === $message->chat_room_id && 
                $lastMessage->sender_id !== $message->sender_id) {
                
                $responseTime = $message->created_at->diffInMinutes($lastMessage->created_at);
                if ($responseTime <= 60) { // Only count responses within 1 hour
                    $responseTimes[] = $responseTime;
                }
            }
            $lastMessage = $message;
        }

        return count($responseTimes) > 0 ? round(array_sum($responseTimes) / count($responseTimes), 2) : 0;
    }

    /**
     * Get busiest chat hours
     */
    private function getBusiestHours(): array
    {
        $hourlyStats = ChatMessage::selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        // Fill missing hours with 0
        $result = [];
        for ($i = 0; $i < 24; $i++) {
            $result[$i] = $hourlyStats[$i] ?? 0;
        }

        return $result;
    }
}
