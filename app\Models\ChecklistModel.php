<?php

namespace App\Models;

use App\Config\Database;
use PDO;

class ChecklistModel
{
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
    }
    /**
     * Get all categories with item counts for admin management
     */
    public function getAllCategoriesWithItems()
    {
        $query = "SELECT c.*,
                         COUNT(i.id) as item_count,
                         SUM(CASE WHEN i.is_critical = 1 THEN 1 ELSE 0 END) as critical_count
                  FROM inspection_checklist_categories c
                  LEFT JOIN inspection_checklist_items i ON c.id = i.category_id
                  GROUP BY c.id
                  ORDER BY c.sort_order ASC, c.name ASC";

        $stmt = $this->db->query($query);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get items for each category
        foreach ($categories as &$category) {
            $category['items'] = $this->getItemsByCategory($category['id']);
        }

        return $categories;
    }

    /**
     * Get items by category ID for admin management
     */
    public function getItemsByCategory($categoryId)
    {
        $query = "SELECT * FROM inspection_checklist_items
                  WHERE category_id = :category_id
                  ORDER BY sort_order ASC, item_name ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute([':category_id' => $categoryId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all categories (for dropdowns)
     */
    public function getAllCategories()
    {
        $query = "SELECT * FROM inspection_checklist_categories 
                  ORDER BY sort_order ASC, name ASC";
        
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get category by ID
     */
    public function getCategoryById($id)
    {
        $query = "SELECT * FROM inspection_checklist_categories WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new category
     */
    public function createCategory($data)
    {
        $query = "INSERT INTO inspection_checklist_categories 
                  (id, name, description, weight, sort_order, is_active, created_at) 
                  VALUES (UUID(), :name, :description, :weight, :sort_order, :is_active, NOW())";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':name' => $data['name'],
            ':description' => $data['description'],
            ':weight' => $data['weight'],
            ':sort_order' => $data['sort_order'],
            ':is_active' => $data['is_active']
        ]);
    }

    /**
     * Update category
     */
    public function updateCategory($id, $data)
    {
        $query = "UPDATE inspection_checklist_categories 
                  SET name = :name, description = :description, weight = :weight, 
                      sort_order = :sort_order, is_active = :is_active, updated_at = NOW()
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':name' => $data['name'],
            ':description' => $data['description'],
            ':weight' => $data['weight'],
            ':sort_order' => $data['sort_order'],
            ':is_active' => $data['is_active']
        ]);
    }

    /**
     * Delete category (and all its items)
     */
    public function deleteCategory($id)
    {
        // First delete all items in this category
        $deleteItems = "DELETE FROM inspection_checklist_items WHERE category_id = :id";
        $stmt = $this->db->prepare($deleteItems);
        $stmt->execute([':id' => $id]);

        // Then delete the category
        $deleteCategory = "DELETE FROM inspection_checklist_categories WHERE id = :id";
        $stmt = $this->db->prepare($deleteCategory);
        return $stmt->execute([':id' => $id]);
    }

    /**
     * Get checklist item by ID with category info
     */
    public function getItemById($id)
    {
        $query = "SELECT i.*, c.name as category_name 
                  FROM inspection_checklist_items i
                  LEFT JOIN inspection_checklist_categories c ON i.category_id = c.id
                  WHERE i.id = :id";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create new checklist item
     */
    public function createItem($data)
    {
        $query = "INSERT INTO inspection_checklist_items 
                  (id, category_id, item_code, item_name, description, compliance_requirement, 
                   points, is_critical, sort_order, is_active, created_at) 
                  VALUES (UUID(), :category_id, :item_code, :item_name, :description, 
                          :compliance_requirement, :points, :is_critical, :sort_order, :is_active, NOW())";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':category_id' => $data['category_id'],
            ':item_code' => $data['item_code'],
            ':item_name' => $data['item_name'],
            ':description' => $data['description'],
            ':compliance_requirement' => $data['compliance_requirement'],
            ':points' => $data['points'],
            ':is_critical' => $data['is_critical'],
            ':sort_order' => $data['sort_order'],
            ':is_active' => $data['is_active']
        ]);
    }

    /**
     * Update checklist item
     */
    public function updateItem($id, $data)
    {
        $query = "UPDATE inspection_checklist_items 
                  SET category_id = :category_id, item_code = :item_code, item_name = :item_name,
                      description = :description, compliance_requirement = :compliance_requirement,
                      points = :points, is_critical = :is_critical, sort_order = :sort_order,
                      is_active = :is_active, updated_at = NOW()
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':category_id' => $data['category_id'],
            ':item_code' => $data['item_code'],
            ':item_name' => $data['item_name'],
            ':description' => $data['description'],
            ':compliance_requirement' => $data['compliance_requirement'],
            ':points' => $data['points'],
            ':is_critical' => $data['is_critical'],
            ':sort_order' => $data['sort_order'],
            ':is_active' => $data['is_active']
        ]);
    }

    /**
     * Delete checklist item
     */
    public function deleteItem($id)
    {
        $query = "DELETE FROM inspection_checklist_items WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $id]);
    }

    /**
     * Toggle item active status
     */
    public function toggleItemStatus($id)
    {
        $query = "UPDATE inspection_checklist_items 
                  SET is_active = NOT is_active, updated_at = NOW() 
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $id]);
    }

    /**
     * Count critical items
     */
    public function countCriticalItems()
    {
        $query = "SELECT COUNT(*) FROM inspection_checklist_items 
                  WHERE is_critical = 1 AND is_active = 1";
        
        $stmt = $this->db->query($query);
        return $stmt->fetchColumn();
    }

    /**
     * Count active items
     */
    public function countActiveItems()
    {
        $query = "SELECT COUNT(*) FROM inspection_checklist_items WHERE is_active = 1";
        
        $stmt = $this->db->query($query);
        return $stmt->fetchColumn();
    }

    /**
     * Get all items with category info for admin listing
     */
    public function getAllItemsWithCategory()
    {
        $query = "SELECT i.*, c.name as category_name, c.sort_order as category_sort
                  FROM inspection_checklist_items i
                  LEFT JOIN inspection_checklist_categories c ON i.category_id = c.id
                  ORDER BY c.sort_order ASC, i.sort_order ASC, i.item_name ASC";
        
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Check if item code already exists (for validation)
     */
    public function itemCodeExists($itemCode, $excludeId = null)
    {
        $query = "SELECT COUNT(*) FROM inspection_checklist_items WHERE item_code = :item_code";
        $params = [':item_code' => $itemCode];
        
        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Get next sort order for category
     */
    public function getNextCategorySortOrder()
    {
        $query = "SELECT COALESCE(MAX(sort_order), 0) + 1 FROM inspection_checklist_categories";
        $stmt = $this->db->query($query);
        return $stmt->fetchColumn();
    }

    /**
     * Get next sort order for item in category
     */
    public function getNextItemSortOrder($categoryId)
    {
        $query = "SELECT COALESCE(MAX(sort_order), 0) + 1 
                  FROM inspection_checklist_items 
                  WHERE category_id = :category_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':category_id' => $categoryId]);
        return $stmt->fetchColumn();
    }
}
