<?php
require_once __DIR__ . '/../../layouts/header.php';
?>

<div class="container">
    <h1>Register New Business</h1>
    
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($_SESSION['error']); ?></div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <form action="/business/register" method="POST">
        <div class="form-group">
            <label for="name">Business Name</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="registration_number">Registration Number</label>
            <input type="text" id="registration_number" name="registration_number" required>
        </div>
        
        <div class="form-group">
            <label for="category_id">Business Category</label>
            <select id="category_id" name="category_id" required>
                <option value="">Select Category</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?= htmlspecialchars($category['id']); ?>">
                        <?= htmlspecialchars($category['name']); ?> (<?= htmlspecialchars($category['risk_level']); ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="district_id">District</label>
            <select id="district_id" name="district_id" required>
                <option value="">Select District</option>
                <?php foreach ($districts as $district): ?>
                    <option value="<?= htmlspecialchars($district['id']); ?>">
                        <?= htmlspecialchars($district['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="address">Business Address</label>
            <textarea id="address" name="address" rows="3" required></textarea>
        </div>
        
        <div class="form-group">
            <label for="contact_email">Contact Email</label>
            <input type="email" id="contact_email" name="contact_email" required>
        </div>
        
        <div class="form-group">
            <label for="contact_phone">Contact Phone</label>
            <input type="tel" id="contact_phone" name="contact_phone" required>
        </div>
        
        <div class="form-group">
            <label for="employee_count">Number of Employees</label>
            <input type="number" id="employee_count" name="employee_count" min="1" value="1" required>
        </div>
        
        <button type="submit" class="btn btn-primary">Register Business</button>
    </form>
</div>

<?php
require_once __DIR__ . '/../../layouts/footer.php';