<?php
require_once __DIR__ . '/../Config/Database.php';

class ReportDelivery {
    private $db;
    private $table = 'report_deliveries';

    public function __construct() {
        $this->db = (new Database())->connect();
    }

    public function logDelivery($scheduleId, $status, $details) {
        $query = "INSERT INTO {$this->table} 
                 (id, schedule_id, status, details, sent_at)
                 VALUES (:id, :schedule_id, :status, :details, NOW())";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            'id' => bin2hex(random_bytes(16)),
            'schedule_id' => $scheduleId,
            'status' => $status,
            'details' => json_encode($details)
        ]);
    }

    public function getDeliveryHistory($scheduleId = null) {
        $query = "SELECT d.*, s.name as schedule_name
                 FROM {$this->table} d
                 JOIN report_schedules s ON d.schedule_id = s.id";
        
        $params = [];
        if ($scheduleId) {
            $query .= " WHERE d.schedule_id = :schedule_id";
            $params['schedule_id'] = $scheduleId;
        }
        
        $query .= " ORDER BY d.sent_at DESC LIMIT 100";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON details
        return array_map(function($row) {
            $row['details'] = json_decode($row['details'], true);
            return $row;
        }, $results);
    }

    public function getTestHistory($scheduleId, $limit = 20) {
        $query = "SELECT * FROM report_deliveries 
                WHERE schedule_id = :schedule_id 
                AND status = 'test'
                ORDER BY sent_at DESC
                LIMIT :limit";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':schedule_id', $scheduleId, PDO::PARAM_STR);
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return array_map(function($row) {
            $row['details'] = json_decode($row['details'], true);
            return $row;
        }, $results);
    }

    public function logTest($scheduleId, $recipientEmail, $status, $message = '') {
        return $this->logDelivery($scheduleId, 'test', [
            'recipient' => $recipientEmail,
            'status' => $status,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }


}