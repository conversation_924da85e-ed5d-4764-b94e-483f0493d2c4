-- Update Bacoor City barangays with accurate current data (47 barangays total)
-- Based on official PSA data and current administrative divisions

-- Clear existing barangay data
DELETE FROM barangays;

-- Insert District 1 (Bacoor West) - 32 barangays
INSERT INTO barangays (id, name, district_id, created_at) VALUES
-- District 1 - Bacoor West (32 barangays)
(UUID(), 'Alima', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Aniban 1', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Aniban 2', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Aniban 4', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Aniban 5', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Banalo', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Camposanto', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Daang-Bukid', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Dulong Bayan', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Kaingin Digman', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Habay I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Habay II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Ligas I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Ligas II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Mabolo', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Maliksi I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Maliksi II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Niog', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu III', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu IV', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu V', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu VI', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu VII', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'P.F. Espiritu VIII', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Real', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Salinas I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Salinas II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'San Nicolas I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'San Nicolas II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'San Nicolas III', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Sineguelasan', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Tabing-Dagat', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Talaba I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Talaba II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Talaba III', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Zapote I', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Zapote II', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),
(UUID(), 'Zapote III', (SELECT id FROM districts WHERE name = 'District 1 - Bacoor West'), NOW()),

-- District 2 - Bacoor East (15 barangays)
(UUID(), 'Bayanan', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Mambog 1', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Mambog 2', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Mambog 3', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Mambog 4', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino I', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino II', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino III', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino IV', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino V', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino VI', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Molino VII', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Queens Row Central', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Queens Row East', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW()),
(UUID(), 'Queens Row West', (SELECT id FROM districts WHERE name = 'District 2 - Bacoor East'), NOW());

-- Verify the count
SELECT 
    d.name as district_name,
    COUNT(b.id) as barangay_count
FROM districts d
LEFT JOIN barangays b ON d.id = b.district_id
GROUP BY d.id, d.name
ORDER BY d.name;
