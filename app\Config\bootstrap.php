<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', ROOT_PATH . '/storage/logs/php_error.log');

// Create logs directory if it doesn't exist
if (!file_exists(ROOT_PATH . '/storage/logs')) {
    mkdir(ROOT_PATH . '/storage/logs', 0777, true);
}

// Load configuration
require_once ROOT_PATH . '/app/Config/config.php';

// Load autoloader
require_once ROOT_PATH . '/app/Core/Autoloader.php';
$autoloader = new App\Core\Autoloader();
$autoloader->register();

// Configure session settings
ini_set('session.cookie_lifetime', SESSION_LIFETIME);
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
ini_set('session.cookie_path', SESSION_PATH);
ini_set('session.cookie_domain', SESSION_DOMAIN);
ini_set('session.cookie_secure', SESSION_SECURE);
ini_set('session.cookie_httponly', SESSION_HTTP_ONLY);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Register autoloader for app classes
spl_autoload_register(function ($className) {
    // Convert namespace to full file path
    $file = ROOT_PATH . '/' . str_replace('\\', '/', $className) . '.php';
      // If the file exists, require it
      if (file_exists($file)) {
          require_once $file;
          return true;
      }
      
// Log error if file not found
    error_log("Class file not found: " . $file);
    return false;
});// Register autoloader for app classes
/*
spl_autoload_register(function ($className) {
    // Convert namespace to full file path
    $file = ROOT_PATH . '/' . str_replace('\\', '/', $className) . '.php';

    // If the file exists, require it
    if (file_exists($file)) {
        require_once $file; // Changed from require to require_once
        return true;
    }

    // Log error if file not found
    error_log("Class file not found: " . $file);
    return false;
});
*/
