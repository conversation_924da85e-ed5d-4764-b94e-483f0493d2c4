<?php

namespace App\Models;

use App\Core\Model;
use PDO;

class BusinessChecklistEvidence extends Model
{
    protected $table = 'business_checklist_evidence';
    protected $fillable = [
        'id', 'business_id', 'checklist_item_id', 'inspection_id', 'file_path', 'file_name',
        'file_type', 'file_size', 'notes', 'status', 'uploaded_by', 'reviewed_by',
        'reviewed_at', 'review_notes', 'created_at', 'updated_at'
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get evidence by business ID
     */
    public function getByBusinessId($businessId)
    {
        $query = "SELECT bce.*, ici.item_name, ici.item_code, ici.description as item_description,
                         icc.name as category_name, u.full_name as uploaded_by_name,
                         r.full_name as reviewed_by_name
                  FROM business_checklist_evidence bce
                  LEFT JOIN inspection_checklist_items ici ON bce.checklist_item_id = ici.id
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  LEFT JOIN users u ON bce.uploaded_by = u.id
                  LEFT JOIN users r ON bce.reviewed_by = r.id
                  WHERE bce.business_id = :business_id
                  ORDER BY icc.sort_order ASC, ici.sort_order ASC, bce.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':business_id' => $businessId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get evidence by business ID and checklist item ID
     */
    public function getByBusinessAndItem($businessId, $checklistItemId)
    {
        $query = "SELECT bce.*, ici.item_name, ici.item_code, ici.description as item_description,
                         icc.name as category_name, u.full_name as uploaded_by_name,
                         r.full_name as reviewed_by_name
                  FROM business_checklist_evidence bce
                  LEFT JOIN inspection_checklist_items ici ON bce.checklist_item_id = ici.id
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  LEFT JOIN users u ON bce.uploaded_by = u.id
                  LEFT JOIN users r ON bce.reviewed_by = r.id
                  WHERE bce.business_id = :business_id AND bce.checklist_item_id = :checklist_item_id
                  ORDER BY bce.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            ':business_id' => $businessId,
            ':checklist_item_id' => $checklistItemId
        ]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get evidence by inspection ID
     */
    public function getByInspectionId($inspectionId)
    {
        $query = "SELECT bce.*, ici.item_name, ici.item_code, ici.description as item_description,
                         icc.name as category_name, u.full_name as uploaded_by_name,
                         r.full_name as reviewed_by_name
                  FROM business_checklist_evidence bce
                  LEFT JOIN inspection_checklist_items ici ON bce.checklist_item_id = ici.id
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  LEFT JOIN users u ON bce.uploaded_by = u.id
                  LEFT JOIN users r ON bce.reviewed_by = r.id
                  WHERE bce.inspection_id = :inspection_id
                  ORDER BY icc.sort_order ASC, ici.sort_order ASC, bce.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':inspection_id' => $inspectionId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Upload evidence for a checklist item
     */
    public function uploadEvidence($data)
    {
        $data['id'] = $this->generateUUID();
        $data['status'] = 'pending';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->create($data);
    }

    /**
     * Review evidence (for inspectors/admins)
     */
    public function reviewEvidence($evidenceId, $reviewerId, $status, $reviewNotes = '')
    {
        $query = "UPDATE business_checklist_evidence 
                  SET status = :status, 
                      reviewed_by = :reviewed_by,
                      reviewed_at = NOW(),
                      review_notes = :review_notes,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $evidenceId,
            ':status' => $status,
            ':reviewed_by' => $reviewerId,
            ':review_notes' => $reviewNotes
        ]);
    }

    /**
     * Get evidence statistics for a business
     */
    public function getBusinessEvidenceStats($businessId)
    {
        $query = "SELECT
                    COUNT(*) as total_evidence,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'reviewed' THEN 1 END) as reviewed_count,
                    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count
                  FROM business_checklist_evidence
                  WHERE business_id = :business_id";

        $stmt = $this->db->prepare($query);
        $stmt->execute([':business_id' => $businessId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Count all evidence
     */
    public function countAll()
    {
        $query = "SELECT COUNT(*) FROM business_checklist_evidence";
        return $this->db->query($query)->fetchColumn();
    }

    /**
     * Count evidence by status
     */
    public function countByStatus($status)
    {
        $query = "SELECT COUNT(*) FROM business_checklist_evidence WHERE status = :status";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':status' => $status]);
        return $stmt->fetchColumn();
    }

    /**
     * Get checklist items with evidence status for a business
     */
    public function getChecklistWithEvidenceStatus($businessId)
    {
        $query = "SELECT ici.*, icc.name as category_name, icc.sort_order as category_sort,
                         COUNT(bce.id) as evidence_count,
                         MAX(bce.status) as latest_status,
                         MAX(bce.created_at) as latest_upload
                  FROM inspection_checklist_items ici
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  LEFT JOIN business_checklist_evidence bce ON (ici.id = bce.checklist_item_id AND bce.business_id = :business_id)
                  WHERE ici.is_active = 1 AND icc.is_active = 1
                  GROUP BY ici.id, icc.id
                  ORDER BY icc.sort_order ASC, ici.sort_order ASC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':business_id' => $businessId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Delete evidence file
     */
    public function deleteEvidence($evidenceId)
    {
        // First get the file path to delete the physical file
        $evidence = $this->find($evidenceId);
        if ($evidence && file_exists($evidence['file_path'])) {
            unlink($evidence['file_path']);
        }

        return $this->delete($evidenceId);
    }

    /**
     * Get evidence for inspector checklist view
     */
    public function getEvidenceForInspection($inspectionId, $checklistItemId)
    {
        $query = "SELECT bce.*, u.full_name as uploaded_by_name
                  FROM business_checklist_evidence bce
                  LEFT JOIN users u ON bce.uploaded_by = u.id
                  LEFT JOIN inspections i ON bce.business_id = i.business_id
                  WHERE i.id = :inspection_id AND bce.checklist_item_id = :checklist_item_id
                  ORDER BY bce.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            ':inspection_id' => $inspectionId,
            ':checklist_item_id' => $checklistItemId
        ]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all evidence for admin review
     */
    public function getAllForReview($status = null)
    {
        $whereClause = $status ? "WHERE bce.status = :status" : "";
        
        $query = "SELECT bce.*, ici.item_name, ici.item_code, 
                         icc.name as category_name, b.name as business_name,
                         u.full_name as uploaded_by_name, r.full_name as reviewed_by_name
                  FROM business_checklist_evidence bce
                  LEFT JOIN inspection_checklist_items ici ON bce.checklist_item_id = ici.id
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  LEFT JOIN businesses b ON bce.business_id = b.id
                  LEFT JOIN users u ON bce.uploaded_by = u.id
                  LEFT JOIN users r ON bce.reviewed_by = r.id
                  $whereClause
                  ORDER BY bce.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        if ($status) {
            $stmt->execute([':status' => $status]);
        } else {
            $stmt->execute();
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
