<?php
namespace App\Models;

use App\Core\Model;

class InspectorAssignment extends Model {
    protected $table = 'inspector_district_assignments';
    protected $fillable = ['inspector_id', 'district_id', 'assigned_by', 'status'];

    public function assignInspectorToDistrict($inspectorId, $districtId, $assignedBy) {
        // Check if assignment already exists
        $existing = $this->whereFirst('inspector_id', $inspectorId);
        if ($existing && $existing['district_id'] == $districtId) {
            // Update status to active
            return $this->update($existing['id'], [
                'status' => 'active',
                'assigned_by' => $assignedBy
            ]);
        }

        // Create new assignment
        return $this->create([
            'inspector_id' => $inspectorId,
            'district_id' => $districtId,
            'assigned_by' => $assignedBy,
            'status' => 'active'
        ]);
    }

    public function removeInspectorFromDistrict($inspectorId, $districtId) {
        $query = "UPDATE inspector_district_assignments
                  SET status = 'inactive'
                  WHERE inspector_id = ? AND district_id = ?";
        $stmt = $this->query($query, [$inspectorId, $districtId]);
        return $stmt->rowCount() > 0;
    }

    public function getInspectorDistricts($inspectorId) {
        $query = "SELECT ida.*, d.name as district_name, d.code as district_code, u.full_name as assigned_by_name
                  FROM inspector_district_assignments ida
                  LEFT JOIN districts d ON ida.district_id = d.id
                  LEFT JOIN users u ON ida.assigned_by = u.id
                  WHERE ida.inspector_id = ? AND ida.status = 'active'
                  ORDER BY d.name ASC";
        $stmt = $this->query($query, [$inspectorId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getDistrictInspectors($districtId) {
        $query = "SELECT ida.*, u.id, u.full_name, u.email, ida.assigned_at,
                         assigner.full_name as assigned_by_name
                  FROM inspector_district_assignments ida
                  LEFT JOIN users u ON ida.inspector_id = u.id
                  LEFT JOIN users assigner ON ida.assigned_by = assigner.id
                  WHERE ida.district_id = ? AND ida.status = 'active'
                  AND u.full_name IS NOT NULL AND u.full_name != ''
                  ORDER BY u.full_name ASC";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getAllAssignments() {
        $query = "SELECT ida.*,
                         u.full_name as inspector_name, u.email as inspector_email,
                         d.name as district_name, d.code as district_code,
                         assigner.full_name as assigned_by_name
                  FROM inspector_district_assignments ida
                  LEFT JOIN users u ON ida.inspector_id = u.id
                  LEFT JOIN districts d ON ida.district_id = d.id
                  LEFT JOIN users assigner ON ida.assigned_by = assigner.id
                  WHERE ida.status = 'active'
                  ORDER BY d.name ASC, u.full_name ASC";
        $stmt = $this->query($query);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getInspectorsForDistrict($districtId) {
        $query = "SELECT u.id, u.full_name, u.email
                  FROM users u
                  JOIN inspector_district_assignments ida ON u.id = ida.inspector_id
                  WHERE ida.district_id = ? AND ida.status = 'active' AND u.role = 'inspector'
                  ORDER BY u.full_name ASC";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function getUnassignedInspectors() {
        try {
            $query = "SELECT u.id, u.full_name, u.email
                      FROM users u
                      WHERE u.role = 'inspector'
                      AND (u.status = 'active' OR u.status IS NULL)
                      AND u.full_name IS NOT NULL AND u.full_name != ''
                      AND u.id NOT IN (
                          SELECT DISTINCT inspector_id
                          FROM inspector_district_assignments
                          WHERE is_active = 1
                      )
                      ORDER BY u.full_name ASC";

            $stmt = $this->db->query($query);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getUnassignedInspectors: " . $e->getMessage());
            return [];
        }
    }

    public function getAvailableInspectorsForDistrict($districtId) {
        try {
            $query = "SELECT u.id, u.full_name, u.email
                      FROM users u
                      WHERE u.role = 'inspector'
                      AND (u.status = 'active' OR u.status IS NULL)
                      AND u.full_name IS NOT NULL AND u.full_name != ''
                      AND u.id NOT IN (
                          SELECT inspector_id
                          FROM inspector_district_assignments
                          WHERE district_id = ? AND is_active = 1
                      )
                      ORDER BY u.full_name ASC";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$districtId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getAvailableInspectorsForDistrict: " . $e->getMessage());
            return [];
        }
    }

    public function bulkAssignInspectors($districtId, $inspectorIds, $assignedBy, $notes = null) {
        $success = true;
        
        foreach ($inspectorIds as $inspectorId) {
            if (!$this->assignInspectorToDistrict($inspectorId, $districtId, $assignedBy, $notes)) {
                $success = false;
            }
        }
        
        return $success;
    }

    public function getAssignmentStats() {
        try {
            $query = "SELECT
                        d.id as district_id,
                        d.name as district_name,
                        COUNT(ida.inspector_id) as inspector_count,
                        COUNT(DISTINCT b.id) as business_count
                      FROM districts d
                      LEFT JOIN inspector_district_assignments ida ON d.id = ida.district_id AND ida.status = 'active'
                      LEFT JOIN barangays brg ON d.id = brg.district_id
                      LEFT JOIN businesses b ON brg.id = b.barangay_id
                      GROUP BY d.id, d.name
                      ORDER BY d.name ASC";

            $stmt = $this->db->query($query);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getAssignmentStats: " . $e->getMessage());
            return [];
        }
    }

    public function getOverallStats() {
        try {
            $query = "SELECT
                        COUNT(DISTINCT u.id) as total_inspectors,
                        COUNT(DISTINCT d.id) as total_districts,
                        COUNT(DISTINCT b.id) as total_businesses,
                        COUNT(DISTINCT ida.id) as total_assignments
                      FROM users u
                      LEFT JOIN inspector_district_assignments ida ON u.id = ida.inspector_id AND ida.status = 'active'
                      LEFT JOIN districts d ON ida.district_id = d.id
                      LEFT JOIN barangays brg ON d.id = brg.district_id
                      LEFT JOIN businesses b ON brg.id = b.barangay_id
                      WHERE u.role = 'inspector'";

            $stmt = $this->db->query($query);
            return $stmt->fetch(\PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getOverallStats: " . $e->getMessage());
            return [
                'total_inspectors' => 0,
                'total_districts' => 0,
                'total_businesses' => 0,
                'total_assignments' => 0
            ];
        }
    }

    public function getInspectorWorkload($inspectorId) {
        try {
            $query = "SELECT
                        COUNT(DISTINCT ida.district_id) as assigned_districts,
                        COUNT(DISTINCT b.id) as businesses_in_districts,
                        COUNT(DISTINCT i.id) as total_inspections,
                        COUNT(DISTINCT CASE WHEN i.status IN ('scheduled', 'confirmed') THEN i.id END) as pending_inspections
                      FROM inspector_district_assignments ida
                      LEFT JOIN barangays brg ON ida.district_id = brg.district_id
                      LEFT JOIN businesses b ON brg.id = b.barangay_id
                      LEFT JOIN inspections i ON b.id = i.business_id AND i.inspector_id = ?
                      WHERE ida.inspector_id = ? AND ida.status = 'active'";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$inspectorId, $inspectorId]);
            return $stmt->fetch(\PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getInspectorWorkload: " . $e->getMessage());
            return [
                'assigned_districts' => 0,
                'businesses_in_districts' => 0,
                'total_inspections' => 0,
                'pending_inspections' => 0
            ];
        }
    }

    public function isInspectorAssignedToDistrict($inspectorId, $districtId) {
        try {
            $query = "SELECT COUNT(*) as count
                      FROM inspector_district_assignments
                      WHERE inspector_id = ? AND district_id = ? AND status = 'active'";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$inspectorId, $districtId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Error in isInspectorAssignedToDistrict: " . $e->getMessage());
            return false;
        }
    }

    public function getOptimalInspectorForBusiness($businessId) {
        try {
            // Get business district through barangay
            $businessQuery = "SELECT brg.district_id
                             FROM businesses b
                             INNER JOIN barangays brg ON b.barangay_id = brg.id
                             WHERE b.id = ?";
            $stmt = $this->db->prepare($businessQuery);
            $stmt->execute([$businessId]);
            $business = $stmt->fetch(\PDO::FETCH_ASSOC);

            if (!$business || !$business['district_id']) {
                return null;
            }

            // Get inspectors assigned to this district, ordered by current workload
            $query = "SELECT u.id, u.full_name, u.email,
                             COUNT(DISTINCT i.id) as total_inspections,
                             COUNT(DISTINCT CASE WHEN i.status IN ('scheduled', 'confirmed') THEN i.id END) as pending_inspections,
                             'district_assigned' as assignment_reason
                      FROM inspector_district_assignments ida
                      JOIN users u ON ida.inspector_id = u.id
                      LEFT JOIN inspections i ON u.id = i.inspector_id
                      WHERE ida.district_id = ? AND ida.status = 'active' AND u.role = 'inspector'
                      GROUP BY u.id, u.full_name, u.email
                      ORDER BY pending_inspections ASC, total_inspections ASC, u.full_name ASC
                      LIMIT 1";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$business['district_id']]);
            return $stmt->fetch(\PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getOptimalInspectorForBusiness: " . $e->getMessage());
            return null;
        }
    }

    public function getPendingInspectionsForDistrict($districtId) {
        try {
            $query = "SELECT i.*, b.name as business_name, u.full_name as inspector_name
                      FROM inspections i
                      JOIN businesses b ON i.business_id = b.id
                      JOIN barangays brg ON b.barangay_id = brg.id
                      LEFT JOIN users u ON i.inspector_id = u.id
                      WHERE brg.district_id = ? AND i.status IN ('scheduled', 'confirmed')
                      ORDER BY i.scheduled_date ASC";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$districtId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getPendingInspectionsForDistrict: " . $e->getMessage());
            return [];
        }
    }

    public function getRecentInspectionsForDistrict($districtId) {
        try {
            $query = "SELECT i.*, b.name as business_name, u.full_name as inspector_name
                      FROM inspections i
                      JOIN businesses b ON i.business_id = b.id
                      JOIN barangays brg ON b.barangay_id = brg.id
                      LEFT JOIN users u ON i.inspector_id = u.id
                      WHERE brg.district_id = ? AND i.status = 'completed'
                      AND i.updated_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                      ORDER BY i.updated_at DESC";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$districtId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error in getRecentInspectionsForDistrict: " . $e->getMessage());
            return [];
        }
    }

    // New methods for barangay assignments
    public function assignInspectorToBarangay($inspectorId, $barangayId, $assignedBy) {
        $query = "INSERT INTO inspector_barangay_assignments
                  (inspector_id, barangay_id, assigned_by, status)
                  VALUES (?, ?, ?, 'active')
                  ON DUPLICATE KEY UPDATE
                  status = 'active', assigned_by = ?, assigned_at = CURRENT_TIMESTAMP";
        $stmt = $this->query($query, [$inspectorId, $barangayId, $assignedBy, $assignedBy]);
        return $stmt->rowCount() > 0;
    }

    public function getBusinessesInInspectorDistricts($inspectorId) {
        $query = "SELECT b.*, brg.name as barangay_name, d.name as district_name
                  FROM businesses b
                  INNER JOIN barangays brg ON b.barangay_id = brg.id
                  INNER JOIN districts d ON brg.district_id = d.id
                  INNER JOIN inspector_district_assignments ida ON d.id = ida.district_id
                  WHERE ida.inspector_id = ? AND ida.status = 'active'
                  ORDER BY d.name, brg.name, b.name";
        $stmt = $this->query($query, [$inspectorId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    public function canInspectorAccessBusiness($inspectorId, $businessId) {
        $query = "SELECT COUNT(*) as count
                  FROM businesses b
                  INNER JOIN barangays brg ON b.barangay_id = brg.id
                  INNER JOIN districts d ON brg.district_id = d.id
                  INNER JOIN inspector_district_assignments ida ON d.id = ida.district_id
                  WHERE b.id = ? AND ida.inspector_id = ? AND ida.status = 'active'";
        $stmt = $this->query($query, [$businessId, $inspectorId]);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        return $result['count'] > 0;
    }
}
