<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-shield text-primary me-2"></i>
            Inspector Profile - <?= htmlspecialchars($user['full_name']) ?>
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/users/<?= $user['id'] ?>/edit" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Inspector
            </a>
            <a href="<?= BASE_URL ?>admin/users" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Inspector Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Inspector Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-lg bg-primary bg-opacity-10 text-primary mx-auto mb-3">
                            <?= strtoupper(substr($user['full_name'], 0, 2)) ?>
                        </div>
                        <h5 class="mb-1"><?= htmlspecialchars($user['full_name']) ?></h5>
                        <p class="text-muted mb-0"><?= htmlspecialchars($user['email']) ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">User ID</h6>
                        <p class="mb-0"><code><?= htmlspecialchars($user['id']) ?></code></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Role</h6>
                        <span class="badge bg-primary">Inspector</span>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                            <?= ucfirst($user['status']) ?>
                        </span>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Member Since</h6>
                        <p class="mb-0"><?= date('M d, Y', strtotime($user['created_at'])) ?></p>
                    </div>

                    <?php if (!empty($user['last_login'])): ?>
                    <div class="mb-3">
                        <h6 class="text-muted">Last Login</h6>
                        <p class="mb-0"><?= date('M d, Y h:i A', strtotime($user['last_login'])) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Inspector Details -->
        <div class="col-lg-8">
            <!-- Barangay Assignments -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-map-marked-alt me-2"></i>Barangay Assignments
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($barangays)): ?>
                        <div class="row">
                            <?php foreach ($barangays as $assignment): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-left-primary h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title text-primary mb-1">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?= htmlspecialchars($assignment['barangay_name']) ?>
                                                </h6>
                                                <span class="badge bg-success">Active</span>
                                            </div>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-layer-group me-1"></i>
                                                <?= htmlspecialchars($assignment['district_name']) ?>
                                            </p>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>
                                                Assigned by: <?= htmlspecialchars($assignment['assigned_by_name'] ?? 'System') ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?= date('M d, Y', strtotime($assignment['assigned_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Barangay Assignments</h5>
                            <p class="text-muted mb-3">This inspector hasn't been assigned to any barangays yet.</p>
                            <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Assign to Barangays
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Workload Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Workload Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <div class="text-white-50 small">Assigned Barangays</div>
                                    <div class="text-lg font-weight-bold"><?= $workload['assigned_barangays'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <div class="text-white-50 small">Businesses</div>
                                    <div class="text-lg font-weight-bold"><?= $workload['businesses_in_barangays'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body">
                                    <div class="text-white-50 small">Pending Inspections</div>
                                    <div class="text-lg font-weight-bold"><?= $workload['pending_inspections'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <div class="text-white-50 small">Completed</div>
                                    <div class="text-lg font-weight-bold"><?= $workload['completed_inspections'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Inspections -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-list me-2"></i>Recent Inspections
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_inspections)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Business</th>
                                        <th>Location</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Score</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($recent_inspections, 0, 5) as $inspection): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($inspection['business_name']) ?></td>
                                            <td><?= htmlspecialchars($inspection['barangay_name'] ?? 'N/A') ?></td>
                                            <td><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                                                    <?= ucfirst($inspection['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($inspection['score']): ?>
                                                    <?= $inspection['score'] ?>%
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="<?= BASE_URL ?>admin/inspections?inspector=<?= $user['id'] ?>" class="btn btn-outline-primary">
                                View All Inspections
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No inspections found for this inspector.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.5rem;
}

.card.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
</style>
<?php $this->endSection() ?>
