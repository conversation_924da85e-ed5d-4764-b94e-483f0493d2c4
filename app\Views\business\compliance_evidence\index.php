<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= $title ?></h1>
        <div>
            <?php if (isset($business['id'])): ?>
                <a href="<?= BASE_URL ?>business" class="btn btn-outline-primary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to My Businesses
                </a>
                <?php // Updated the upload link to point to the new submission route ?>
                <a href="<?= BASE_URL ?>business/compliance/<?= $business['id'] ?>/upload" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Upload Evidence
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="card border-0 rounded-3 shadow-sm mb-4">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
            <h5 class="mb-0">Evidence Submissions for <?= htmlspecialchars($business['name'] ?? 'Your Business') ?></h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($evidence)): ?>
                <p class="text-center py-4">No compliance evidence submitted for this business yet.</p>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="ps-4">Compliance Type</th>
                                <th>Status</th>
                                <th>Uploaded At</th>
                                <th>Verified By</th>
                                <th>Verified At</th>
                                <th>Verification Notes</th>
                                <th class="pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($evidence as $item): ?>
                                <tr>
                                    <td class="ps-4"><?= htmlspecialchars($compliance_types[$item['compliance_type']] ?? $item['compliance_type'] ?? 'N/A') ?></td>
                                    <td>
                                        <?php if ($item['status'] === 'verified'): ?>
                                            <span class="badge bg-success fs-6">
                                                <i class="fas fa-check-circle me-1"></i>Admin Verified
                                            </span>
                                        <?php elseif ($item['status'] === 'rejected'): ?>
                                            <span class="badge bg-danger fs-6">
                                                <i class="fas fa-times-circle me-1"></i>Rejected
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark fs-6">
                                                <i class="fas fa-clock me-1"></i>Pending Review
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= isset($item['created_at']) ? date('M d, Y H:i', strtotime($item['created_at'])) : 'N/A' ?></td>
                                    <td><?= htmlspecialchars($item['verifier_name'] ?? 'N/A') ?></td>
                                    <td><?= isset($item['verified_at']) ? date('M d, Y H:i', strtotime($item['verified_at'])) : 'N/A' ?></td>
                                    <td><?= htmlspecialchars($item['verification_notes'] ?? 'N/A') ?></td>
                                    <td class="pe-4">
                                        <?php if (isset($item['photo_path'])): ?>
                                            <a href="<?= BASE_URL ?>public/serve-evidence-simple.php?file=<?= htmlspecialchars(basename($item['photo_path'])) ?>" class="btn btn-sm btn-outline-secondary me-1" target="_blank" title="View Photo">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php
                                            // Check if the current user is Admin, Inspector, or the owner of this business
                                            $canDelete = $auth->isAdmin() || $auth->isInspector() || ($auth->isBusinessOwner() && isset($business['owner_id']) && $business['owner_id'] === $auth->getUserId());
                                        ?>
                                        <?php if ($canDelete): ?>
                                            <form action="<?= BASE_URL ?>admin/compliance/delete/<?= $item['id'] ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this evidence?');">
                                                <button type="submit" class="btn btn-sm btn-danger" title="Delete Evidence">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>
