<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Business;
use App\Models\Inspection;
use App\Models\InspectionChecklistItem;
use App\Models\InspectionChecklistResponse;
use App\Models\BusinessChecklistEvidence;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Services\InspectionService;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\HasApiTokens;

class ApiController extends Controller
{
    protected $inspectionService;
    protected $fileUploadService;

    public function __construct(InspectionService $inspectionService, FileUploadService $fileUploadService)
    {
        $this->inspectionService = $inspectionService;
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * API Authentication
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', $validator->errors(), 422);
        }

        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            
            if ($user->status !== 'active') {
                return $this->errorResponse('Account is not active', [], 403);
            }

            $token = $user->createToken('OHS Mobile App')->plainTextToken;
            
            return $this->successResponse([
                'user' => $this->formatUser($user),
                'token' => $token,
                'token_type' => 'Bearer',
            ], 'Login successful');
        }

        return $this->errorResponse('Invalid credentials', [], 401);
    }

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        return $this->successResponse([], 'Logged out successfully');
    }

    public function me(Request $request)
    {
        return $this->successResponse([
            'user' => $this->formatUser($request->user())
        ]);
    }

    /**
     * Dashboard Data
     */
    public function getDashboardData(Request $request)
    {
        $user = $request->user();
        
        switch ($user->role) {
            case 'admin':
                return $this->getAdminDashboardData();
            case 'inspector':
                return $this->getInspectorDashboardData($user);
            case 'business_owner':
                return $this->getBusinessOwnerDashboardData($user);
            default:
                return $this->errorResponse('Invalid user role', [], 403);
        }
    }

    /**
     * Inspections API
     */
    public function getInspections(Request $request)
    {
        $user = $request->user();
        $query = Inspection::with(['business.barangay.district', 'inspector', 'assignedBy']);

        // Filter based on user role
        switch ($user->role) {
            case 'admin':
                // Admin can see all inspections
                break;
            case 'inspector':
                $query->where('inspector_id', $user->id);
                break;
            case 'business_owner':
                $business = Business::where('owner_id', $user->id)->first();
                if (!$business) {
                    return $this->errorResponse('No business found for user', [], 404);
                }
                $query->where('business_id', $business->id);
                break;
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
        }

        $inspections = $query->orderBy('scheduled_date', 'desc')
            ->paginate($request->get('per_page', 15));

        return $this->successResponse([
            'inspections' => $inspections->items(),
            'pagination' => [
                'current_page' => $inspections->currentPage(),
                'last_page' => $inspections->lastPage(),
                'total' => $inspections->total(),
                'per_page' => $inspections->perPage(),
            ]
        ]);
    }

    public function getInspection(Request $request, string $id)
    {
        $user = $request->user();
        $inspection = Inspection::with([
            'business.barangay.district',
            'business.category',
            'inspector',
            'assignedBy',
            'verifiedBy',
            'checklistResponses.checklistItem.category'
        ])->findOrFail($id);

        // Check permissions
        if (!$this->canAccessInspection($user, $inspection)) {
            return $this->errorResponse('Access denied', [], 403);
        }

        return $this->successResponse(['inspection' => $inspection]);
    }

    /**
     * Checklist API
     */
    public function getChecklistItems(Request $request, string $inspectionId)
    {
        $user = $request->user();
        $inspection = Inspection::findOrFail($inspectionId);

        if (!$this->canAccessInspection($user, $inspection)) {
            return $this->errorResponse('Access denied', [], 403);
        }

        $categories = \App\Models\InspectionChecklistCategory::with(['activeChecklistItems'])
            ->active()
            ->ordered()
            ->get();

        $existingResponses = InspectionChecklistResponse::where('inspection_id', $inspection->id)
            ->get()
            ->keyBy('checklist_item_id');

        return $this->successResponse([
            'categories' => $categories,
            'existing_responses' => $existingResponses,
            'inspection' => $inspection,
        ]);
    }

    public function submitChecklistResponse(Request $request, string $inspectionId)
    {
        $user = $request->user();
        $inspection = Inspection::findOrFail($inspectionId);

        // Only inspectors can submit checklist responses
        if ($user->role !== 'inspector' || $inspection->inspector_id !== $user->id) {
            return $this->errorResponse('Access denied', [], 403);
        }

        $validator = Validator::make($request->all(), [
            'checklist_item_id' => 'required|exists:inspection_checklist_items,id',
            'compliance_status' => 'required|in:compliant,needs_improvement,non_compliant,not_applicable',
            'notes' => 'nullable|string|max:1000',
            'corrective_action' => 'nullable|string|max:1000',
            'deadline' => 'nullable|date|after:today',
            'photo_evidence' => 'nullable|image|max:5120',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', $validator->errors(), 422);
        }

        $checklistItem = InspectionChecklistItem::findOrFail($request->checklist_item_id);
        
        // Calculate score
        $score = match($request->compliance_status) {
            'compliant' => $checklistItem->points,
            'needs_improvement' => round($checklistItem->points * 0.7),
            'non_compliant' => 0,
            'not_applicable' => $checklistItem->points,
        };

        $responseData = [
            'inspection_id' => $inspection->id,
            'checklist_item_id' => $request->checklist_item_id,
            'inspector_id' => $user->id,
            'compliance_status' => $request->compliance_status,
            'score' => $score,
            'notes' => $request->notes,
            'corrective_action' => $request->corrective_action,
            'deadline' => $request->deadline,
        ];

        // Handle photo evidence
        if ($request->hasFile('photo_evidence')) {
            $uploadResult = $this->fileUploadService->uploadInspectionPhoto(
                $request->file('photo_evidence'),
                $inspection->id,
                $request->checklist_item_id
            );
            $responseData['photo_evidence'] = $uploadResult['file_path'];
        }

        $response = InspectionChecklistResponse::updateOrCreate(
            [
                'inspection_id' => $inspection->id,
                'checklist_item_id' => $request->checklist_item_id,
            ],
            $responseData
        );

        return $this->successResponse(['response' => $response], 'Response saved successfully');
    }

    /**
     * Evidence API
     */
    public function uploadEvidence(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'business_owner') {
            return $this->errorResponse('Only business owners can upload evidence', [], 403);
        }

        $validator = Validator::make($request->all(), [
            'checklist_item_id' => 'required|exists:inspection_checklist_items,id',
            'evidence_file' => 'required|file|max:10240',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Validation failed', $validator->errors(), 422);
        }

        $business = Business::where('owner_id', $user->id)->first();
        if (!$business) {
            return $this->errorResponse('No business found for user', [], 404);
        }

        $uploadResult = $this->fileUploadService->uploadEvidenceFile(
            $request->file('evidence_file'),
            $business->id,
            $request->checklist_item_id
        );

        $evidence = BusinessChecklistEvidence::create([
            'business_id' => $business->id,
            'checklist_item_id' => $request->checklist_item_id,
            'file_path' => $uploadResult['file_path'],
            'file_name' => $uploadResult['file_name'],
            'file_type' => $uploadResult['file_type'],
            'file_size' => $uploadResult['file_size'],
            'notes' => $request->notes,
            'status' => 'pending',
        ]);

        return $this->successResponse(['evidence' => $evidence], 'Evidence uploaded successfully');
    }

    public function getBusinessEvidence(Request $request)
    {
        $user = $request->user();
        
        if ($user->role !== 'business_owner') {
            return $this->errorResponse('Access denied', [], 403);
        }

        $business = Business::where('owner_id', $user->id)->first();
        if (!$business) {
            return $this->errorResponse('No business found for user', [], 404);
        }

        $evidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->with('checklistItem.category')
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return $this->successResponse([
            'evidence' => $evidence->items(),
            'pagination' => [
                'current_page' => $evidence->currentPage(),
                'last_page' => $evidence->lastPage(),
                'total' => $evidence->total(),
                'per_page' => $evidence->perPage(),
            ]
        ]);
    }

    /**
     * Chat API
     */
    public function getChatRooms(Request $request)
    {
        $user = $request->user();
        $query = ChatRoom::with(['business', 'businessOwner', 'admin', 'inspector', 'latestMessage.sender']);

        switch ($user->role) {
            case 'admin':
                $query->where('admin_id', $user->id);
                break;
            case 'inspector':
                $query->where('inspector_id', $user->id);
                break;
            case 'business_owner':
                $query->where('business_owner_id', $user->id);
                break;
        }

        $chatRooms = $query->where('status', 'active')
            ->orderBy('updated_at', 'desc')
            ->get();

        return $this->successResponse(['chat_rooms' => $chatRooms]);
    }

    public function getChatMessages(Request $request, string $chatRoomId)
    {
        $user = $request->user();
        $chatRoom = ChatRoom::findOrFail($chatRoomId);

        if (!$chatRoom->hasParticipant($user)) {
            return $this->errorResponse('Access denied', [], 403);
        }

        $messages = ChatMessage::where('chat_room_id', $chatRoom->id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 50));

        return $this->successResponse([
            'messages' => $messages->items(),
            'pagination' => [
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
                'total' => $messages->total(),
                'per_page' => $messages->perPage(),
            ]
        ]);
    }

    /**
     * Helper Methods
     */
    private function successResponse(array $data, string $message = 'Success', int $code = 200)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
        ], $code);
    }

    private function errorResponse(string $message, array $errors = [], int $code = 400)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ], $code);
    }

    private function formatUser(User $user): array
    {
        return [
            'id' => $user->id,
            'email' => $user->email,
            'full_name' => $user->full_name,
            'role' => $user->role,
            'status' => $user->status,
            'last_login' => $user->last_login,
        ];
    }

    private function canAccessInspection(User $user, Inspection $inspection): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'inspector':
                return $inspection->inspector_id === $user->id;
            case 'business_owner':
                $business = Business::where('owner_id', $user->id)->first();
                return $business && $inspection->business_id === $business->id;
            default:
                return false;
        }
    }

    private function getAdminDashboardData(): \Illuminate\Http\JsonResponse
    {
        $stats = [
            'total_businesses' => Business::count(),
            'active_businesses' => Business::where('status', 'active')->count(),
            'total_inspections' => Inspection::count(),
            'completed_inspections' => Inspection::where('status', 'completed')->count(),
            'pending_verification' => Inspection::where('status', 'completed')
                ->where('verification_status', 'pending')->count(),
            'compliance_rate' => $this->getComplianceRate(),
        ];

        return $this->successResponse(['stats' => $stats]);
    }

    private function getInspectorDashboardData(User $inspector): \Illuminate\Http\JsonResponse
    {
        $stats = [
            'total_inspections' => Inspection::where('inspector_id', $inspector->id)->count(),
            'completed_inspections' => Inspection::where('inspector_id', $inspector->id)
                ->where('status', 'completed')->count(),
            'scheduled_inspections' => Inspection::where('inspector_id', $inspector->id)
                ->where('status', 'scheduled')->count(),
            'today_inspections' => Inspection::where('inspector_id', $inspector->id)
                ->whereDate('scheduled_date', today())->count(),
        ];

        return $this->successResponse(['stats' => $stats]);
    }

    private function getBusinessOwnerDashboardData(User $owner): \Illuminate\Http\JsonResponse
    {
        $business = Business::where('owner_id', $owner->id)->first();
        
        if (!$business) {
            return $this->errorResponse('No business found for user', [], 404);
        }

        $stats = [
            'business' => $business,
            'total_inspections' => Inspection::where('business_id', $business->id)->count(),
            'completed_inspections' => Inspection::where('business_id', $business->id)
                ->where('status', 'completed')->count(),
            'compliance_status' => $business->compliance_status,
            'evidence_uploaded' => BusinessChecklistEvidence::where('business_id', $business->id)->count(),
            'evidence_approved' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'approved')->count(),
        ];

        return $this->successResponse(['stats' => $stats]);
    }

    private function getComplianceRate(): float
    {
        $totalBusinesses = Business::where('status', 'active')->count();
        
        if ($totalBusinesses === 0) {
            return 0;
        }
        
        $compliantBusinesses = Business::where('status', 'active')
            ->where('compliance_status', 'compliant')
            ->count();
            
        return round(($compliantBusinesses / $totalBusinesses) * 100, 2);
    }
}
