/* OHS System - Main Stylesheet */

/* Base styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Layout */
.main-content {
    min-height: calc(100vh - 60px);
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
}

/* Sidebar */
.sidebar {
    min-height: calc(100vh - 56px);
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    transform: translateX(2px);
}

.sidebar .nav-link.active {
    font-weight: 600;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.form-control:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Dashboard specific */
.dashboard-card {
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
}

/* Chat specific */
.chat-messages {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.message-bubble {
    max-width: 70%;
    padding: 0.75rem;
    border-radius: 1rem;
    margin-bottom: 0.5rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* Utilities */
.text-muted {
    color: #6b7280 !important;
}

.border-radius-lg {
    border-radius: 0.75rem !important;
}

.shadow-sm {
    box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important;
}

.shadow-md {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
}

.shadow-lg {
    box-shadow: 0 10px 15px rgba(0,0,0,0.1) !important;
}

/* Print styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Dark mode support (future) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Performance optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* Error states */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success states */
.is-valid {
    border-color: #28a745 !important;
}

.valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
