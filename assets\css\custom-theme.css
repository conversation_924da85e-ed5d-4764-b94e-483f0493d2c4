:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
}

body {
    --primary-color: #2563eb !important;
    --secondary-color: #1e40af !important;
    --accent-color: #3b82f6 !important;
    --success-color: #059669 !important;
    --warning-color: #d97706 !important;
    --danger-color: #dc2626 !important;
    --dark-color: #1f2937 !important;
    --light-color: #f8fafc !important;
}


/* Primary elements */
.btn-primary, .bg-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Secondary elements */
.btn-secondary, .bg-secondary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

/* Success elements */
.btn-success, .bg-success, .alert-success {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

/* Warning elements */
.btn-warning, .bg-warning, .alert-warning {
    background-color: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
}

/* Danger elements */
.btn-danger, .bg-danger, .alert-danger {
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

/* Navigation */
.navbar-custom {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Feature icons */
.feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* Cards and buttons */
.btn-hero {
    color: var(--primary-color) !important;
}

.btn-hero:hover {
    color: var(--primary-color) !important;
}

/* Stats and highlights */
.stat-number {
    color: var(--primary-color) !important;
}

/* Homepage specific overrides */
.hero-section, .hero-section .bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.navbar-brand, .navbar-nav .nav-link {
    color: white !important;
}

/* Admin Dashboard specific overrides */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .nav-link:hover, .sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
    color: white !important;
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

/* Dashboard cards */
.dashboard-card {
    border-left: 4px solid var(--primary-color) !important;
}

.dashboard-card .card-body {
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(139, 92, 246, 0.05)) !important;
}

/* Table styling */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(124, 58, 237, 0.05) !important;
}

.table th {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--secondary-color) !important;
}

/* Badge styling */
.badge-success {
    background-color: var(--success-color) !important;
}

.badge-warning {
    background-color: var(--warning-color) !important;
}

.badge-danger {
    background-color: var(--danger-color) !important;
}

/* Form controls */
.form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25) !important;
}

/* Links */
a {
    color: var(--primary-color) !important;
}

a:hover {
    color: var(--secondary-color) !important;
}

/* Progress bars */
.progress-bar {
    background-color: var(--primary-color) !important;
}

/* Alerts */
.alert-info {
    background-color: rgba(124, 58, 237, 0.1) !important;
    border-color: var(--primary-color) !important;
    color: var(--secondary-color) !important;
}

/* Force override for specific elements */
.navbar-dark.bg-primary, .bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.navbar-custom, .navbar-dark {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Hero section override */
.hero-section, .hero-section * {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Sidebar override for admin */
#sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Button overrides */
.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

/* Stats cards */
.card-primary .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
    color: white !important;
}

/* Force override all primary colors */
html body .navbar-custom,
html body .hero-section,
html body .bg-primary,
html body .btn-primary,
html body #sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    background-color: var(--primary-color) !important;
}

/* Text colors */
html body .text-primary {
    color: var(--primary-color) !important;
}

/* Border colors */
html body .border-primary {
    border-color: var(--primary-color) !important;
}

/* Ensure navbar brand and links are white */
html body .navbar-brand,
html body .navbar-nav .nav-link {
    color: white !important;
}

/* Public Page Headers - Dynamic Colors */
.page-header,
section[style*='background: linear-gradient'] {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Feature Icons - Dynamic Colors */
.feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* Process Steps - Dynamic Colors */
.process-step {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* Call to Action Sections */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Contact Icons */
.contact-icon i {
    color: var(--primary-color) !important;
}

/* Service Cards */
.service-card .feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* News Cards */
.news-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
}

/* About Page Elements */
.about-section .highlight {
    color: var(--primary-color) !important;
}

/* Override any hardcoded blue gradients */
[style*='#2563eb'],
[style*='#1e40af'],
[style*='linear-gradient(135deg, #2563eb'],
[style*='linear-gradient(135deg, #1e40af'] {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Ensure all primary text uses dynamic colors */
.text-primary,
.btn-primary,
.bg-primary {
    color: var(--primary-color) !important;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Public page specific button styles */
.btn-hero {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-hero:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}
