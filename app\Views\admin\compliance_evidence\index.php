<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Inspection Results</h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                <i class="fas fa-calendar-check me-2"></i>Schedule New Inspection
            </a>
        </div>
    </div>

    <!-- Inspection Statistics -->
    <div class="row g-4 mb-4">
        <!-- Scheduled -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-primary bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-calendar text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['scheduled_inspections'] ?></h3>
                            <p class="text-muted mb-0">Scheduled</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- In Progress -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-warning bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-clock text-warning"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['in_progress_inspections'] ?></h3>
                            <p class="text-muted mb-0">In Progress</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approved -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-success bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-check-circle text-success"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['completed_inspections'] ?></h3>
                            <p class="text-muted mb-0">Approved</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cancelled -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100 border-0 rounded-3 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="bg-danger bg-opacity-10 p-3 rounded-3">
                                <i class="fas fa-times-circle text-danger"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="h2 mb-1"><?= $stats['cancelled_inspections'] ?></h3>
                            <p class="text-muted mb-0">Cancelled</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Approved Inspections -->
    <div class="card border-0 rounded-3 shadow-sm">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    <h5 class="mb-0">Recent Approved Inspections</h5>
                </div>
                <span class="badge bg-success">Verified & Approved</span>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($recent_inspections)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Approved Inspections</h5>
                    <p class="text-muted">No inspections have been approved yet. Completed inspections need admin verification before appearing here.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="<?= BASE_URL ?>admin/inspections/pending-verification" class="btn btn-warning">
                            <i class="fas fa-clipboard-check me-1"></i>Review Pending
                        </a>
                        <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                            <i class="fas fa-calendar-check me-1"></i>Schedule Inspections
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="ps-4">Business</th>
                                <th>Inspector</th>
                                <th>Completed</th>
                                <th>Score</th>
                                <th class="pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_inspections as $inspection): ?>
                                <tr>
                                    <td class="ps-4">
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($inspection['business_name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($inspection['inspector_name']) ?></div>
                                            <small class="text-muted">Inspector</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= date('M d, Y', strtotime($inspection['completed_date'])) ?></div>
                                            <small class="text-muted"><?= date('h:i A', strtotime($inspection['completed_date'])) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($inspection['checklist_score']): ?>
                                            <span class="badge bg-<?=
                                                $inspection['checklist_score']['grade'] === 'A' ? 'success' :
                                                (in_array($inspection['checklist_score']['grade'], ['B', 'C']) ? 'warning' : 'danger')
                                            ?>">
                                                <?= $inspection['checklist_score']['grade'] ?> (<?= $inspection['checklist_score']['percentage'] ?>%)
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">No score</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="pe-4">
                                        <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>"
                                           class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($inspection['checklist_score']): ?>
                                            <a href="<?= BASE_URL ?>admin/inspections/report/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- In Progress Inspections -->
    <div class="card border-0 rounded-3 shadow-sm mt-4">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clock text-warning me-2"></i>
                    <h5 class="mb-0">In Progress Inspections</h5>
                </div>
                <span class="badge bg-warning">Currently Active</span>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($in_progress_inspections)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No In Progress Inspections</h5>
                    <p class="text-muted">No inspections are currently in progress. Inspectors start inspections by accessing their assigned checklists.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                            <i class="fas fa-calendar-check me-1"></i>Schedule Inspections
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table mb-0" id="inProgressTable">
                        <thead class="table-light">
                            <tr>
                                <th class="ps-4">Business</th>
                                <th>Inspector</th>
                                <th>Started</th>
                                <th>Progress</th>
                                <th class="pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($in_progress_inspections as $inspection): ?>
                                <tr>
                                    <td class="ps-4">
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($inspection['business_name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($inspection['inspector_name']) ?></div>
                                            <small class="text-muted">Inspector</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium"><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></div>
                                            <small class="text-muted"><?= date('h:i A', strtotime($inspection['scheduled_date'])) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($inspection['checklist_completion']): ?>
                                            <?php
                                            $completion = $inspection['checklist_completion'];
                                            $percentage = $completion['total_items'] > 0 ? round(($completion['completed_items'] / $completion['total_items']) * 100) : 0;
                                            ?>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-warning" style="width: <?= $percentage ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?= $percentage ?>%</small>
                                            </div>
                                            <small class="text-muted"><?= $completion['completed_items'] ?>/<?= $completion['total_items'] ?> items</small>
                                        <?php else: ?>
                                            <span class="text-muted">Not started</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="pe-4">
                                        <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>"
                                           class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($inspection['checklist_completion'] && $inspection['checklist_completion']['completed_items'] > 0): ?>
                                            <a href="<?= BASE_URL ?>admin/inspections/report/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>



<style>
.card {
    background: #fff;
}
.table > :not(caption) > * > * {
    padding: 1rem;
    background: none;
}
.table {
    margin: 0;
}
.badge {
    font-weight: 500;
}
.btn-primary {
    background: #0d6efd;
    border-color: #0d6efd;
}
.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
}
.btn-outline-primary:hover {
    background: #0d6efd;
    color: #fff;
}
.table-light {
    background: #f8f9fa !important;
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable for recent approved inspections
    $('.table').not('#inProgressTable').DataTable({
        order: [[2, 'desc']], // Sort by completed date
        pageLength: 10,
        responsive: true,
        language: {
            search: '',
            searchPlaceholder: 'Search inspections...',
            lengthMenu: 'Show _MENU_ inspections per page',
            info: 'Showing _START_ to _END_ of _TOTAL_ inspections'
        },
        columnDefs: [
            { orderable: false, targets: [4] } // Disable sorting for actions column
        ]
    });

    // Initialize DataTable for in-progress inspections
    $('#inProgressTable').DataTable({
        order: [[2, 'desc']], // Sort by started date
        pageLength: 10,
        responsive: true,
        language: {
            search: '',
            searchPlaceholder: 'Search in-progress inspections...',
            lengthMenu: 'Show _MENU_ inspections per page',
            info: 'Showing _START_ to _END_ of _TOTAL_ inspections'
        },
        columnDefs: [
            { orderable: false, targets: [4] } // Disable sorting for actions column
        ]
    });
});
</script>

<?php $this->endSection(); ?>
