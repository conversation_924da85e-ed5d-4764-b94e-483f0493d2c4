<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Notification;

class NotificationController extends Controller {
    private $notificationModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->notificationModel = new Notification();
    }

    public function index() {
        $this->auth->requireLogin();

        $user = $this->auth->getUser();
        $notifications = $this->notificationModel->getByUser($user['id'], 50);

        return $this->render('notifications/index', [
            'title' => 'Notifications',
            'active_page' => 'notifications',
            'notifications' => $notifications,
            'user' => $user
        ]);
    }

    public function getUnread() {
        $this->auth->requireLogin();

        $user = $this->auth->getUser();
        $notifications = $this->notificationModel->getUnreadByUser($user['id']);

        $this->jsonResponse([
            'success' => true,
            'notifications' => $notifications
        ]);
    }

    public function getUnreadCount() {
        $this->auth->requireLogin();

        $user = $this->auth->getUser();
        $count = $this->notificationModel->getUnreadCount($user['id']);

        $this->jsonResponse([
            'success' => true,
            'count' => $count
        ]);
    }

    public function markAsRead($notificationId) {
        $this->auth->requireLogin();

        if (!$this->isPost()) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $result = $this->notificationModel->markAsRead($notificationId);

        $this->jsonResponse([
            'success' => $result,
            'message' => $result ? 'Notification marked as read' : 'Failed to mark notification as read'
        ]);
    }

    public function markAllAsRead() {
        $this->auth->requireLogin();

        if (!$this->isPost()) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $user = $this->auth->getUser();
        $result = $this->notificationModel->markAllAsRead($user['id']);

        $this->jsonResponse([
            'success' => $result,
            'message' => $result ? 'All notifications marked as read' : 'Failed to mark notifications as read'
        ]);
    }

    // jsonResponse() method inherited from base Controller class
}