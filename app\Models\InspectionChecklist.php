<?php

namespace App\Models;

use App\Config\Database;
use PDO;

class InspectionChecklist
{
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
    }

    /**
     * Get all active checklist categories with their items
     */
    public function getChecklistCategories($includeItems = true)
    {
        $query = "SELECT * FROM inspection_checklist_categories 
                  WHERE is_active = 1 
                  ORDER BY sort_order ASC, name ASC";
        
        $stmt = $this->db->query($query);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($includeItems) {
            foreach ($categories as &$category) {
                $category['items'] = $this->getChecklistItems($category['id']);
            }
        }
        
        return $categories;
    }

    /**
     * Get checklist items for a specific category
     */
    public function getChecklistItems($categoryId)
    {
        $query = "SELECT * FROM inspection_checklist_items 
                  WHERE category_id = :category_id AND is_active = 1 
                  ORDER BY sort_order ASC, item_name ASC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':category_id' => $categoryId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all active checklist items
     */
    public function getAllChecklistItems()
    {
        $query = "SELECT ci.*, cc.name as category_name, cc.weight as category_weight
                  FROM inspection_checklist_items ci
                  LEFT JOIN inspection_checklist_categories cc ON ci.category_id = cc.id
                  WHERE ci.is_active = 1 AND cc.is_active = 1
                  ORDER BY cc.sort_order ASC, ci.sort_order ASC";
        
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Save checklist responses for an inspection
     */
    public function saveChecklistResponse($data)
    {
        $query = "INSERT INTO inspection_checklist_responses 
                  (id, inspection_id, checklist_item_id, inspector_id, compliance_status, score, notes, photo_evidence, corrective_action, deadline)
                  VALUES (UUID(), :inspection_id, :checklist_item_id, :inspector_id, :compliance_status, :score, :notes, :photo_evidence, :corrective_action, :deadline)
                  ON DUPLICATE KEY UPDATE
                  compliance_status = VALUES(compliance_status),
                  score = VALUES(score),
                  notes = VALUES(notes),
                  photo_evidence = VALUES(photo_evidence),
                  corrective_action = VALUES(corrective_action),
                  deadline = VALUES(deadline),
                  updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':inspection_id' => $data['inspection_id'],
            ':checklist_item_id' => $data['checklist_item_id'],
            ':inspector_id' => $data['inspector_id'],
            ':compliance_status' => $data['compliance_status'],
            ':score' => $data['score'] ?? 0,
            ':notes' => $data['notes'] ?? null,
            ':photo_evidence' => $data['photo_evidence'] ?? null,
            ':corrective_action' => $data['corrective_action'] ?? null,
            ':deadline' => $data['deadline'] ?? null
        ]);
    }

    /**
     * Check if an inspection has any checklist responses (inspector has viewed/started the checklist)
     */
    public function hasInspectionResponses($inspectionId)
    {
        $query = "SELECT COUNT(*) FROM inspection_checklist_responses WHERE inspection_id = :inspection_id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':inspection_id' => $inspectionId]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Get checklist responses for an inspection
     */
    public function getInspectionChecklistResponses($inspectionId)
    {
        $query = "SELECT icr.*, ici.item_code, ici.item_name, ici.points, ici.is_critical,
                         ici.compliance_requirement, ici.description,
                         icc.name as category_name, icc.weight as category_weight
                  FROM inspection_checklist_responses icr
                  LEFT JOIN inspection_checklist_items ici ON icr.checklist_item_id = ici.id
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  WHERE icr.inspection_id = :inspection_id
                  ORDER BY icc.sort_order ASC, ici.sort_order ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute([':inspection_id' => $inspectionId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Calculate inspection score based on checklist responses
     */
    public function calculateInspectionScore($inspectionId)
    {
        $query = "SELECT 
                    SUM(CASE WHEN icr.compliance_status = 'compliant' THEN ici.points ELSE 0 END) as earned_points,
                    SUM(ici.points) as total_points,
                    COUNT(CASE WHEN ici.is_critical = 1 AND icr.compliance_status != 'compliant' THEN 1 END) as critical_violations
                  FROM inspection_checklist_responses icr
                  LEFT JOIN inspection_checklist_items ici ON icr.checklist_item_id = ici.id
                  WHERE icr.inspection_id = :inspection_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':inspection_id' => $inspectionId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || $result['total_points'] == 0) {
            return [
                'score' => 0,
                'percentage' => 0,
                'grade' => 'F',
                'critical_violations' => 0,
                'status' => 'incomplete'
            ];
        }
        
        $percentage = ($result['earned_points'] / $result['total_points']) * 100;
        $grade = $this->getGradeFromPercentage($percentage);
        
        // If there are critical violations, grade cannot be higher than D
        if ($result['critical_violations'] > 0 && in_array($grade, ['A', 'B', 'C'])) {
            $grade = 'D';
            $percentage = min($percentage, 69);
        }
        
        return [
            'score' => round($result['earned_points'], 2),
            'total_points' => round($result['total_points'], 2),
            'percentage' => round($percentage, 2),
            'grade' => $grade,
            'critical_violations' => (int)$result['critical_violations'],
            'status' => $percentage >= 60 ? 'passed' : 'failed'
        ];
    }

    /**
     * Get grade from percentage
     */
    private function getGradeFromPercentage($percentage)
    {
        if ($percentage >= 90) return 'A';
        if ($percentage >= 80) return 'B';
        if ($percentage >= 70) return 'C';
        if ($percentage >= 60) return 'D';
        return 'F';
    }

    /**
     * Get inspection completion status
     */
    public function getInspectionCompletionStatus($inspectionId)
    {
        $totalItems = $this->db->query("SELECT COUNT(*) FROM inspection_checklist_items WHERE is_active = 1")->fetchColumn();

        $completedItems = $this->db->prepare("SELECT COUNT(*) FROM inspection_checklist_responses WHERE inspection_id = ?");
        $completedItems->execute([$inspectionId]);
        $completed = $completedItems->fetchColumn();

        // Check if inspection has been accessed/started by inspector
        $inspectionStatus = $this->db->prepare("SELECT status FROM inspections WHERE id = ?");
        $inspectionStatus->execute([$inspectionId]);
        $status = $inspectionStatus->fetchColumn();

        // Use actual completed count - no artificial inflation
        $displayCompleted = $completed;

        return [
            'total_items' => (int)$totalItems,
            'completed_items' => (int)$displayCompleted,
            'completion_percentage' => $totalItems > 0 ? round(($displayCompleted / $totalItems) * 100, 2) : 0,
            'is_complete' => $completed >= $totalItems && $completed > 0,
            'is_started' => $status === 'in_progress' || $completed > 0
        ];
    }

    /**
     * Get checklist item by ID
     */
    public function getChecklistItem($itemId)
    {
        $query = "SELECT ici.*, icc.name as category_name, icc.weight as category_weight
                  FROM inspection_checklist_items ici
                  LEFT JOIN inspection_checklist_categories icc ON ici.category_id = icc.id
                  WHERE ici.id = :item_id";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([':item_id' => $itemId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get checklist statistics
     */
    public function getChecklistStatistics()
    {
        $stats = [];
        
        // Total categories and items
        $stats['total_categories'] = $this->db->query("SELECT COUNT(*) FROM inspection_checklist_categories WHERE is_active = 1")->fetchColumn();
        $stats['total_items'] = $this->db->query("SELECT COUNT(*) FROM inspection_checklist_items WHERE is_active = 1")->fetchColumn();
        $stats['critical_items'] = $this->db->query("SELECT COUNT(*) FROM inspection_checklist_items WHERE is_active = 1 AND is_critical = 1")->fetchColumn();
        
        // Total possible points
        $stats['total_points'] = $this->db->query("SELECT SUM(points) FROM inspection_checklist_items WHERE is_active = 1")->fetchColumn();
        
        return $stats;
    }
}
?>
