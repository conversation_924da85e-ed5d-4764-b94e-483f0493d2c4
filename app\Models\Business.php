<?php
namespace App\Models;

use App\Core\Model;
use PDO;

class Business extends Model {
    protected $table = 'businesses';
    protected $fillable = [
        'id', 'owner_id', 'owner_name', 'name', 'registration_number', 'email',
        'contact_number', 'address', 'category_id', 'barangay_id', 'employee_count',
        'date_established', 'status', 'compliance_status', 'business_permit',
        'sanitary_permit', 'fire_safety_certificate', 'environmental_permit',
        'safety_officer_count', 'has_safety_signage', 'has_first_aid',
        'has_fire_extinguishers', 'has_cctv', 'has_waste_segregation',
        'last_inspection_date', 'next_inspection_date', 'created_at', 'updated_at'
    ];

    private $complianceEvidenceModel;

    public function __construct() {
        parent::__construct();
        $this->complianceEvidenceModel = new ComplianceEvidence();
    }

    public function getBusinessesByUserId($userId) {
        return $this->getByOwnerId($userId);
    }

    // create() method inherited from base Model class
    public function getByOwnerId($ownerId) {
        $query = "SELECT b.*, c.name as category_name, brg.name as barangay_name, d.name as district_name, u.email as owner_email
                 FROM businesses b
                 LEFT JOIN business_categories c ON b.category_id = c.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 LEFT JOIN users u ON b.owner_id = u.id
                 WHERE b.owner_id = :owner_id
                 ORDER BY b.created_at DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':owner_id' => $ownerId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getAll() {
        $query = "SELECT b.*, c.name as category_name, brg.name as barangay_name, d.name as district_name, u.email as owner_email
                 FROM businesses b
                 LEFT JOIN business_categories c ON b.category_id = c.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 LEFT JOIN users u ON b.owner_id = u.id
                 ORDER BY b.created_at DESC";
        $stmt = $this->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $query = "SELECT b.*, c.name as category_name, brg.name as barangay_name, d.name as district_name, u.email as owner_email
                 FROM businesses b
                 LEFT JOIN business_categories c ON b.category_id = c.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 LEFT JOIN users u ON b.owner_id = u.id
                 WHERE b.id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getCategories() {
        $query = "SELECT * FROM business_categories ORDER BY name ASC";
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDistricts() {
        $query = "SELECT * FROM districts ORDER BY name ASC";
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getBarangays() {
        // First try to get from barangays table
        try {
            $query = "SELECT b.*, d.name as district_name FROM barangays b
                     LEFT JOIN districts d ON b.district_id = d.id
                     ORDER BY d.name ASC, b.name ASC";
            $stmt = $this->db->query($query);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // If barangays table doesn't exist, return districts as barangays for backward compatibility
            $query = "SELECT id, name, name as district_name, id as district_id FROM districts ORDER BY name ASC";
            $stmt = $this->db->query($query);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }

    public function getBarangaysByDistrict($districtId) {
        $query = "SELECT * FROM barangays WHERE district_id = ? ORDER BY name ASC";
        $stmt = $this->query($query, [$districtId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function updateComplianceStatus($businessId, $status) {
        $query = "UPDATE businesses SET
                  compliance_status = :status,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :business_id";

        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':status' => $status,
            ':business_id' => $businessId
        ]);
    }

    public function updateStatus($id, $status) {
        $query = "UPDATE businesses SET status = :status, updated_at = CURRENT_TIMESTAMP WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':status' => $status
        ]);
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        // Dynamically build the SET clause based on provided data
        foreach ($data as $key => $value) {
            if ($key != 'id') {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        $fields[] = "updated_at = CURRENT_TIMESTAMP";

        $query = "UPDATE businesses SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute($params);
    }

    public function updateComplianceChecks($id, $data) {
        $query = "UPDATE businesses SET
                  business_permit = :business_permit,
                  sanitary_permit = :sanitary_permit,
                  fire_safety_certificate = :fire_safety_certificate,
                  environmental_permit = :environmental_permit,
                  safety_officer_count = :safety_officer_count,
                  has_safety_signage = :has_safety_signage,
                  has_first_aid = :has_first_aid,
                  has_fire_extinguishers = :has_fire_extinguishers,
                  has_cctv = :has_cctv,
                  has_waste_segregation = :has_waste_segregation,
                  compliance_status = :compliance_status,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':business_permit' => $data['business_permit'] ?? 0,
            ':sanitary_permit' => $data['sanitary_permit'] ?? 0,
            ':fire_safety_certificate' => $data['fire_safety_certificate'] ?? 0,
            ':environmental_permit' => $data['environmental_permit'] ?? 0,
            ':safety_officer_count' => $data['safety_officer_count'] ?? 0,
            ':has_safety_signage' => $data['has_safety_signage'] ?? 0,
            ':has_first_aid' => $data['has_first_aid'] ?? 0,
            ':has_fire_extinguishers' => $data['has_fire_extinguishers'] ?? 0,
            ':has_cctv' => $data['has_cctv'] ?? 0,
            ':has_waste_segregation' => $data['has_waste_segregation'] ?? 0,
            ':compliance_status' => $data['compliance_status'] ?? 'pending_review'
        ]);
    }
    public function updateInspectionDates($id, $lastInspection = null, $nextInspection = null) {
        $query = "UPDATE businesses SET
                  last_inspection_date = :last_inspection_date,
                  next_inspection_date = :next_inspection_date,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':last_inspection_date' => $lastInspection,
            ':next_inspection_date' => $nextInspection
        ]);
    }

    public function delete($id) {
        $query = "DELETE FROM businesses WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $id]);
    }

    public function countAll() {
        $query = "SELECT COUNT(*) FROM businesses";
        return $this->db->query($query)->fetchColumn();
    }

    public function countByComplianceStatus($status) {
        $query = "SELECT COUNT(*) FROM businesses WHERE compliance_status = :status";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':status' => $status]);
        return $stmt->fetchColumn();
    }

    public function countByStatus($status) {
        $query = "SELECT COUNT(*) FROM businesses WHERE status = :status";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':status' => $status]);
        return $stmt->fetchColumn();
    }

    public function getRecent($limit = 5) {
        $query = "SELECT b.*, c.name as category_name, brg.name as barangay_name, d.name as district_name,
                         u.full_name as owner_name, u.email as owner_email
                 FROM businesses b
                 LEFT JOIN business_categories c ON b.category_id = c.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 LEFT JOIN users u ON b.owner_id = u.id
                 ORDER BY b.created_at DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getComplianceRequirements() {
        // This currently returns all possible requirements.
        // In a more complex system, this would fetch requirements based on business category.
        return [
            'business_permit' => 'Business Permit',
            'sanitary_permit' => 'Sanitary Permit',
            'fire_safety_certificate' => 'Fire Safety Certificate',
            'environmental_permit' => 'Environmental Permit',
            'has_safety_signage' => 'Safety Signage',
            'has_first_aid' => 'First Aid Kit',
            'has_fire_extinguishers' => 'Fire Extinguishers',
            'has_cctv' => 'CCTV System',
            'has_waste_segregation' => 'Waste Segregation System'
        ];
    }

    public function all($orderBy = null, $direction = 'DESC') {
        return $this->getAll();
    }

    /**
     * Checks the status of all required compliance evidence for a business
     * and updates the business's overall compliance status.
     * Assumes all types returned by getComplianceRequirements() are required.
     *
     * @param string $businessId The ID of the business.
     * @return bool True on success, false on failure.
     */
    public function checkAndSetComplianceStatus($businessId) {
        $business = $this->getById($businessId);
        if (!$business) {
            return false; // Business not found
        }

        $requiredTypes = array_keys($this->getComplianceRequirements());
        $allEvidence = $this->complianceEvidenceModel->getByBusinessId($businessId);

        $latestEvidence = [];
        foreach ($allEvidence as $evidence) {
            $type = $evidence['compliance_type'];
            // Keep only the latest evidence for each type
            if (!isset($latestEvidence[$type]) || $evidence['created_at'] > $latestEvidence[$type]['created_at']) {
                $latestEvidence[$type] = $evidence;
            }
        }

        $allRequiredVerified = true;
        $anyRejected = false;
        $anyPending = false;

        foreach ($requiredTypes as $type) {
            if (!isset($latestEvidence[$type])) {
                // Missing required evidence
                $allRequiredVerified = false;
                $anyPending = true; // Treat missing as pending for overall status
            } else {
                $status = $latestEvidence[$type]['status'];
                if ($status === 'rejected') {
                    $anyRejected = true;
                    $allRequiredVerified = false;
                } elseif ($status === 'pending') {
                    $anyPending = true;
                    $allRequiredVerified = false;
                }
                // If status is 'verified', it contributes to allRequiredVerified being true
            }
        }

        $newComplianceStatus = 'pending_review'; // Default

        if ($anyRejected) {
            $newComplianceStatus = 'non_compliant';
        } elseif ($allRequiredVerified) {
            $newComplianceStatus = 'compliant';
        } else {
             $newComplianceStatus = 'pending_review';
        }


        // Update the business's compliance status in the database
        return $this->updateComplianceStatus($businessId, $newComplianceStatus);
    }

    public function getDistrictById($id) {
        $query = "SELECT * FROM districts WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByDistrict($districtId) {
        $query = "SELECT b.*, c.name as category_name, brg.name as barangay_name, d.name as district_name
                 FROM businesses b
                 LEFT JOIN business_categories c ON b.category_id = c.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 WHERE brg.district_id = :district_id
                 ORDER BY b.name ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute([':district_id' => $districtId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getByBarangay($barangayId) {
        // First try with barangay_id column
        try {
            $query = "SELECT b.*, c.name as category_name, brg.name as barangay_name, d.name as district_name
                     FROM businesses b
                     LEFT JOIN business_categories c ON b.category_id = c.id
                     LEFT JOIN barangays brg ON b.barangay_id = brg.id
                     LEFT JOIN districts d ON brg.district_id = d.id
                     WHERE b.barangay_id = :barangay_id
                     ORDER BY b.name ASC";

            $stmt = $this->db->prepare($query);
            $stmt->execute([':barangay_id' => $barangayId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // Fallback: try with district_id if barangay_id doesn't exist
            try {
                $query = "SELECT b.*, c.name as category_name, d.name as district_name
                         FROM businesses b
                         LEFT JOIN business_categories c ON b.category_id = c.id
                         LEFT JOIN districts d ON b.district_id = d.id
                         WHERE b.district_id = :district_id
                         ORDER BY b.name ASC";

                $stmt = $this->db->prepare($query);
                $stmt->execute([':district_id' => $barangayId]);
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (Exception $e2) {
                return [];
            }
        }
    }

    public function getBarangayById($barangayId) {
        $query = "SELECT * FROM barangays WHERE id = ?";
        $stmt = $this->query($query, [$barangayId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Check if the businesses table has barangay_id column
     */
    public function hasBarangayIdColumn() {
        try {
            $query = "SHOW COLUMNS FROM businesses LIKE 'barangay_id'";
            $stmt = $this->db->query($query);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return true; // Default to true (assume barangay_id exists)
        }
    }
}
