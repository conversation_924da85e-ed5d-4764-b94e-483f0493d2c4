<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Business extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'owner_id',
        'owner_name',
        'category_id',
        'barangay_id',
        'name',
        'registration_number',
        'email',
        'contact_number',
        'address',
        'employee_count',
        'date_established',
        'status',
        'compliance_status',
        'business_permit',
        'sanitary_permit',
        'fire_safety_certificate',
        'environmental_permit',
        'safety_officer_count',
        'has_safety_signage',
        'has_first_aid',
        'has_fire_extinguishers',
        'has_cctv',
        'has_waste_segregation',
        'last_inspection_date',
        'next_inspection_date',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'date_established' => 'date',
            'last_inspection_date' => 'date',
            'next_inspection_date' => 'date',
            'business_permit' => 'boolean',
            'sanitary_permit' => 'boolean',
            'fire_safety_certificate' => 'boolean',
            'environmental_permit' => 'boolean',
            'has_safety_signage' => 'boolean',
            'has_first_aid' => 'boolean',
            'has_fire_extinguishers' => 'boolean',
            'has_cctv' => 'boolean',
            'has_waste_segregation' => 'boolean',
        ];
    }

    /**
     * Get the business owner
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the business category
     */
    public function category()
    {
        return $this->belongsTo(BusinessCategory::class, 'category_id');
    }

    /**
     * Get the barangay
     */
    public function barangay()
    {
        return $this->belongsTo(Barangay::class, 'barangay_id');
    }

    /**
     * Get the district through barangay
     */
    public function district()
    {
        return $this->hasOneThrough(District::class, Barangay::class, 'id', 'id', 'barangay_id', 'district_id');
    }

    /**
     * Get inspections for this business
     */
    public function inspections()
    {
        return $this->hasMany(Inspection::class);
    }

    /**
     * Get checklist evidence for this business
     */
    public function checklistEvidence()
    {
        return $this->hasMany(BusinessChecklistEvidence::class);
    }

    /**
     * Get chat rooms for this business
     */
    public function chatRooms()
    {
        return $this->hasMany(ChatRoom::class);
    }

    /**
     * Get the latest inspection
     */
    public function latestInspection()
    {
        return $this->hasOne(Inspection::class)->latest('scheduled_date');
    }

    /**
     * Check if business is compliant
     */
    public function isCompliant(): bool
    {
        return $this->compliance_status === 'compliant';
    }

    /**
     * Get compliance percentage
     */
    public function getCompliancePercentage(): int
    {
        $totalRequirements = 10; // Total compliance requirements
        $metRequirements = 0;

        if ($this->business_permit) $metRequirements++;
        if ($this->sanitary_permit) $metRequirements++;
        if ($this->fire_safety_certificate) $metRequirements++;
        if ($this->environmental_permit) $metRequirements++;
        if ($this->has_safety_signage) $metRequirements++;
        if ($this->has_first_aid) $metRequirements++;
        if ($this->has_fire_extinguishers) $metRequirements++;
        if ($this->has_cctv) $metRequirements++;
        if ($this->has_waste_segregation) $metRequirements++;
        if ($this->safety_officer_count > 0) $metRequirements++;

        return round(($metRequirements / $totalRequirements) * 100);
    }
}
