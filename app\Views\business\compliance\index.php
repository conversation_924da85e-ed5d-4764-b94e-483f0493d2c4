<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-shield-alt text-primary"></i> <?= htmlspecialchars($title) ?>
        </h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-building me-2"></i>Your Businesses for Compliance Evidence
            </h6>
        </div>
        <div class="card-body">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success">
                    <?= $_SESSION['success'] ?>
                    <?php unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger">
                    <?= $_SESSION['error'] ?>
                    <?php unset($_SESSION['error']); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($businesses)): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="businessesTable">
                        <thead class="table-light">
                            <tr>
                                <th><i class="fas fa-building me-2"></i>Business Name</th>
                                <th><i class="fas fa-id-card me-2"></i>Registration Number</th>
                                <th><i class="fas fa-info-circle me-2"></i>Status</th>
                                <th><i class="fas fa-cogs me-2"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($businesses as $business): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($business['name'] ?? 'N/A') ?></strong>
                                    </td>
                                    <td>
                                        <code><?= htmlspecialchars($business['registration_number'] ?? 'N/A') ?></code>
                                    </td>
                                    <td>
                                        <?php
                                        $businessStatus = $business['status'] ?? 'pending';
                                        $statusClass = ($businessStatus === 'active' || $businessStatus === 'approved') ? 'success' :
                                                      ($businessStatus === 'pending' ? 'warning' : 'danger');
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?> fs-6">
                                            <?= ucfirst(htmlspecialchars($businessStatus)) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= BASE_URL ?>business/compliance/<?= htmlspecialchars($business['id'] ?? '') ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-upload me-1"></i>Manage Evidence
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted mb-3">No Businesses Registered</h5>
                    <p class="text-muted mb-4">You need to register a business before you can upload compliance evidence.</p>
                    <div class="d-flex justify-content-center gap-2">
                        <a href="<?= BASE_URL ?>business/create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Register Your Business
                        </a>
                        <a href="<?= BASE_URL ?>business" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>View All Businesses
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $this->endSection() ?>

<?php $this->section('scripts') ?>
<!-- Add any specific scripts for this page here -->
<script>
    $(document).ready(function() {
        $('#businessesTable').DataTable();
    });
</script>
<?php $this->endSection() ?>
