<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-check-circle text-success"></i> Verify Inspection
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspections/pending-verification" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Pending
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Inspection Details Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-check"></i> Inspection Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><?= htmlspecialchars($business['name']) ?></h5>
                            <p class="mb-2">
                                <strong><i class="fas fa-map-marker-alt me-2"></i>Address:</strong><br>
                                <?= htmlspecialchars($business['address']) ?>
                            </p>
                            <p class="mb-2">
                                <strong><i class="fas fa-user-tie me-2"></i>Inspector:</strong>
                                <?= htmlspecialchars($inspection['inspector_name']) ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong><i class="fas fa-calendar me-2"></i>Completed Date:</strong>
                                <?= date('F j, Y g:i A', strtotime($inspection['completed_date'])) ?>
                            </p>
                            <p class="mb-2">
                                <strong><i class="fas fa-chart-line me-2"></i>Score:</strong>
                                <?php if ($inspection['score']): ?>
                                    <span class="badge bg-primary fs-6"><?= $inspection['score'] ?>%</span>
                                <?php else: ?>
                                    <span class="text-muted">No score assigned</span>
                                <?php endif; ?>
                            </p>
                            <p class="mb-2">
                                <strong><i class="fas fa-award me-2"></i>Compliance Rating:</strong>
                                <?php if ($inspection['compliance_rating']): ?>
                                    <?php
                                    $ratingClass = '';
                                    $ratingText = '';
                                    switch ($inspection['compliance_rating']) {
                                        case 'A': 
                                            $ratingClass = 'success'; 
                                            $ratingText = 'Excellent (90-100%)';
                                            break;
                                        case 'B': 
                                            $ratingClass = 'info'; 
                                            $ratingText = 'Good (80-89%)';
                                            break;
                                        case 'C': 
                                            $ratingClass = 'warning'; 
                                            $ratingText = 'Satisfactory (70-79%)';
                                            break;
                                        case 'D': 
                                            $ratingClass = 'danger'; 
                                            $ratingText = 'Needs Improvement (60-69%)';
                                            break;
                                        case 'F': 
                                            $ratingClass = 'dark'; 
                                            $ratingText = 'Non-compliant (<60%)';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?= $ratingClass ?> fs-6">
                                        Grade <?= htmlspecialchars($inspection['compliance_rating']) ?> - <?= $ratingText ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">No rating assigned</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>

                    <?php if ($inspection['findings']): ?>
                        <hr>
                        <h6><i class="fas fa-search me-2"></i>Findings:</h6>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($inspection['findings'])) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($inspection['recommendations']): ?>
                        <hr>
                        <h6><i class="fas fa-lightbulb me-2"></i>Recommendations:</h6>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($inspection['recommendations'])) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($inspection['inspector_notes']): ?>
                        <hr>
                        <h6><i class="fas fa-sticky-note me-2"></i>Inspector Notes:</h6>
                        <div class="bg-light p-3 rounded">
                            <?= nl2br(htmlspecialchars($inspection['inspector_notes'])) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Verification Form -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-check-circle"></i> Admin Verification
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>admin/inspections/verify/<?= $inspection['id'] ?>" method="POST">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-clipboard-check me-1"></i>Verification Decision
                            </label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="verification_status"
                                       id="approve" value="approved" checked>
                                <label class="form-check-label text-success" for="approve">
                                    <i class="fas fa-check-circle me-1"></i>Approve Inspection
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="verification_status"
                                       id="reject" value="rejected">
                                <label class="form-check-label text-danger" for="reject">
                                    <i class="fas fa-times-circle me-1"></i>Reject Inspection
                                </label>
                            </div>
                            <div class="form-text">
                                Choose whether to approve or reject this inspection based on your review.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="verification_notes" class="form-label">
                                <i class="fas fa-comment me-1"></i>Verification Notes
                            </label>
                            <textarea class="form-control" id="verification_notes" name="verification_notes"
                                      rows="4" placeholder="Add notes about your verification decision..."></textarea>
                            <div class="form-text">
                                Explain your decision, especially if rejecting the inspection.
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>Submit Verification
                            </button>
                            <a href="<?= BASE_URL ?>admin/inspections/pending-verification" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>" class="btn btn-outline-info">
                            <i class="fas fa-eye"></i> View Full Details
                        </a>
                        <a href="<?= BASE_URL ?>admin/inspections/report/<?= $inspection['id'] ?>" class="btn btn-outline-primary">
                            <i class="fas fa-file-alt"></i> View Report
                        </a>
                        <a href="<?= BASE_URL ?>admin/businesses/view/<?= $business['id'] ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-building"></i> View Business
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>
