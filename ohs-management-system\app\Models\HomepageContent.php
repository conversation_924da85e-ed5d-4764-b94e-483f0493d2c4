<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class HomepageContent extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'page',
        'section_type',
        'title',
        'content',
        'image_url',
        'button_text',
        'button_url',
        'sort_order',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the user who created this content
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active content
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific page
     */
    public function scopeForPage($query, $page)
    {
        return $query->where('page', $page);
    }

    /**
     * Scope for specific section type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('section_type', $type);
    }

    /**
     * Scope for ordered content
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Get content for homepage
     */
    public static function getHomepageContent()
    {
        return static::active()
            ->forPage('home')
            ->ordered()
            ->get()
            ->groupBy('section_type');
    }

    /**
     * Get content for specific page
     */
    public static function getPageContent($page)
    {
        return static::active()
            ->forPage($page)
            ->ordered()
            ->get()
            ->groupBy('section_type');
    }

    /**
     * Get hero section for page
     */
    public static function getHeroSection($page = 'home')
    {
        return static::active()
            ->forPage($page)
            ->ofType('hero')
            ->ordered()
            ->first();
    }

    /**
     * Get feature sections for page
     */
    public static function getFeatureSections($page = 'home')
    {
        return static::active()
            ->forPage($page)
            ->ofType('feature')
            ->ordered()
            ->get();
    }

    /**
     * Get content sections for page
     */
    public static function getContentSections($page = 'home')
    {
        return static::active()
            ->forPage($page)
            ->ofType('content')
            ->ordered()
            ->get();
    }

    /**
     * Get CTA sections for page
     */
    public static function getCtaSections($page = 'home')
    {
        return static::active()
            ->forPage($page)
            ->ofType('cta')
            ->ordered()
            ->get();
    }

    /**
     * Get testimonial sections for page
     */
    public static function getTestimonialSections($page = 'home')
    {
        return static::active()
            ->forPage($page)
            ->ofType('testimonial')
            ->ordered()
            ->get();
    }

    /**
     * Get available section types
     */
    public static function getSectionTypes()
    {
        return [
            'hero' => 'Hero Section',
            'content' => 'Content Section',
            'feature' => 'Feature Section',
            'testimonial' => 'Testimonial Section',
            'cta' => 'Call to Action',
        ];
    }

    /**
     * Get available pages
     */
    public static function getAvailablePages()
    {
        return [
            'home' => 'Homepage',
            'about' => 'About Page',
            'services' => 'Services Page',
            'contact' => 'Contact Page',
        ];
    }

    /**
     * Check if content has image
     */
    public function hasImage()
    {
        return !empty($this->image_url);
    }

    /**
     * Get full image URL
     */
    public function getImageUrl()
    {
        if (!$this->hasImage()) {
            return null;
        }

        if (str_starts_with($this->image_url, 'http')) {
            return $this->image_url;
        }

        return asset('storage/' . $this->image_url);
    }

    /**
     * Check if content has button
     */
    public function hasButton()
    {
        return !empty($this->button_text) && !empty($this->button_url);
    }

    /**
     * Get truncated content for preview
     */
    public function getPreviewContent($length = 150)
    {
        if (empty($this->content)) {
            return '';
        }

        return strlen($this->content) > $length 
            ? substr($this->content, 0, $length) . '...'
            : $this->content;
    }

    /**
     * Get next sort order for page and section type
     */
    public static function getNextSortOrder($page, $sectionType = null)
    {
        $query = static::forPage($page);
        
        if ($sectionType) {
            $query->ofType($sectionType);
        }

        $maxOrder = $query->max('sort_order');
        
        return ($maxOrder ?? 0) + 1;
    }

    /**
     * Reorder content sections
     */
    public static function reorderSections($items)
    {
        foreach ($items as $item) {
            static::where('id', $item['id'])
                ->update(['sort_order' => $item['sort_order']]);
        }
    }
}
