<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3 text-gray-800">
            <i class="fas fa-file-shield me-2 text-primary"></i>Compliance Evidence Review
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-filter me-1"></i>Filter by Status
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?status=all">All Evidence</a></li>
                    <li><a class="dropdown-item" href="?status=pending">Pending Review</a></li>
                    <li><a class="dropdown-item" href="?status=verified">Verified</a></li>
                    <li><a class="dropdown-item" href="?status=rejected">Rejected</a></li>
                </ul>
            </div>
            <a href="<?= BASE_URL ?>inspector/dashboard" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= count($evidence) ?></h4>
                            <div class="small">Total Evidence</div>
                        </div>
                        <i class="fas fa-file-alt fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($evidence, function($item) { return $item['status'] === 'pending'; })) ?></h4>
                            <div class="small">Pending Review</div>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($evidence, function($item) { return $item['status'] === 'verified'; })) ?></h4>
                            <div class="small">Verified</div>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= count(array_filter($evidence, function($item) { return $item['status'] === 'rejected'; })) ?></h4>
                            <div class="small">Rejected</div>
                        </div>
                        <i class="fas fa-times-circle fa-2x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>Compliance Evidence for Review
                </h6>
                <span class="badge bg-primary"><?= count($evidence) ?> items</span>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($evidence)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Compliance Evidence Found</h5>
                    <p class="text-muted">No compliance evidence has been submitted for businesses in your assigned districts yet.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0 datatable">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0">
                                    <i class="fas fa-building me-1"></i>Business
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-certificate me-1"></i>Compliance Type
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-info-circle me-1"></i>Status
                                </th>
                                <th class="border-0">
                                    <i class="fas fa-calendar me-1"></i>Uploaded At
                                </th>
                                <th class="border-0 text-center">
                                    <i class="fas fa-cogs me-1"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($evidence as $item): ?>
                                <tr>
                                    <td>
                                        <h6 class="mb-1"><?= htmlspecialchars($item['business_name'] ?? 'Unknown Business') ?></h6>
                                        <small class="text-muted"><?= substr($item['business_id'] ?? '', 0, 8) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= htmlspecialchars($item['compliance_type'] ?? 'N/A') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $item['status'] ?? 'pending';
                                        $statusConfig = [
                                            'verified' => ['success', 'check-circle', 'Verified'],
                                            'rejected' => ['danger', 'times-circle', 'Rejected'],
                                            'pending' => ['warning', 'clock', 'Pending Review']
                                        ];
                                        $config = $statusConfig[$status] ?? ['secondary', 'question-circle', 'Unknown'];
                                        ?>
                                        <span class="badge bg-<?= $config[0] ?>-subtle text-<?= $config[0] ?>">
                                            <i class="fas fa-<?= $config[1] ?> me-1"></i><?= $config[2] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?= isset($item['created_at']) ? date('M d, Y', strtotime($item['created_at'])) : 'N/A' ?>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>inspector/compliance/verify/<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="Review & Verify Evidence">
                                                <i class="fas fa-eye me-1"></i>Review
                                            </a>
                                            <?php if ($item['status'] === 'pending'): ?>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-success"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#quickVerifyModal<?= $item['id'] ?>"
                                                        title="Quick Verify">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#quickRejectModal<?= $item['id'] ?>"
                                                        title="Quick Reject">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Quick Action Modals -->
<?php foreach ($evidence as $item): ?>
    <?php if ($item['status'] === 'pending'): ?>
        <!-- Quick Verify Modal -->
        <div class="modal fade" id="quickVerifyModal<?= $item['id'] ?>" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-check-circle me-2"></i>Quick Verify Evidence
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="<?= BASE_URL ?>inspector/compliance/verify/<?= $item['id'] ?>" method="POST">
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <strong>Business:</strong> <?= htmlspecialchars($item['business_name'] ?? 'Unknown') ?><br>
                                <strong>Type:</strong> <?= htmlspecialchars($item['compliance_type'] ?? 'N/A') ?>
                            </div>
                            <div class="mb-3">
                                <label for="notes<?= $item['id'] ?>" class="form-label">Verification Notes (Optional)</label>
                                <textarea class="form-control" id="notes<?= $item['id'] ?>" name="notes" rows="3" 
                                          placeholder="Add any notes about this verification..."></textarea>
                            </div>
                            <input type="hidden" name="status" value="verified">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-1"></i>Verify Evidence
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Quick Reject Modal -->
        <div class="modal fade" id="quickRejectModal<?= $item['id'] ?>" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-times-circle me-2"></i>Reject Evidence
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="<?= BASE_URL ?>inspector/compliance/verify/<?= $item['id'] ?>" method="POST">
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <strong>Business:</strong> <?= htmlspecialchars($item['business_name'] ?? 'Unknown') ?><br>
                                <strong>Type:</strong> <?= htmlspecialchars($item['compliance_type'] ?? 'N/A') ?>
                            </div>
                            <div class="mb-3">
                                <label for="reject_notes<?= $item['id'] ?>" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="reject_notes<?= $item['id'] ?>" name="notes" rows="3" 
                                          placeholder="Please provide a reason for rejection..." required></textarea>
                            </div>
                            <input type="hidden" name="status" value="rejected">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times me-1"></i>Reject Evidence
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; ?>

<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize DataTable
    $('.datatable').DataTable({
        order: [[3, 'desc']], // Sort by upload date
        pageLength: 25,
        language: {
            search: '<i class="fas fa-search"></i>',
            searchPlaceholder: 'Search evidence...'
        },
        columnDefs: [
            { orderable: false, targets: [4] } // Disable sorting for actions column
        ]
    });
});
</script>

<style>
.card:hover {
    transform: translateY(-1px);
}
</style>

<?php $this->endSection(); ?>
