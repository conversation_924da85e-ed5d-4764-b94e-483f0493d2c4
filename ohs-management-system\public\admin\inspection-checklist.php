<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed.');
}

// Create tables if they don't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS inspection_checklist_categories (
        id CHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS inspection_checklist_items (
        id CHAR(36) PRIMARY KEY,
        category_id CHAR(36) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        points INT DEFAULT 1,
        is_required BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES inspection_checklist_categories(id) ON DELETE CASCADE
    )");
} catch (Exception $e) {
    // Tables might already exist
}

// Handle form submissions
if ($_POST) {
    $success = false;
    $error = '';
    
    try {
        if ($_POST['action'] === 'add_category') {
            $stmt = $pdo->prepare("INSERT INTO inspection_checklist_categories (id, name, description, sort_order) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                bin2hex(random_bytes(16)),
                $_POST['category_name'],
                $_POST['category_description'],
                $_POST['sort_order'] ?? 0
            ]);
            $success = "Category added successfully!";
        }
        
        elseif ($_POST['action'] === 'add_item') {
            $stmt = $pdo->prepare("INSERT INTO inspection_checklist_items (id, category_id, title, description, points, is_required, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                bin2hex(random_bytes(16)),
                $_POST['category_id'],
                $_POST['item_title'],
                $_POST['item_description'],
                $_POST['points'] ?? 1,
                isset($_POST['is_required']) ? 1 : 0,
                $_POST['item_sort_order'] ?? 0
            ]);
            $success = "Checklist item added successfully!";
        }
        
        elseif ($_POST['action'] === 'update_item') {
            $stmt = $pdo->prepare("UPDATE inspection_checklist_items SET title = ?, description = ?, points = ?, is_required = ?, sort_order = ? WHERE id = ?");
            $stmt->execute([
                $_POST['item_title'],
                $_POST['item_description'],
                $_POST['points'] ?? 1,
                isset($_POST['is_required']) ? 1 : 0,
                $_POST['item_sort_order'] ?? 0,
                $_POST['item_id']
            ]);
            $success = "Checklist item updated successfully!";
        }
        
        elseif ($_POST['action'] === 'delete_item') {
            $stmt = $pdo->prepare("DELETE FROM inspection_checklist_items WHERE id = ?");
            $stmt->execute([$_POST['item_id']]);
            $success = "Checklist item deleted successfully!";
        }
        
        elseif ($_POST['action'] === 'toggle_status') {
            $new_status = $_POST['current_status'] === 'active' ? 'inactive' : 'active';
            $stmt = $pdo->prepare("UPDATE inspection_checklist_items SET status = ? WHERE id = ?");
            $stmt->execute([$new_status, $_POST['item_id']]);
            $success = "Item status updated successfully!";
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get categories with item counts
$categories = $pdo->query("
    SELECT c.*, 
           COUNT(i.id) as item_count,
           COUNT(CASE WHEN i.status = 'active' THEN 1 END) as active_items
    FROM inspection_checklist_categories c 
    LEFT JOIN inspection_checklist_items i ON c.id = i.category_id 
    GROUP BY c.id 
    ORDER BY c.sort_order, c.name
")->fetchAll(PDO::FETCH_ASSOC);

// Get all checklist items with categories
$checklist_items = $pdo->query("
    SELECT i.*, c.name as category_name 
    FROM inspection_checklist_items i 
    JOIN inspection_checklist_categories c ON i.category_id = c.id 
    ORDER BY c.sort_order, i.sort_order, i.title
")->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [
    'total_categories' => count($categories),
    'total_items' => count($checklist_items),
    'active_items' => count(array_filter($checklist_items, fn($item) => $item['status'] === 'active')),
    'required_items' => count(array_filter($checklist_items, fn($item) => $item['is_required'] == 1)),
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspection Checklist Management - OHS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 280px;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover { background: rgba(255,255,255,0.1); color: white; }
        .sidebar .nav-link.active { background: rgba(255,255,255,0.2); color: white; }
        .main-content { margin-left: 280px; padding: 2rem; }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
        }
        .stat-card.categories { border-left-color: #007bff; }
        .stat-card.items { border-left-color: #28a745; }
        .stat-card.active { border-left-color: #ffc107; }
        .stat-card.required { border-left-color: #dc3545; }
        .checklist-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .checklist-item.inactive {
            opacity: 0.6;
            border-left-color: #6c757d;
        }
        .category-section {
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="p-3">
                    <h4><i class="fas fa-shield-alt me-2"></i>OHS Admin</h4>
                    <hr class="text-white">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="businesses.php">
                            <i class="fas fa-building me-2"></i>Businesses
                        </a>
                        <a class="nav-link" href="inspections.php">
                            <i class="fas fa-clipboard-check me-2"></i>Inspections
                        </a>
                        <a class="nav-link active" href="inspection-checklist.php">
                            <i class="fas fa-list-check me-2"></i>Inspection Checklist
                        </a>
                        <a class="nav-link" href="chat.php">
                            <i class="fas fa-comments me-2"></i>Live Chat
                        </a>
                        <a class="nav-link" href="website-settings.php">
                            <i class="fas fa-cog me-2"></i>Website Settings
                        </a>
                        <hr class="text-white">
                        <a class="nav-link" href="../index.php?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-list-check me-2"></i>Inspection Checklist Management</h2>
                        <p class="text-muted mb-0">Manage inspection checklist categories and items</p>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus me-2"></i>Add Category
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-2"></i>Add Item
                        </button>
                    </div>
                </div>
                
                <?php if (isset($success) && $success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?= $success ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error) && $error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?= $error ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card categories">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_categories'] ?></h3>
                                    <p class="text-muted mb-0">Categories</p>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-folder fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card items">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_items'] ?></h3>
                                    <p class="text-muted mb-0">Total Items</p>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card active">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['active_items'] ?></h3>
                                    <p class="text-muted mb-0">Active Items</p>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card required">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['required_items'] ?></h3>
                                    <p class="text-muted mb-0">Required Items</p>
                                </div>
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Checklist Items by Category -->
                <?php 
                $items_by_category = [];
                foreach ($checklist_items as $item) {
                    $items_by_category[$item['category_id']][] = $item;
                }
                ?>
                
                <?php foreach ($categories as $category): ?>
                <div class="category-section">
                    <div class="category-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0"><?= htmlspecialchars($category['name']) ?></h5>
                                <small><?= htmlspecialchars($category['description']) ?></small>
                            </div>
                            <div>
                                <span class="badge bg-light text-dark"><?= $category['item_count'] ?> items</span>
                                <span class="badge bg-success"><?= $category['active_items'] ?> active</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-3">
                        <?php if (isset($items_by_category[$category['id']])): ?>
                            <?php foreach ($items_by_category[$category['id']] as $item): ?>
                            <div class="checklist-item <?= $item['status'] === 'inactive' ? 'inactive' : '' ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?= htmlspecialchars($item['title']) ?>
                                            <?php if ($item['is_required']): ?>
                                                <span class="badge bg-danger ms-2">Required</span>
                                            <?php endif; ?>
                                            <span class="badge bg-info ms-1"><?= $item['points'] ?> pts</span>
                                            <span class="badge bg-<?= $item['status'] === 'active' ? 'success' : 'secondary' ?> ms-1">
                                                <?= ucfirst($item['status']) ?>
                                            </span>
                                        </h6>
                                        <p class="text-muted mb-0"><?= htmlspecialchars($item['description']) ?></p>
                                    </div>
                                    <div class="btn-group btn-group-sm ms-3">
                                        <button class="btn btn-outline-primary" onclick="editItem('<?= $item['id'] ?>', '<?= htmlspecialchars($item['title']) ?>', '<?= htmlspecialchars($item['description']) ?>', '<?= $item['points'] ?>', '<?= $item['is_required'] ?>', '<?= $item['sort_order'] ?>', '<?= $item['category_id'] ?>')" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-<?= $item['status'] === 'active' ? 'warning' : 'success' ?>" onclick="toggleStatus('<?= $item['id'] ?>', '<?= $item['status'] ?>')" title="Toggle Status">
                                            <i class="fas fa-<?= $item['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteItem('<?= $item['id'] ?>', '<?= htmlspecialchars($item['title']) ?>')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <p>No items in this category yet.</p>
                                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addItemModal" onclick="setDefaultCategory('<?= $category['id'] ?>')">
                                    <i class="fas fa-plus me-1"></i>Add First Item
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <?php if (empty($categories)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                    <h4>No Categories Yet</h4>
                    <p class="text-muted">Create your first checklist category to get started.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>Create First Category
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_category">
                        <div class="mb-3">
                            <label for="category_name" class="form-label">Category Name</label>
                            <input type="text" class="form-control" id="category_name" name="category_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="category_description" class="form-label">Description</label>
                            <textarea class="form-control" id="category_description" name="category_description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Add Category</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="itemModalTitle">Add New Checklist Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="itemForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_item" id="itemAction">
                        <input type="hidden" name="item_id" id="itemId">
                        <div class="mb-3">
                            <label for="category_id" class="form-label">Category</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?= $cat['id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="item_title" class="form-label">Item Title</label>
                            <input type="text" class="form-control" id="item_title" name="item_title" required>
                        </div>
                        <div class="mb-3">
                            <label for="item_description" class="form-label">Description</label>
                            <textarea class="form-control" id="item_description" name="item_description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="points" class="form-label">Points</label>
                                    <input type="number" class="form-control" id="points" name="points" value="1" min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item_sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="item_sort_order" name="item_sort_order" value="0">
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_required" name="is_required" checked>
                            <label class="form-check-label" for="is_required">
                                Required Item
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="itemSubmitBtn">Add Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Hidden forms for actions -->
    <form method="POST" id="toggleForm" style="display: none;">
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="item_id" id="toggleItemId">
        <input type="hidden" name="current_status" id="toggleCurrentStatus">
    </form>
    
    <form method="POST" id="deleteForm" style="display: none;">
        <input type="hidden" name="action" value="delete_item">
        <input type="hidden" name="item_id" id="deleteItemId">
    </form>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editItem(id, title, description, points, isRequired, sortOrder, categoryId) {
            document.getElementById('itemModalTitle').textContent = 'Edit Checklist Item';
            document.getElementById('itemAction').value = 'update_item';
            document.getElementById('itemSubmitBtn').textContent = 'Update Item';
            document.getElementById('itemId').value = id;
            document.getElementById('item_title').value = title;
            document.getElementById('item_description').value = description;
            document.getElementById('points').value = points;
            document.getElementById('item_sort_order').value = sortOrder;
            document.getElementById('category_id').value = categoryId;
            document.getElementById('is_required').checked = isRequired == '1';
            
            new bootstrap.Modal(document.getElementById('addItemModal')).show();
        }
        
        function toggleStatus(itemId, currentStatus) {
            if (confirm('Are you sure you want to ' + (currentStatus === 'active' ? 'deactivate' : 'activate') + ' this item?')) {
                document.getElementById('toggleItemId').value = itemId;
                document.getElementById('toggleCurrentStatus').value = currentStatus;
                document.getElementById('toggleForm').submit();
            }
        }
        
        function deleteItem(itemId, title) {
            if (confirm('Are you sure you want to delete "' + title + '"? This action cannot be undone.')) {
                document.getElementById('deleteItemId').value = itemId;
                document.getElementById('deleteForm').submit();
            }
        }
        
        function setDefaultCategory(categoryId) {
            document.getElementById('category_id').value = categoryId;
        }
        
        // Reset form when modal is closed
        document.getElementById('addItemModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('itemModalTitle').textContent = 'Add New Checklist Item';
            document.getElementById('itemAction').value = 'add_item';
            document.getElementById('itemSubmitBtn').textContent = 'Add Item';
            document.getElementById('itemForm').reset();
            document.getElementById('itemId').value = '';
        });
    </script>
</body>
</html>
