<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3 text-gray-800">Manage Businesses</h1>
        <a href="<?= BASE_URL ?>admin/businesses/create" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i>Create New Business
        </a>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (!empty($businesses)): ?>
                <div class="table-responsive">
                    <table class="table table-hover datatable" style="font-size: 0.9rem;">
                        <thead>
                            <tr>
                                <th style="width: 20%;">Business Name</th>
                                <th style="width: 15%;">Owner</th>
                                <th style="width: 18%;">Contact</th>
                                <th style="width: 15%;">Location</th>
                                <th style="width: 10%;">Category</th>
                                <th style="width: 8%;">Status</th>
                                <th class="text-center" style="width: 14%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($businesses as $business): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary-subtle rounded-circle me-2 flex-shrink-0" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                                <span class="avatar-title"><?= strtoupper(substr($business['name'], 0, 2)) ?></span>
                                            </div>
                                            <div class="min-w-0">
                                                <div class="fw-semibold text-truncate" style="max-width: 150px;" title="<?= htmlspecialchars($business['name']) ?>">
                                                    <?= htmlspecialchars($business['name']) ?>
                                                </div>
                                                <small class="text-muted">ID: <?= substr($business['id'], 0, 8) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold text-truncate" style="max-width: 120px;" title="<?= htmlspecialchars($business['owner_name']) ?>">
                                            <?= htmlspecialchars($business['owner_name']) ?>
                                        </div>
                                        <small class="text-muted text-truncate d-block" style="max-width: 120px;" title="<?= htmlspecialchars($business['owner_email']) ?>">
                                            <?= htmlspecialchars($business['owner_email']) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="small text-truncate" style="max-width: 140px;" title="<?= htmlspecialchars($business['email']) ?>">
                                            <i class="fas fa-envelope text-muted me-1"></i>
                                            <?= htmlspecialchars($business['email']) ?>
                                        </div>
                                        <div class="small text-truncate" style="max-width: 140px;" title="<?= htmlspecialchars($business['contact_number']) ?>">
                                            <i class="fas fa-phone text-muted me-1"></i>
                                            <?= htmlspecialchars($business['contact_number']) ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold text-truncate" style="max-width: 110px;" title="<?= htmlspecialchars($business['barangay_name'] ?? 'N/A') ?>">
                                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                            <?= htmlspecialchars($business['barangay_name'] ?? 'N/A') ?>
                                        </div>
                                        <small class="text-muted text-truncate d-block" style="max-width: 110px;" title="<?= htmlspecialchars($business['district_name'] ?? 'N/A') ?>">
                                            <?= htmlspecialchars($business['district_name'] ?? 'N/A') ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info-subtle text-info small text-truncate" style="max-width: 80px;" title="<?= htmlspecialchars($business['category_name']) ?>">
                                            <?= htmlspecialchars($business['category_name']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $business['status'] ?? 'pending';
                                        $statusClasses = [
                                            'pending' => 'warning',
                                            'active' => 'success',
                                            'suspended' => 'danger',
                                            'inactive' => 'secondary'
                                        ];
                                        $statusClass = $statusClasses[$status] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>-subtle text-<?= $statusClass ?> small">
                                            <?= ucfirst($status) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-center gap-1">
                                            <a href="<?= BASE_URL ?>admin/businesses/view/<?= $business['id'] ?>"
                                               class="btn btn-sm btn-outline-primary px-2"
                                               title="View Business Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= BASE_URL ?>admin/businesses/edit/<?= $business['id'] ?>"
                                               class="btn btn-sm btn-outline-secondary px-2"
                                               title="Edit Business">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle px-2"
                                                        data-bs-toggle="dropdown" title="More Actions">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <?php if ($status === 'active'): ?>
                                                        <li>
                                                            <button type="button" class="dropdown-item text-danger"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#suspendModal<?= $business['id'] ?>">
                                                                <i class="fas fa-ban me-2"></i>Suspend Business
                                                            </button>
                                                        </li>
                                                    <?php elseif ($status === 'suspended' || $status === 'pending'): ?>
                                                        <li>
                                                            <button type="button" class="dropdown-item text-success"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#activateModal<?= $business['id'] ?>">
                                                                <i class="fas fa-check-circle me-2"></i>Activate Business
                                                            </button>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <img src="<?= BASE_URL ?>assets/images/empty-business.svg" alt="No businesses" class="mb-3" style="width: 200px;">
                    <h5 class="text-muted mb-3">No Businesses Found</h5>
                    <p class="text-muted mb-3">There are no businesses registered in the system yet.</p>
                    <a href="<?= BASE_URL ?>admin/businesses/create" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i>Create First Business
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modals for each business -->
<?php if (!empty($businesses)): ?>
    <?php foreach ($businesses as $business): ?>
        <?php $status = $business['status'] ?? 'pending'; ?>

        <!-- Suspend Modal for <?= $business['name'] ?> -->
        <?php if ($status === 'active'): ?>
            <div class="modal fade" id="suspendModal<?= $business['id'] ?>" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-ban me-2"></i>Suspend Business
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <p class="text-center mb-3">Are you sure you want to suspend <strong><?= htmlspecialchars($business['name']) ?></strong>?</p>
                            <div class="alert alert-warning">
                                <strong>Business:</strong> <?= htmlspecialchars($business['name']) ?><br>
                                <strong>Owner:</strong> <?= htmlspecialchars($business['owner_name']) ?>
                            </div>
                            <p class="text-muted small">This will prevent the business from operating and restrict their access to system features until reactivated.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <form action="<?= BASE_URL ?>admin/businesses/<?= $business['id'] ?>/suspend" method="POST" style="display: inline;">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-ban me-1"></i>Yes, Suspend
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Activate Modal for <?= $business['name'] ?> -->
        <?php if ($status === 'suspended' || $status === 'pending'): ?>
            <div class="modal fade" id="activateModal<?= $business['id'] ?>" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-check-circle me-2"></i>Activate Business
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                            </div>
                            <p class="text-center mb-3">Are you sure you want to activate <strong><?= htmlspecialchars($business['name']) ?></strong>?</p>
                            <div class="alert alert-info">
                                <strong>Business:</strong> <?= htmlspecialchars($business['name']) ?><br>
                                <strong>Owner:</strong> <?= htmlspecialchars($business['owner_name']) ?>
                            </div>
                            <p class="text-muted small">This will allow the business to access all system features and resume operations.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <form action="<?= BASE_URL ?>admin/businesses/<?= $business['id'] ?>/activate" method="POST" style="display: inline;">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-1"></i>Yes, Activate
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>
<?php endif; ?>

<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialize DataTable
    $('.datatable').DataTable({
        order: [[0, 'asc']],
        pageLength: 25,
        language: {
            search: '<i class="fas fa-search"></i>',
            searchPlaceholder: 'Search businesses...'
        }
    });
});
</script>
<?php $this->endSection(); ?>