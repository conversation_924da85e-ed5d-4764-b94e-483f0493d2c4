<?php

namespace App\Services;

use App\Models\Inspection;
use App\Models\Business;
use App\Models\User;
use App\Models\InspectionChecklistResponse;
use App\Models\BusinessChecklistEvidence;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class ReportService
{
    /**
     * Generate inspection report PDF
     */
    public function generateInspectionReport(Inspection $inspection): string
    {
        $inspection->load([
            'business.barangay.district',
            'business.category',
            'inspector',
            'assignedBy',
            'verifiedBy',
            'checklistResponses.checklistItem.category'
        ]);

        // Group responses by category
        $responsesByCategory = $inspection->checklistResponses
            ->groupBy('checklistItem.category.name');

        // Calculate statistics
        $stats = [
            'total_items' => $inspection->checklistResponses->count(),
            'compliant' => $inspection->checklistResponses->where('compliance_status', 'compliant')->count(),
            'needs_improvement' => $inspection->checklistResponses->where('compliance_status', 'needs_improvement')->count(),
            'non_compliant' => $inspection->checklistResponses->where('compliance_status', 'non_compliant')->count(),
            'not_applicable' => $inspection->checklistResponses->where('compliance_status', 'not_applicable')->count(),
            'critical_violations' => $inspection->getCriticalViolationsCount(),
            'score_percentage' => $inspection->getScorePercentage(),
        ];

        $data = [
            'inspection' => $inspection,
            'responsesByCategory' => $responsesByCategory,
            'stats' => $stats,
            'generated_at' => now(),
            'generated_by' => auth()->user(),
        ];

        $pdf = Pdf::loadView('reports.inspection-report', $data);
        $pdf->setPaper('A4', 'portrait');

        $filename = "inspection_report_{$inspection->business->name}_{$inspection->scheduled_date->format('Y-m-d')}.pdf";
        $path = storage_path("app/reports/{$filename}");

        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $pdf->save($path);

        return $path;
    }

    /**
     * Generate business compliance report
     */
    public function generateBusinessComplianceReport(Business $business): string
    {
        $business->load([
            'category',
            'barangay.district',
            'owner',
            'inspections.inspector',
            'checklistEvidence.checklistItem.category'
        ]);

        // Get compliance statistics
        $complianceStats = $this->getBusinessComplianceStats($business);
        
        // Get recent inspections
        $recentInspections = $business->inspections()
            ->with(['inspector', 'verifiedBy'])
            ->orderBy('scheduled_date', 'desc')
            ->limit(10)
            ->get();

        // Get evidence by category
        $evidenceByCategory = $business->checklistEvidence()
            ->with('checklistItem.category')
            ->get()
            ->groupBy('checklistItem.category.name');

        $data = [
            'business' => $business,
            'complianceStats' => $complianceStats,
            'recentInspections' => $recentInspections,
            'evidenceByCategory' => $evidenceByCategory,
            'generated_at' => now(),
            'generated_by' => auth()->user(),
        ];

        $pdf = Pdf::loadView('reports.business-compliance-report', $data);
        $pdf->setPaper('A4', 'portrait');

        $filename = "compliance_report_{$business->name}_{now()->format('Y-m-d')}.pdf";
        $path = storage_path("app/reports/{$filename}");

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $pdf->save($path);

        return $path;
    }

    /**
     * Generate district summary report
     */
    public function generateDistrictSummaryReport(string $districtId, array $dateRange = null): string
    {
        $dateRange = $dateRange ?? [now()->subMonths(3), now()];
        
        $district = \App\Models\District::with(['barangays.businesses.inspections'])
            ->findOrFail($districtId);

        // Get statistics
        $stats = [
            'total_businesses' => $district->barangays->sum(fn($b) => $b->businesses->count()),
            'active_businesses' => $district->barangays->sum(fn($b) => $b->businesses->where('status', 'active')->count()),
            'compliant_businesses' => $district->barangays->sum(fn($b) => $b->businesses->where('compliance_status', 'compliant')->count()),
            'total_inspections' => $this->getDistrictInspectionCount($district, $dateRange),
            'completed_inspections' => $this->getDistrictCompletedInspectionCount($district, $dateRange),
            'average_compliance_rate' => $this->getDistrictAverageComplianceRate($district),
        ];

        // Get top performing businesses
        $topBusinesses = $this->getTopPerformingBusinesses($district, 10);
        
        // Get businesses needing attention
        $attentionBusinesses = $this->getBusinessesNeedingAttention($district, 10);

        $data = [
            'district' => $district,
            'stats' => $stats,
            'topBusinesses' => $topBusinesses,
            'attentionBusinesses' => $attentionBusinesses,
            'dateRange' => $dateRange,
            'generated_at' => now(),
            'generated_by' => auth()->user(),
        ];

        $pdf = Pdf::loadView('reports.district-summary-report', $data);
        $pdf->setPaper('A4', 'landscape');

        $filename = "district_summary_{$district->name}_{now()->format('Y-m-d')}.pdf";
        $path = storage_path("app/reports/{$filename}");

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $pdf->save($path);

        return $path;
    }

    /**
     * Generate inspector performance report
     */
    public function generateInspectorPerformanceReport(User $inspector, array $dateRange = null): string
    {
        $dateRange = $dateRange ?? [now()->subMonths(6), now()];
        
        $inspector->load(['assignedInspections' => function($query) use ($dateRange) {
            $query->whereBetween('scheduled_date', $dateRange)
                  ->with(['business.barangay.district', 'checklistResponses']);
        }]);

        // Calculate performance metrics
        $metrics = [
            'total_inspections' => $inspector->assignedInspections->count(),
            'completed_inspections' => $inspector->assignedInspections->where('status', 'completed')->count(),
            'average_score' => $inspector->assignedInspections->where('status', 'completed')->avg('score'),
            'completion_rate' => $inspector->assignedInspections->count() > 0 
                ? round(($inspector->assignedInspections->where('status', 'completed')->count() / $inspector->assignedInspections->count()) * 100, 2)
                : 0,
            'average_inspection_time' => $this->calculateAverageInspectionTime($inspector, $dateRange),
            'businesses_inspected' => $inspector->assignedInspections->pluck('business_id')->unique()->count(),
        ];

        // Get monthly performance data
        $monthlyPerformance = $this->getInspectorMonthlyPerformance($inspector, $dateRange);

        $data = [
            'inspector' => $inspector,
            'metrics' => $metrics,
            'monthlyPerformance' => $monthlyPerformance,
            'dateRange' => $dateRange,
            'generated_at' => now(),
            'generated_by' => auth()->user(),
        ];

        $pdf = Pdf::loadView('reports.inspector-performance-report', $data);
        $pdf->setPaper('A4', 'portrait');

        $filename = "inspector_performance_{$inspector->full_name}_{now()->format('Y-m-d')}.pdf";
        $path = storage_path("app/reports/{$filename}");

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $pdf->save($path);

        return $path;
    }

    /**
     * Export businesses to Excel
     */
    public function exportBusinessesToExcel(array $filters = []): string
    {
        $query = Business::with(['category', 'barangay.district', 'owner']);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        if (isset($filters['compliance_status'])) {
            $query->where('compliance_status', $filters['compliance_status']);
        }
        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }
        if (isset($filters['district_id'])) {
            $query->whereHas('barangay', function($q) use ($filters) {
                $q->where('district_id', $filters['district_id']);
            });
        }

        $businesses = $query->get();

        $data = $businesses->map(function($business) {
            return [
                'Business Name' => $business->name,
                'Registration Number' => $business->registration_number,
                'Owner Name' => $business->owner_name,
                'Owner Email' => $business->email,
                'Category' => $business->category->name,
                'District' => $business->barangay->district->name,
                'Barangay' => $business->barangay->name,
                'Address' => $business->address,
                'Contact Number' => $business->contact_number,
                'Employee Count' => $business->employee_count,
                'Status' => ucfirst($business->status),
                'Compliance Status' => ucfirst(str_replace('_', ' ', $business->compliance_status)),
                'Last Inspection' => $business->last_inspection_date ? $business->last_inspection_date->format('Y-m-d') : 'Never',
                'Date Established' => $business->date_established ? $business->date_established->format('Y-m-d') : '',
                'Created At' => $business->created_at->format('Y-m-d H:i:s'),
            ];
        })->toArray();

        $filename = "businesses_export_" . now()->format('Y-m-d_H-i-s') . ".xlsx";
        $path = storage_path("app/exports/{$filename}");

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        Excel::store(new \App\Exports\BusinessesExport($data), "exports/{$filename}");

        return $path;
    }

    /**
     * Generate system analytics report
     */
    public function generateSystemAnalyticsReport(array $dateRange = null): array
    {
        $dateRange = $dateRange ?? [now()->subYear(), now()];

        return [
            'overview' => [
                'total_businesses' => Business::count(),
                'active_businesses' => Business::where('status', 'active')->count(),
                'total_inspections' => Inspection::count(),
                'completed_inspections' => Inspection::where('status', 'completed')->count(),
                'total_inspectors' => User::where('role', 'inspector')->count(),
                'compliance_rate' => $this->getOverallComplianceRate(),
            ],
            'monthly_trends' => $this->getMonthlyTrends($dateRange),
            'category_performance' => $this->getCategoryPerformance(),
            'district_comparison' => $this->getDistrictComparison(),
            'inspector_workload' => $this->getInspectorWorkloadDistribution(),
            'compliance_trends' => $this->getComplianceTrends($dateRange),
            'top_violations' => $this->getTopViolations(),
            'response_times' => $this->getAverageResponseTimes(),
        ];
    }

    /**
     * Helper methods for calculations
     */
    private function getBusinessComplianceStats(Business $business): array
    {
        $totalItems = \App\Models\InspectionChecklistItem::active()->count();
        $uploadedEvidence = $business->checklistEvidence()
            ->where('status', '!=', 'rejected')
            ->distinct('checklist_item_id')
            ->count();
        $approvedEvidence = $business->checklistEvidence()
            ->where('status', 'approved')
            ->distinct('checklist_item_id')
            ->count();

        return [
            'total_items' => $totalItems,
            'uploaded_evidence' => $uploadedEvidence,
            'approved_evidence' => $approvedEvidence,
            'evidence_completion_rate' => $totalItems > 0 ? round(($uploadedEvidence / $totalItems) * 100, 2) : 0,
            'approval_rate' => $uploadedEvidence > 0 ? round(($approvedEvidence / $uploadedEvidence) * 100, 2) : 0,
        ];
    }

    private function getDistrictInspectionCount($district, $dateRange): int
    {
        return Inspection::whereHas('business.barangay', function($query) use ($district) {
            $query->where('district_id', $district->id);
        })->whereBetween('scheduled_date', $dateRange)->count();
    }

    private function getDistrictCompletedInspectionCount($district, $dateRange): int
    {
        return Inspection::whereHas('business.barangay', function($query) use ($district) {
            $query->where('district_id', $district->id);
        })->whereBetween('scheduled_date', $dateRange)
          ->where('status', 'completed')->count();
    }

    private function getDistrictAverageComplianceRate($district): float
    {
        $businesses = Business::whereHas('barangay', function($query) use ($district) {
            $query->where('district_id', $district->id);
        })->where('status', 'active')->get();

        if ($businesses->isEmpty()) {
            return 0;
        }

        $compliantCount = $businesses->where('compliance_status', 'compliant')->count();
        return round(($compliantCount / $businesses->count()) * 100, 2);
    }

    private function getTopPerformingBusinesses($district, $limit): \Illuminate\Database\Eloquent\Collection
    {
        return Business::whereHas('barangay', function($query) use ($district) {
            $query->where('district_id', $district->id);
        })->where('compliance_status', 'compliant')
          ->with(['category', 'barangay'])
          ->orderBy('last_inspection_date', 'desc')
          ->limit($limit)
          ->get();
    }

    private function getBusinessesNeedingAttention($district, $limit): \Illuminate\Database\Eloquent\Collection
    {
        return Business::whereHas('barangay', function($query) use ($district) {
            $query->where('district_id', $district->id);
        })->where('compliance_status', 'non_compliant')
          ->with(['category', 'barangay'])
          ->orderBy('last_inspection_date', 'asc')
          ->limit($limit)
          ->get();
    }

    private function calculateAverageInspectionTime(User $inspector, array $dateRange): float
    {
        $inspections = $inspector->assignedInspections()
            ->whereBetween('scheduled_date', $dateRange)
            ->where('status', 'completed')
            ->whereNotNull('completed_date')
            ->get();

        if ($inspections->isEmpty()) {
            return 0;
        }

        $totalMinutes = $inspections->sum(function($inspection) {
            return $inspection->scheduled_date->diffInMinutes($inspection->completed_date);
        });

        return round($totalMinutes / $inspections->count(), 2);
    }

    private function getInspectorMonthlyPerformance(User $inspector, array $dateRange): array
    {
        return Inspection::where('inspector_id', $inspector->id)
            ->whereBetween('scheduled_date', $dateRange)
            ->selectRaw('YEAR(scheduled_date) as year, MONTH(scheduled_date) as month, COUNT(*) as total, SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    private function getOverallComplianceRate(): float
    {
        $totalBusinesses = Business::where('status', 'active')->count();
        if ($totalBusinesses === 0) return 0;

        $compliantBusinesses = Business::where('status', 'active')
            ->where('compliance_status', 'compliant')->count();

        return round(($compliantBusinesses / $totalBusinesses) * 100, 2);
    }

    private function getMonthlyTrends(array $dateRange): array
    {
        return Inspection::whereBetween('scheduled_date', $dateRange)
            ->selectRaw('YEAR(scheduled_date) as year, MONTH(scheduled_date) as month, COUNT(*) as total, SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    private function getCategoryPerformance(): array
    {
        return \App\Models\BusinessCategory::withCount(['businesses' => function($query) {
            $query->where('compliance_status', 'compliant');
        }])->withCount('businesses')->get()->toArray();
    }

    private function getDistrictComparison(): array
    {
        return \App\Models\District::with(['barangays.businesses'])
            ->get()
            ->map(function($district) {
                $businesses = $district->barangays->flatMap->businesses;
                $compliant = $businesses->where('compliance_status', 'compliant')->count();
                $total = $businesses->count();
                
                return [
                    'name' => $district->name,
                    'total_businesses' => $total,
                    'compliant_businesses' => $compliant,
                    'compliance_rate' => $total > 0 ? round(($compliant / $total) * 100, 2) : 0,
                ];
            })->toArray();
    }

    private function getInspectorWorkloadDistribution(): array
    {
        return User::where('role', 'inspector')
            ->withCount(['assignedInspections' => function($query) {
                $query->where('scheduled_date', '>=', now()->subMonths(3));
            }])
            ->get()
            ->map(function($inspector) {
                return [
                    'name' => $inspector->full_name,
                    'inspection_count' => $inspector->assigned_inspections_count,
                ];
            })->toArray();
    }

    private function getComplianceTrends(array $dateRange): array
    {
        return Business::selectRaw('DATE(updated_at) as date, compliance_status, COUNT(*) as count')
            ->whereBetween('updated_at', $dateRange)
            ->groupBy('date', 'compliance_status')
            ->orderBy('date')
            ->get()
            ->groupBy('date')
            ->toArray();
    }

    private function getTopViolations(): array
    {
        return InspectionChecklistResponse::where('compliance_status', 'non_compliant')
            ->with('checklistItem')
            ->selectRaw('checklist_item_id, COUNT(*) as violation_count')
            ->groupBy('checklist_item_id')
            ->orderBy('violation_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function($response) {
                return [
                    'item_name' => $response->checklistItem->item_name,
                    'violation_count' => $response->violation_count,
                ];
            })->toArray();
    }

    private function getAverageResponseTimes(): array
    {
        // Calculate average time between inspection completion and verification
        $verificationTimes = Inspection::where('status', 'completed')
            ->whereNotNull('verified_at')
            ->whereNotNull('completed_date')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, completed_date, verified_at)) as avg_hours')
            ->first();

        return [
            'verification_time_hours' => $verificationTimes->avg_hours ?? 0,
        ];
    }
}
