<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\District;
use App\Models\Barangay;
use App\Models\InspectorDistrictAssignment;
use App\Models\InspectorBarangayAssignment;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    protected $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Apply filters
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('contact_number', 'like', "%{$search}%");
            });
        }

        $users = $query->latest()->paginate(15);

        // Get filter options
        $roles = [
            'admin' => 'Administrator',
            'inspector' => 'Inspector',
            'business_owner' => 'Business Owner'
        ];

        $statuses = [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'suspended' => 'Suspended',
            'pending' => 'Pending'
        ];

        return view('admin.users.index', compact('users', 'roles', 'statuses'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $districts = District::all();
        $barangays = Barangay::all();

        return view('admin.users.create', compact('districts', 'barangays'));
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'contact_number' => 'required|string|max:50',
            'role' => 'required|in:admin,inspector,business_owner',
            'status' => 'required|in:active,inactive,suspended,pending',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'emergency_contact' => 'nullable|string|max:255',
            'emergency_phone' => 'nullable|string|max:50',
            'password' => 'required|string|min:8|confirmed',
            'districts' => 'nullable|array',
            'districts.*' => 'exists:districts,id',
            'barangays' => 'nullable|array',
            'barangays.*' => 'exists:barangays,id',
        ]);

        DB::transaction(function () use ($validated, $request) {
            // Create user
            $user = User::create([
                'full_name' => $validated['full_name'],
                'email' => $validated['email'],
                'contact_number' => $validated['contact_number'],
                'role' => $validated['role'],
                'status' => $validated['status'],
                'address' => $validated['address'] ?? null,
                'date_of_birth' => $validated['date_of_birth'] ?? null,
                'gender' => $validated['gender'] ?? null,
                'emergency_contact' => $validated['emergency_contact'] ?? null,
                'emergency_phone' => $validated['emergency_phone'] ?? null,
                'password' => Hash::make($validated['password']),
            ]);

            // If user is inspector, assign districts and barangays
            if ($validated['role'] === 'inspector') {
                if ($request->filled('districts')) {
                    foreach ($request->districts as $districtId) {
                        InspectorDistrictAssignment::create([
                            'inspector_id' => $user->id,
                            'district_id' => $districtId,
                            'assigned_by' => auth()->id(),
                        ]);
                    }
                }

                if ($request->filled('barangays')) {
                    foreach ($request->barangays as $barangayId) {
                        InspectorBarangayAssignment::create([
                            'inspector_id' => $user->id,
                            'barangay_id' => $barangayId,
                            'assigned_by' => auth()->id(),
                        ]);
                    }
                }
            }

            // Send welcome email with credentials
            $this->emailService->sendUserCredentials($user, $validated['password']);
        });

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully and credentials sent via email.');
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load([
            'districtAssignments.district',
            'barangayAssignments.barangay',
            'businesses',
            'assignedInspections.business',
            'conductedInspections.business'
        ]);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $districts = District::all();
        $barangays = Barangay::all();
        
        $assignedDistricts = $user->districtAssignments->pluck('district_id')->toArray();
        $assignedBarangays = $user->barangayAssignments->pluck('barangay_id')->toArray();

        return view('admin.users.edit', compact('user', 'districts', 'barangays', 'assignedDistricts', 'assignedBarangays'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'contact_number' => 'required|string|max:50',
            'role' => 'required|in:admin,inspector,business_owner',
            'status' => 'required|in:active,inactive,suspended,pending',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'emergency_contact' => 'nullable|string|max:255',
            'emergency_phone' => 'nullable|string|max:50',
            'password' => 'nullable|string|min:8|confirmed',
            'districts' => 'nullable|array',
            'districts.*' => 'exists:districts,id',
            'barangays' => 'nullable|array',
            'barangays.*' => 'exists:barangays,id',
        ]);

        DB::transaction(function () use ($validated, $request, $user) {
            // Update user data
            $updateData = [
                'full_name' => $validated['full_name'],
                'email' => $validated['email'],
                'contact_number' => $validated['contact_number'],
                'role' => $validated['role'],
                'status' => $validated['status'],
                'address' => $validated['address'] ?? null,
                'date_of_birth' => $validated['date_of_birth'] ?? null,
                'gender' => $validated['gender'] ?? null,
                'emergency_contact' => $validated['emergency_contact'] ?? null,
                'emergency_phone' => $validated['emergency_phone'] ?? null,
            ];

            // Update password if provided
            if (!empty($validated['password'])) {
                $updateData['password'] = Hash::make($validated['password']);
            }

            $user->update($updateData);

            // Update inspector assignments if role is inspector
            if ($validated['role'] === 'inspector') {
                // Remove existing assignments
                InspectorDistrictAssignment::where('inspector_id', $user->id)->delete();
                InspectorBarangayAssignment::where('inspector_id', $user->id)->delete();

                // Add new district assignments
                if ($request->filled('districts')) {
                    foreach ($request->districts as $districtId) {
                        InspectorDistrictAssignment::create([
                            'inspector_id' => $user->id,
                            'district_id' => $districtId,
                            'assigned_by' => auth()->id(),
                        ]);
                    }
                }

                // Add new barangay assignments
                if ($request->filled('barangays')) {
                    foreach ($request->barangays as $barangayId) {
                        InspectorBarangayAssignment::create([
                            'inspector_id' => $user->id,
                            'barangay_id' => $barangayId,
                            'assigned_by' => auth()->id(),
                        ]);
                    }
                }
            } else {
                // If role changed from inspector, remove all assignments
                InspectorDistrictAssignment::where('inspector_id', $user->id)->delete();
                InspectorBarangayAssignment::where('inspector_id', $user->id)->delete();
            }
        });

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        // Prevent deletion of current user
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        // Check if user has related data
        if ($user->businesses()->count() > 0 || $user->assignedInspections()->count() > 0) {
            return back()->with('error', 'Cannot delete user with associated businesses or inspections. Consider deactivating instead.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status
     */
    public function toggleStatus(User $user)
    {
        $newStatus = $user->status === 'active' ? 'inactive' : 'active';
        
        $user->update(['status' => $newStatus]);

        $statusText = $newStatus === 'active' ? 'activated' : 'deactivated';

        return back()->with('success', "User {$statusText} successfully.");
    }

    /**
     * Reset user password
     */
    public function resetPassword(User $user)
    {
        $newPassword = $this->generateRandomPassword();
        
        $user->update([
            'password' => Hash::make($newPassword)
        ]);

        // Send new password via email
        $this->emailService->sendPasswordReset($user, $newPassword);

        return back()->with('success', 'Password reset successfully. New password sent to user via email.');
    }

    /**
     * Generate random password
     */
    private function generateRandomPassword($length = 12)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
}
