-- Chatbot Database Schema
-- Add chatbot functionality to existing OHS system

-- Create chatbot_settings table for global chatbot configuration
CREATE TABLE IF NOT EXISTS chatbot_settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create chatbot_responses table for predefined responses
CREATE TABLE IF NOT EXISTS chatbot_responses (
    id VARCHAR(36) PRIMARY KEY,
    trigger_keywords TEXT NOT NULL,
    response_text TEXT NOT NULL,
    response_type ENUM('text', 'quick_reply', 'redirect', 'escalate') DEFAULT 'text',
    category VARCHAR(100) DEFAULT 'general',
    priority INT DEFAULT 1,
    is_active TINYINT(1) DEFAULT 1,
    usage_count INT DEFAULT 0,
    created_by <PERSON><PERSON><PERSON><PERSON>(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create chatbot_conversations table to log all chatbot interactions
CREATE TABLE IF NOT EXISTS chatbot_conversations (
    id VARCHAR(36) PRIMARY KEY,
    chat_room_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    user_message TEXT NOT NULL,
    bot_response TEXT NOT NULL,
    response_id VARCHAR(36) DEFAULT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    was_helpful TINYINT(1) DEFAULT NULL,
    escalated_to_human TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (response_id) REFERENCES chatbot_responses(id) ON DELETE SET NULL
);

-- Create chatbot_knowledge_base table for OHS-specific information
CREATE TABLE IF NOT EXISTS chatbot_knowledge_base (
    id VARCHAR(36) PRIMARY KEY,
    topic VARCHAR(200) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    keywords TEXT NOT NULL,
    category ENUM('safety', 'compliance', 'inspection', 'permits', 'general') DEFAULT 'general',
    is_active TINYINT(1) DEFAULT 1,
    view_count INT DEFAULT 0,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create chatbot_escalations table to track when conversations are escalated to humans
CREATE TABLE IF NOT EXISTS chatbot_escalations (
    id VARCHAR(36) PRIMARY KEY,
    chat_room_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    reason TEXT NOT NULL,
    escalated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    handled_by VARCHAR(36) DEFAULT NULL,
    handled_at TIMESTAMP NULL,
    status ENUM('pending', 'assigned', 'resolved') DEFAULT 'pending',
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (handled_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default chatbot settings
INSERT INTO chatbot_settings (id, setting_name, setting_value, description) VALUES
(UUID(), 'chatbot_enabled', 'true', 'Enable or disable the chatbot system'),
(UUID(), 'auto_respond_when_offline', 'true', 'Automatically respond when no admins are online'),
(UUID(), 'response_delay', '2', 'Delay in seconds before bot responds (to simulate typing)'),
(UUID(), 'escalation_threshold', '3', 'Number of unresolved queries before escalating to human'),
(UUID(), 'greeting_message', 'Hello! I''m the OHS Assistant. How can I help you today?', 'Default greeting message'),
(UUID(), 'offline_message', 'Our support team is currently offline. I''ll do my best to help you!', 'Message when admins are offline'),
(UUID(), 'escalation_message', 'Let me connect you with a human agent for better assistance.', 'Message when escalating to human'),
(UUID(), 'fallback_message', 'I''m sorry, I didn''t understand that. Could you please rephrase your question?', 'Default response when no match found');

-- Insert default OHS knowledge base
INSERT INTO chatbot_knowledge_base (id, topic, question, answer, keywords, category, created_by) VALUES
(UUID(), 'Business Registration', 'How do I register my business?', 'To register your business, you need to provide: business name, address, contact information, business category, and required permits. You can start the registration process from your dashboard.', 'register business registration how to register', 'general', NULL),
(UUID(), 'Safety Permits', 'What safety permits do I need?', 'Common safety permits include: Business Permit, Sanitary Permit, Fire Safety Permit, and Environmental Compliance Certificate. The specific permits depend on your business type and location.', 'permits safety permit fire sanitary environmental', 'permits', NULL),
(UUID(), 'Inspection Process', 'What happens during an inspection?', 'During an inspection, our certified inspectors will check your workplace for safety compliance, review your permits, assess working conditions, and provide recommendations for improvements if needed.', 'inspection process what happens during inspect', 'inspection', NULL),
(UUID(), 'Compliance Evidence', 'How do I submit compliance evidence?', 'You can submit compliance evidence by uploading photos or documents through your business dashboard. Go to Compliance section and click "Upload Evidence" for the specific requirement.', 'compliance evidence submit upload how to submit', 'compliance', NULL),
(UUID(), 'Safety Requirements', 'What are the basic safety requirements?', 'Basic safety requirements include: proper ventilation, emergency exits, fire extinguishers, first aid kits, safety signage, and employee safety training. Specific requirements vary by business type.', 'safety requirements basic workplace safety', 'safety', NULL),
(UUID(), 'Contact Information', 'How can I contact support?', 'You can contact support through this chat system, or reach out to the Bacoor City OHS office during business hours. For emergencies, please call the emergency hotline.', 'contact support help phone number', 'general', NULL),
(UUID(), 'Inspection Scheduling', 'How do I schedule an inspection?', 'Inspections are typically scheduled by our admin team based on your business registration and compliance status. You will receive notifications about upcoming inspections.', 'schedule inspection when inspection appointment', 'inspection', NULL),
(UUID(), 'Business Categories', 'What business categories are available?', 'Available categories include: Retail, Food & Beverage, Manufacturing, Services, and Construction. Choose the category that best describes your primary business activity.', 'business category categories type types', 'general', NULL);

-- Insert default chatbot responses
INSERT INTO chatbot_responses (id, trigger_keywords, response_text, response_type, category, priority, created_by) VALUES
(UUID(), 'hello,hi,hey,good morning,good afternoon', 'Hello! I''m the OHS Assistant. How can I help you with your occupational health and safety needs today?', 'text', 'greeting', 1, NULL),
(UUID(), 'help,assistance,support', 'I''m here to help! I can assist you with:\n• Business registration\n• Safety permits and requirements\n• Inspection processes\n• Compliance evidence submission\n• General OHS questions\n\nWhat would you like to know about?', 'quick_reply', 'general', 1, NULL),
(UUID(), 'thank you,thanks,appreciate', 'You''re welcome! Is there anything else I can help you with regarding your business safety and compliance?', 'text', 'general', 1, NULL),
(UUID(), 'bye,goodbye,see you,exit', 'Thank you for using the OHS support system. Have a safe and productive day! Feel free to return if you have more questions.', 'text', 'general', 1, NULL),
(UUID(), 'human,agent,person,talk to someone', 'I understand you''d like to speak with a human agent. Let me check if someone is available to assist you.', 'escalate', 'escalation', 1, NULL),
(UUID(), 'emergency,urgent,accident,injury', 'For emergencies, please call 911 immediately. For urgent safety concerns, contact the Bacoor City Emergency Response Team. I can also escalate this to our safety team right away.', 'escalate', 'emergency', 1, NULL),
(UUID(), 'business hours,office hours,when open', 'The Bacoor City OHS office is open Monday to Friday, 8:00 AM to 5:00 PM. This chat support is available 24/7, and I''m here to help even when the office is closed!', 'text', 'general', 1, NULL),
(UUID(), 'complaint,report,violation,unsafe', 'I take safety concerns seriously. Please provide details about the issue, and I''ll make sure it gets proper attention from our safety team.', 'escalate', 'safety', 1, NULL);

-- Add chatbot_active column to chat_rooms table to track if chatbot is active in a room
ALTER TABLE chat_rooms ADD COLUMN IF NOT EXISTS chatbot_active TINYINT(1) DEFAULT 0;
ALTER TABLE chat_rooms ADD COLUMN IF NOT EXISTS last_admin_activity TIMESTAMP NULL;

-- Add indexes for better performance
CREATE INDEX idx_chatbot_responses_keywords ON chatbot_responses(trigger_keywords(100));
CREATE INDEX idx_chatbot_conversations_room ON chatbot_conversations(chat_room_id);
CREATE INDEX idx_chatbot_knowledge_keywords ON chatbot_knowledge_base(keywords(100));
CREATE INDEX idx_chatbot_escalations_status ON chatbot_escalations(status);
