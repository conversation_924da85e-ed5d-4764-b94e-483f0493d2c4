<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">My Profile</h1>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= BASE_URL ?>inspector/profile/update">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?= htmlspecialchars($user['full_name']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($user['email']) ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role</label>
                                    <input type="text" class="form-control" value="Safety Inspector" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <input type="text" class="form-control" value="<?= ucfirst($user['status']) ?>" readonly>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <h6 class="text-muted mb-3">Change Password (Optional)</h6>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Inspector Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle bg-info text-white mx-auto mb-3" style="width: 80px; height: 80px; line-height: 80px; border-radius: 50%; font-size: 2rem;">
                            <?= strtoupper(substr($user['full_name'], 0, 1)) ?>
                        </div>
                        <h5 class="mb-1"><?= htmlspecialchars($user['full_name']) ?></h5>
                        <p class="text-muted mb-0">Safety Inspector</p>
                    </div>

                    <hr>

                    <div class="mb-2">
                        <strong>Inspector Since:</strong><br>
                        <span class="text-muted"><?= date('F d, Y', strtotime($user['created_at'])) ?></span>
                    </div>

                    <?php if (!empty($user['last_login'])): ?>
                    <div class="mb-2">
                        <strong>Last Login:</strong><br>
                        <span class="text-muted"><?= date('M d, Y h:i A', strtotime($user['last_login'])) ?></span>
                    </div>
                    <?php endif; ?>

                    <div class="mb-2">
                        <strong>Account Status:</strong><br>
                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                            <?= ucfirst($user['status']) ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>inspector/settings" class="btn btn-outline-primary">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                        <a href="<?= BASE_URL ?>inspector/dashboard" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-outline-info">
                            <i class="fas fa-calendar"></i> My Schedule
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentPassword = document.getElementById('current_password');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    function togglePasswordFields() {
        const hasCurrentPassword = currentPassword.value.length > 0;
        newPassword.required = hasCurrentPassword;
        confirmPassword.required = hasCurrentPassword;
    }

    currentPassword.addEventListener('input', togglePasswordFields);
    
    // Password confirmation validation
    confirmPassword.addEventListener('input', function() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    });
});
</script>
<?php $this->endSection() ?>
