<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <?= $business ? 'Inspections for ' . htmlspecialchars($business['name']) : 'All Inspections' ?>
        </h1>
        <div>
            <?php if ($business): ?>
                <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Business
                </a>
            <?php endif; ?>
            <a href="<?= BASE_URL ?>business" class="btn btn-outline-secondary">
                <i class="fas fa-building"></i> My Businesses
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($business): ?>
        <!-- Business Information Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Business Information</h6>
                                <p class="mb-1"><strong>Name:</strong> <?= htmlspecialchars($business['name']) ?></p>
                                <p class="mb-1"><strong>Registration:</strong> <?= htmlspecialchars($business['registration_number']) ?></p>
                                <p class="mb-1"><strong>Category:</strong> <?= htmlspecialchars($business['category_name'] ?? 'N/A') ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Contact Information</h6>
                                <p class="mb-1"><strong>Email:</strong> <?= htmlspecialchars($business['email']) ?></p>
                                <p class="mb-1"><strong>Phone:</strong> <?= htmlspecialchars($business['contact_number']) ?></p>
                                <p class="mb-1"><strong>Address:</strong> <?= htmlspecialchars($business['address']) ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Inspections List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-clipboard-check me-2"></i>Inspection History
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($inspections)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Date Scheduled</th>
                                <th>Inspector</th>
                                <th>Status</th>
                                <th>Score</th>
                                <th>Compliance Rating</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inspections as $inspection): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></strong>
                                        <?php if ($inspection['completed_date']): ?>
                                            <br><small class="text-muted">Completed: <?= date('M d, Y', strtotime($inspection['completed_date'])) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?= htmlspecialchars($inspection['inspector_name'] ?? 'Not Assigned') ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= 
                                        $inspection['status'] == 'completed' ? 'success' : 
                                        ($inspection['status'] == 'scheduled' ? 'primary' : 
                                        ($inspection['status'] == 'confirmed' ? 'info' : 
                                        ($inspection['status'] == 'cancelled' ? 'danger' : 'warning'))) 
                                    ?>">
                                        <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($inspection['score']): ?>
                                        <span class="badge bg-<?= 
                                            $inspection['score'] >= 90 ? 'success' : 
                                            ($inspection['score'] >= 70 ? 'warning' : 'danger') 
                                        ?>">
                                            <?= $inspection['score'] ?>%
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($inspection['compliance_rating']): ?>
                                        <span class="badge bg-<?= 
                                            in_array($inspection['compliance_rating'], ['A', 'B']) ? 'success' : 
                                            ($inspection['compliance_rating'] == 'C' ? 'warning' : 'danger') 
                                        ?>">
                                            Grade <?= $inspection['compliance_rating'] ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= BASE_URL ?>inspection/view/<?= $inspection['id'] ?>" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($inspection['status'] == 'completed' && $inspection['findings']): ?>
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#findingsModal<?= $inspection['id'] ?>"
                                                    title="View Findings">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Findings Modals -->
                <?php foreach ($inspections as $inspection): ?>
                    <?php if ($inspection['status'] == 'completed' && $inspection['findings']): ?>
                        <div class="modal fade" id="findingsModal<?= $inspection['id'] ?>" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Inspection Findings - <?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Findings:</h6>
                                                <p><?= nl2br(htmlspecialchars($inspection['findings'])) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Recommendations:</h6>
                                                <p><?= nl2br(htmlspecialchars($inspection['recommendations'] ?? 'None provided')) ?></p>
                                            </div>
                                        </div>
                                        <?php if ($inspection['notes']): ?>
                                            <div class="mt-3">
                                                <h6>Additional Notes:</h6>
                                                <p><?= nl2br(htmlspecialchars($inspection['notes'])) ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted mb-3">No Inspections Found</h5>
                    <p class="text-muted mb-3">
                        <?= $business ? 'This business has no inspection history yet.' : 'No inspections have been scheduled.' ?>
                    </p>
                    <?php if ($business): ?>
                        <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Business Details
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "order": [[ 0, "desc" ]], // Sort by date descending
        "pageLength": 25,
        "responsive": true
    });
});
</script>
<?php $this->endSection() ?>
