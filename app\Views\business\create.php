<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h4>Register New Business</h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger">
                            <?= $_SESSION['error'] ?>
                            <?php unset($_SESSION['error']); ?>
                        </div>
                    <?php endif; ?>

                    <form action="<?= BASE_URL ?>business/create" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Business Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registration_number" class="form-label">Registration Number</label>
                                    <input type="text" class="form-control" id="registration_number" name="registration_number" required>
                                </div>
                            </div>
                        </div>
  
                          <div class="row">
                               <div class="col-md-6">
                                  <div class="mb-3">
                                      <label for="category_id" class="form-label">Business Category</label>
<select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>"><?= $category['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="district_id" class="form-label">District <span class="text-danger">*</span></label>
                                    <select class="form-select" id="district_id" name="district_id" onchange="loadBarangays()" required>
                                        <option value="">Select District First</option>
                                        <?php foreach ($districts as $district): ?>
                                            <option value="<?= $district['id'] ?>"><?= $district['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="barangay_id" class="form-label">Barangay <span class="text-danger">*</span></label>
                                    <select class="form-select" id="barangay_id" name="barangay_id" required>
                                        <option value="">Select District First</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a barangay.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee_count" class="form-label">Employee Count</label>
                                    <input type="number" class="form-control" id="employee_count" name="employee_count" min="1" required value="1">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_established" class="form-label">Date Established</label>
                                    <input type="date" class="form-control" id="date_established" name="date_established">
                                </div>
                            </div>
                             <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_number" class="form-label">Contact Number</label>
                                    <input type="tel" class="form-control" id="contact_number" name="contact_number" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                             <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Business Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Business Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                                </div>
                            </div>
                        </div>

                        <?php if ($user['role'] === 'admin'): ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="owner_id" class="form-label">Business Owner</label>
                                    <select class="form-select" id="owner_id" name="owner_id" required>
                                        <option value="">Select Business Owner</option>
                                        <?php foreach ($users as $owner): ?>
                                            <option value="<?= $owner['id'] ?>"><?= $owner['full_name'] ?> (<?= $owner['email'] ?>)</option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Register Business</button>
                                <a href="<?= BASE_URL ?>business" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
      </div>
  </div>

<script>
// Store data for JavaScript use
const barangaysData = <?= json_encode($barangays) ?>;

function loadBarangays() {
    const districtId = document.getElementById('district_id').value;
    const barangaySelect = document.getElementById('barangay_id');

    // Clear existing options
    barangaySelect.innerHTML = '<option value="">Select Barangay</option>';

    if (!districtId) {
        barangaySelect.innerHTML = '<option value="">Select District First</option>';
        return;
    }

    // Filter barangays by district
    const districtBarangays = barangaysData.filter(barangay => barangay.district_id == districtId);

    // Add barangay options
    districtBarangays.forEach(barangay => {
        const option = document.createElement('option');
        option.value = barangay.id;
        option.textContent = barangay.name;
        barangaySelect.appendChild(option);
    });
}
</script>

<?php $this->endSection() ?>