<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Barangay extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'district_id',
        'name',
        'code',
    ];

    /**
     * Get the district this barangay belongs to
     */
    public function district()
    {
        return $this->belongsTo(District::class);
    }

    /**
     * Get businesses in this barangay
     */
    public function businesses()
    {
        return $this->hasMany(Business::class);
    }

    /**
     * Get inspector assignments for this barangay
     */
    public function inspectorAssignments()
    {
        return $this->hasMany(InspectorBarangayAssignment::class);
    }

    /**
     * Get assigned inspectors
     */
    public function assignedInspectors()
    {
        return $this->belongsToMany(User::class, 'inspector_barangay_assignments', 'barangay_id', 'inspector_id')
                    ->where('status', 'active');
    }
}
