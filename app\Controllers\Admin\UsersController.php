<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Models\User;

class UsersController extends Controller {
    private $userModel;

    public function __construct() {
        parent::__construct();
        $this->requireAdmin();
        $this->userModel = new User();
    }

    public function index() {
        $users = $this->userModel->all();
        return $this->render('admin/users/index', [
            'users' => $users,
            'title' => 'Users Management'
        ]);
    }

    public function create() {
        return $this->render('admin/users/create', [
            'title' => 'Create User'
        ]);
    }

    public function store() {
        if (!$this->isPost()) {
            $this->redirect('admin/users');
        }

        $errors = $this->validateRequired(['email', 'password', 'full_name', 'role', 'status']);
        
        if (!empty($errors)) {
            $this->setFlash('error', 'Please fill in all required fields');
            $_SESSION['errors'] = $errors;
            $_SESSION['old'] = $this->input();
            $this->redirect('admin/users/create');
            return;
        }

        if (!$this->validateEmail($this->input('email'))) {
            $this->setFlash('error', 'Please enter a valid email address');
            $_SESSION['old'] = $this->input();
            $this->redirect('admin/users/create');
            return;
        }

        if ($this->userModel->findByEmail($this->input('email'))) {
            $this->setFlash('error', 'Email address is already taken');
            $_SESSION['old'] = $this->input();
            $this->redirect('admin/users/create');
            return;
        }

        $user = [
            'email' => $this->input('email'),
            'password' => password_hash($this->input('password'), PASSWORD_DEFAULT),
            'full_name' => $this->input('full_name'),
            'role' => $this->input('role'),
            'status' => $this->input('status')
        ];

        if ($this->userModel->create($user)) {
            $this->setFlash('success', 'User created successfully');
            $this->redirect('admin/users');
        } else {
            $this->setFlash('error', 'Failed to create user');
            $_SESSION['old'] = $this->input();
            $this->redirect('admin/users/create');
        }
    }

    public function edit($id) {
        $user = $this->userModel->find($id);
        
        if (!$user) {
            $this->setFlash('error', 'User not found');
            $this->redirect('admin/users');
            return;
        }

        return $this->render('admin/users/edit', [
            'user' => $user,
            'title' => 'Edit User'
        ]);
    }

    public function update($id) {
        if (!$this->isPost()) {
            $this->redirect('admin/users');
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            $this->setFlash('error', 'User not found');
            $this->redirect('admin/users');
            return;
        }

        $errors = $this->validateRequired(['email', 'full_name', 'role', 'status']);
        
        if (!empty($errors)) {
            $this->setFlash('error', 'Please fill in all required fields');
            $_SESSION['errors'] = $errors;
            $this->redirect("admin/users/{$id}/edit");
            return;
        }

        if (!$this->validateEmail($this->input('email'))) {
            $this->setFlash('error', 'Please enter a valid email address');
            $this->redirect("admin/users/{$id}/edit");
            return;
        }

        $existingUser = $this->userModel->findByEmail($this->input('email'));
        if ($existingUser && $existingUser['id'] !== $id) {
            $this->setFlash('error', 'Email address is already taken');
            $this->redirect("admin/users/{$id}/edit");
            return;
        }

        $userData = [
            'email' => $this->input('email'),
            'full_name' => $this->input('full_name'),
            'role' => $this->input('role'),
            'status' => $this->input('status')
        ];

        // Only update password if a new one is provided
        if ($this->input('password')) {
            $userData['password'] = password_hash($this->input('password'), PASSWORD_DEFAULT);
        }

        if ($this->userModel->update($id, $userData)) {
            $this->setFlash('success', 'User updated successfully');
            $this->redirect('admin/users');
        } else {
            $this->setFlash('error', 'Failed to update user');
            $this->redirect("admin/users/{$id}/edit");
        }
    }

    public function delete($id) {
        $user = $this->userModel->find($id);
        
        if (!$user) {
            $this->setFlash('error', 'User not found');
            $this->redirect('admin/users');
            return;
        }

        if ($user['id'] === $_SESSION['user_id']) {
            $this->setFlash('error', 'You cannot delete your own account');
            $this->redirect('admin/users');
            return;
        }

        if ($this->userModel->delete($id)) {
            $this->setFlash('success', 'User deleted successfully');
        } else {
            $this->setFlash('error', 'Failed to delete user');
        }

        $this->redirect('admin/users');
    }
} 