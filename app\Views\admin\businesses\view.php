<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Business Details</h1>
        <div>
            <a href="<?= BASE_URL ?>admin/businesses/edit/<?= $business['id'] ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?= BASE_URL ?>admin/businesses" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Business Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Business Name</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['name']) ?></p>
                            
                            <h6 class="text-muted">Registration Number</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['registration_number']) ?></p>
                            
                            <h6 class="text-muted">Email</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['email']) ?></p>
                            
                            <h6 class="text-muted">Contact Number</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['contact_number']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Owner</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['owner_name']) ?></p>
                            
                            <h6 class="text-muted">Category</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['category_name']) ?></p>
                            
                            <h6 class="text-muted">District/Barangay</h6>
                            <p class="mb-3">
                                <?php if (!empty($business['barangay_name'])): ?>
                                    <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                    <?= htmlspecialchars($business['barangay_name']) ?>, <?= htmlspecialchars($business['district_name']) ?>
                                <?php elseif (!empty($business['district_name'])): ?>
                                    <i class="fas fa-map-marker-alt text-warning me-1"></i>
                                    <?= htmlspecialchars($business['district_name']) ?>
                                    <span class="badge bg-warning text-dark ms-2">No barangay assigned</span>
                                <?php else: ?>
                                    <i class="fas fa-exclamation-triangle text-danger me-1"></i>
                                    <span class="text-danger">No location assigned</span>
                                <?php endif; ?>
                            </p>
                            
                            <h6 class="text-muted">Status</h6>
                            <p class="mb-3">
                                <?php
                                $status = $business['status'] ?? 'pending';
                                $statusClasses = [
                                    'pending' => 'warning',
                                    'active' => 'success',
                                    'suspended' => 'danger',
                                    'inactive' => 'secondary'
                                ];
                                $statusClass = $statusClasses[$status] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?= $statusClass ?>">
                                    <?= ucfirst($status) ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6 class="text-muted">Address</h6>
                        <p><?= nl2br(htmlspecialchars($business['address'])) ?></p>
                    </div>
                    
                    <?php if (!empty($business['employee_count'])): ?>
                    <div class="mt-3">
                        <h6 class="text-muted">Number of Employees</h6>
                        <p><?= $business['employee_count'] ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($business['date_established'])): ?>
                    <div class="mt-3">
                        <h6 class="text-muted">Date Established</h6>
                        <p><?= date('F d, Y', strtotime($business['date_established'])) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Inspections -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Inspections</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($inspections)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Inspector</th>
                                        <th>Status</th>
                                        <th>Score</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inspections as $inspection): ?>
                                    <tr>
                                        <td><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                        <td><?= htmlspecialchars($inspection['inspector_name'] ?? 'Not assigned') ?></td>
                                        <td>
                                            <?php
                                            $status = $inspection['status'] ?? 'pending';
                                            $statusClasses = [
                                                'scheduled' => 'primary',
                                                'confirmed' => 'info',
                                                'completed' => 'success',
                                                'cancelled' => 'danger',
                                                'in_progress' => 'warning',
                                                'pending' => 'warning'
                                            ];
                                            $statusClass = $statusClasses[$status] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?>">
                                                <?= ucfirst(str_replace('_', ' ', $status)) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($inspection['score'])): ?>
                                                <?= $inspection['score'] ?>%
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No inspections found for this business.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/businesses/edit/<?= $business['id'] ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Business
                        </a>
                        
                        <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-success">
                            <i class="fas fa-calendar-check"></i> Schedule Inspection
                        </a>
                        
                        <form method="POST" action="<?= BASE_URL ?>admin/businesses/delete/<?= $business['id'] ?>" 
                              onsubmit="return confirm('Are you sure you want to delete this business?')">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash"></i> Delete Business
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Business Registered</h6>
                                <small class="text-muted"><?= date('M d, Y', strtotime($business['created_at'])) ?></small>
                            </div>
                        </div>
                        
                        <?php if (!empty($business['date_established'])): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Business Established</h6>
                                <small class="text-muted"><?= date('M d, Y', strtotime($business['date_established'])) ?></small>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    background: #f8f9fc;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #4e73df;
}
</style>
<?php $this->endSection() ?>
