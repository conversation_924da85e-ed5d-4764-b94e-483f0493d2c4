<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3 text-gray-800">
            <i class="fas fa-file-check me-2 text-primary"></i>Verify Compliance Evidence
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="<?= BASE_URL ?>inspector/compliance" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Evidence List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Evidence Details -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Evidence Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h5 class="mb-1"><?= htmlspecialchars($business['name'] ?? 'Unknown Business') ?></h5>
                            <p class="text-muted mb-2"><?= htmlspecialchars($business['owner_name'] ?? 'N/A') ?></p>
                            <span class="badge bg-info fs-6 me-2">
                                <?= htmlspecialchars($compliance_types[$evidence['compliance_type']] ?? $evidence['compliance_type'] ?? 'N/A') ?>
                            </span>
                            <?php
                            $status = $evidence['status'] ?? 'pending';
                            $statusConfig = [
                                'verified' => ['success', 'check-circle', 'Verified'],
                                'rejected' => ['danger', 'times-circle', 'Rejected'],
                                'pending' => ['warning', 'clock', 'Pending Review']
                            ];
                            $config = $statusConfig[$status] ?? ['secondary', 'question-circle', 'Unknown'];
                            ?>
                            <span class="badge bg-<?= $config[0] ?> fs-6">
                                <i class="fas fa-<?= $config[1] ?> me-1"></i><?= $config[2] ?>
                            </span>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">Uploaded</small>
                            <p class="mb-0">
                                <?= isset($evidence['created_at']) ? date('M d, Y', strtotime($evidence['created_at'])) : 'N/A' ?>
                            </p>
                        </div>
                    </div>

                    <?php if ($evidence['verification_notes']): ?>
                        <div class="alert alert-info">
                            <strong>Previous Notes:</strong> <?= htmlspecialchars($evidence['verification_notes']) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Evidence File Preview -->
                    <?php if (isset($evidence['photo_path']) && !empty($evidence['photo_path'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted">Evidence File:</h6>
                                <div class="border rounded p-3 bg-light">
                                    <?php
                                    $fileName = basename($evidence['photo_path']);
                                    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                                    ?>
                                    
                                    <?php if (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                        <!-- Image Preview -->
                                        <div class="text-center">
                                            <img src="<?= BASE_URL ?>public/serve-evidence-simple.php?file=<?= htmlspecialchars($fileName) ?>" 
                                                 class="img-fluid rounded shadow-sm" 
                                                 style="max-height: 400px; max-width: 100%;"
                                                 alt="Evidence Image">
                                        </div>
                                    <?php else: ?>
                                        <!-- File Download Link -->
                                        <div class="text-center">
                                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                            <p class="mb-2"><strong><?= htmlspecialchars($fileName) ?></strong></p>
                                            <p class="text-muted">File type: <?= strtoupper($fileExtension) ?></p>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="text-center mt-3">
                                        <a href="<?= BASE_URL ?>public/serve-evidence-simple.php?file=<?= htmlspecialchars($fileName) ?>" 
                                           class="btn btn-outline-primary" target="_blank">
                                            <i class="fas fa-external-link-alt me-1"></i>View Full Size
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Verification Form -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clipboard-check me-2"></i>Verification Action
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($evidence['status'] === 'pending'): ?>
                        <form action="<?= BASE_URL ?>inspector/compliance/verify/<?= $evidence['id'] ?>" method="POST">
                            <div class="mb-3">
                                <label for="status" class="form-label">
                                    <i class="fas fa-check-circle me-1"></i>Verification Decision <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Select Decision</option>
                                    <option value="verified" class="text-success">✅ Verify Evidence</option>
                                    <option value="rejected" class="text-danger">❌ Reject Evidence</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Verification Notes
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="4" 
                                          placeholder="Add your verification notes here..."></textarea>
                                <div class="form-text">
                                    Provide details about your verification decision. Required for rejections.
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Submit Verification
                                </button>
                                <a href="<?= BASE_URL ?>inspector/compliance" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This evidence has already been <?= $evidence['status'] ?>. 
                            <?php if ($evidence['status'] === 'verified'): ?>
                                No further action is required.
                            <?php else: ?>
                                You can re-verify it if needed.
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($evidence['status'] === 'rejected'): ?>
                            <form action="<?= BASE_URL ?>inspector/compliance/verify/<?= $evidence['id'] ?>" method="POST">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Re-verification Decision</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Decision</option>
                                        <option value="verified" class="text-success">✅ Verify Evidence</option>
                                        <option value="rejected" class="text-danger">❌ Keep Rejected</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Re-verification Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="Add notes for re-verification..."></textarea>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-redo me-1"></i>Re-verify Evidence
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>


        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation
    $('form').on('submit', function(e) {
        const status = $('#status').val();
        const notes = $('#notes').val().trim();
        
        if (status === 'rejected' && notes === '') {
            e.preventDefault();
            alert('Please provide a reason for rejecting this evidence.');
            $('#notes').focus();
            return false;
        }
        
        if (status === '') {
            e.preventDefault();
            alert('Please select a verification decision.');
            $('#status').focus();
            return false;
        }
        
        return confirm('Are you sure you want to ' + status + ' this evidence?');
    });
    
    // Status change handler
    $('#status').on('change', function() {
        const status = $(this).val();
        const notesField = $('#notes');
        
        if (status === 'rejected') {
            notesField.attr('required', true);
            notesField.attr('placeholder', 'Please provide a reason for rejection (required)');
            $('.form-text').html('<strong class="text-danger">Rejection reason is required.</strong>');
        } else {
            notesField.removeAttr('required');
            notesField.attr('placeholder', 'Add your verification notes here...');
            $('.form-text').html('Provide details about your verification decision. Required for rejections.');
        }
    });
});
</script>

<style>
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-1px);
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}
</style>

<?php $this->endSection(); ?>
