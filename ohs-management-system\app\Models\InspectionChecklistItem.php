<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InspectionChecklistItem extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'category_id',
        'item_code',
        'item_name',
        'description',
        'compliance_requirement',
        'points',
        'is_critical',
        'sort_order',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_critical' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the category this item belongs to
     */
    public function category()
    {
        return $this->belongsTo(InspectionChecklistCategory::class, 'category_id');
    }

    /**
     * Get inspection responses for this item
     */
    public function responses()
    {
        return $this->hasMany(InspectionChecklistResponse::class, 'checklist_item_id');
    }

    /**
     * Get business evidence for this item
     */
    public function businessEvidence()
    {
        return $this->hasMany(BusinessChecklistEvidence::class, 'checklist_item_id');
    }

    /**
     * Scope for active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for critical items
     */
    public function scopeCritical($query)
    {
        return $query->where('is_critical', true);
    }

    /**
     * Scope for ordered items
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get compliance rate for this item across all inspections
     */
    public function getComplianceRateAttribute(): float
    {
        $totalResponses = $this->responses()->count();
        if ($totalResponses === 0) {
            return 0;
        }

        $compliantResponses = $this->responses()
            ->where('compliance_status', 'compliant')
            ->count();

        return round(($compliantResponses / $totalResponses) * 100, 2);
    }
}
