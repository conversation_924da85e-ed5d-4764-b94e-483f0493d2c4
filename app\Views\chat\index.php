<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Chat</h1>
        <?php if ($user['role'] === 'business_owner'): ?>
            <div>
                <a href="<?= BASE_URL ?>business" class="btn btn-outline-secondary">
                    <i class="fas fa-building"></i> My Businesses
                </a>
            </div>
        <?php endif; ?>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comments me-2"></i>Chat Rooms
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($rooms)): ?>
                        <div class="list-group">
                            <?php foreach ($rooms as $room): ?>
                                <a href="<?= BASE_URL ?>chat/room/<?= $room['id'] ?>" 
                                   class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <i class="fas fa-building text-primary me-2"></i>
                                                <?= htmlspecialchars($room['business_name']) ?>
                                                <?php if ($room['unread_count'] > 0): ?>
                                                    <span class="badge bg-danger ms-2"><?= $room['unread_count'] ?></span>
                                                <?php endif; ?>
                                            </h6>
                                            <p class="mb-1 text-muted">
                                                <?php if ($room['subject']): ?>
                                                    <strong>Subject:</strong> <?= htmlspecialchars($room['subject']) ?>
                                                <?php endif; ?>
                                            </p>
                                            <?php if ($room['last_message']): ?>
                                                <p class="mb-1 text-muted small">
                                                    <i class="fas fa-comment me-1"></i>
                                                    <?= htmlspecialchars(substr($room['last_message'], 0, 100)) ?>
                                                    <?= strlen($room['last_message']) > 100 ? '...' : '' ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">
                                                <?php if ($room['last_message_time']): ?>
                                                    <?= date('M d, Y h:i A', strtotime($room['last_message_time'])) ?>
                                                <?php else: ?>
                                                    <?= date('M d, Y h:i A', strtotime($room['created_at'])) ?>
                                                <?php endif; ?>
                                            </small>
                                            <div class="mt-1">
                                                <span class="badge bg-<?= $room['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst($room['status']) ?>
                                                </span>
                                            </div>
                                            <div class="mt-1 small text-muted">
                                                <?php if ($user['role'] !== 'business_owner'): ?>
                                                    <div><strong>Owner:</strong> <?= htmlspecialchars($room['business_owner_name']) ?></div>
                                                <?php endif; ?>
                                                <?php if ($room['admin_name'] && $user['role'] !== 'admin'): ?>
                                                    <div><strong>Admin:</strong> <?= htmlspecialchars($room['admin_name']) ?></div>
                                                <?php endif; ?>
                                                <?php if ($room['inspector_name'] && $user['role'] !== 'inspector'): ?>
                                                    <div><strong>Inspector:</strong> <?= htmlspecialchars($room['inspector_name']) ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-3">No Chat Rooms</h5>
                            <p class="text-muted mb-3">
                                <?php if ($user['role'] === 'business_owner'): ?>
                                    You haven't started any conversations yet. Create a chat room from your business page to get support.
                                <?php elseif ($user['role'] === 'admin'): ?>
                                    No active chat rooms. Business owners will create chat rooms when they need support.
                                <?php else: ?>
                                    No chat rooms assigned to you yet.
                                <?php endif; ?>
                            </p>
                            <?php if ($user['role'] === 'business_owner'): ?>
                                <a href="<?= BASE_URL ?>business" class="btn btn-primary">
                                    <i class="fas fa-building me-2"></i>Go to My Businesses
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh chat list every 30 seconds
setInterval(function() {
    // Only refresh if user is still on the chat index page
    if (window.location.pathname.endsWith('/chat') || window.location.pathname.endsWith('/chat/')) {
        location.reload();
    }
}, 30000);
</script>
<?php $this->endSection() ?>
