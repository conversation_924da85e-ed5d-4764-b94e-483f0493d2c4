<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-check"></i> Inspection Checklist
        </h1>
        <div>
            <a href="<?= BASE_URL ?>business" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Businesses
            </a>
        </div>
    </div>

    <!-- Business Info -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-building"></i> <?= htmlspecialchars($business['name']) ?>
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Address:</strong> <?= htmlspecialchars($business['address']) ?></p>
                    <p><strong>Contact:</strong> <?= htmlspecialchars($business['contact_number']) ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Email:</strong> <?= htmlspecialchars($business['email']) ?></p>
                    <p><strong>Status:</strong> 
                        <span class="badge bg-<?= $business['status'] === 'active' ? 'success' : 'warning' ?>">
                            <?= ucfirst($business['status']) ?>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Inspector Progress -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-check me-2"></i>Inspector Checklist Progress
                </h6>
                <span class="text-muted">
                    <?php if ($inspector_progress && $current_inspection): ?>
                        <?= $inspector_progress['completed_items'] ?> / <?= $inspector_progress['total_items'] ?> items reviewed (<?= $inspector_progress['completion_percentage'] ?>%)
                    <?php else: ?>
                        No active inspection
                    <?php endif; ?>
                </span>
            </div>
            <div class="progress" style="height: 20px;">
                <?php if ($inspector_progress && $current_inspection): ?>
                    <div class="progress-bar bg-info" role="progressbar"
                         style="width: <?= $inspector_progress['completion_percentage'] ?>%"
                         aria-valuenow="<?= $inspector_progress['completion_percentage'] ?>"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        <?= $inspector_progress['completion_percentage'] ?>%
                    </div>
                <?php else: ?>
                    <div class="progress-bar bg-secondary" role="progressbar"
                         style="width: 0%"
                         aria-valuenow="0"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        0%
                    </div>
                <?php endif; ?>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    <?php if ($inspector_progress && $current_inspection): ?>
                        This shows how many checklist items the inspector has reviewed during your inspection.
                        <?php if ($current_inspection['status'] === 'completed'): ?>
                            <span class="text-success"><strong>Inspection completed!</strong></span>
                        <?php elseif ($current_inspection['status'] === 'in_progress'): ?>
                            <span class="text-info"><strong>Inspection in progress...</strong></span>
                        <?php else: ?>
                            <span class="text-warning"><strong>Inspection scheduled</strong></span>
                        <?php endif; ?>
                    <?php else: ?>
                        No inspection has been scheduled for this business yet.
                    <?php endif; ?>
                </small>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Instructions:</strong> Upload evidence documents for each checklist item to prepare for your inspection. 
        Inspectors will review your uploaded evidence during the inspection process. Accepted file types: Images (JPG, PNG, GIF), PDF, and Word documents. Maximum file size: 10MB.
    </div>

    <!-- Checklist Categories -->
    <?php if (!empty($categorized_checklist)): ?>
        <?php foreach ($categorized_checklist as $category): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3" data-bs-toggle="collapse" data-bs-target="#category-<?= md5($category['name']) ?>" style="cursor: pointer;">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chevron-down me-2"></i>
                        <?= htmlspecialchars($category['name']) ?>
                        <span class="badge bg-secondary ms-2">
                            <?= count($category['items']) ?> items
                        </span>
                        <span class="badge bg-info ms-1">
                            <?= count(array_filter($category['items'], function($item) { return $item['evidence_count'] > 0; })) ?>/<?= count($category['items']) ?> with evidence
                        </span>
                    </h6>
                </div>
                <div class="collapse show" id="category-<?= md5($category['name']) ?>">
                    <div class="card-body">
                        <?php foreach ($category['items'] as $item): ?>
                            <div class="checklist-item border rounded p-3 mb-3" data-item-id="<?= $item['id'] ?>">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6 class="mb-2">
                                            <?= htmlspecialchars($item['item_name']) ?>
                                            <?php if ($item['is_critical']): ?>
                                                <span class="badge bg-danger ms-2">CRITICAL</span>
                                            <?php endif; ?>
                                            <span class="badge bg-secondary ms-1"><?= $item['points'] ?> pts</span>
                                        </h6>
                                        <p class="text-muted mb-2"><?= htmlspecialchars($item['description']) ?></p>
                                        <?php if ($item['compliance_requirement']): ?>
                                            <div class="alert alert-light py-2 mb-2">
                                                <small><strong>Requirement:</strong> <?= htmlspecialchars($item['compliance_requirement']) ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="evidence-status mb-3">
                                            <?php if ($item['evidence_count'] > 0): ?>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-file-check text-success me-2"></i>
                                                    <span class="text-success"><?= $item['evidence_count'] ?> evidence file(s) uploaded</span>
                                                </div>
                                                <?php if ($item['latest_status']): ?>
                                                    <span class="badge bg-<?= 
                                                        $item['latest_status'] === 'approved' ? 'success' : 
                                                        ($item['latest_status'] === 'rejected' ? 'danger' : 'warning') 
                                                    ?>">
                                                        <?= ucfirst($item['latest_status']) ?>
                                                    </span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-file-times text-muted me-2"></i>
                                                    <span class="text-muted">No evidence uploaded</span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="btn-group-vertical w-100" role="group">
                                            <button type="button" class="btn btn-primary btn-sm upload-evidence-btn" 
                                                    data-item-id="<?= $item['id'] ?>" 
                                                    data-item-name="<?= htmlspecialchars($item['item_name']) ?>">
                                                <i class="fas fa-upload me-1"></i>Upload Evidence
                                            </button>
                                            <?php if ($item['evidence_count'] > 0): ?>
                                                <a href="<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>/evidence/<?= $item['id'] ?>" 
                                                   class="btn btn-outline-info btn-sm">
                                                    <i class="fas fa-eye me-1"></i>View Evidence (<?= $item['evidence_count'] ?>)
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Checklist Items Available</h5>
                <p class="text-muted">The inspection checklist has not been configured yet.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Upload Evidence Modal -->
<div class="modal fade" id="uploadEvidenceModal" tabindex="-1" aria-labelledby="uploadEvidenceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadEvidenceModalLabel">
                    <i class="fas fa-upload me-2"></i>Upload Evidence
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="uploadEvidenceForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label"><strong>Checklist Item:</strong></label>
                        <p id="modalItemName" class="text-muted"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="evidenceFile" class="form-label">Evidence File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="evidenceFile" name="evidence_file" 
                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx" required>
                        <div class="form-text">
                            Accepted formats: JPG, PNG, GIF, PDF, DOC, DOCX. Maximum size: 10MB.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="evidenceNotes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="evidenceNotes" name="notes" rows="3" 
                                  placeholder="Add any additional notes about this evidence..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="uploadSubmitBtn">
                        <i class="fas fa-upload me-1"></i>Upload Evidence
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let currentItemId = null;

// Handle upload evidence button clicks
document.querySelectorAll('.upload-evidence-btn').forEach(button => {
    button.addEventListener('click', function() {
        currentItemId = this.dataset.itemId;
        const itemName = this.dataset.itemName;
        
        document.getElementById('modalItemName').textContent = itemName;
        document.getElementById('evidenceFile').value = '';
        document.getElementById('evidenceNotes').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('uploadEvidenceModal'));
        modal.show();
    });
});

// Handle form submission
document.getElementById('uploadEvidenceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!currentItemId) {
        alert('No checklist item selected');
        return;
    }
    
    const formData = new FormData(this);
    const submitBtn = document.getElementById('uploadSubmitBtn');
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
    submitBtn.disabled = true;
    
    fetch(`<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>/upload/${currentItemId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and reload page
            bootstrap.Modal.getInstance(document.getElementById('uploadEvidenceModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while uploading the evidence');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Upload Evidence';
        submitBtn.disabled = false;
    });
});
</script>

<?php $this->endSection(); ?>
