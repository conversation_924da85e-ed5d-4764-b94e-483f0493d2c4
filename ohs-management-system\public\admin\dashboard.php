<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed.');
}

// Get comprehensive statistics
$stats = [];
$stats['total_users'] = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
$stats['total_admins'] = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'")->fetchColumn();
$stats['total_inspectors'] = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'inspector'")->fetchColumn();
$stats['total_business_owners'] = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'business_owner'")->fetchColumn();
$stats['total_districts'] = $pdo->query("SELECT COUNT(*) FROM districts")->fetchColumn();
$stats['total_barangays'] = $pdo->query("SELECT COUNT(*) FROM barangays")->fetchColumn();
$stats['total_categories'] = $pdo->query("SELECT COUNT(*) FROM business_categories")->fetchColumn();

// Get recent users
$recent_users = $pdo->query("SELECT * FROM users ORDER BY created_at DESC LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);

// Get recent districts
$recent_districts = $pdo->query("SELECT * FROM districts ORDER BY created_at DESC LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - OHS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 280px;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .sidebar .nav-header {
            font-size: 0.75rem;
            font-weight: 600;
            letter-spacing: 1px;
        }
        .main-content {
            margin-left: 280px;
            padding: 2rem;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card.users { border-left-color: #007bff; }
        .stat-card.inspectors { border-left-color: #28a745; }
        .stat-card.business-owners { border-left-color: #ffc107; }
        .stat-card.districts { border-left-color: #dc3545; }
        .stat-card.barangays { border-left-color: #6f42c1; }
        .stat-card.categories { border-left-color: #fd7e14; }
        .quick-action-btn {
            border-radius: 10px;
            padding: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .recent-activity {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="p-3">
                    <h4><i class="fas fa-shield-alt me-2"></i>OHS Admin</h4>
                    <hr class="text-white">
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        
                        <!-- User Management -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">USER MANAGEMENT</h6>
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                            <a class="nav-link" href="inspector-assignments.php">
                                <i class="fas fa-user-tie me-2"></i>Inspector Assignments
                            </a>
                        </div>
                        
                        <!-- Business Management -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">BUSINESS MANAGEMENT</h6>
                            <a class="nav-link" href="businesses.php">
                                <i class="fas fa-building me-2"></i>Businesses
                            </a>
                            <a class="nav-link" href="business-categories.php">
                                <i class="fas fa-tags me-2"></i>Business Categories
                            </a>
                        </div>
                        
                        <!-- Location Management -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">LOCATION MANAGEMENT</h6>
                            <a class="nav-link" href="districts.php">
                                <i class="fas fa-map me-2"></i>Districts
                            </a>
                            <a class="nav-link" href="barangays.php">
                                <i class="fas fa-map-marker-alt me-2"></i>Barangays
                            </a>
                        </div>
                        
                        <!-- Inspection Management -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">INSPECTION MANAGEMENT</h6>
                            <a class="nav-link" href="inspections.php">
                                <i class="fas fa-clipboard-check me-2"></i>Inspections
                            </a>
                            <a class="nav-link" href="inspection-checklist.php">
                                <i class="fas fa-list-check me-2"></i>Inspection Checklist
                            </a>
                            <a class="nav-link" href="compliance-evidence.php">
                                <i class="fas fa-file-upload me-2"></i>Compliance Evidence
                            </a>
                        </div>
                        
                        <!-- Communication -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">COMMUNICATION</h6>
                            <a class="nav-link" href="chat.php">
                                <i class="fas fa-comments me-2"></i>Live Chat
                            </a>
                            <a class="nav-link" href="notifications.php">
                                <i class="fas fa-bell me-2"></i>Notifications
                            </a>
                            <a class="nav-link" href="contact-messages.php">
                                <i class="fas fa-envelope me-2"></i>Contact Messages
                            </a>
                        </div>
                        
                        <!-- Content Management -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">CONTENT MANAGEMENT</h6>
                            <a class="nav-link" href="homepage-content.php">
                                <i class="fas fa-home me-2"></i>Homepage Content
                            </a>
                            <a class="nav-link" href="announcements.php">
                                <i class="fas fa-bullhorn me-2"></i>Announcements
                            </a>
                        </div>
                        
                        <!-- Website Settings -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">WEBSITE SETTINGS</h6>
                            <a class="nav-link" href="website-settings.php">
                                <i class="fas fa-cog me-2"></i>Website Settings
                            </a>
                            <a class="nav-link" href="website-customization.php">
                                <i class="fas fa-palette me-2"></i>Website Customization
                            </a>
                        </div>
                        
                        <!-- Reports & Analytics -->
                        <div class="nav-section">
                            <h6 class="nav-header text-white-50 mt-3 mb-2">REPORTS & ANALYTICS</h6>
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>Reports
                            </a>
                            <a class="nav-link" href="analytics.php">
                                <i class="fas fa-chart-line me-2"></i>Analytics
                            </a>
                        </div>
                        
                        <hr class="text-white mt-4">
                        <a class="nav-link" href="../index.php?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>Admin Dashboard</h2>
                        <p class="text-muted mb-0">Complete OHS Management System Overview</p>
                    </div>
                    <div class="text-end">
                        <div class="text-muted">Welcome back,</div>
                        <strong><?= htmlspecialchars($_SESSION['user_name']) ?></strong>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-2 mb-3">
                        <div class="stat-card users">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_users'] ?></h3>
                                    <p class="text-muted mb-0">Total Users</p>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card inspectors">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_inspectors'] ?></h3>
                                    <p class="text-muted mb-0">Inspectors</p>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-user-tie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card business-owners">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_business_owners'] ?></h3>
                                    <p class="text-muted mb-0">Business Owners</p>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-briefcase fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card districts">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_districts'] ?></h3>
                                    <p class="text-muted mb-0">Districts</p>
                                </div>
                                <div class="text-danger">
                                    <i class="fas fa-map fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card barangays">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_barangays'] ?></h3>
                                    <p class="text-muted mb-0">Barangays</p>
                                </div>
                                <div class="text-purple">
                                    <i class="fas fa-map-marker-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card categories">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total_categories'] ?></h3>
                                    <p class="text-muted mb-0">Categories</p>
                                </div>
                                <div class="text-orange">
                                    <i class="fas fa-tags fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="recent-activity">
                            <div class="card-header bg-transparent border-0 pb-0">
                                <h5 class="mb-3">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2 mb-2">
                                        <a href="users.php?action=create" class="btn btn-primary quick-action-btn w-100">
                                            <i class="fas fa-user-plus d-block mb-2"></i>Add User
                                        </a>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <a href="inspector-assignments.php" class="btn btn-success quick-action-btn w-100">
                                            <i class="fas fa-user-tie d-block mb-2"></i>Assign Inspector
                                        </a>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <a href="businesses.php" class="btn btn-warning quick-action-btn w-100">
                                            <i class="fas fa-building d-block mb-2"></i>Manage Businesses
                                        </a>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <a href="chat.php" class="btn btn-info quick-action-btn w-100">
                                            <i class="fas fa-comments d-block mb-2"></i>Live Chat
                                        </a>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <a href="website-settings.php" class="btn btn-secondary quick-action-btn w-100">
                                            <i class="fas fa-cog d-block mb-2"></i>Website Settings
                                        </a>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <a href="reports.php" class="btn btn-dark quick-action-btn w-100">
                                            <i class="fas fa-chart-bar d-block mb-2"></i>View Reports
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="recent-activity">
                            <div class="card-header bg-transparent border-0">
                                <h5 class="mb-0">Recent Users</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Role</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_users as $user): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($user['full_name']) ?></td>
                                                <td><?= htmlspecialchars($user['email']) ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'inspector' ? 'warning' : 'info') ?>">
                                                        <?= ucfirst(str_replace('_', ' ', $user['role'])) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                        <?= ucfirst($user['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= date('M j, Y', strtotime($user['created_at'])) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="recent-activity">
                            <div class="card-header bg-transparent border-0">
                                <h5 class="mb-0">System Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Database Connection</span>
                                    <span class="badge bg-success">Active</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>System Status</span>
                                    <span class="badge bg-success">Online</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Last Backup</span>
                                    <span class="text-muted">Today</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Version</span>
                                    <span class="text-muted">v2.0.0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
