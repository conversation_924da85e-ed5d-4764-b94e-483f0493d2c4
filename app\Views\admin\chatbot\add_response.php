<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus text-primary me-2"></i>Add Chatbot Response
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/chatbot/responses" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Responses
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot me-2"></i>Response Details
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>admin/chatbot/responses/add" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trigger_keywords" class="form-label">
                                        <i class="fas fa-key me-1"></i>Trigger Keywords *
                                    </label>
                                    <textarea class="form-control" id="trigger_keywords" name="trigger_keywords" 
                                              rows="3" required placeholder="hello,hi,hey,good morning"><?= htmlspecialchars($data['trigger_keywords'] ?? '') ?></textarea>
                                    <div class="form-text">
                                        Separate multiple keywords with commas. These words will trigger this response.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="response_type" class="form-label">
                                        <i class="fas fa-cog me-1"></i>Response Type
                                    </label>
                                    <select class="form-select" id="response_type" name="response_type" required>
                                        <option value="text" <?= ($data['response_type'] ?? '') === 'text' ? 'selected' : '' ?>>
                                            Text Response
                                        </option>
                                        <option value="quick_reply" <?= ($data['response_type'] ?? '') === 'quick_reply' ? 'selected' : '' ?>>
                                            Quick Reply (with options)
                                        </option>
                                        <option value="escalate" <?= ($data['response_type'] ?? '') === 'escalate' ? 'selected' : '' ?>>
                                            Escalate to Human
                                        </option>
                                        <option value="redirect" <?= ($data['response_type'] ?? '') === 'redirect' ? 'selected' : '' ?>>
                                            Redirect to Page
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Category
                                    </label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="general" <?= ($data['category'] ?? '') === 'general' ? 'selected' : '' ?>>
                                            General
                                        </option>
                                        <option value="greeting" <?= ($data['category'] ?? '') === 'greeting' ? 'selected' : '' ?>>
                                            Greeting
                                        </option>
                                        <option value="safety" <?= ($data['category'] ?? '') === 'safety' ? 'selected' : '' ?>>
                                            Safety
                                        </option>
                                        <option value="compliance" <?= ($data['category'] ?? '') === 'compliance' ? 'selected' : '' ?>>
                                            Compliance
                                        </option>
                                        <option value="inspection" <?= ($data['category'] ?? '') === 'inspection' ? 'selected' : '' ?>>
                                            Inspection
                                        </option>
                                        <option value="permits" <?= ($data['category'] ?? '') === 'permits' ? 'selected' : '' ?>>
                                            Permits
                                        </option>
                                        <option value="emergency" <?= ($data['category'] ?? '') === 'emergency' ? 'selected' : '' ?>>
                                            Emergency
                                        </option>
                                        <option value="escalation" <?= ($data['category'] ?? '') === 'escalation' ? 'selected' : '' ?>>
                                            Escalation
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="priority" class="form-label">
                                        <i class="fas fa-sort-numeric-up me-1"></i>Priority
                                    </label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="1" <?= ($data['priority'] ?? '') == '1' ? 'selected' : '' ?>>1 - Lowest</option>
                                        <option value="2" <?= ($data['priority'] ?? '') == '2' ? 'selected' : '' ?>>2 - Low</option>
                                        <option value="3" <?= ($data['priority'] ?? '') == '3' ? 'selected' : '' ?>>3 - Normal</option>
                                        <option value="4" <?= ($data['priority'] ?? '') == '4' ? 'selected' : '' ?>>4 - Medium</option>
                                        <option value="5" <?= ($data['priority'] ?? '') == '5' ? 'selected' : '' ?>>5 - High</option>
                                        <option value="6" <?= ($data['priority'] ?? '') == '6' ? 'selected' : '' ?>>6 - Higher</option>
                                        <option value="7" <?= ($data['priority'] ?? '') == '7' ? 'selected' : '' ?>>7 - Very High</option>
                                        <option value="8" <?= ($data['priority'] ?? '') == '8' ? 'selected' : '' ?>>8 - Critical</option>
                                        <option value="9" <?= ($data['priority'] ?? '') == '9' ? 'selected' : '' ?>>9 - Emergency</option>
                                        <option value="10" <?= ($data['priority'] ?? '') == '10' ? 'selected' : '' ?>>10 - Highest</option>
                                    </select>
                                    <div class="form-text">
                                        Higher priority responses are matched first.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="response_text" class="form-label">
                                        <i class="fas fa-comment me-1"></i>Response Text *
                                    </label>
                                    <textarea class="form-control" id="response_text" name="response_text" 
                                              rows="8" required placeholder="Enter the chatbot's response message..."><?= htmlspecialchars($data['response_text'] ?? '') ?></textarea>
                                    <div class="form-text">
                                        This is the message the chatbot will send to users.
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-lightbulb me-2"></i>Tips for Good Responses:</h6>
                                    <ul class="mb-0">
                                        <li>Be clear and concise</li>
                                        <li>Use friendly, professional tone</li>
                                        <li>Include specific OHS information</li>
                                        <li>Provide next steps when possible</li>
                                        <li>Use line breaks (\n) for formatting</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Response
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Response Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Response Types:</h6>
                    <ul class="list-unstyled">
                        <li><strong>Text:</strong> Simple text response</li>
                        <li><strong>Quick Reply:</strong> Response with suggested actions</li>
                        <li><strong>Escalate:</strong> Transfer to human agent</li>
                        <li><strong>Redirect:</strong> Send to specific page</li>
                    </ul>

                    <h6 class="text-primary mt-3">Categories:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-primary me-2">General</span> Common questions</li>
                        <li><span class="badge bg-success me-2">Safety</span> Safety procedures</li>
                        <li><span class="badge bg-info me-2">Compliance</span> Compliance help</li>
                        <li><span class="badge bg-warning me-2">Emergency</span> Urgent matters</li>
                    </ul>

                    <h6 class="text-primary mt-3">Priority Levels:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-danger me-2">8-10</span> Emergency/Critical</li>
                        <li><span class="badge bg-warning me-2">5-7</span> Important</li>
                        <li><span class="badge bg-secondary me-2">1-4</span> Normal</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>
