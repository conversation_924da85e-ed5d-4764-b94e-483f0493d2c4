<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt"></i> Evidence for: <?= htmlspecialchars($checklist_item['item_name']) ?>
        </h1>
        <div>
            <a href="<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Checklist
            </a>
        </div>
    </div>

    <!-- Checklist Item Info -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-clipboard-check"></i> Checklist Item Details
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5><?= htmlspecialchars($checklist_item['item_name']) ?>
                        <?php if ($checklist_item['is_critical']): ?>
                            <span class="badge bg-danger ms-2">CRITICAL</span>
                        <?php endif; ?>
                        <span class="badge bg-secondary ms-1"><?= $checklist_item['points'] ?> pts</span>
                    </h5>
                    <p class="text-muted"><?= htmlspecialchars($checklist_item['description']) ?></p>
                    <?php if ($checklist_item['compliance_requirement']): ?>
                        <div class="alert alert-light">
                            <strong>Compliance Requirement:</strong><br>
                            <?= htmlspecialchars($checklist_item['compliance_requirement']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-primary w-100" id="uploadNewEvidenceBtn">
                        <i class="fas fa-upload me-1"></i>Upload New Evidence
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Evidence List -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-folder-open"></i> Uploaded Evidence (<?= count($evidence) ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($evidence)): ?>
                <div class="row">
                    <?php foreach ($evidence as $item): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0"><?= htmlspecialchars($item['file_name']) ?></h6>
                                        <span class="badge bg-<?= 
                                            $item['status'] === 'approved' ? 'success' : 
                                            ($item['status'] === 'rejected' ? 'danger' : 'warning') 
                                        ?>">
                                            <?= ucfirst($item['status']) ?>
                                        </span>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-file me-1"></i><?= strtoupper(pathinfo($item['file_name'], PATHINFO_EXTENSION)) ?>
                                            <span class="ms-2">
                                                <i class="fas fa-weight me-1"></i><?= number_format($item['file_size'] / 1024, 1) ?> KB
                                            </span>
                                        </small>
                                    </div>
                                    
                                    <?php if ($item['notes']): ?>
                                        <p class="card-text small"><?= htmlspecialchars($item['notes']) ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>Uploaded: <?= date('M d, Y g:i A', strtotime($item['created_at'])) ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($item['reviewed_at']): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-user-check me-1"></i>Reviewed: <?= date('M d, Y g:i A', strtotime($item['reviewed_at'])) ?>
                                                <?php if ($item['reviewed_by_name']): ?>
                                                    by <?= htmlspecialchars($item['reviewed_by_name']) ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($item['review_notes']): ?>
                                        <div class="alert alert-<?= $item['status'] === 'approved' ? 'success' : 'danger' ?> py-2">
                                            <small><strong>Review Notes:</strong> <?= htmlspecialchars($item['review_notes']) ?></small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group w-100" role="group">
                                        <?php
                                        $fileExtension = strtolower(pathinfo($item['file_name'], PATHINFO_EXTENSION));
                                        $isImage = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif']);
                                        ?>

                                        <?php if ($isImage): ?>
                                            <button type="button" class="btn btn-outline-primary btn-sm view-image-btn"
                                                    data-image-src="<?= BASE_URL ?>public/serve-evidence.php?file=<?= urlencode($item['file_path']) ?>"
                                                    data-image-name="<?= htmlspecialchars($item['file_name']) ?>">
                                                <i class="fas fa-eye me-1"></i>View
                                            </button>
                                        <?php else: ?>
                                            <a href="<?= BASE_URL ?>public/serve-evidence.php?file=<?= urlencode($item['file_path']) ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>View
                                            </a>
                                        <?php endif; ?>

                                        <a href="<?= BASE_URL ?>public/serve-evidence.php?file=<?= urlencode($item['file_path']) ?>" download class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-download me-1"></i>Download
                                        </a>
                                        <?php if ($item['status'] === 'pending'): ?>
                                            <button type="button" class="btn btn-outline-danger btn-sm delete-evidence-btn"
                                                    data-evidence-id="<?= $item['id'] ?>"
                                                    data-file-name="<?= htmlspecialchars($item['file_name']) ?>">
                                                <i class="fas fa-trash me-1"></i>Delete
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Evidence Uploaded</h5>
                    <p class="text-muted">Upload evidence documents to support compliance with this checklist item.</p>
                    <button type="button" class="btn btn-primary" id="uploadFirstEvidenceBtn">
                        <i class="fas fa-upload me-1"></i>Upload First Evidence
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Image Viewer Modal -->
<div class="modal fade" id="imageViewerModal" tabindex="-1" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="imageViewerModalLabel">
                    <i class="fas fa-image me-2"></i>Evidence Image
                </h5>
                <div class="d-flex align-items-center">
                    <!-- Zoom Controls -->
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-outline-light btn-sm" id="zoomOutBtn" title="Zoom Out">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button type="button" class="btn btn-outline-light btn-sm" id="resetZoomBtn" title="Reset Zoom">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button type="button" class="btn btn-outline-light btn-sm" id="zoomInBtn" title="Zoom In">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-3 text-center bg-light" style="min-height: 500px;">
                <div id="imageContainer" style="overflow: auto; max-height: 75vh; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); cursor: grab; position: relative; min-height: 400px; display: flex; align-items: center; justify-content: center;">
                    <div id="imageLoader" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); display: none; z-index: 10;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2 text-muted">Loading image...</div>
                    </div>
                    <img id="modalImage" src="" alt="Evidence Image"
                         style="max-width: 100%; max-height: 100%; height: auto; transition: transform 0.3s ease; transform-origin: center; display: none; object-fit: contain;"
                         onload="document.getElementById('imageLoader').style.display='none'; this.style.display='block'; this.parentElement.style.alignItems='flex-start'; this.parentElement.style.justifyContent='flex-start';"
                         onerror="document.getElementById('imageLoader').innerHTML='<div class=\'text-danger text-center\'><i class=\'fas fa-exclamation-triangle fa-2x\'></i><br><br>Failed to load image<br><small>Please check if the file exists and is accessible</small></div>';">
                </div>
            </div>
            <div class="modal-footer bg-light justify-content-center">
                <div class="text-muted">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Use zoom controls or scroll wheel to zoom. Click and drag to pan when zoomed.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Evidence Modal -->
<div class="modal fade" id="uploadEvidenceModal" tabindex="-1" aria-labelledby="uploadEvidenceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadEvidenceModalLabel">
                    <i class="fas fa-upload me-2"></i>Upload Evidence
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="uploadEvidenceForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label"><strong>Checklist Item:</strong></label>
                        <p class="text-muted"><?= htmlspecialchars($checklist_item['item_name']) ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="evidenceFile" class="form-label">Evidence File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="evidenceFile" name="evidence_file" 
                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx" required>
                        <div class="form-text">
                            Accepted formats: JPG, PNG, GIF, PDF, DOC, DOCX. Maximum size: 10MB.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="evidenceNotes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="evidenceNotes" name="notes" rows="3" 
                                  placeholder="Add any additional notes about this evidence..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="uploadSubmitBtn">
                        <i class="fas fa-upload me-1"></i>Upload Evidence
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Image Viewer Variables
let currentZoom = 1;
let isDragging = false;
let startX, startY, scrollLeft, scrollTop;

// Handle upload evidence button clicks
document.getElementById('uploadNewEvidenceBtn').addEventListener('click', showUploadModal);
document.getElementById('uploadFirstEvidenceBtn')?.addEventListener('click', showUploadModal);

function showUploadModal() {
    document.getElementById('evidenceFile').value = '';
    document.getElementById('evidenceNotes').value = '';

    const modal = new bootstrap.Modal(document.getElementById('uploadEvidenceModal'));
    modal.show();
}

// Handle image viewer button clicks
document.querySelectorAll('.view-image-btn').forEach(button => {
    button.addEventListener('click', function() {
        const imageSrc = this.dataset.imageSrc;
        const imageName = this.dataset.imageName;

        // Show loading state
        const loader = document.getElementById('imageLoader');
        const modalImage = document.getElementById('modalImage');

        loader.style.display = 'block';
        modalImage.style.display = 'none';
        modalImage.src = ''; // Clear previous image

        // Set image source and title
        document.getElementById('imageViewerModalLabel').innerHTML =
            '<i class="fas fa-image me-2"></i>' + imageName;

        // Reset zoom and container
        currentZoom = 1;
        modalImage.style.transform = 'scale(1)';
        const container = document.getElementById('imageContainer');
        container.scrollLeft = 0;
        container.scrollTop = 0;

        // Show modal first
        const modal = new bootstrap.Modal(document.getElementById('imageViewerModal'));
        modal.show();

        // Load image after modal is shown
        setTimeout(() => {
            modalImage.src = imageSrc;
        }, 100);
    });
});

// Zoom Controls
document.getElementById('zoomInBtn').addEventListener('click', function() {
    currentZoom = Math.min(currentZoom * 1.2, 5); // Max zoom 5x
    document.getElementById('modalImage').style.transform = `scale(${currentZoom})`;
});

document.getElementById('zoomOutBtn').addEventListener('click', function() {
    currentZoom = Math.max(currentZoom / 1.2, 0.1); // Min zoom 0.1x
    document.getElementById('modalImage').style.transform = `scale(${currentZoom})`;
});

document.getElementById('resetZoomBtn').addEventListener('click', function() {
    currentZoom = 1;
    const modalImage = document.getElementById('modalImage');
    const container = document.getElementById('imageContainer');

    modalImage.style.transform = 'scale(1)';
    container.scrollLeft = 0;
    container.scrollTop = 0;

    // Reset container alignment for normal view
    container.style.alignItems = 'center';
    container.style.justifyContent = 'center';
});

// Mouse wheel zoom
document.getElementById('imageContainer').addEventListener('wheel', function(e) {
    e.preventDefault();

    if (e.deltaY < 0) {
        // Zoom in
        currentZoom = Math.min(currentZoom * 1.1, 5);
    } else {
        // Zoom out
        currentZoom = Math.max(currentZoom / 1.1, 0.1);
    }

    document.getElementById('modalImage').style.transform = `scale(${currentZoom})`;
});

// Pan functionality
const imageContainer = document.getElementById('imageContainer');
const modalImage = document.getElementById('modalImage');

imageContainer.addEventListener('mousedown', function(e) {
    if (currentZoom > 1) {
        isDragging = true;
        startX = e.pageX - imageContainer.offsetLeft;
        startY = e.pageY - imageContainer.offsetTop;
        scrollLeft = imageContainer.scrollLeft;
        scrollTop = imageContainer.scrollTop;
        imageContainer.style.cursor = 'grabbing';
    }
});

imageContainer.addEventListener('mouseleave', function() {
    isDragging = false;
    imageContainer.style.cursor = 'grab';
});

imageContainer.addEventListener('mouseup', function() {
    isDragging = false;
    imageContainer.style.cursor = 'grab';
});

imageContainer.addEventListener('mousemove', function(e) {
    if (!isDragging) return;
    e.preventDefault();

    const x = e.pageX - imageContainer.offsetLeft;
    const y = e.pageY - imageContainer.offsetTop;
    const walkX = (x - startX) * 2;
    const walkY = (y - startY) * 2;

    imageContainer.scrollLeft = scrollLeft - walkX;
    imageContainer.scrollTop = scrollTop - walkY;
});

// Reset zoom when modal is closed
document.getElementById('imageViewerModal').addEventListener('hidden.bs.modal', function() {
    currentZoom = 1;
    const modalImage = document.getElementById('modalImage');
    const container = document.getElementById('imageContainer');

    modalImage.style.transform = 'scale(1)';
    modalImage.style.display = 'none';
    modalImage.src = '';
    container.scrollLeft = 0;
    container.scrollTop = 0;
    container.style.alignItems = 'center';
    container.style.justifyContent = 'center';

    // Hide loader
    document.getElementById('imageLoader').style.display = 'none';
});

// Handle form submission
document.getElementById('uploadEvidenceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = document.getElementById('uploadSubmitBtn');
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
    submitBtn.disabled = true;
    
    fetch(`<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>/upload/<?= $checklist_item['id'] ?>`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and reload page
            bootstrap.Modal.getInstance(document.getElementById('uploadEvidenceModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while uploading the evidence');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Upload Evidence';
        submitBtn.disabled = false;
    });
});

// Handle delete evidence
document.querySelectorAll('.delete-evidence-btn').forEach(button => {
    button.addEventListener('click', function() {
        const evidenceId = this.dataset.evidenceId;
        const fileName = this.dataset.fileName;
        
        if (confirm(`Are you sure you want to delete "${fileName}"? This action cannot be undone.`)) {
            fetch(`<?= BASE_URL ?>business/checklist/evidence/delete/${evidenceId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the evidence');
            });
        }
    });
});
</script>

<?php $this->endSection(); ?>
