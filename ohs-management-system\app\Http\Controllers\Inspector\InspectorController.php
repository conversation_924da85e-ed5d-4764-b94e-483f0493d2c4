<?php

namespace App\Http\Controllers\Inspector;

use App\Http\Controllers\Controller;
use App\Models\Inspection;
use App\Models\Business;
use App\Models\InspectionChecklistItem;
use App\Models\InspectionChecklistResponse;
use App\Models\User;
use App\Models\District;
use App\Models\Barangay;
use App\Models\InspectorDistrictAssignment;
use App\Services\FileUploadService;
use App\Http\Controllers\NotificationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InspectorController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
        $this->middleware('auth');
        $this->middleware('role:inspector');
    }

    /**
     * Display inspector dashboard
     */
    public function dashboard()
    {
        $inspector = Auth::user();

        // Get assigned inspections
        $assignedInspections = Inspection::where('inspector_id', $inspector->id)
            ->with(['business.barangay.district'])
            ->latest('scheduled_date')
            ->limit(10)
            ->get();

        // Get statistics
        $stats = [
            'total_assigned' => Inspection::where('inspector_id', $inspector->id)->count(),
            'pending_inspections' => Inspection::where('inspector_id', $inspector->id)
                ->where('status', 'scheduled')->count(),
            'in_progress' => Inspection::where('inspector_id', $inspector->id)
                ->where('status', 'in_progress')->count(),
            'completed_inspections' => Inspection::where('inspector_id', $inspector->id)
                ->where('status', 'completed')->count(),
            'assigned_districts' => InspectorDistrictAssignment::where('inspector_id', $inspector->id)
                ->where('status', 'active')->count(),
        ];

        // Get assigned districts and barangays
        $assignments = InspectorDistrictAssignment::where('inspector_id', $inspector->id)
            ->where('status', 'active')
            ->with(['district', 'barangay'])
            ->get();

        // Get businesses in assigned areas
        $assignedBusinesses = $this->getAssignedBusinesses($inspector->id);

        return view('inspector.dashboard', compact(
            'inspector',
            'assignedInspections',
            'stats',
            'assignments',
            'assignedBusinesses'
        ));
    }

    /**
     * Display inspector inspections
     */
    public function inspections(Request $request)
    {
        $inspector = Auth::user();
        $query = Inspection::where('inspector_id', $inspector->id)
            ->with(['business.barangay.district', 'assignedBy']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
        }

        $inspections = $query->latest('scheduled_date')->paginate(15);

        return view('inspector.inspections.index', compact('inspections'));
    }

    /**
     * Display specific inspection
     */
    public function inspection($id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->with(['business.barangay.district', 'business.category', 'assignedBy'])
            ->firstOrFail();

        return view('inspector.inspections.show', compact('inspection'));
    }

    /**
     * Start inspection checklist
     */
    public function startInspectionChecklist($id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->with(['business.barangay.district', 'business.category'])
            ->firstOrFail();

        // Check if inspection can be started
        if (!in_array($inspection->status, ['scheduled', 'confirmed'])) {
            return redirect()->route('inspector.inspections.show', $inspection)
                ->with('error', 'This inspection cannot be started.');
        }

        // Get checklist items with categories
        $checklistItems = InspectionChecklistItem::with('category')
            ->active()
            ->ordered()
            ->get()
            ->groupBy('category.name');

        // Get existing responses
        $existingResponses = InspectionChecklistResponse::where('inspection_id', $inspection->id)
            ->get()
            ->keyBy('checklist_item_id');

        return view('inspector.inspections.checklist', compact(
            'inspection',
            'checklistItems',
            'existingResponses'
        ));
    }

    /**
     * Save checklist response
     */
    public function saveChecklistResponse(Request $request)
    {
        $validated = $request->validate([
            'inspection_id' => 'required|exists:inspections,id',
            'checklist_item_id' => 'required|exists:inspection_checklist_items,id',
            'compliance_status' => 'required|in:compliant,non_compliant,not_applicable',
            'notes' => 'nullable|string|max:1000',
            'evidence_photo' => 'nullable|image|mimes:jpg,jpeg,png|max:5120',
        ]);

        // Verify inspector owns this inspection
        $inspection = Inspection::where('id', $validated['inspection_id'])
            ->where('inspector_id', Auth::id())
            ->firstOrFail();

        // Update inspection status to in_progress if not already
        if ($inspection->status === 'scheduled' || $inspection->status === 'confirmed') {
            $inspection->update(['status' => 'in_progress']);
        }

        $responseData = [
            'inspection_id' => $validated['inspection_id'],
            'checklist_item_id' => $validated['checklist_item_id'],
            'compliance_status' => $validated['compliance_status'],
            'notes' => $validated['notes'],
            'inspector_id' => Auth::id(),
        ];

        // Handle evidence photo upload
        if ($request->hasFile('evidence_photo')) {
            $uploadResult = $this->fileUploadService->uploadFile(
                $request->file('evidence_photo'),
                'inspection-evidence',
                [
                    'inspection_id' => $validated['inspection_id'],
                    'checklist_item_id' => $validated['checklist_item_id']
                ]
            );
            $responseData['evidence_photo'] = $uploadResult['path'];
        }

        // Create or update response
        InspectionChecklistResponse::updateOrCreate(
            [
                'inspection_id' => $validated['inspection_id'],
                'checklist_item_id' => $validated['checklist_item_id'],
            ],
            $responseData
        );

        return response()->json(['success' => true, 'message' => 'Response saved successfully.']);
    }

    /**
     * Complete inspection
     */
    public function completeInspection(Request $request, $id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->firstOrFail();

        $validated = $request->validate([
            'overall_notes' => 'nullable|string|max:2000',
            'recommendations' => 'nullable|string|max:2000',
        ]);

        // Calculate inspection score and compliance rating
        $responses = InspectionChecklistResponse::where('inspection_id', $inspection->id)->get();
        $scoreData = $this->calculateInspectionScore($responses);

        $inspection->update([
            'status' => 'completed',
            'completed_date' => now(),
            'score' => $scoreData['score'],
            'compliance_rating' => $scoreData['rating'],
            'overall_notes' => $validated['overall_notes'],
            'recommendations' => $validated['recommendations'],
        ]);

        // Send notification to admin
        NotificationController::createNotification(
            $inspection->assigned_by,
            'Inspection Completed',
            "Inspection of {$inspection->business->name} has been completed and is pending verification.",
            'inspection',
            $inspection->id,
            'inspection',
            route('admin.inspections.show', $inspection)
        );

        return redirect()->route('inspector.inspections.show', $inspection)
            ->with('success', 'Inspection completed successfully and submitted for verification.');
    }

    /**
     * Start inspection
     */
    public function startInspection($id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->firstOrFail();

        if ($inspection->status !== 'scheduled') {
            return back()->with('error', 'This inspection cannot be started.');
        }

        $inspection->update(['status' => 'in_progress']);

        return redirect()->route('inspector.inspections.checklist', $inspection)
            ->with('success', 'Inspection started. Please complete the checklist.');
    }

    /**
     * Confirm inspection
     */
    public function confirmInspection($id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->firstOrFail();

        if ($inspection->status !== 'scheduled') {
            return back()->with('error', 'This inspection cannot be confirmed.');
        }

        $inspection->update(['status' => 'confirmed']);

        return back()->with('success', 'Inspection confirmed.');
    }

    /**
     * Cancel inspection
     */
    public function cancelInspection(Request $request, $id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->firstOrFail();

        $validated = $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        $inspection->update([
            'status' => 'cancelled',
            'cancellation_reason' => $validated['cancellation_reason'],
            'cancelled_by' => Auth::id(),
            'cancelled_at' => now(),
        ]);

        // Notify admin
        NotificationController::createNotification(
            $inspection->assigned_by,
            'Inspection Cancelled',
            "Inspection of {$inspection->business->name} has been cancelled by inspector.",
            'warning',
            $inspection->id,
            'inspection',
            route('admin.inspections.show', $inspection)
        );

        return back()->with('success', 'Inspection cancelled successfully.');
    }

    /**
     * Display inspection report
     */
    public function inspectionReport($id)
    {
        $inspection = Inspection::where('id', $id)
            ->where('inspector_id', Auth::id())
            ->with([
                'business.barangay.district',
                'business.category',
                'checklistResponses.checklistItem.category'
            ])
            ->firstOrFail();

        // Group responses by category
        $responsesByCategory = $inspection->checklistResponses
            ->groupBy('checklistItem.category.name');

        return view('inspector.inspections.report', compact('inspection', 'responsesByCategory'));
    }

    /**
     * Display inspector profile
     */
    public function profile()
    {
        $inspector = Auth::user();
        
        // Get assignments
        $assignments = InspectorDistrictAssignment::where('inspector_id', $inspector->id)
            ->where('status', 'active')
            ->with(['district', 'barangay'])
            ->get();

        // Get workload statistics
        $workload = $this->getInspectorWorkload($inspector->id);

        return view('inspector.profile', compact('inspector', 'assignments', 'workload'));
    }

    /**
     * Update inspector profile
     */
    public function updateProfile(Request $request)
    {
        $inspector = Auth::user();

        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $inspector->id,
            'phone' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:500',
        ]);

        $inspector->update($validated);

        return back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Display inspector settings
     */
    public function settings()
    {
        $inspector = Auth::user();
        return view('inspector.settings', compact('inspector'));
    }

    /**
     * Update inspector settings
     */
    public function updateSettings(Request $request)
    {
        $inspector = Auth::user();

        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'inspection_reminders' => 'boolean',
        ]);

        // Update user preferences (you might want to create a separate preferences table)
        $inspector->update([
            'preferences' => json_encode($validated)
        ]);

        return back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Get businesses assigned to inspector
     */
    private function getAssignedBusinesses($inspectorId)
    {
        $assignments = InspectorDistrictAssignment::where('inspector_id', $inspectorId)
            ->where('status', 'active')
            ->get();

        $barangayIds = $assignments->pluck('barangay_id')->filter();
        $districtIds = $assignments->where('barangay_id', null)->pluck('district_id');

        $query = Business::with(['barangay.district', 'category']);

        if ($barangayIds->isNotEmpty()) {
            $query->whereIn('barangay_id', $barangayIds);
        }

        if ($districtIds->isNotEmpty()) {
            $query->orWhereHas('barangay', function($q) use ($districtIds) {
                $q->whereIn('district_id', $districtIds);
            });
        }

        return $query->where('status', 'active')->get();
    }

    /**
     * Get inspector workload statistics
     */
    private function getInspectorWorkload($inspectorId)
    {
        return [
            'assigned_barangays' => InspectorDistrictAssignment::where('inspector_id', $inspectorId)
                ->where('status', 'active')
                ->whereNotNull('barangay_id')
                ->count(),
            'assigned_districts' => InspectorDistrictAssignment::where('inspector_id', $inspectorId)
                ->where('status', 'active')
                ->whereNull('barangay_id')
                ->count(),
            'total_inspections' => Inspection::where('inspector_id', $inspectorId)->count(),
            'pending_inspections' => Inspection::where('inspector_id', $inspectorId)
                ->where('status', 'scheduled')->count(),
            'completed_inspections' => Inspection::where('inspector_id', $inspectorId)
                ->where('status', 'completed')->count(),
        ];
    }

    /**
     * Calculate inspection score and rating
     */
    private function calculateInspectionScore($responses)
    {
        if ($responses->isEmpty()) {
            return ['score' => 0, 'rating' => 'F'];
        }

        $totalPoints = 0;
        $earnedPoints = 0;

        foreach ($responses as $response) {
            $item = $response->checklistItem;
            $totalPoints += $item->points;

            if ($response->compliance_status === 'compliant') {
                $earnedPoints += $item->points;
            } elseif ($response->compliance_status === 'not_applicable') {
                // Don't count not applicable items
                $totalPoints -= $item->points;
            }
        }

        $score = $totalPoints > 0 ? round(($earnedPoints / $totalPoints) * 100, 1) : 0;

        // Determine rating
        $rating = 'F';
        if ($score >= 90) $rating = 'A';
        elseif ($score >= 80) $rating = 'B';
        elseif ($score >= 70) $rating = 'C';
        elseif ($score >= 60) $rating = 'D';

        return ['score' => $score, 'rating' => $rating];
    }
}
