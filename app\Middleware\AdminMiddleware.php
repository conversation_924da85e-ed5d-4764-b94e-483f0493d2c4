<?php
namespace App\Middleware;

use App\Libraries\Auth;

class AdminMiddleware {
    private $auth;

    public function __construct() {
        $this->auth = Auth::getInstance();
    }

    public function handle() {
        if (!$this->auth->isLoggedIn()) {
            $_SESSION['redirect_url'] = $_SERVER['REQUEST_URI'];
            header('Location: ' . BASE_URL . 'login');
            exit();
        }

        if (!$this->auth->isAdmin()) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit();
        }
    }
} 