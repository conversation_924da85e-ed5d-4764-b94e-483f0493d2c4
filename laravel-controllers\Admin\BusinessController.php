<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessCategory;
use App\Models\Barangay;
use App\Models\District;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BusinessController extends Controller
{
    /**
     * Display a listing of businesses
     */
    public function index(Request $request)
    {
        $query = Business::with(['owner', 'category', 'barangay.district']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('compliance_status')) {
            $query->where('compliance_status', $request->compliance_status);
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('district_id')) {
            $query->whereHas('barangay', function($q) use ($request) {
                $q->where('district_id', $request->district_id);
            });
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('owner_name', 'like', "%{$search}%")
                  ->orWhere('registration_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $businesses = $query->latest()->paginate(15);

        // Get filter options
        $categories = BusinessCategory::all();
        $districts = District::all();

        return view('admin.businesses.index', compact('businesses', 'categories', 'districts'));
    }

    /**
     * Show the form for creating a new business
     */
    public function create()
    {
        $categories = BusinessCategory::all();
        $districts = District::with('barangays')->get();
        
        return view('admin.businesses.create', compact('categories', 'districts'));
    }

    /**
     * Store a newly created business
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'owner_name' => 'required|string|max:255',
            'owner_email' => 'required|email|unique:users,email',
            'business_name' => 'required|string|max:255',
            'category_id' => 'required|exists:business_categories,id',
            'barangay_id' => 'required|exists:barangays,id',
            'address' => 'required|string',
            'contact_number' => 'required|string|max:20',
            'employee_count' => 'required|integer|min:1',
            'date_established' => 'nullable|date',
        ]);

        // Generate random password for business owner
        $password = Str::random(12);
        
        // Create business owner user account
        $owner = User::create([
            'full_name' => $validated['owner_name'],
            'email' => $validated['owner_email'],
            'password' => Hash::make($password),
            'role' => 'business_owner',
            'status' => 'active',
        ]);

        // Generate unique registration number
        $registrationNumber = 'BUS-' . date('Y') . '-' . str_pad(Business::count() + 1, 4, '0', STR_PAD_LEFT);

        // Create business
        $business = Business::create([
            'owner_id' => $owner->id,
            'owner_name' => $validated['owner_name'],
            'name' => $validated['business_name'],
            'category_id' => $validated['category_id'],
            'barangay_id' => $validated['barangay_id'],
            'registration_number' => $registrationNumber,
            'email' => $validated['owner_email'],
            'contact_number' => $validated['contact_number'],
            'address' => $validated['address'],
            'employee_count' => $validated['employee_count'],
            'date_established' => $validated['date_established'],
            'status' => 'active',
        ]);

        // Send email with login credentials
        try {
            // TODO: Implement email sending with Laravel Mail
            // Mail::to($owner->email)->send(new BusinessOwnerCredentials($owner, $password));
        } catch (\Exception $e) {
            // Log email error but don't fail the registration
            \Log::error('Failed to send business owner credentials email: ' . $e->getMessage());
        }

        return redirect()->route('admin.businesses.index')
            ->with('success', 'Business registered successfully. Login credentials sent to owner\'s email.');
    }

    /**
     * Display the specified business
     */
    public function show(Business $business)
    {
        $business->load([
            'owner',
            'category',
            'barangay.district',
            'inspections.inspector',
            'checklistEvidence.checklistItem',
            'chatRooms'
        ]);

        return view('admin.businesses.view', compact('business'));
    }

    /**
     * Show the form for editing the specified business
     */
    public function edit(Business $business)
    {
        $categories = BusinessCategory::all();
        $districts = District::with('barangays')->get();
        
        return view('admin.businesses.edit', compact('business', 'categories', 'districts'));
    }

    /**
     * Update the specified business
     */
    public function update(Request $request, Business $business)
    {
        $validated = $request->validate([
            'owner_name' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'category_id' => 'required|exists:business_categories,id',
            'barangay_id' => 'required|exists:barangays,id',
            'address' => 'required|string',
            'contact_number' => 'required|string|max:20',
            'employee_count' => 'required|integer|min:1',
            'date_established' => 'nullable|date',
            'status' => 'required|in:pending,active,suspended,inactive',
            'compliance_status' => 'required|in:compliant,non_compliant,pending_review',
        ]);

        $business->update([
            'owner_name' => $validated['owner_name'],
            'name' => $validated['business_name'],
            'category_id' => $validated['category_id'],
            'barangay_id' => $validated['barangay_id'],
            'address' => $validated['address'],
            'contact_number' => $validated['contact_number'],
            'employee_count' => $validated['employee_count'],
            'date_established' => $validated['date_established'],
            'status' => $validated['status'],
            'compliance_status' => $validated['compliance_status'],
        ]);

        // Update owner name in user table
        $business->owner->update(['full_name' => $validated['owner_name']]);

        return redirect()->route('admin.businesses.show', $business)
            ->with('success', 'Business updated successfully.');
    }

    /**
     * Remove the specified business
     */
    public function destroy(Business $business)
    {
        // Delete associated user account
        $business->owner->delete();
        
        // Delete business (cascading will handle related records)
        $business->delete();

        return redirect()->route('admin.businesses.index')
            ->with('success', 'Business deleted successfully.');
    }

    /**
     * Get barangays for a district (AJAX)
     */
    public function getBarangays(District $district)
    {
        return response()->json($district->barangays);
    }

    /**
     * Export businesses data
     */
    public function export(Request $request)
    {
        // TODO: Implement export functionality (Excel/CSV)
        return response()->json(['message' => 'Export functionality to be implemented']);
    }
}
