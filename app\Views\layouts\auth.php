<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>OHS System</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= BASE_URL ?>assets/img/favicon.ico">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?= BASE_URL ?>assets/css/style.css" rel="stylesheet">

    <!-- Custom Theme CSS (loaded last to override other styles) -->
    <link href="<?= BASE_URL ?>assets/css/custom-theme.css?v=<?= time() ?>" rel="stylesheet">

    <!-- Dynamic Color Variables -->
    <?php
    $settingsModel = new \App\Models\WebsiteSettings();
    $colors = $settingsModel->getCurrentColorPalette();
    ?>
    <style id="dynamic-colors">
        :root {
            --primary-color: <?= $colors['primary_color'] ?> !important;
            --secondary-color: <?= $colors['secondary_color'] ?> !important;
            --accent-color: <?= $colors['accent_color'] ?> !important;
            --success-color: <?= $colors['success_color'] ?> !important;
            --warning-color: <?= $colors['warning_color'] ?> !important;
            --danger-color: <?= $colors['danger_color'] ?> !important;
            --dark-color: <?= $colors['dark_color'] ?> !important;
            --light-color: <?= $colors['light_color'] ?> !important;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
        }

        /* Navigation Styles */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 20px rgba(37, 99, 235, 0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-decoration: none;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem !important;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .btn-login {
            background: white;
            color: var(--primary-color) !important;
            border: 2px solid white;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            background: transparent;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
        }

        .auth-page {
            padding-top: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .auth-container {
            width: 100%;
            max-width: 500px;
            text-align: center;
            padding: 20px;
        }

        .auth-logo {
            margin-bottom: 1.5rem;
        }

        .auth-logo h1 {
            color: white;
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .auth-logo p {
            color: rgba(255,255,255,0.9);
            font-size: 1.1rem;
            margin: 0;
        }

        .auth-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            padding: 3rem;
            margin-bottom: 1.5rem;
            border: none;
            backdrop-filter: blur(10px);
        }
        
        .auth-card .card-header {
            background: none;
            border: none;
            padding: 0 0 2rem 0;
            text-align: center;
        }

        .auth-card .card-header h4 {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.8rem;
            margin: 0;
        }

        .auth-card .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .auth-card .form-control {
            padding: 15px 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .auth-card .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
            background: white;
        }

        .auth-card .btn-primary {
            padding: 15px;
            font-weight: 700;
            font-size: 1.1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .auth-card .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(139, 92, 246, 0.4);
        }

        .auth-card .btn-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .auth-card .btn-link:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }
        
        .auth-footer {
            color: rgba(255,255,255,0.8);
            font-size: 0.9rem;
            margin-top: 1rem;
            text-align: center;
        }

        .alert {
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            border: none;
        }

        .alert-success {
            background-color: #dcfce7;
            color: #166534;
        }

        .alert-danger {
            background-color: #fef2f2;
            color: #dc2626;
        }
    </style>
    
    <?php $this->renderSection('styles') ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= BASE_URL ?>">
                <i class="fas fa-shield-alt me-2"></i>
                Bacoor OHS
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>about">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>services">
                            <i class="fas fa-cogs me-1"></i>Services
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>news">
                            <i class="fas fa-newspaper me-1"></i>News
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= BASE_URL ?>contact">
                            <i class="fas fa-envelope me-1"></i>Contact
                        </a>
                    </li>
                </ul>

                <div class="d-flex">
                    <a href="<?= BASE_URL ?>login" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="auth-page">
        <?php $this->renderSection('content') ?>
    </main>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <?php $this->renderSection('scripts') ?>
</body>
</html>