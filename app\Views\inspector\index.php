<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid"><?php $auth = \App\Libraries\Auth::getInstance(); ?>
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Inspections</h1>
        <div>
            <?php if ($auth->isAdmin()): ?>
                <a href="<?= BASE_URL ?>admin/inspections/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Schedule New Inspection
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-clipboard-check me-2"></i>Inspection List
            </h6>
        </div>
        <div class="card-body">
                    <?php if (empty($inspections)): ?>
                        <div class="text-center py-4">
                            <img src="<?= BASE_URL ?>assets/img/empty-state.svg" alt="No inspections" class="mb-3" style="max-width: 200px;">
                            <p class="text-muted">No inspections found.</p>
                            <?php if ($auth->isAdmin()): ?>
                                <a href="<?= BASE_URL ?>admin/inspections/create" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Schedule Your First Inspection
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="inspectionsTable">
                                <thead>
                                    <tr>
                                        <th>Business</th>
                                        <th>Inspector</th>
                                        <th>Scheduled Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inspections as $inspection): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($inspection['business_name']) ?></td>
                                            <td><?= htmlspecialchars($inspection['inspector_name']) ?></td>
                                            <td><?= date('M j, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                                                    <?= ucfirst($inspection['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <?php if ($auth->isAdmin()): ?>
                                                        <button type="button" class="btn btn-sm btn-danger"
                                                                onclick="confirmDelete(<?= $inspection['id'] ?>)">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </button>
                                                    <?php else: ?>
                                                        <a href="<?= BASE_URL ?>inspector/inspection/<?= $inspection['id'] ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($auth->isAdmin()): ?>
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?? '' ?>">
</form>

<script>
function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this inspection?')) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= BASE_URL ?>admin/inspections/delete/' + id;
        form.submit();
    }
}

$(document).ready(function() {
    $('#inspectionsTable').DataTable({
        responsive: true,
        order: [[2, 'asc']],
        pageLength: 10,
        language: {
            emptyTable: "No inspections found"
        }
    });
});
</script>
<?php endif; ?>
<?php $this->endSection() ?>