@extends('layouts.business-owner')

@section('title', 'Compliance Checklist')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Compliance Checklist</h1>
    <div class="d-none d-sm-inline-block">
        <span class="badge badge-{{ $business->compliance_status === 'compliant' ? 'success' : ($business->compliance_status === 'pending_review' ? 'warning' : 'danger') }} badge-lg">
            {{ ucfirst(str_replace('_', ' ', $business->compliance_status)) }}
        </span>
    </div>
</div>

<!-- Progress Overview -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Overall Progress</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="text-center">
                    <div class="h4 mb-0 font-weight-bold text-primary">{{ $stats['uploaded_evidence'] }}/{{ $stats['total_evidence_items'] }}</div>
                    <div class="text-xs font-weight-bold text-primary text-uppercase">Evidence Uploaded</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: {{ $stats['evidence_completion_rate'] }}%" 
                             aria-valuenow="{{ $stats['evidence_completion_rate'] }}" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="h4 mb-0 font-weight-bold text-success">{{ $stats['approved_evidence'] }}</div>
                    <div class="text-xs font-weight-bold text-success text-uppercase">Evidence Approved</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ $stats['total_evidence_items'] > 0 ? round(($stats['approved_evidence'] / $stats['total_evidence_items']) * 100, 2) : 0 }}%" 
                             aria-valuenow="{{ $stats['total_evidence_items'] > 0 ? round(($stats['approved_evidence'] / $stats['total_evidence_items']) * 100, 2) : 0 }}" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="h4 mb-0 font-weight-bold text-warning">{{ $stats['pending_evidence'] }}</div>
                    <div class="text-xs font-weight-bold text-warning text-uppercase">Pending Review</div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {{ $stats['total_evidence_items'] > 0 ? round(($stats['pending_evidence'] / $stats['total_evidence_items']) * 100, 2) : 0 }}%" 
                             aria-valuenow="{{ $stats['total_evidence_items'] > 0 ? round(($stats['pending_evidence'] / $stats['total_evidence_items']) * 100, 2) : 0 }}" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Checklist Categories -->
@foreach($categories as $category)
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{{ $category->name }}</h6>
        <div class="text-xs text-gray-500">
            {{ $category->activeChecklistItems->count() }} items
        </div>
    </div>
    <div class="card-body">
        @if($category->description)
            <p class="text-muted mb-4">{{ $category->description }}</p>
        @endif
        
        <div class="row">
            @foreach($category->activeChecklistItems as $item)
                @php
                    $evidence = $businessEvidence->get($item->id);
                    $hasEvidence = $evidence && $evidence->count() > 0;
                    $latestEvidence = $hasEvidence ? $evidence->first() : null;
                    $statusClass = $hasEvidence ? 
                        ($latestEvidence->status === 'approved' ? 'success' : 
                         ($latestEvidence->status === 'rejected' ? 'danger' : 'warning')) : 'secondary';
                    $statusText = $hasEvidence ? ucfirst($latestEvidence->status) : 'No Evidence';
                @endphp
                
                <div class="col-lg-6 mb-4">
                    <div class="card border-left-{{ $statusClass }} h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="font-weight-bold text-{{ $statusClass }}">
                                    {{ $item->item_name }}
                                    @if($item->is_critical)
                                        <span class="badge badge-danger badge-sm ml-1">Critical</span>
                                    @endif
                                </h6>
                                <span class="badge badge-{{ $statusClass }}">{{ $statusText }}</span>
                            </div>
                            
                            @if($item->description)
                                <p class="text-muted small mb-2">{{ $item->description }}</p>
                            @endif
                            
                            @if($item->compliance_requirement)
                                <div class="alert alert-info alert-sm mb-3">
                                    <strong>Requirement:</strong> {{ $item->compliance_requirement }}
                                </div>
                            @endif
                            
                            <!-- Current Evidence -->
                            @if($hasEvidence)
                                <div class="mb-3">
                                    <h6 class="text-sm font-weight-bold">Current Evidence:</h6>
                                    @foreach($evidence as $file)
                                        <div class="d-flex align-items-center justify-content-between border rounded p-2 mb-2">
                                            <div class="d-flex align-items-center">
                                                @if($file->isImage())
                                                    <i class="fas fa-image text-primary mr-2"></i>
                                                @else
                                                    <i class="fas fa-file text-secondary mr-2"></i>
                                                @endif
                                                <div>
                                                    <div class="font-weight-bold">{{ $file->file_name }}</div>
                                                    <div class="text-xs text-muted">
                                                        {{ $file->getFormattedFileSizeAttribute() }} • 
                                                        {{ $file->created_at->format('M j, Y') }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                @if($file->isImage())
                                                    <button type="button" class="btn btn-sm btn-outline-primary mr-2" 
                                                            onclick="viewImage('{{ $file->getFileUrlAttribute() }}', '{{ $file->file_name }}')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                @else
                                                    <a href="{{ $file->getFileUrlAttribute() }}" target="_blank" 
                                                       class="btn btn-sm btn-outline-primary mr-2">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                @endif
                                                @if($file->status !== 'approved')
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteEvidence('{{ $file->id }}', '{{ $file->file_name }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        @if($file->status === 'rejected' && $file->review_notes)
                                            <div class="alert alert-danger alert-sm">
                                                <strong>Rejection Reason:</strong> {{ $file->review_notes }}
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            @endif
                            
                            <!-- Upload Form -->
                            @if(!$hasEvidence || $latestEvidence->status === 'rejected')
                                <form action="{{ route('business-owner.checklist.upload-evidence', $item) }}" 
                                      method="POST" enctype="multipart/form-data" class="upload-form">
                                    @csrf
                                    <div class="form-group">
                                        <label for="evidence_{{ $item->id }}" class="form-label">
                                            Upload Evidence
                                            <span class="text-danger">*</span>
                                        </label>
                                        <input type="file" class="form-control-file" 
                                               id="evidence_{{ $item->id }}" name="evidence" 
                                               accept="image/*,.pdf,.doc,.docx,.xls,.xlsx" required>
                                        <small class="form-text text-muted">
                                            Accepted formats: Images (JPG, PNG, GIF), PDF, Word, Excel. Max size: 10MB.
                                        </small>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="notes_{{ $item->id }}">Notes (Optional)</label>
                                        <textarea class="form-control" id="notes_{{ $item->id }}" 
                                                  name="notes" rows="2" 
                                                  placeholder="Additional information about this evidence..."></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-upload mr-1"></i>Upload Evidence
                                    </button>
                                </form>
                            @elseif($latestEvidence->status === 'pending')
                                <div class="alert alert-warning alert-sm">
                                    <i class="fas fa-clock mr-1"></i>
                                    Evidence is pending review. You will be notified once it's reviewed.
                                </div>
                            @elseif($latestEvidence->status === 'approved')
                                <div class="alert alert-success alert-sm">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Evidence approved! This item is compliant.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
@endforeach

<!-- Image Viewer Modal -->
<div class="modal fade" id="imageViewerModal" tabindex="-1" role="dialog" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageViewerModalLabel">Evidence Image</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Evidence" class="img-fluid" style="max-height: 70vh;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <a id="downloadImageBtn" href="" download class="btn btn-primary">
                    <i class="fas fa-download mr-1"></i>Download
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteEvidenceModal" tabindex="-1" role="dialog" aria-labelledby="deleteEvidenceModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEvidenceModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the evidence file "<span id="evidenceFileName"></span>"? 
                This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteEvidenceForm" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Evidence</button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Handle form submissions with loading states
    $('.upload-form').on('submit', function() {
        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const originalText = $button.html();
        
        $button.prop('disabled', true)
               .html('<i class="fas fa-spinner fa-spin mr-1"></i>Uploading...');
        
        // Re-enable button after 10 seconds as fallback
        setTimeout(function() {
            $button.prop('disabled', false).html(originalText);
        }, 10000);
    });
    
    // File input validation
    $('input[type="file"]').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size cannot exceed 10MB. Please choose a smaller file.');
                $(this).val('');
                return;
            }
            
            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 
                                'application/pdf', 'application/msword', 
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
            
            if (!allowedTypes.includes(file.type)) {
                alert('Invalid file type. Please upload an image, PDF, Word document, or Excel file.');
                $(this).val('');
                return;
            }
        }
    });
});

function viewImage(imageUrl, fileName) {
    $('#modalImage').attr('src', imageUrl);
    $('#imageViewerModalLabel').text(fileName);
    $('#downloadImageBtn').attr('href', imageUrl);
    $('#imageViewerModal').modal('show');
}

function deleteEvidence(evidenceId, fileName) {
    $('#evidenceFileName').text(fileName);
    $('#deleteEvidenceForm').attr('action', '{{ route("business-owner.evidence.delete", "") }}/' + evidenceId);
    $('#deleteEvidenceModal').modal('show');
}

// Auto-refresh page every 5 minutes to check for evidence review updates
setInterval(function() {
    // Only refresh if no modals are open and no forms are being submitted
    if (!$('.modal').hasClass('show') && !$('button[type="submit"]').prop('disabled')) {
        location.reload();
    }
}, 300000);
</script>
@endpush
