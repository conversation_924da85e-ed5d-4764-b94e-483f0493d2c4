/* Auth Pages Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f1c40f;
    --text-color: #2c3e50;
    --light-gray: #f8f9fa;
    --border-color: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

body.auth-page {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 20px;
}

.auth-container {
    width: 100%;
    max-width: 420px;
    margin: auto;
}

.auth-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo h1 {
    color: white;
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.auth-card .card-header {
    background: white;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.auth-card .card-header h4 {
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.auth-card .card-body {
    padding: 2rem;
}

.form-label {
    color: var(--text-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-link {
    color: var(--secondary-color);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
}

.btn-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

.alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
}

.auth-footer p {
    color: white;
    margin: 0;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.auth-footer a {
    color: white;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 480px) {
    .auth-container {
        padding: 0 15px;
    }
    
    .auth-card .card-body {
        padding: 1.5rem;
    }
    
    .auth-logo h1 {
        font-size: 1.75rem;
    }
}

/* Animation for form elements */
.form-control, .btn {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 