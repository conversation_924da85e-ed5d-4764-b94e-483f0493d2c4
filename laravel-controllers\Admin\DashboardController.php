<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Business;
use App\Models\Inspection;
use App\Models\BusinessChecklistEvidence;
use App\Models\ChatRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show admin dashboard
     */
    public function index()
    {
        // Get statistics
        $stats = $this->getDashboardStats();
        
        // Get recent activities
        $recentInspections = Inspection::with(['business', 'inspector'])
            ->latest()
            ->limit(5)
            ->get();
            
        $pendingEvidence = BusinessChecklistEvidence::with(['business', 'checklistItem'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();
            
        $activeChatRooms = ChatRoom::with(['business', 'businessOwner'])
            ->where('status', 'active')
            ->latest()
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentInspections',
            'pendingEvidence',
            'activeChatRooms'
        ));
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        return [
            'total_businesses' => Business::count(),
            'active_businesses' => Business::where('status', 'active')->count(),
            'pending_businesses' => Business::where('status', 'pending')->count(),
            'total_inspectors' => User::where('role', 'inspector')->count(),
            'active_inspectors' => User::where('role', 'inspector')
                ->where('status', 'active')->count(),
            'total_inspections' => Inspection::count(),
            'completed_inspections' => Inspection::where('status', 'completed')->count(),
            'approved_inspections' => Inspection::where('verification_status', 'approved')->count(),
            'scheduled_inspections' => Inspection::where('status', 'scheduled')->count(),
            'pending_verification' => Inspection::where('status', 'completed')
                ->where('verification_status', 'pending')->count(),
            'pending_evidence' => BusinessChecklistEvidence::where('status', 'pending')->count(),
            'active_chat_rooms' => ChatRoom::where('status', 'active')->count(),
            'compliance_rate' => $this->getComplianceRate(),
            'monthly_inspections' => $this->getMonthlyInspections(),
            'district_stats' => $this->getDistrictStats(),
        ];
    }

    /**
     * Get overall compliance rate
     */
    private function getComplianceRate(): float
    {
        $totalBusinesses = Business::where('status', 'active')->count();
        
        if ($totalBusinesses === 0) {
            return 0;
        }
        
        $compliantBusinesses = Business::where('status', 'active')
            ->where('compliance_status', 'compliant')
            ->count();
            
        return round(($compliantBusinesses / $totalBusinesses) * 100, 2);
    }

    /**
     * Get monthly inspection data for charts
     */
    private function getMonthlyInspections(): array
    {
        $monthlyData = Inspection::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $months = [];
        $totals = [];
        $completed = [];

        for ($i = 1; $i <= 12; $i++) {
            $monthData = $monthlyData->firstWhere('month', $i);
            $months[] = date('M', mktime(0, 0, 0, $i, 1));
            $totals[] = $monthData ? $monthData->total : 0;
            $completed[] = $monthData ? $monthData->completed : 0;
        }

        return [
            'months' => $months,
            'totals' => $totals,
            'completed' => $completed,
        ];
    }

    /**
     * Get district-wise statistics
     */
    private function getDistrictStats(): array
    {
        return DB::table('districts')
            ->leftJoin('barangays', 'districts.id', '=', 'barangays.district_id')
            ->leftJoin('businesses', 'barangays.id', '=', 'businesses.barangay_id')
            ->leftJoin('inspections', 'businesses.id', '=', 'inspections.business_id')
            ->select(
                'districts.name as district_name',
                DB::raw('COUNT(DISTINCT businesses.id) as business_count'),
                DB::raw('COUNT(DISTINCT CASE WHEN inspections.status = "completed" THEN inspections.id END) as completed_inspections'),
                DB::raw('COUNT(DISTINCT CASE WHEN businesses.compliance_status = "compliant" THEN businesses.id END) as compliant_businesses')
            )
            ->groupBy('districts.id', 'districts.name')
            ->get()
            ->toArray();
    }

    /**
     * Get dashboard data for AJAX requests
     */
    public function getData(Request $request)
    {
        $type = $request->get('type');

        return match($type) {
            'stats' => response()->json($this->getDashboardStats()),
            'recent_inspections' => response()->json(
                Inspection::with(['business', 'inspector'])
                    ->latest()
                    ->limit(10)
                    ->get()
            ),
            'pending_evidence' => response()->json(
                BusinessChecklistEvidence::with(['business', 'checklistItem'])
                    ->where('status', 'pending')
                    ->latest()
                    ->limit(10)
                    ->get()
            ),
            default => response()->json(['error' => 'Invalid data type'], 400)
        };
    }
}
