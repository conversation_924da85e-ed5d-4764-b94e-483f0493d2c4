<?php
namespace App\Config;

use App\Libraries\Auth;
use App\Controllers\AuthController;
use App\Controllers\AdminController;
use App\Controllers\BusinessController;
use App\Controllers\ComplianceEvidenceController;
use App\Controllers\InspectorController;
use App\Controllers\UserController;
use App\Controllers\InspectionController;
use App\Controllers\ChatController;
use App\Controllers\ChatbotController;
use App\Controllers\NotificationController;
use App\Controllers\InspectorAssignmentController;
use App\Controllers\HomeController;
use App\Controllers\HomepageAdminController;
use App\Controllers\WebsiteSettingsController;
use App\Controllers\FileController;
use App\Controllers\ChecklistController;
  
class Routes {
    private $auth;
    private $routes = [];
    private $currentRoute;

    public function __construct() {
        $this->auth = Auth::getInstance();
        $this->setupRoutes();
    }

    private function setupRoutes() {
        // Public homepage routes
        $this->get('', [HomeController::class, 'index']);
        $this->get('home', [HomeController::class, 'index']);
        $this->get('about', [HomeController::class, 'about']);
        $this->get('contact', [HomeController::class, 'contact']);
        $this->post('contact/submit', [HomeController::class, 'submitContact']);
        $this->get('services', [HomeController::class, 'services']);
        $this->get('news', [HomeController::class, 'news']);

        // Auth routes
        $this->get('login', [AuthController::class, 'showLogin']);
        $this->post('login', [AuthController::class, 'login']);
        $this->get('register', [AuthController::class, 'showRegister']);
        $this->post('register', [AuthController::class, 'register']);
        $this->get('logout', [AuthController::class, 'logout']);
        $this->get('auth/logout', [AuthController::class, 'logout']);

        // Dashboard routes
        $this->get('dashboard', function() {
            $auth = Auth::getInstance();
            if (!$auth->isLoggedIn()) {
                header('Location: ' . BASE_URL . 'login');
                exit();
            }
            
            $user = $auth->getUser();
            switch ($user['role']) {
                case 'admin':
                    header('Location: ' . BASE_URL . 'admin/dashboard');
                    break;
                case 'inspector':
                    header('Location: ' . BASE_URL . 'inspector/dashboard');
                    break;
                case 'business_owner':
                    header('Location: ' . BASE_URL . 'business/dashboard');
                    break;
            }
            exit();
        });

        // Admin routes
        $this->get('admin/dashboard', [AdminController::class, 'dashboard']);
        $this->get('admin/users', [UserController::class, 'index']);
        $this->get('admin/users/create', [UserController::class, 'create']);
        $this->post('admin/users/store', [UserController::class, 'store']);
        $this->get('admin/users/{id}/view', [UserController::class, 'view']);
        $this->get('admin/users/{id}/edit', [UserController::class, 'edit']);
        $this->post('admin/users/{id}/update', [UserController::class, 'update']);
        $this->post('admin/users/{id}/delete', [UserController::class, 'delete']);
        $this->post('admin/users/{id}/send-password', [UserController::class, 'sendPassword']);
        $this->post('admin/users/{id}/resend-welcome', [UserController::class, 'resendWelcome']);

        // Dedicated profile routes
        $this->get('admin/inspector-profile/{id}', [\App\Controllers\InspectorProfileController::class, 'view']);
        $this->get('admin/business-owner-profile/{id}', [\App\Controllers\BusinessOwnerProfileController::class, 'view']);

        // Homepage management routes for admin
        $this->get('admin/homepage', [HomepageAdminController::class, 'index']);
        $this->get('admin/homepage/create-content', [HomepageAdminController::class, 'createContent']);
        $this->post('admin/homepage/store-content', [HomepageAdminController::class, 'storeContent']);
        $this->get('admin/homepage/edit-content/{id}', [HomepageAdminController::class, 'editContent']);
        $this->post('admin/homepage/update-content/{id}', [HomepageAdminController::class, 'updateContent']);
        $this->post('admin/homepage/delete-content/{id}', [HomepageAdminController::class, 'deleteContent']);
        $this->get('admin/homepage/create-announcement', [HomepageAdminController::class, 'createAnnouncement']);
        $this->post('admin/homepage/store-announcement', [HomepageAdminController::class, 'storeAnnouncement']);
        $this->post('admin/homepage/delete-announcement/{id}', [HomepageAdminController::class, 'deleteAnnouncement']);
        $this->get('admin/homepage/contact-messages', [HomepageAdminController::class, 'contactMessages']);
        $this->post('admin/homepage/mark-read/{id}', [HomepageAdminController::class, 'markMessageRead']);

        // Website settings routes for admin
        $this->get('admin/website-settings', [WebsiteSettingsController::class, 'index']);
        $this->post('admin/website-settings/update-general', [WebsiteSettingsController::class, 'updateGeneral']);
        $this->post('admin/website-settings/update-colors', [WebsiteSettingsController::class, 'updateColors']);
        $this->post('admin/website-settings/apply-color-scheme/{id}', [WebsiteSettingsController::class, 'applyColorScheme']);
        $this->post('admin/website-settings/upload-image', [WebsiteSettingsController::class, 'uploadImage']);
        $this->post('admin/website-settings/reset-defaults', [WebsiteSettingsController::class, 'resetToDefaults']);
        $this->get('admin/website-settings/preview', [WebsiteSettingsController::class, 'preview']);

        // Checklist management routes for admin
        $this->get('admin/checklist', [ChecklistController::class, 'index']);
        $this->get('admin/checklist/categories/create', [ChecklistController::class, 'createCategory']);
        $this->post('admin/checklist/categories/create', [ChecklistController::class, 'storeCategory']);
        $this->get('admin/checklist/categories/{id}/edit', [ChecklistController::class, 'editCategory']);
        $this->post('admin/checklist/categories/{id}/edit', [ChecklistController::class, 'updateCategory']);
        $this->post('admin/checklist/categories/{id}/delete', [ChecklistController::class, 'deleteCategory']);
        $this->get('admin/checklist/items/create', [ChecklistController::class, 'createItem']);
        $this->post('admin/checklist/items/create', [ChecklistController::class, 'storeItem']);
        $this->get('admin/checklist/items/{id}/edit', [ChecklistController::class, 'editItem']);
        $this->post('admin/checklist/items/{id}/edit', [ChecklistController::class, 'updateItem']);
        $this->post('admin/checklist/items/{id}/delete', [ChecklistController::class, 'deleteItem']);
        $this->post('admin/checklist/items/{id}/toggle', [ChecklistController::class, 'toggleItemStatus']);

        // Business routes for admin
        $this->get('admin/businesses', [AdminController::class, 'businesses']);
        $this->get('admin/businesses/create', [AdminController::class, 'createBusiness']);
        $this->post('admin/businesses/create', [AdminController::class, 'createBusiness']);
        $this->get('admin/businesses/view/{id}', [AdminController::class, 'viewBusiness']);
        $this->get('admin/businesses/edit/{id}', [AdminController::class, 'editBusiness']);
        $this->post('admin/businesses/edit/{id}', [AdminController::class, 'editBusiness']);
        $this->post('admin/businesses/delete/{id}', [AdminController::class, 'deleteBusiness']);
        $this->post('admin/businesses/{id}/activate', [AdminController::class, 'activateBusiness']);
        $this->post('admin/businesses/{id}/suspend', [AdminController::class, 'suspendBusiness']);
        $this->get('admin/business/{id}/activate', [AdminController::class, 'activateBusiness']);
        $this->get('admin/business/{id}/deactivate', [AdminController::class, 'suspendBusiness']);

        // API routes for admin
        $this->get('admin/api/barangays/district/{districtId}', [AdminController::class, 'getBarangaysByDistrict']);

        // Compliance evidence routes for admin (verification results dashboard)
        $this->get('admin/compliance', [ComplianceEvidenceController::class, 'index']);
        $this->get('admin/compliance/business/{id}', [ComplianceEvidenceController::class, 'index']);
        // Redirect old pending route to new results dashboard
        $this->get('admin/compliance/pending', function() {
            header('Location: ' . BASE_URL . 'admin/compliance');
            exit;
        });
 
        // Individual inspection routes for admin (view and reports only)

        $this->get('admin/inspections/view/{id}', [InspectionController::class, 'view']);
        $this->get('admin/inspections/report/{id}', [InspectionController::class, 'adminInspectionReport']);
        $this->post('admin/inspections/delete/{id}', [InspectionController::class, 'delete']);
        $this->post('admin/inspections/assign/{id}', [InspectionController::class, 'assignInspectors']);
        $this->get('admin/inspections/suggest-inspectors/{businessId}', [InspectionController::class, 'suggestInspectors']);
        $this->get('admin/inspections/pending-verification', [InspectionController::class, 'pendingVerification']);
        $this->get('admin/inspections/verify/{id}', [InspectionController::class, 'showVerifyInspection']);
        $this->post('admin/inspections/verify/{id}', [InspectionController::class, 'verifyInspection']);

        // Inspector District Assignment routes for admin
        $this->get('admin/inspector-assignments', [InspectorAssignmentController::class, 'index']);
        $this->get('admin/inspector-assignments/assign/{districtId}', [InspectorAssignmentController::class, 'assignToDistrict']);
        $this->post('admin/inspector-assignments/assign/{districtId}', [InspectorAssignmentController::class, 'assignToDistrict']);
        $this->post('admin/inspector-assignments/remove', [InspectorAssignmentController::class, 'removeFromDistrict']);
        $this->get('admin/inspector-assignments/district/{districtId}', [InspectorAssignmentController::class, 'viewDistrict']);
        $this->get('admin/inspector-assignments/inspector/{inspectorId}', [InspectorAssignmentController::class, 'viewInspector']);

        $this->get('admin/inspector-assignments/integrated', [InspectorAssignmentController::class, 'integratedView']);
        $this->post('admin/inspector-assignments/schedule-inspection', [InspectorAssignmentController::class, 'scheduleInspection']);
        $this->post('admin/inspector-assignments/bulk-schedule', [InspectorAssignmentController::class, 'bulkScheduleInspections']);
        $this->post('admin/inspector-assignments/schedule-barangay', [InspectorAssignmentController::class, 'scheduleBarangayInspections']);
        $this->get('admin/api/barangay-businesses/{barangayId}', [InspectorAssignmentController::class, 'getBarangayBusinesses']);
        $this->post('admin/api/check-conflicts', [InspectorAssignmentController::class, 'checkConflicts']);

        // Inspection Assignments
        $this->get('admin/inspection-assignments', [InspectorAssignmentController::class, 'inspectionAssignments']);
        $this->post('admin/inspector-assignments/reassign', [InspectorAssignmentController::class, 'reassignInspection']);

        // Business routes for business owners
        $this->get('business', [BusinessController::class, 'index']);
        $this->get('business/create', [BusinessController::class, 'create']);
        $this->post('business/create', [BusinessController::class, 'create']);
        $this->get('business/view/{id}', [BusinessController::class, 'view']);
        $this->get('business/edit/{id}', [BusinessController::class, 'edit']);
        $this->post('business/edit/{id}', [BusinessController::class, 'edit']);
        $this->post('business/delete/{id}', [BusinessController::class, 'delete']);



        // Inspection routes for business owners
        $this->get('business/inspections', [BusinessController::class, 'inspections']);
        $this->get('business/inspections/{id}', [BusinessController::class, 'inspections']);
        $this->get('business/inspection/{id}', [BusinessController::class, 'viewInspection']);
        $this->get('business/inspection-report/{id}', [BusinessController::class, 'inspectionReport']);
        $this->get('business/dashboard', [BusinessController::class, 'dashboard']);
        $this->get('business/settings', [BusinessController::class, 'settings']);
        $this->post('business/settings/update', [BusinessController::class, 'updateSettings']);

        // Business checklist routes
        $this->get('business/checklist/{businessId}', [BusinessController::class, 'checklist']);
        $this->post('business/checklist/{businessId}/upload/{checklistItemId}', [BusinessController::class, 'uploadEvidence']);
        $this->get('business/checklist/{businessId}/evidence/{checklistItemId}', [BusinessController::class, 'viewEvidence']);
        $this->post('business/checklist/evidence/delete/{evidenceId}', [BusinessController::class, 'deleteEvidence']);
  
        // Inspector routes
        $this->get('inspector/dashboard', [InspectorController::class, 'dashboard']);
        $this->get('inspector/schedule', [InspectorController::class, 'inspections']); // Redirect to unified interface
        $this->get('inspector/inspections', [InspectorController::class, 'inspections']);
        $this->get('inspector/inspection/{id}', [InspectorController::class, 'inspection']);
        $this->get('inspector/inspection-checklist/{id}', [InspectorController::class, 'startInspectionChecklist']);
        $this->get('inspector/inspection-report/{id}', [InspectorController::class, 'inspectionReport']);
        $this->post('inspector/save-checklist-response', [InspectorController::class, 'saveChecklistResponse']);
        $this->post('inspector/inspection/{id}/complete', [InspectorController::class, 'completeInspection']);
        $this->post('inspector/inspection/{id}/start', [InspectorController::class, 'startInspection']);
        $this->post('inspector/cancel-inspection/{id}', [InspectorController::class, 'cancelInspection']);
        $this->post('inspector/confirm-inspection/{id}', [InspectorController::class, 'confirmInspection']);





        // Note: Old compliance evidence verification routes removed
        // Evidence verification now happens through inspection checklist system

        // Profile and Settings routes for all roles
        $this->get('admin/profile', [AdminController::class, 'profile']);
        $this->post('admin/profile/update', [AdminController::class, 'updateProfile']);
        $this->get('admin/settings', [AdminController::class, 'settings']);
        $this->post('admin/settings/update', [AdminController::class, 'updateSettings']);

        $this->get('inspector/profile', [InspectorController::class, 'profile']);
        $this->post('inspector/profile/update', [InspectorController::class, 'updateProfile']);
        $this->get('inspector/settings', [InspectorController::class, 'settings']);
        $this->post('inspector/settings/update', [InspectorController::class, 'updateSettings']);

        $this->get('business/profile', [BusinessController::class, 'profile']);
        $this->post('business/profile/update', [BusinessController::class, 'updateProfile']);
        // business/settings routes already exist above

        // Chat routes
        $this->get('chat', [ChatController::class, 'index']);
        $this->get('chat/room/{id}', [ChatController::class, 'room']);
        $this->post('chat/room/{id}/send', [ChatController::class, 'sendMessage']);
        $this->get('chat/create/{businessId}', [ChatController::class, 'createRoom']);
        $this->post('chat/create/{businessId}', [ChatController::class, 'createRoom']);
        $this->get('chat/messages/{roomId}', [ChatController::class, 'getMessages']);
        $this->post('chat/mark-read/{roomId}', [ChatController::class, 'markAsRead']);

        // Chatbot routes
        $this->get('admin/chatbot', [ChatbotController::class, 'adminDashboard']);
        $this->post('admin/chatbot/settings', [ChatbotController::class, 'updateSettings']);
        $this->get('admin/chatbot/responses', [ChatbotController::class, 'manageResponses']);
        $this->get('admin/chatbot/responses/add', [ChatbotController::class, 'addResponse']);
        $this->post('admin/chatbot/responses/add', [ChatbotController::class, 'addResponse']);
        $this->get('admin/chatbot/responses/edit/{id}', [ChatbotController::class, 'editResponse']);
        $this->post('admin/chatbot/responses/edit/{id}', [ChatbotController::class, 'editResponse']);
        $this->post('admin/chatbot/responses/delete/{id}', [ChatbotController::class, 'deleteResponse']);
        $this->get('admin/chatbot/knowledge', [ChatbotController::class, 'manageKnowledge']);
        $this->post('chatbot/process', [ChatbotController::class, 'processMessage']);
        $this->post('chatbot/check-admin-status', [ChatbotController::class, 'checkAdminStatus']);

        // Notification routes
        $this->get('notifications', [NotificationController::class, 'index']);
        $this->get('notifications/unread', [NotificationController::class, 'getUnread']);
        $this->post('notifications/mark-read/{id}', [NotificationController::class, 'markAsRead']);
        $this->post('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
        $this->get('notifications/count', [NotificationController::class, 'getUnreadCount']);

        // File serving routes (secure file access)
        $this->get('files/compliance-evidence/{filename}', [FileController::class, 'serveComplianceEvidence']);
        $this->get('files/uploads/{type}/{filename}', [FileController::class, 'serveUpload']);

        // Test route for debugging
        $this->get('test-file-route', function() {
            echo "File route test working!";
        });

        // Direct test for the specific file
        $this->get('test-specific-file', function() {
            $filePath = ROOT_PATH . '/public/uploads/compliance_evidence/684fabef5158d_1750051823.png';
            if (file_exists($filePath)) {
                header('Content-Type: image/png');
                header('Content-Length: ' . filesize($filePath));
                readfile($filePath);
                exit;
            } else {
                echo "File not found at: " . $filePath;
            }
        });

        // Debug route patterns
        $this->get('debug-routes', function() {
            echo "<h3>Route Debug</h3>";
            echo "<p>Current URI: " . $_SERVER['REQUEST_URI'] . "</p>";
            echo "<p>Base URL: " . BASE_URL . "</p>";
            echo "<p>ROOT_PATH: " . ROOT_PATH . "</p>";

            $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
            $basePath = parse_url(BASE_URL, PHP_URL_PATH);

            if ($basePath && strpos($uri, $basePath) === 0) {
                $uri = substr($uri, strlen($basePath));
            }

            $uri = preg_replace('#^/?public/#', '', $uri);
            $uri = trim($uri, '/');

            echo "<p>Processed URI: " . $uri . "</p>";

            // Test pattern matching
            $testRoute = 'files/compliance-evidence/{filename}';
            $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $testRoute);
            $pattern = "#^{$pattern}$#";
            echo "<p>Test Route: " . $testRoute . "</p>";
            echo "<p>Pattern: " . $pattern . "</p>";

            $testUri = 'files/compliance-evidence/684fabef5158d_1750051823.png';
            if (preg_match($pattern, $testUri, $matches)) {
                echo "<p>Pattern matches! Matches: " . print_r($matches, true) . "</p>";
            } else {
                echo "<p>Pattern does not match</p>";
            }
        });
    }

    public function get($path, $handler) {
        $this->routes['GET'][$path] = $handler;
    }

    public function post($path, $handler) {
        $this->routes['POST'][$path] = $handler;
    }

    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $basePath = parse_url(BASE_URL, PHP_URL_PATH);

        if ($basePath && strpos($uri, $basePath) === 0) {
            $uri = substr($uri, strlen($basePath));
        }

        $uri = preg_replace('#^/?public/#', '', $uri);
        $uri = trim($uri, '/');

        // Debug logging
        error_log("Routes::dispatch - Method: $method, URI: $uri");

        foreach ($this->routes[$method] ?? [] as $route => $handler) {
            // Special handling for filename parameters to allow dots and special characters
            if (strpos($route, '{filename}') !== false) {
                $pattern = str_replace('{filename}', '([^/]+)', $route);
            } else {
                $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $route);
            }
            $pattern = "#^{$pattern}$#";

            error_log("Routes::dispatch - Testing route: $route, pattern: $pattern");

            if (preg_match($pattern, $uri, $matches)) {
                error_log("Routes::dispatch - Route matched! Matches: " . print_r($matches, true));
                array_shift($matches);

                if (is_array($handler)) {
                    $controller = new $handler[0]();
                    $method = $handler[1];
                    call_user_func_array([$controller, $method], $matches);
                } else {
                    call_user_func_array($handler, $matches);
                }
                return;
            }
        }

        error_log("Routes::dispatch - No route matched for URI: $uri");
        http_response_code(404);
        include __DIR__ . '/../Views/errors/404.php';
    }
}