<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">Document Verification</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?= BASE_URL ?>admin/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Document Verification</li>
    </ol>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?= $_SESSION['success'] ?>
            <?php unset($_SESSION['success']); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger">
            <?= $_SESSION['error'] ?>
            <?php unset($_SESSION['error']); ?>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-file-alt me-1"></i>
            Pending Documents
        </div>
        <div class="card-body">
            <table id="documentsTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Business</th>
                        <th>Owner</th>
                        <th>Document Type</th>
                        <th>Submitted Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $document): ?>
                        <tr>
                            <td><?= htmlspecialchars($document['business_name']) ?></td>
                            <td><?= htmlspecialchars($document['owner_name'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($document['type']) ?></td>
                            <td><?= date('Y-m-d', strtotime($document['created_at'])) ?></td>
                            <td>
                                <a href="<?= BASE_URL ?>admin/documents/<?= $document['id'] ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#documentsTable').DataTable({
            order: [[3, 'desc']]
        });
    });
</script>
<?php $this->endSection() ?>