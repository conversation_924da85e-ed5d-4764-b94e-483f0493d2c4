-- Cleanup script to truncate inspection-related data while preserving user accounts
-- This will remove all inspection data but keep users, businesses, districts, and barangays

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear inspection-related tables
TRUNCATE TABLE inspection_checklist_responses;
TRUNCATE TABLE business_checklist_evidence;
TRUNCATE TABLE inspection_assignments;
TRUNCATE TABLE inspections;

-- Clear inspector district assignments (can be reassigned)
TRUNCATE TABLE inspector_district_assignments;

-- Clear old compliance evidence (if you want to clean this too)
-- TRUNCATE TABLE compliance_evidences;

-- Clear notifications related to inspections
DELETE FROM notifications WHERE related_type IN ('inspection', 'assignment', 'evidence');

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Show what remains (should show users, businesses, districts, barangays)
SELECT 'Users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Businesses', COUNT(*) FROM businesses
UNION ALL
SELECT 'Districts', COUNT(*) FROM districts
UNION ALL
SELECT 'Barangays', COUNT(*) FROM barangays
UNION ALL
SELECT 'Inspections', COUNT(*) FROM inspections
UNION ALL
SELECT 'Inspection Assignments', COUNT(*) FROM inspection_assignments
UNION ALL
SELECT 'Inspector District Assignments', COUNT(*) FROM inspector_district_assignments
UNION ALL
SELECT 'Business Checklist Evidence', COUNT(*) FROM business_checklist_evidence
UNION ALL
SELECT 'Inspection Checklist Responses', COUNT(*) FROM inspection_checklist_responses;
