<?php
/**
 * OHS Management System - Complete System with Public Website
 * Includes ALL features from original system
 */

// Check if setup is complete
if (!file_exists('../storage/app/setup_complete.txt')) {
    header('Location: ../setup.php');
    exit;
}

// Start session
session_start();

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed. Please run setup.php first.');
}

// Get current route
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/ohs-management-system/public', '', $path);
$path = trim($path, '/');

// Handle login
if ($_POST && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];

    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_name'] = $user['full_name'];
        $_SESSION['user_email'] = $user['email'];

        // Redirect based on role
        switch ($user['role']) {
            case 'admin':
                header('Location: /ohs-management-system/public/admin/dashboard');
                break;
            case 'inspector':
                header('Location: /ohs-management-system/public/inspector/dashboard');
                break;
            case 'business_owner':
                header('Location: /ohs-management-system/public/business/dashboard');
                break;
        }
        exit;
    } else {
        $error = 'Invalid email or password';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: /ohs-management-system/public/');
    exit;
}

// Route handling
switch ($path) {
    case '':
    case 'home':
        include 'pages/home.php';
        break;
    case 'about':
        include 'pages/about.php';
        break;
    case 'contact':
        include 'pages/contact.php';
        break;
    case 'services':
        include 'pages/services.php';
        break;
    case 'news':
        include 'pages/news.php';
        break;
    case 'login':
        include 'pages/login.php';
        break;
    case 'register':
        include 'pages/register.php';
        break;

    // Admin routes
    case (preg_match('/^admin\/(.*)/', $path, $matches) ? true : false):
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
            header('Location: /ohs-management-system/public/login');
            exit;
        }
        $admin_page = $matches[1] ?: 'dashboard';
        $admin_file = "admin/{$admin_page}.php";
        if (file_exists($admin_file)) {
            include $admin_file;
        } else {
            include 'pages/404.php';
        }
        break;

    // Inspector routes
    case (preg_match('/^inspector\/(.*)/', $path, $matches) ? true : false):
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'inspector') {
            header('Location: /ohs-management-system/public/login');
            exit;
        }
        $inspector_page = $matches[1] ?: 'dashboard';
        $inspector_file = "inspector/{$inspector_page}.php";
        if (file_exists($inspector_file)) {
            include $inspector_file;
        } else {
            include 'pages/404.php';
        }
        break;

    // Business owner routes
    case (preg_match('/^business\/(.*)/', $path, $matches) ? true : false):
        if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'business_owner') {
            header('Location: /ohs-management-system/public/login');
            exit;
        }
        $business_page = $matches[1] ?: 'dashboard';
        $business_file = "business/{$business_page}.php";
        if (file_exists($business_file)) {
            include $business_file;
        } else {
            include 'pages/404.php';
        }
        break;

    default:
        include 'pages/404.php';
        break;
}
?>
