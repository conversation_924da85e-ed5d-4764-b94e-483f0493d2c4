<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Settings</h1>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Preferences</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= BASE_URL ?>admin/settings/update">
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Notification Settings</h6>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" checked>
                                <label class="form-check-label" for="email_notifications">
                                    Email Notifications
                                </label>
                                <small class="form-text text-muted d-block">Receive email notifications for important system events</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="inspection_alerts" name="inspection_alerts" checked>
                                <label class="form-check-label" for="inspection_alerts">
                                    Inspection Alerts
                                </label>
                                <small class="form-text text-muted d-block">Get notified about upcoming and overdue inspections</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="compliance_alerts" name="compliance_alerts" checked>
                                <label class="form-check-label" for="compliance_alerts">
                                    Compliance Alerts
                                </label>
                                <small class="form-text text-muted d-block">Receive alerts for compliance evidence submissions</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="user_activity" name="user_activity">
                                <label class="form-check-label" for="user_activity">
                                    User Activity Notifications
                                </label>
                                <small class="form-text text-muted d-block">Get notified about new user registrations and activities</small>
                            </div>
                        </div>

                        <hr>

                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Dashboard Preferences</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="default_view" class="form-label">Default Dashboard View</label>
                                        <select class="form-select" id="default_view" name="default_view">
                                            <option value="overview">Overview</option>
                                            <option value="businesses">Businesses</option>
                                            <option value="inspections">Inspections</option>
                                            <option value="compliance">Compliance</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="items_per_page" class="form-label">Items Per Page</label>
                                        <select class="form-select" id="items_per_page" name="items_per_page">
                                            <option value="10">10</option>
                                            <option value="25" selected>25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="auto_refresh" name="auto_refresh">
                                <label class="form-check-label" for="auto_refresh">
                                    Auto-refresh Dashboard
                                </label>
                                <small class="form-text text-muted d-block">Automatically refresh dashboard data every 5 minutes</small>
                            </div>
                        </div>

                        <hr>

                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Security Settings</h6>
                            
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="two_factor" name="two_factor">
                                <label class="form-check-label" for="two_factor">
                                    Two-Factor Authentication
                                </label>
                                <small class="form-text text-muted d-block">Enable two-factor authentication for enhanced security</small>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="login_alerts" name="login_alerts" checked>
                                <label class="form-check-label" for="login_alerts">
                                    Login Alerts
                                </label>
                                <small class="form-text text-muted d-block">Get notified of login attempts from new devices</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="session_timeout" class="form-label">Session Timeout (minutes)</label>
                                        <select class="form-select" id="session_timeout" name="session_timeout">
                                            <option value="30">30 minutes</option>
                                            <option value="60">1 hour</option>
                                            <option value="120" selected>2 hours</option>
                                            <option value="240">4 hours</option>
                                            <option value="480">8 hours</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>System Version:</strong><br>
                        <span class="text-muted">OHS System v1.0.0</span>
                    </div>

                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <span class="text-muted"><?= date('F d, Y') ?></span>
                    </div>

                    <div class="mb-3">
                        <strong>Database Status:</strong><br>
                        <span class="badge bg-success">Connected</span>
                    </div>

                    <div class="mb-3">
                        <strong>Storage Used:</strong><br>
                        <div class="progress mb-1" style="height: 10px;">
                            <div class="progress-bar" role="progressbar" style="width: 35%" aria-valuenow="35" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted">350 MB of 1 GB used</small>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/profile" class="btn btn-outline-primary">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="<?= BASE_URL ?>admin/dashboard" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <button type="button" class="btn btn-outline-warning" onclick="clearCache()">
                            <i class="fas fa-sync"></i> Clear Cache
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearCache() {
    if (confirm('Are you sure you want to clear the system cache?')) {
        // Add cache clearing functionality here
        alert('Cache cleared successfully!');
    }
}
</script>
<?php $this->endSection() ?>
