<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-building text-primary me-2"></i>
                <?= htmlspecialchars($room['business_name']) ?>
            </h1>
            <?php if ($room['subject']): ?>
                <p class="text-muted mb-0">
                    <strong>Subject:</strong> <?= htmlspecialchars($room['subject']) ?>
                </p>
            <?php endif; ?>
        </div>
        <div>
            <a href="<?= BASE_URL ?>chat" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Chat
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Chat Messages -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 bg-primary text-white d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-comments me-2"></i>Conversation
                    </h6>
                    <div id="chatbot-status" class="d-none">
                        <span class="badge bg-light text-dark">
                            <i class="fas fa-robot me-1"></i>AI Assistant Active
                        </span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="chat-messages" class="chat-messages" style="height: 400px; overflow-y: auto; padding: 1rem;">
                        <?php if (!empty($messages)): ?>
                            <?php foreach ($messages as $message): ?>
                                <div class="message mb-3 <?= $message['sender_id'] === $user['id'] ? 'text-end' : '' ?>">
                                    <div class="message-bubble <?= $message['sender_id'] === $user['id'] ? 'bg-primary text-white ms-auto' : 'bg-light' ?>" 
                                         style="max-width: 70%; padding: 0.75rem; border-radius: 1rem; display: inline-block;">
                                        <div class="message-content">
                                            <?= nl2br(htmlspecialchars($message['message'])) ?>
                                        </div>
                                        <div class="message-meta mt-1">
                                            <small class="<?= $message['sender_id'] === $user['id'] ? 'text-white-50' : 'text-muted' ?>">
                                                <strong><?= htmlspecialchars($message['sender_name']) ?></strong>
                                                (<?= ucfirst($message['sender_role']) ?>)
                                                • <?= date('M d, Y h:i A', strtotime($message['created_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-comments fa-2x mb-3"></i>
                                <p>No messages yet. Start the conversation!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-footer">
                    <form id="message-form" class="d-flex">
                        <input type="hidden" id="room-id" value="<?= $room['id'] ?>">
                        <div class="flex-grow-1 me-2">
                            <textarea id="message-input" 
                                      class="form-control" 
                                      rows="2" 
                                      placeholder="Type your message..."
                                      required></textarea>
                        </div>
                        <div class="align-self-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Chat Info -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Chat Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted">Business</h6>
                        <p class="mb-0"><?= htmlspecialchars($room['business_name']) ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Participants</h6>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item px-0 py-2">
                                <i class="fas fa-user-tie text-primary me-2"></i>
                                <strong>Business Owner:</strong><br>
                                <?= htmlspecialchars($room['business_owner_name']) ?>
                                <br><small class="text-muted"><?= htmlspecialchars($room['business_owner_email']) ?></small>
                            </div>
                            
                            <?php if ($room['admin_name']): ?>
                                <div class="list-group-item px-0 py-2">
                                    <i class="fas fa-user-shield text-success me-2"></i>
                                    <strong>Admin:</strong><br>
                                    <?= htmlspecialchars($room['admin_name']) ?>
                                    <br><small class="text-muted"><?= htmlspecialchars($room['admin_email']) ?></small>
                                </div>
                            <?php else: ?>
                                <div class="list-group-item px-0 py-2 text-muted">
                                    <i class="fas fa-user-shield me-2"></i>
                                    <em>No admin assigned yet</em>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($room['inspector_name']): ?>
                                <div class="list-group-item px-0 py-2">
                                    <i class="fas fa-user-check text-info me-2"></i>
                                    <strong>Inspector:</strong><br>
                                    <?= htmlspecialchars($room['inspector_name']) ?>
                                    <br><small class="text-muted"><?= htmlspecialchars($room['inspector_email']) ?></small>
                                </div>
                            <?php else: ?>
                                <div class="list-group-item px-0 py-2 text-muted">
                                    <i class="fas fa-user-check me-2"></i>
                                    <em>No inspector assigned yet</em>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        <span class="badge bg-<?= $room['status'] === 'active' ? 'success' : 'secondary' ?> fs-6">
                            <?= ucfirst($room['status']) ?>
                        </span>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Created</h6>
                        <p class="mb-0"><?= date('M d, Y h:i A', strtotime($room['created_at'])) ?></p>
                    </div>

                    <?php if ($user['role'] === 'admin' && $room['status'] === 'active'): ?>
                        <div class="mt-4">
                            <div class="mb-2">
                                <button id="admin-toggle-chatbot" class="btn btn-outline-primary btn-sm w-100" onclick="toggleChatbot()">
                                    <i class="fas fa-robot me-2"></i><span class="chatbot-toggle-text">Enable AI Assistant</span>
                                </button>
                            </div>
                            <button class="btn btn-outline-danger btn-sm w-100" onclick="closeChat()">
                                <i class="fas fa-times me-2"></i>Close Chat
                            </button>
                        </div>
                    <?php endif; ?>

                    <?php if ($user['role'] === 'business_owner'): ?>
                        <div class="mt-4">
                            <div class="mb-2">
                                <button id="business-toggle-chatbot" class="btn btn-outline-primary btn-sm w-100" onclick="toggleChatbot()">
                                    <i class="fas fa-robot me-2"></i><span class="chatbot-toggle-text">Enable AI Assistant</span>
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm w-100 mt-2" onclick="testChatbotDirect()">
                                    <i class="fas fa-bug me-2"></i>Test Bot Direct
                                </button>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Need immediate help?</strong><br>
                                Click "Enable AI Assistant" above to get instant help with common OHS questions.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables for chatbot functionality
let chatbotActive = false;
let lastAdminActivity = null;
let roomId, userRole, messageForm, messageInput, messagesContainer;

document.addEventListener('DOMContentLoaded', function() {
    messageForm = document.getElementById('message-form');
    messageInput = document.getElementById('message-input');
    messagesContainer = document.getElementById('chat-messages');
    roomId = document.getElementById('room-id').value;
    userRole = '<?= $user['role'] ?>';

    // Debug logging
    console.log('Chat room loaded for user role:', userRole);
    console.log('Room ID:', roomId);

    // Scroll to bottom of messages
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Initial scroll to bottom
    scrollToBottom();

    // Handle form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const message = messageInput.value.trim();
        if (!message) return;

        // Disable form while sending
        messageInput.disabled = true;
        
        fetch(`<?= BASE_URL ?>chat/room/${roomId}/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `message=${encodeURIComponent(message)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                messageInput.value = '';
                loadMessages();

                // Check if chatbot should respond
                if (userRole === 'business_owner' && chatbotActive) {
                    setTimeout(() => {
                        triggerChatbotResponse(message);
                    }, 2000); // 2 second delay
                }
            } else {
                alert('Failed to send message: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to send message');
        })
        .finally(() => {
            messageInput.disabled = false;
            messageInput.focus();
        });
    });

    // Load messages
    function loadMessages() {
        fetch(`<?= BASE_URL ?>chat/messages/${roomId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateMessages(data.messages);
            }
        })
        .catch(error => console.error('Error loading messages:', error));
    }

    // Update messages display
    function updateMessages(messages) {
        const currentUserId = '<?= $user['id'] ?>';
        let html = '';
        
        if (messages.length === 0) {
            html = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-comments fa-2x mb-3"></i>
                    <p>No messages yet. Start the conversation!</p>
                </div>
            `;
        } else {
            messages.forEach(message => {
                const isOwn = message.sender_id === currentUserId;
                const alignClass = isOwn ? 'text-end' : '';
                const bubbleClass = isOwn ? 'bg-primary text-white ms-auto' : 'bg-light';
                const textClass = isOwn ? 'text-white-50' : 'text-muted';
                
                html += `
                    <div class="message mb-3 ${alignClass}">
                        <div class="message-bubble ${bubbleClass}" 
                             style="max-width: 70%; padding: 0.75rem; border-radius: 1rem; display: inline-block;">
                            <div class="message-content">
                                ${message.message.replace(/\n/g, '<br>')}
                            </div>
                            <div class="message-meta mt-1">
                                <small class="${textClass}">
                                    <strong>${message.sender_name}</strong>
                                    (${message.sender_role.charAt(0).toUpperCase() + message.sender_role.slice(1)})
                                    • ${new Date(message.created_at).toLocaleString()}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
        }
        
        messagesContainer.innerHTML = html;

        // Re-add chatbot messages that were stored
        chatbotMessages.forEach(chatbotMsg => {
            addChatbotMessageToDOM(chatbotMsg.message, chatbotMsg.type);
        });

        scrollToBottom();
    }

    // Auto-refresh messages every 5 seconds
    setInterval(loadMessages, 5000);

    // Handle Enter key in textarea
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            messageForm.dispatchEvent(new Event('submit'));
        }
    });

    // Check if chatbot should be auto-activated
    checkChatbotAutoActivation();

    // For business owners, also try to auto-activate immediately
    if (userRole === 'business_owner') {
        console.log('Business owner detected - attempting auto-activation...');
        console.log('BASE_URL is:', '<?= BASE_URL ?>');
        console.log('Chatbot endpoint will be:', '<?= BASE_URL ?>chatbot/process');

        setTimeout(() => {
            if (!chatbotActive) {
                console.log('Auto-activating chatbot for business owner...');
                activateChatbot();
                showChatbotGreeting();
            }
        }, 3000); // Wait 3 seconds then auto-activate
    }

    // Check admin activity every 30 seconds
    setInterval(checkChatbotAutoActivation, 30000);
});

// Chatbot Functions
function checkChatbotAutoActivation() {
    // Only for business owners
    if (userRole !== 'business_owner') return;

    fetch(`<?= BASE_URL ?>chatbot/check-admin-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `room_id=${roomId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.should_activate_chatbot && !chatbotActive) {
            activateChatbot();
            showChatbotGreeting();
        }
    })
    .catch(error => console.error('Error checking admin status:', error));
}

function triggerChatbotResponse(userMessage) {
    console.log('triggerChatbotResponse called with message:', userMessage);

    // Show typing indicator
    showTypingIndicator();

    fetch(`<?= BASE_URL ?>chatbot/process`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `chat_room_id=${roomId}&message=${encodeURIComponent(userMessage)}`
    })
    .then(response => {
        console.log('Chatbot API response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Chatbot API response data:', data);
        hideTypingIndicator();

        if (data.success) {
            addChatbotMessage(data.response, data.type);

            if (data.escalated) {
                deactivateChatbot();
                addSystemMessage('Your conversation has been escalated to a human agent.');
            }
        } else {
            console.error('Chatbot API error:', data.message);
            addSystemMessage('Sorry, the AI assistant is having trouble responding. Please try again.');
        }
    })
    .catch(error => {
        hideTypingIndicator();
        console.error('Error getting chatbot response:', error);
        addSystemMessage('Sorry, there was a network error. Please try again.');
    });
}

function activateChatbot() {
    chatbotActive = true;
    document.getElementById('chatbot-status').classList.remove('d-none');

    // Update admin button if exists
    const adminButton = document.getElementById('admin-toggle-chatbot');
    if (adminButton) {
        adminButton.querySelector('.chatbot-toggle-text').textContent = 'Disable AI Assistant';
        adminButton.classList.remove('btn-outline-primary');
        adminButton.classList.add('btn-outline-warning');
    }

    // Update business owner button if exists
    const businessButton = document.getElementById('business-toggle-chatbot');
    if (businessButton) {
        businessButton.querySelector('.chatbot-toggle-text').textContent = 'Disable AI Assistant';
        businessButton.classList.remove('btn-outline-primary');
        businessButton.classList.add('btn-outline-warning');
    }
}

function deactivateChatbot() {
    chatbotActive = false;
    document.getElementById('chatbot-status').classList.add('d-none');

    // Update admin button if exists
    const adminButton = document.getElementById('admin-toggle-chatbot');
    if (adminButton) {
        adminButton.querySelector('.chatbot-toggle-text').textContent = 'Enable AI Assistant';
        adminButton.classList.remove('btn-outline-warning');
        adminButton.classList.add('btn-outline-primary');
    }

    // Update business owner button if exists
    const businessButton = document.getElementById('business-toggle-chatbot');
    if (businessButton) {
        businessButton.querySelector('.chatbot-toggle-text').textContent = 'Enable AI Assistant';
        businessButton.classList.remove('btn-outline-warning');
        businessButton.classList.add('btn-outline-primary');
    }
}

function showChatbotGreeting() {
    addChatbotMessage('Hello! I\'m the OHS Assistant. I noticed no admin is currently available. How can I help you with your occupational health and safety questions?', 'text');
}

// Store chatbot messages to prevent them from being lost
let chatbotMessages = [];

function addChatbotMessage(message, type) {
    // Store the chatbot message
    const chatbotMsg = {
        id: 'chatbot_' + Date.now(),
        message: message,
        type: type,
        timestamp: new Date().toISOString(),
        sender_name: 'OHS Assistant',
        sender_role: 'chatbot'
    };

    chatbotMessages.push(chatbotMsg);

    // Add to DOM
    addChatbotMessageToDOM(message, type);
}

function addChatbotMessageToDOM(message, type) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageHtml = `
        <div class="message mb-3 chatbot-message" data-chatbot="true">
            <div class="message-bubble bg-info text-white"
                 style="max-width: 70%; padding: 0.75rem; border-radius: 1rem; display: inline-block;">
                <div class="message-content">
                    <i class="fas fa-robot me-2"></i>${message.replace(/\n/g, '<br>')}
                </div>
                <div class="message-meta mt-1">
                    <small class="text-white-50">
                        <strong>OHS Assistant</strong> (AI Bot) • ${new Date().toLocaleString()}
                    </small>
                </div>
            </div>
        </div>
    `;

    messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function addSystemMessage(message) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageHtml = `
        <div class="message mb-3 text-center">
            <div class="alert alert-info d-inline-block" style="max-width: 70%;">
                <i class="fas fa-info-circle me-2"></i>${message}
            </div>
        </div>
    `;

    messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function showTypingIndicator() {
    const messagesContainer = document.getElementById('chat-messages');
    const typingHtml = `
        <div id="typing-indicator" class="message mb-3">
            <div class="message-bubble bg-light"
                 style="max-width: 70%; padding: 0.75rem; border-radius: 1rem; display: inline-block;">
                <div class="typing-dots">
                    <i class="fas fa-robot me-2"></i>
                    <span class="dot">.</span><span class="dot">.</span><span class="dot">.</span>
                </div>
            </div>
        </div>
    `;

    messagesContainer.insertAdjacentHTML('beforeend', typingHtml);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    if (indicator) {
        indicator.remove();
    }
}

function toggleChatbot() {
    console.log('toggleChatbot called, current state:', chatbotActive);

    if (chatbotActive) {
        console.log('Deactivating chatbot...');
        deactivateChatbot();
        addSystemMessage('AI Assistant has been disabled.');
    } else {
        console.log('Activating chatbot...');
        activateChatbot();
        showChatbotGreeting();
    }

    console.log('New chatbot state:', chatbotActive);
}

function closeChat() {
    if (confirm('Are you sure you want to close this chat? This action cannot be undone.')) {
        // Implement close chat functionality
        alert('Close chat functionality will be implemented');
    }
}

function testChatbotDirect() {
    console.log('🔧 DIRECT CHATBOT TEST');
    console.log('BASE_URL:', '<?= BASE_URL ?>');
    console.log('Room ID:', roomId);
    console.log('User Role:', userRole);
    console.log('Chatbot Active:', chatbotActive);

    // Force activate chatbot
    chatbotActive = true;
    document.getElementById('chatbot-status').classList.remove('d-none');

    // Test direct response
    triggerChatbotResponse('hello test');

    alert('Check console for debug info and watch for chatbot response!');
}
</script>

<style>
.chat-messages {
    background-color: #f8f9fa;
}

.message-bubble {
    word-wrap: break-word;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Chatbot typing animation */
.typing-dots .dot {
    animation: typing 1.4s infinite;
    opacity: 0;
}

.typing-dots .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots .dot:nth-child(3) {
    animation-delay: 0.4s;
}

.typing-dots .dot:nth-child(4) {
    animation-delay: 0.6s;
}

@keyframes typing {
    0%, 60%, 100% {
        opacity: 0;
    }
    30% {
        opacity: 1;
    }
}
</style>
<?php $this->endSection() ?>
