<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user text-primary me-2"></i>
            User Details - <?= htmlspecialchars($user['full_name']) ?>
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/users/<?= $user['id'] ?>/edit" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit User
            </a>
            <a href="<?= BASE_URL ?>admin/users" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>User Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-lg bg-primary bg-opacity-10 text-primary mx-auto mb-3">
                            <?= strtoupper(substr($user['full_name'], 0, 2)) ?>
                        </div>
                        <h5 class="mb-1"><?= htmlspecialchars($user['full_name']) ?></h5>
                        <p class="text-muted mb-0"><?= htmlspecialchars($user['email']) ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">User ID</h6>
                        <p class="mb-0"><code><?= htmlspecialchars($user['id']) ?></code></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Role</h6>
                        <p class="mb-0">
                            <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'inspector' ? 'success' : ($user['role'] === 'staff' ? 'info' : 'secondary')) ?> fs-6">
                                <?= ucfirst($user['role']) ?>
                            </span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        <p class="mb-0">
                            <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'warning' ?> fs-6">
                                <?= ucfirst($user['status']) ?>
                            </span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Created</h6>
                        <p class="mb-0"><?= date('M d, Y h:i A', strtotime($user['created_at'])) ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Last Login</h6>
                        <p class="mb-0">
                            <?php if (!empty($user['last_login'])): ?>
                                <?= date('M d, Y h:i A', strtotime($user['last_login'])) ?>
                            <?php else: ?>
                                <span class="text-muted">Never logged in</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/users/<?= $user['id'] ?>/edit" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit User Information
                        </a>
                        <button class="btn btn-success" onclick="confirmSendPassword('<?= $user['id'] ?>', '<?= htmlspecialchars($user['full_name']) ?>', '<?= htmlspecialchars($user['email']) ?>')">
                            <i class="fas fa-key"></i> Send New Password
                        </button>
                        <button class="btn btn-info" onclick="confirmResendWelcome('<?= $user['id'] ?>', '<?= htmlspecialchars($user['full_name']) ?>', '<?= htmlspecialchars($user['email']) ?>')">
                            <i class="fas fa-envelope"></i> Resend Welcome Email
                        </button>
                        <?php if ($user['role'] === 'inspector'): ?>
                            <a href="<?= BASE_URL ?>admin/inspector-assignments/inspector/<?= $user['id'] ?>" class="btn btn-warning">
                                <i class="fas fa-map-marked-alt"></i> Manage District Assignments
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role-Specific Information -->
        <div class="col-lg-8">
            <?php if ($user['role'] === 'inspector'): ?>
                <!-- Inspector-Specific Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marked-alt me-2"></i>Barangay Assignments
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($barangays)): ?>
                            <div class="row">
                                <?php foreach ($barangays as $assignment): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-left-primary h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title text-primary mb-1">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?= htmlspecialchars($assignment['barangay_name']) ?>
                                                    </h6>
                                                    <span class="badge bg-success">Active</span>
                                                </div>
                                                <p class="text-muted mb-2">
                                                    <i class="fas fa-layer-group me-1"></i>
                                                    <?= htmlspecialchars($assignment['district_name']) ?>
                                                </p>
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    Assigned by: <?= htmlspecialchars($assignment['assigned_by_name'] ?? 'System') ?>
                                                </small>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?= date('M d, Y', strtotime($assignment['assigned_at'])) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Barangay Assignments</h5>
                                <p class="text-muted mb-3">This inspector hasn't been assigned to any barangays yet.</p>
                                <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> Assign to Barangays
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Workload Statistics -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-bar me-2"></i>Workload Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <div class="card bg-primary text-white h-100">
                                    <div class="card-body">
                                        <div class="text-white-50 small">Assigned Barangays</div>
                                        <div class="text-lg font-weight-bold"><?= $workload['assigned_barangays'] ?? 0 ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card bg-info text-white h-100">
                                    <div class="card-body">
                                        <div class="text-white-50 small">Businesses</div>
                                        <div class="text-lg font-weight-bold"><?= $workload['businesses_in_barangays'] ?? 0 ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card bg-warning text-white h-100">
                                    <div class="card-body">
                                        <div class="text-white-50 small">Pending Inspections</div>
                                        <div class="text-lg font-weight-bold"><?= $workload['pending_inspections'] ?? 0 ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card bg-success text-white h-100">
                                    <div class="card-body">
                                        <div class="text-white-50 small">Completed</div>
                                        <div class="text-lg font-weight-bold"><?= $workload['completed_inspections'] ?? 0 ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>


                <!-- Recent Inspections -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-clipboard-list me-2"></i>Recent Inspections
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_inspections)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Business</th>
                                            <th>District</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Score</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($recent_inspections, 0, 5) as $inspection): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($inspection['business_name']) ?></td>
                                                <td><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></td>
                                                <td><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                                                        <?= ucfirst($inspection['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($inspection['score']): ?>
                                                        <?= $inspection['score'] ?>%
                                                    <?php else: ?>
                                                        -
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="<?= BASE_URL ?>admin/inspections?inspector=<?= $user['id'] ?>" class="btn btn-outline-primary">
                                    View All Inspections
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-2x text-muted mb-3"></i>
                                <p class="text-muted mb-0">No inspections found for this inspector.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            <?php elseif ($user['role'] === 'business_owner'): ?>
                <!-- Business Owner Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-building me-2"></i>Owned Businesses
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($businesses)): ?>
                            <div class="row">
                                <?php foreach ($businesses as $business): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-left-info h-100">
                                            <div class="card-body">
                                                <h6 class="card-title text-info">
                                                    <?= htmlspecialchars($business['name']) ?>
                                                </h6>
                                                <p class="text-muted mb-2">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?= htmlspecialchars($business['address']) ?>
                                                </p>
                                                <span class="badge bg-<?= $business['status'] === 'active' ? 'success' : 'warning' ?>">
                                                    <?= ucfirst($business['status']) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                <p class="text-muted mb-0">No businesses registered for this owner.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            <?php else: ?>
                <!-- Admin/Staff Information -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user-shield me-2"></i>Administrative Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="fas fa-user-shield fa-3x text-primary mb-3"></i>
                            <h5 class="text-primary mb-3"><?= ucfirst($user['role']) ?> User</h5>
                            <p class="text-muted">
                                This user has <?= $user['role'] === 'admin' ? 'full administrative' : 'staff-level' ?> access to the system.
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Send Password Modal -->
<div class="modal fade" id="sendPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send New Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Generate a new random password for <strong id="passwordUserName"></strong> and send it to:</p>
                <p class="text-primary"><i class="fas fa-envelope me-2"></i><strong id="passwordUserEmail"></strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This will replace the user's current password and send the new one via email.
                </div>
            </div>
            <div class="modal-footer">
                <form id="sendPasswordForm" action="" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-key me-2"></i>Generate & Send Password
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Resend Welcome Modal -->
<div class="modal fade" id="resendWelcomeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Resend Welcome Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Send a welcome email with new login credentials to <strong id="welcomeUserName"></strong>:</p>
                <p class="text-primary"><i class="fas fa-envelope me-2"></i><strong id="welcomeUserEmail"></strong></p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This will generate a new password and send a complete welcome email with login instructions.
                </div>
            </div>
            <div class="modal-footer">
                <form id="resendWelcomeForm" action="" method="POST">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-envelope me-2"></i>Send Welcome Email
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.5rem;
}

.card.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.card.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.card.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.card.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>

<script>
function confirmSendPassword(userId, userName, userEmail) {
    document.getElementById('passwordUserName').textContent = userName;
    document.getElementById('passwordUserEmail').textContent = userEmail;
    document.getElementById('sendPasswordForm').action = `<?= BASE_URL ?>admin/users/${userId}/send-password`;
    new bootstrap.Modal(document.getElementById('sendPasswordModal')).show();
}

function confirmResendWelcome(userId, userName, userEmail) {
    document.getElementById('welcomeUserName').textContent = userName;
    document.getElementById('welcomeUserEmail').textContent = userEmail;
    document.getElementById('resendWelcomeForm').action = `<?= BASE_URL ?>admin/users/${userId}/resend-welcome`;
    new bootstrap.Modal(document.getElementById('resendWelcomeModal')).show();
}
</script>
<?php $this->endSection() ?>
