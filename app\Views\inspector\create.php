<?php include __DIR__ . '/../layouts/header.php'; ?>

<div class="container">
    <h1>Schedule Inspection for <?= htmlspecialchars($business['name']) ?></h1>
    
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($_SESSION['error']) ?></div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <form action="<?= BASE_URL ?>business/<?= $business['id'] ?>/inspections/store" method="POST">
        <div class="form-group">
            <label for="inspector_id">Inspector</label>
            <select id="inspector_id" name="inspector_id" class="form-control" required>
                <option value="">Select Inspector</option>
                <?php foreach ($inspectors as $inspector): ?>
                    <option value="<?= $inspector['id'] ?>"><?= htmlspecialchars($inspector['full_name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="scheduled_date">Scheduled Date</label>
            <input type="datetime-local" id="scheduled_date" name="scheduled_date" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label for="notes">Notes</label>
            <textarea id="notes" name="notes" rows="3" class="form-control"></textarea>
        </div>
        
        <button type="submit" class="btn btn-primary">Schedule Inspection</button>
        <a href="<?= BASE_URL ?>business/<?= $business['id'] ?>/inspections" class="btn btn-secondary">Cancel</a>
    </form>
</div>

<?php include __DIR__ . '/../layouts/footer.php'; ?>