<?php
namespace App\Middleware;

use App\Libraries\Auth;

class AuthMiddleware {
    public function handle() {
        $auth = Auth::getInstance();
        
        if (!$auth->isLoggedIn()) {
            $_SESSION['redirect_url'] = $_SERVER['REQUEST_URI'];
            header('Location: ' . BASE_URL . 'login');
            exit();
        }

        // Get current path
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $basePath = parse_url(BASE_URL, PHP_URL_PATH);
        if ($basePath && strpos($path, $basePath) === 0) {
            $path = substr($path, strlen($basePath));
        }
        $path = trim($path, '/');

        // Check if user has access to this path
        $user = $auth->getUser();
        if (!$user) {
            header('Location: ' . BASE_URL . 'login');
            exit();
        }

        $role = strtolower($user['role']);
        $allowedPath = false;

        switch ($role) {
            case 'admin':
                $allowedPath = strpos($path, 'admin') === 0;
                break;
            case 'inspector':
                $allowedPath = strpos($path, 'inspector') === 0;
                break;
            case 'business_owner':
                $allowedPath = strpos($path, 'business') === 0;
                break;
            case 'safety_officer':
                $allowedPath = strpos($path, 'safety') === 0;
                break;
        }

        if (!$allowedPath) {
            switch ($role) {
                case 'admin':
                    header('Location: ' . BASE_URL . 'admin/dashboard');
                    break;
                case 'inspector':
                    header('Location: ' . BASE_URL . 'inspector/dashboard');
                    break;
                case 'business_owner':
                    header('Location: ' . BASE_URL . 'business/dashboard');
                    break;
                case 'safety_officer':
                    header('Location: ' . BASE_URL . 'safety/dashboard');
                    break;
                default:
                    header('Location: ' . BASE_URL . 'login');
            }
            exit();
        }
    }
}

class AdminMiddleware {
    public function handle() {
        $auth = Auth::getInstance();
        if (!$auth->isAdmin()) {
            header('Location: ' . BASE_URL . 'login');
            exit();
        }
    }
}