<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\ChecklistModel;

class ChecklistController extends Controller {
    private $checklistModel;
    protected $auth;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->checklistModel = new ChecklistModel();
    }

    /**
     * Admin checklist management dashboard
     */
    public function index() {
        $this->auth->requireAdmin();

        $categories = $this->checklistModel->getAllCategoriesWithItems();
        $stats = [
            'total_categories' => count($categories),
            'total_items' => array_sum(array_column($categories, 'item_count')),
            'critical_items' => $this->checklistModel->countCriticalItems(),
            'active_items' => $this->checklistModel->countActiveItems()
        ];

        return $this->render('admin/checklist/index', [
            'title' => 'Checklist Management',
            'active_page' => 'checklist',
            'categories' => $categories,
            'stats' => $stats,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Show form to create new category
     */
    public function createCategory() {
        $this->auth->requireAdmin();

        return $this->render('admin/checklist/create_category', [
            'title' => 'Create Checklist Category',
            'active_page' => 'checklist',
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Store new category
     */
    public function storeCategory() {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        $data = [
            'name' => trim($_POST['name'] ?? ''),
            'description' => trim($_POST['description'] ?? ''),
            'weight' => floatval($_POST['weight'] ?? 1.0),
            'sort_order' => intval($_POST['sort_order'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        // Validation
        if (empty($data['name'])) {
            $_SESSION['error'] = 'Category name is required.';
            $this->redirect('admin/checklist/categories/create');
            return;
        }

        try {
            $this->checklistModel->createCategory($data);
            $_SESSION['success'] = 'Category created successfully.';
            $this->redirect('admin/checklist');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error creating category: ' . $e->getMessage();
            $this->redirect('admin/checklist/categories/create');
        }
    }

    /**
     * Show form to edit category
     */
    public function editCategory($id) {
        $this->auth->requireAdmin();

        $category = $this->checklistModel->getCategoryById($id);
        if (!$category) {
            $_SESSION['error'] = 'Category not found.';
            $this->redirect('admin/checklist');
            return;
        }

        return $this->render('admin/checklist/edit_category', [
            'title' => 'Edit Checklist Category',
            'active_page' => 'checklist',
            'category' => $category,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Update category
     */
    public function updateCategory($id) {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        $category = $this->checklistModel->getCategoryById($id);
        if (!$category) {
            $_SESSION['error'] = 'Category not found.';
            $this->redirect('admin/checklist');
            return;
        }

        $data = [
            'name' => trim($_POST['name'] ?? ''),
            'description' => trim($_POST['description'] ?? ''),
            'weight' => floatval($_POST['weight'] ?? 1.0),
            'sort_order' => intval($_POST['sort_order'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        // Validation
        if (empty($data['name'])) {
            $_SESSION['error'] = 'Category name is required.';
            $this->redirect('admin/checklist/categories/' . $id . '/edit');
            return;
        }

        try {
            $this->checklistModel->updateCategory($id, $data);
            $_SESSION['success'] = 'Category updated successfully.';
            $this->redirect('admin/checklist');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error updating category: ' . $e->getMessage();
            $this->redirect('admin/checklist/categories/' . $id . '/edit');
        }
    }

    /**
     * Delete category
     */
    public function deleteCategory($id) {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        try {
            $this->checklistModel->deleteCategory($id);
            $_SESSION['success'] = 'Category deleted successfully.';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error deleting category: ' . $e->getMessage();
        }

        $this->redirect('admin/checklist');
    }

    /**
     * Show form to create new checklist item
     */
    public function createItem() {
        $this->auth->requireAdmin();

        $categories = $this->checklistModel->getAllCategories();

        return $this->render('admin/checklist/create_item', [
            'title' => 'Create Checklist Item',
            'active_page' => 'checklist',
            'categories' => $categories,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Store new checklist item
     */
    public function storeItem() {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        $data = [
            'category_id' => trim($_POST['category_id'] ?? ''),
            'item_code' => trim($_POST['item_code'] ?? ''),
            'item_name' => trim($_POST['item_name'] ?? ''),
            'description' => trim($_POST['description'] ?? ''),
            'compliance_requirement' => trim($_POST['compliance_requirement'] ?? ''),
            'points' => floatval($_POST['points'] ?? 1.0),
            'is_critical' => isset($_POST['is_critical']) ? 1 : 0,
            'sort_order' => intval($_POST['sort_order'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        // Validation
        if (empty($data['category_id']) || empty($data['item_code']) || empty($data['item_name'])) {
            $_SESSION['error'] = 'Category, item code, and item name are required.';
            $this->redirect('admin/checklist/items/create');
            return;
        }

        try {
            $this->checklistModel->createItem($data);
            $_SESSION['success'] = 'Checklist item created successfully.';
            $this->redirect('admin/checklist');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error creating checklist item: ' . $e->getMessage();
            $this->redirect('admin/checklist/items/create');
        }
    }

    /**
     * Show form to edit checklist item
     */
    public function editItem($id) {
        $this->auth->requireAdmin();

        $item = $this->checklistModel->getItemById($id);
        if (!$item) {
            $_SESSION['error'] = 'Checklist item not found.';
            $this->redirect('admin/checklist');
            return;
        }

        $categories = $this->checklistModel->getAllCategories();

        return $this->render('admin/checklist/edit_item', [
            'title' => 'Edit Checklist Item',
            'active_page' => 'checklist',
            'item' => $item,
            'categories' => $categories,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Update checklist item
     */
    public function updateItem($id) {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        $item = $this->checklistModel->getItemById($id);
        if (!$item) {
            $_SESSION['error'] = 'Checklist item not found.';
            $this->redirect('admin/checklist');
            return;
        }

        $data = [
            'category_id' => trim($_POST['category_id'] ?? ''),
            'item_code' => trim($_POST['item_code'] ?? ''),
            'item_name' => trim($_POST['item_name'] ?? ''),
            'description' => trim($_POST['description'] ?? ''),
            'compliance_requirement' => trim($_POST['compliance_requirement'] ?? ''),
            'points' => floatval($_POST['points'] ?? 1.0),
            'is_critical' => isset($_POST['is_critical']) ? 1 : 0,
            'sort_order' => intval($_POST['sort_order'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        // Validation
        if (empty($data['category_id']) || empty($data['item_code']) || empty($data['item_name'])) {
            $_SESSION['error'] = 'Category, item code, and item name are required.';
            $this->redirect('admin/checklist/items/' . $id . '/edit');
            return;
        }

        try {
            $this->checklistModel->updateItem($id, $data);
            $_SESSION['success'] = 'Checklist item updated successfully.';
            $this->redirect('admin/checklist');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error updating checklist item: ' . $e->getMessage();
            $this->redirect('admin/checklist/items/' . $id . '/edit');
        }
    }

    /**
     * Delete checklist item
     */
    public function deleteItem($id) {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        try {
            $this->checklistModel->deleteItem($id);
            $_SESSION['success'] = 'Checklist item deleted successfully.';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error deleting checklist item: ' . $e->getMessage();
        }

        $this->redirect('admin/checklist');
    }

    /**
     * Toggle item active status
     */
    public function toggleItemStatus($id) {
        $this->auth->requireAdmin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('admin/checklist');
            return;
        }

        try {
            $this->checklistModel->toggleItemStatus($id);
            $_SESSION['success'] = 'Item status updated successfully.';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Error updating item status: ' . $e->getMessage();
        }

        $this->redirect('admin/checklist');
    }
}
