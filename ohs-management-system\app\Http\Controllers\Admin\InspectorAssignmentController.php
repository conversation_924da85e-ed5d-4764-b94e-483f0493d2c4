<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\District;
use App\Models\Barangay;
use App\Models\Business;
use App\Models\Inspection;
use App\Models\InspectorDistrictAssignment;
use App\Models\InspectorBarangayAssignment;
use App\Services\EmailService;
use App\Http\Controllers\NotificationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InspectorAssignmentController extends Controller
{
    protected $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Display inspector assignments dashboard
     */
    public function index()
    {
        $inspectors = User::where('role', 'inspector')
            ->where('status', 'active')
            ->with(['districtAssignments.district', 'barangayAssignments.barangay'])
            ->get();

        $districts = District::with(['barangays', 'inspectorAssignments.inspector'])->get();

        // Get assignment statistics
        $stats = [
            'total_inspectors' => User::where('role', 'inspector')->where('status', 'active')->count(),
            'assigned_inspectors' => User::where('role', 'inspector')
                ->where('status', 'active')
                ->whereHas('districtAssignments')
                ->count(),
            'unassigned_inspectors' => User::where('role', 'inspector')
                ->where('status', 'active')
                ->whereDoesntHave('districtAssignments')
                ->count(),
            'total_districts' => District::count(),
            'assigned_districts' => District::whereHas('inspectorAssignments')->count(),
        ];

        return view('admin.inspector-assignments.index', compact('inspectors', 'districts', 'stats'));
    }

    /**
     * Display integrated assignment and scheduling view
     */
    public function integratedView(Request $request)
    {
        $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();
        $districts = District::with('barangays')->get();
        $businesses = Business::where('status', 'active')->with(['barangay.district'])->get();

        // Get inspections with filters
        $query = Inspection::with(['business.barangay.district', 'inspector', 'assignedBy']);

        if ($request->filled('inspector_id')) {
            $query->where('inspector_id', $request->inspector_id);
        }

        if ($request->filled('district_id')) {
            $query->whereHas('business.barangay', function($q) use ($request) {
                $q->where('district_id', $request->district_id);
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
        }

        $inspections = $query->latest('scheduled_date')->paginate(20);

        // Get assignment overview
        $assignmentOverview = $this->getAssignmentOverview();

        return view('admin.inspector-assignments.integrated', compact(
            'inspectors',
            'districts',
            'businesses',
            'inspections',
            'assignmentOverview'
        ));
    }

    /**
     * Assign inspector to district
     */
    public function assignToDistrict(Request $request, $districtId)
    {
        if ($request->isMethod('GET')) {
            $district = District::with('barangays')->findOrFail($districtId);
            $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();
            
            // Get currently assigned inspectors
            $assignedInspectors = InspectorDistrictAssignment::where('district_id', $districtId)
                ->where('status', 'active')
                ->with('inspector')
                ->get();

            return view('admin.inspector-assignments.assign-district', compact(
                'district',
                'inspectors',
                'assignedInspectors'
            ));
        }

        $validated = $request->validate([
            'inspector_ids' => 'required|array',
            'inspector_ids.*' => 'exists:users,id',
            'assignment_type' => 'required|in:district,barangay',
            'barangay_ids' => 'nullable|array',
            'barangay_ids.*' => 'exists:barangays,id',
        ]);

        DB::transaction(function () use ($validated, $districtId) {
            foreach ($validated['inspector_ids'] as $inspectorId) {
                if ($validated['assignment_type'] === 'district') {
                    // Assign to entire district
                    InspectorDistrictAssignment::updateOrCreate(
                        [
                            'inspector_id' => $inspectorId,
                            'district_id' => $districtId,
                        ],
                        [
                            'assigned_by' => Auth::id(),
                            'status' => 'active',
                        ]
                    );
                } else {
                    // Assign to specific barangays
                    if (!empty($validated['barangay_ids'])) {
                        foreach ($validated['barangay_ids'] as $barangayId) {
                            InspectorBarangayAssignment::updateOrCreate(
                                [
                                    'inspector_id' => $inspectorId,
                                    'barangay_id' => $barangayId,
                                ],
                                [
                                    'assigned_by' => Auth::id(),
                                    'status' => 'active',
                                ]
                            );
                        }
                    }
                }

                // Send notification to inspector
                $inspector = User::find($inspectorId);
                $district = District::find($districtId);
                
                NotificationController::createNotification(
                    $inspectorId,
                    'New Assignment',
                    "You have been assigned to {$district->name} district.",
                    'info',
                    $districtId,
                    'district',
                    route('inspector.dashboard')
                );
            }
        });

        return redirect()->route('admin.inspector-assignments.index')
            ->with('success', 'Inspector(s) assigned successfully.');
    }

    /**
     * Remove inspector from district
     */
    public function removeFromDistrict(Request $request)
    {
        $validated = $request->validate([
            'assignment_id' => 'required|exists:inspector_district_assignments,id',
            'assignment_type' => 'required|in:district,barangay',
        ]);

        if ($validated['assignment_type'] === 'district') {
            $assignment = InspectorDistrictAssignment::findOrFail($validated['assignment_id']);
            $assignment->update(['status' => 'inactive']);
        } else {
            $assignment = InspectorBarangayAssignment::findOrFail($validated['assignment_id']);
            $assignment->update(['status' => 'inactive']);
        }

        return back()->with('success', 'Inspector assignment removed successfully.');
    }

    /**
     * View district details
     */
    public function viewDistrict($districtId)
    {
        $district = District::with([
            'barangays',
            'inspectorAssignments.inspector',
            'businesses.inspections'
        ])->findOrFail($districtId);

        // Get district statistics
        $stats = [
            'total_barangays' => $district->barangays->count(),
            'total_businesses' => $district->businesses->count(),
            'active_businesses' => $district->businesses->where('status', 'active')->count(),
            'assigned_inspectors' => $district->inspectorAssignments->where('status', 'active')->count(),
            'total_inspections' => $district->businesses->sum(function($business) {
                return $business->inspections->count();
            }),
            'completed_inspections' => $district->businesses->sum(function($business) {
                return $business->inspections->where('status', 'completed')->count();
            }),
        ];

        return view('admin.inspector-assignments.district', compact('district', 'stats'));
    }

    /**
     * View inspector details
     */
    public function viewInspector($inspectorId)
    {
        $inspector = User::where('id', $inspectorId)
            ->where('role', 'inspector')
            ->with([
                'districtAssignments.district',
                'barangayAssignments.barangay.district',
                'assignedInspections.business.barangay.district'
            ])
            ->firstOrFail();

        // Get inspector statistics
        $stats = [
            'district_assignments' => $inspector->districtAssignments->where('status', 'active')->count(),
            'barangay_assignments' => $inspector->barangayAssignments->where('status', 'active')->count(),
            'total_inspections' => $inspector->assignedInspections->count(),
            'pending_inspections' => $inspector->assignedInspections->where('status', 'scheduled')->count(),
            'completed_inspections' => $inspector->assignedInspections->where('status', 'completed')->count(),
            'average_score' => $inspector->assignedInspections->where('status', 'completed')->avg('score'),
        ];

        return view('admin.inspector-assignments.inspector', compact('inspector', 'stats'));
    }

    /**
     * Schedule inspection
     */
    public function scheduleInspection(Request $request)
    {
        $validated = $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'inspector_id' => 'required|exists:users,id',
            'scheduled_date' => 'required|date|after:today',
            'inspection_type' => 'required|in:routine,follow_up,complaint,initial',
            'priority' => 'required|in:low,medium,high,urgent',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check for conflicts
        $conflict = Inspection::where('inspector_id', $validated['inspector_id'])
            ->whereDate('scheduled_date', $validated['scheduled_date'])
            ->where('status', '!=', 'cancelled')
            ->exists();

        if ($conflict) {
            return back()->with('error', 'Inspector already has an inspection scheduled for this date.');
        }

        $inspection = Inspection::create([
            'business_id' => $validated['business_id'],
            'inspector_id' => $validated['inspector_id'],
            'assigned_by' => Auth::id(),
            'scheduled_date' => $validated['scheduled_date'],
            'inspection_type' => $validated['inspection_type'],
            'priority' => $validated['priority'],
            'notes' => $validated['notes'],
            'status' => 'scheduled',
        ]);

        // Send notifications
        $business = Business::find($validated['business_id']);
        $inspector = User::find($validated['inspector_id']);

        // Notify inspector
        NotificationController::createNotification(
            $validated['inspector_id'],
            'New Inspection Assignment',
            "You have been assigned to inspect {$business->name} on " . Carbon::parse($validated['scheduled_date'])->format('M j, Y'),
            'inspection',
            $inspection->id,
            'inspection',
            route('inspector.inspections.show', $inspection)
        );

        // Notify business owner
        NotificationController::createNotification(
            $business->owner_id,
            'Inspection Scheduled',
            "An inspection has been scheduled for {$business->name} on " . Carbon::parse($validated['scheduled_date'])->format('M j, Y'),
            'inspection',
            $inspection->id,
            'inspection',
            route('business-owner.inspections.show', $inspection)
        );

        return back()->with('success', 'Inspection scheduled successfully.');
    }

    /**
     * Bulk schedule inspections
     */
    public function bulkScheduleInspections(Request $request)
    {
        $validated = $request->validate([
            'business_ids' => 'required|array',
            'business_ids.*' => 'exists:businesses,id',
            'inspector_id' => 'required|exists:users,id',
            'start_date' => 'required|date|after:today',
            'inspection_type' => 'required|in:routine,follow_up,complaint,initial',
            'priority' => 'required|in:low,medium,high,urgent',
        ]);

        $scheduledCount = 0;
        $startDate = Carbon::parse($validated['start_date']);

        foreach ($validated['business_ids'] as $index => $businessId) {
            $scheduledDate = $startDate->copy()->addDays($index);

            // Check for conflicts
            $conflict = Inspection::where('inspector_id', $validated['inspector_id'])
                ->whereDate('scheduled_date', $scheduledDate)
                ->where('status', '!=', 'cancelled')
                ->exists();

            if (!$conflict) {
                Inspection::create([
                    'business_id' => $businessId,
                    'inspector_id' => $validated['inspector_id'],
                    'assigned_by' => Auth::id(),
                    'scheduled_date' => $scheduledDate,
                    'inspection_type' => $validated['inspection_type'],
                    'priority' => $validated['priority'],
                    'status' => 'scheduled',
                ]);

                $scheduledCount++;
            }
        }

        return back()->with('success', "{$scheduledCount} inspections scheduled successfully.");
    }

    /**
     * Schedule barangay inspections
     */
    public function scheduleBarangayInspections(Request $request)
    {
        $validated = $request->validate([
            'barangay_id' => 'required|exists:barangays,id',
            'inspector_id' => 'required|exists:users,id',
            'start_date' => 'required|date|after:today',
            'inspection_type' => 'required|in:routine,follow_up,complaint,initial',
            'priority' => 'required|in:low,medium,high,urgent',
        ]);

        $businesses = Business::where('barangay_id', $validated['barangay_id'])
            ->where('status', 'active')
            ->get();

        $scheduledCount = 0;
        $startDate = Carbon::parse($validated['start_date']);

        foreach ($businesses as $index => $business) {
            $scheduledDate = $startDate->copy()->addDays($index);

            // Check for conflicts
            $conflict = Inspection::where('inspector_id', $validated['inspector_id'])
                ->whereDate('scheduled_date', $scheduledDate)
                ->where('status', '!=', 'cancelled')
                ->exists();

            if (!$conflict) {
                Inspection::create([
                    'business_id' => $business->id,
                    'inspector_id' => $validated['inspector_id'],
                    'assigned_by' => Auth::id(),
                    'scheduled_date' => $scheduledDate,
                    'inspection_type' => $validated['inspection_type'],
                    'priority' => $validated['priority'],
                    'status' => 'scheduled',
                ]);

                $scheduledCount++;
            }
        }

        return back()->with('success', "{$scheduledCount} barangay inspections scheduled successfully.");
    }

    /**
     * Get barangay businesses (API)
     */
    public function getBarangayBusinesses($barangayId)
    {
        $businesses = Business::where('barangay_id', $barangayId)
            ->where('status', 'active')
            ->select('id', 'name', 'address', 'contact_number')
            ->get();

        return response()->json($businesses);
    }

    /**
     * Check scheduling conflicts (API)
     */
    public function checkConflicts(Request $request)
    {
        $validated = $request->validate([
            'inspector_id' => 'required|exists:users,id',
            'date' => 'required|date',
        ]);

        $conflict = Inspection::where('inspector_id', $validated['inspector_id'])
            ->whereDate('scheduled_date', $validated['date'])
            ->where('status', '!=', 'cancelled')
            ->exists();

        return response()->json(['has_conflict' => $conflict]);
    }

    /**
     * Display inspection assignments
     */
    public function inspectionAssignments()
    {
        $inspections = Inspection::with(['business.barangay.district', 'inspector', 'assignedBy'])
            ->latest('scheduled_date')
            ->paginate(20);

        $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();
        $districts = District::all();

        return view('admin.inspector-assignments.inspections', compact('inspections', 'inspectors', 'districts'));
    }

    /**
     * Reassign inspection
     */
    public function reassignInspection(Request $request)
    {
        $validated = $request->validate([
            'inspection_id' => 'required|exists:inspections,id',
            'new_inspector_id' => 'required|exists:users,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $inspection = Inspection::findOrFail($validated['inspection_id']);
        $oldInspector = $inspection->inspector;
        $newInspector = User::find($validated['new_inspector_id']);

        $inspection->update([
            'inspector_id' => $validated['new_inspector_id'],
            'reassignment_reason' => $validated['reason'],
            'reassigned_by' => Auth::id(),
            'reassigned_at' => now(),
        ]);

        // Send notifications
        NotificationController::createNotification(
            $validated['new_inspector_id'],
            'Inspection Reassigned',
            "You have been assigned to inspect {$inspection->business->name}.",
            'inspection',
            $inspection->id,
            'inspection',
            route('inspector.inspections.show', $inspection)
        );

        if ($oldInspector) {
            NotificationController::createNotification(
                $oldInspector->id,
                'Inspection Reassigned',
                "Your inspection of {$inspection->business->name} has been reassigned.",
                'warning',
                $inspection->id,
                'inspection'
            );
        }

        return back()->with('success', 'Inspection reassigned successfully.');
    }

    /**
     * Get assignment overview
     */
    private function getAssignmentOverview()
    {
        return [
            'total_inspectors' => User::where('role', 'inspector')->where('status', 'active')->count(),
            'assigned_inspectors' => User::where('role', 'inspector')
                ->where('status', 'active')
                ->whereHas('districtAssignments')
                ->count(),
            'total_districts' => District::count(),
            'covered_districts' => District::whereHas('inspectorAssignments')->count(),
            'total_barangays' => Barangay::count(),
            'covered_barangays' => Barangay::whereHas('inspectorAssignments')->count(),
            'pending_inspections' => Inspection::where('status', 'scheduled')->count(),
            'overdue_inspections' => Inspection::where('status', 'scheduled')
                ->where('scheduled_date', '<', now())
                ->count(),
        ];
    }
}
