<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt"></i> Inspection Report
        </h1>
        <div>
            <a href="<?= BASE_URL ?>business/inspections" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Inspections
            </a>
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Print Report
            </button>
        </div>
    </div>

    <!-- Business Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-building"></i> Business Information
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= htmlspecialchars($business['name']) ?></h5>
                    <p class="text-muted mb-1">
                        <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($business['address']) ?>
                    </p>
                    <p class="text-muted mb-1">
                        <i class="fas fa-industry"></i> Type: <?= htmlspecialchars($business['business_type'] ?? 'N/A') ?>
                    </p>
                    <p class="mb-1">
                        <strong>Inspector:</strong> <?= htmlspecialchars($inspection['inspector_name']) ?>
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1">
                        <strong>Inspection Date:</strong> <?= date('M d, Y g:i A', strtotime($inspection['scheduled_date'])) ?>
                    </p>
                    <p class="mb-1">
                        <strong>Inspection Type:</strong> <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'])) ?>
                    </p>
                    <p class="mb-1">
                        <strong>Report Generated:</strong> <?= date('M d, Y g:i A') ?>
                    </p>
                    <p class="mb-1">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                            <?= ucfirst($inspection['status']) ?>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Compliance Score -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>">
                <i class="fas fa-chart-pie"></i> Overall Compliance Score
            </h6>
        </div>
        <div class="card-body">
            <div class="row text-center">
                <div class="col-md-3">
                    <h2 class="text-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>">
                        <?= $inspection_score['percentage'] ?>%
                    </h2>
                    <p class="text-muted">Overall Score</p>
                </div>
                <div class="col-md-3">
                    <h2 class="text-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>">
                        <?= $inspection_score['grade'] ?>
                    </h2>
                    <p class="text-muted">Grade</p>
                </div>
                <div class="col-md-3">
                    <h2 class="text-info">
                        <?= $inspection_score['score'] ?>/<?= $inspection_score['total_points'] ?>
                    </h2>
                    <p class="text-muted">Points Earned</p>
                </div>
                <div class="col-md-3">
                    <h2 class="text-<?= $inspection_score['critical_violations'] > 0 ? 'danger' : 'success' ?>">
                        <?= $inspection_score['critical_violations'] ?>
                    </h2>
                    <p class="text-muted">Critical Violations</p>
                </div>
            </div>
            
            <div class="progress mt-3" style="height: 25px;">
                <div class="progress-bar bg-<?= $inspection_score['grade'] === 'A' ? 'success' : (in_array($inspection_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>" 
                     role="progressbar" 
                     style="width: <?= $inspection_score['percentage'] ?>%">
                    <?= $inspection_score['percentage'] ?>% - Grade <?= $inspection_score['grade'] ?>
                </div>
            </div>
            
            <div class="mt-3">
                <strong>Compliance Status:</strong> 
                <span class="badge bg-<?= $inspection_score['status'] === 'passed' ? 'success' : 'danger' ?> fs-6">
                    <?= ucfirst($inspection_score['status']) ?>
                </span>
                
                <?php if ($inspection_score['critical_violations'] > 0): ?>
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Critical Violations Detected:</strong> Your business has <?= $inspection_score['critical_violations'] ?> critical violation(s) that must be addressed immediately.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Detailed Results by Category -->
    <?php foreach ($responses_by_category as $categoryName => $responses): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list-check"></i> <?= htmlspecialchars($categoryName) ?>
                    <span class="badge bg-secondary ms-2"><?= count($responses) ?> items</span>
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Requirement</th>
                                <th>Status</th>
                                <th>Score</th>
                                <th>Notes</th>
                                <th>Corrective Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($responses as $response): ?>
                                <tr class="<?= $response['compliance_status'] === 'non_compliant' ? 'table-danger' : ($response['compliance_status'] === 'needs_improvement' ? 'table-warning' : ($response['compliance_status'] === 'compliant' ? 'table-success' : '')) ?>">
                                    <td>
                                        <strong><?= htmlspecialchars($response['item_name']) ?></strong>
                                        <?php if ($response['is_critical']): ?>
                                            <span class="badge bg-danger ms-1">CRITICAL</span>
                                        <?php endif; ?>
                                        <br><small class="text-muted"><?= htmlspecialchars($response['item_code']) ?></small>
                                    </td>
                                    <td>
                                        <small><?= htmlspecialchars($response['compliance_requirement']) ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $statusIcons = [
                                            'compliant' => ['✅', 'success'],
                                            'needs_improvement' => ['⚠️', 'warning'],
                                            'non_compliant' => ['❌', 'danger'],
                                            'not_applicable' => ['➖', 'secondary']
                                        ];
                                        $icon = $statusIcons[$response['compliance_status']][0] ?? '❓';
                                        $badgeClass = $statusIcons[$response['compliance_status']][1] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $badgeClass ?>">
                                            <?= $icon ?> <?= ucfirst(str_replace('_', ' ', $response['compliance_status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <strong><?= $response['score'] ?>/<?= $response['points'] ?></strong>
                                    </td>
                                    <td>
                                        <?= $response['notes'] ? htmlspecialchars($response['notes']) : '<em class="text-muted">No notes</em>' ?>
                                    </td>
                                    <td>
                                        <?php if ($response['corrective_action']): ?>
                                            <div>
                                                <strong>Action:</strong> <?= htmlspecialchars($response['corrective_action']) ?>
                                                <?php if ($response['deadline']): ?>
                                                    <br><strong>Deadline:</strong> 
                                                    <span class="badge bg-<?= strtotime($response['deadline']) < time() ? 'danger' : 'info' ?>">
                                                        <?= date('M d, Y', strtotime($response['deadline'])) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <em class="text-muted">No action required</em>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Action Plan -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-tasks"></i> Action Plan
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Immediate Actions Required:</h6>
                    <ul>
                        <?php if ($inspection_score['critical_violations'] > 0): ?>
                            <li class="text-danger"><strong>Address all critical violations within 30 days</strong></li>
                        <?php endif; ?>
                        <?php
                        $needsImprovement = array_filter($checklist_responses, function($r) {
                            return $r['compliance_status'] === 'needs_improvement';
                        });
                        $nonCompliant = array_filter($checklist_responses, function($r) {
                            return $r['compliance_status'] === 'non_compliant';
                        });
                        ?>
                        <?php if (count($nonCompliant) > 0): ?>
                            <li>Correct <?= count($nonCompliant) ?> non-compliant item(s)</li>
                        <?php endif; ?>
                        <?php if (count($needsImprovement) > 0): ?>
                            <li>Improve <?= count($needsImprovement) ?> item(s) that need attention</li>
                        <?php endif; ?>
                        <li>Document all corrective actions taken</li>
                        <li>Train staff on compliance requirements</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Timeline:</h6>
                    <p class="mb-1">
                        <strong>Compliance Deadline:</strong> 
                        <?= $inspection_score['critical_violations'] > 0 ? '30 days' : '60 days' ?>
                    </p>
                    <p class="mb-1">
                        <strong>Next Inspection:</strong> 
                        <?= $inspection_score['percentage'] >= 80 ? '6 months' : ($inspection_score['percentage'] >= 60 ? '3 months' : '1 month') ?>
                    </p>
                    <p class="mb-1">
                        <strong>Priority Level:</strong> 
                        <span class="badge bg-<?= $inspection_score['percentage'] >= 80 ? 'success' : ($inspection_score['percentage'] >= 60 ? 'warning' : 'danger') ?>">
                            <?= $inspection_score['percentage'] >= 80 ? 'Low' : ($inspection_score['percentage'] >= 60 ? 'Medium' : 'High') ?>
                        </span>
                    </p>
                    
                    <div class="mt-3">
                        <h6>Contact Information:</h6>
                        <p class="mb-1"><strong>Bacoor City Health Office</strong></p>
                        <p class="mb-1">📞 (046) 417-2000</p>
                        <p class="mb-1">📧 <EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .navbar, .sidebar, .footer {
        display: none !important;
    }
    .container-fluid {
        margin: 0 !important;
        padding: 0 !important;
    }
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
</style>

<?php $this->endSection(); ?>
