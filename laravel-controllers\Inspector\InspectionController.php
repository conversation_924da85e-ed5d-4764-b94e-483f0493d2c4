<?php

namespace App\Http\Controllers\Inspector;

use App\Http\Controllers\Controller;
use App\Models\Inspection;
use App\Models\InspectionChecklistItem;
use App\Models\InspectionChecklistResponse;
use App\Models\InspectionChecklistCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class InspectionController extends Controller
{
    /**
     * Display a listing of inspector's inspections
     */
    public function index(Request $request)
    {
        $inspector = Auth::user();
        $query = Inspection::with(['business.barangay.district', 'assignedBy'])
            ->where('inspector_id', $inspector->id);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('inspection_type')) {
            $query->where('inspection_type', $request->inspection_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('business', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('owner_name', 'like', "%{$search}%");
            });
        }

        $inspections = $query->latest('scheduled_date')->paginate(15);

        return view('inspector.inspections.index', compact('inspections'));
    }

    /**
     * Display the specified inspection
     */
    public function show(Inspection $inspection)
    {
        // Check if inspector is assigned to this inspection
        if ($inspection->inspector_id !== Auth::id()) {
            abort(403, 'You are not authorized to view this inspection.');
        }

        $inspection->load([
            'business.barangay.district',
            'business.category',
            'assignedBy',
            'checklistResponses.checklistItem.category'
        ]);

        return view('inspector.inspections.show', compact('inspection'));
    }

    /**
     * Show inspection checklist
     */
    public function checklist(Inspection $inspection)
    {
        // Check if inspector is assigned to this inspection
        if ($inspection->inspector_id !== Auth::id()) {
            abort(403, 'You are not authorized to access this inspection.');
        }

        // Check if inspection is in correct status
        if (!in_array($inspection->status, ['scheduled', 'confirmed', 'in_progress'])) {
            return redirect()->route('inspector.inspections.show', $inspection)
                ->with('error', 'This inspection cannot be modified.');
        }

        // Get checklist categories with items
        $categories = InspectionChecklistCategory::with(['activeChecklistItems'])
            ->active()
            ->ordered()
            ->get();

        // Get existing responses
        $existingResponses = InspectionChecklistResponse::where('inspection_id', $inspection->id)
            ->get()
            ->keyBy('checklist_item_id');

        // Get business evidence for checklist items
        $businessEvidence = $inspection->business->checklistEvidence()
            ->with('checklistItem')
            ->get()
            ->groupBy('checklist_item_id');

        return view('inspector.inspections.checklist', compact(
            'inspection',
            'categories',
            'existingResponses',
            'businessEvidence'
        ));
    }

    /**
     * Submit inspection checklist
     */
    public function submitChecklist(Request $request, Inspection $inspection)
    {
        // Check if inspector is assigned to this inspection
        if ($inspection->inspector_id !== Auth::id()) {
            abort(403, 'You are not authorized to modify this inspection.');
        }

        $validated = $request->validate([
            'responses' => 'required|array',
            'responses.*.checklist_item_id' => 'required|exists:inspection_checklist_items,id',
            'responses.*.compliance_status' => 'required|in:compliant,needs_improvement,non_compliant,not_applicable',
            'responses.*.notes' => 'nullable|string',
            'responses.*.corrective_action' => 'nullable|string',
            'responses.*.deadline' => 'nullable|date|after:today',
            'photo_evidence.*' => 'nullable|image|max:5120', // 5MB max
        ]);

        DB::transaction(function () use ($validated, $inspection, $request) {
            $totalScore = 0;
            $maxScore = 0;

            foreach ($validated['responses'] as $responseData) {
                $checklistItem = InspectionChecklistItem::find($responseData['checklist_item_id']);
                
                // Calculate score based on compliance status
                $score = match($responseData['compliance_status']) {
                    'compliant' => $checklistItem->points,
                    'needs_improvement' => round($checklistItem->points * 0.7),
                    'non_compliant' => 0,
                    'not_applicable' => $checklistItem->points, // Don't penalize for N/A
                };

                if ($responseData['compliance_status'] !== 'not_applicable') {
                    $maxScore += $checklistItem->points;
                }

                $totalScore += $score;

                // Handle photo evidence upload
                $photoPath = null;
                if ($request->hasFile("photo_evidence.{$responseData['checklist_item_id']}")) {
                    $file = $request->file("photo_evidence.{$responseData['checklist_item_id']}");
                    $photoPath = $file->store('inspection-evidence', 'public');
                }

                // Update or create response
                InspectionChecklistResponse::updateOrCreate(
                    [
                        'inspection_id' => $inspection->id,
                        'checklist_item_id' => $responseData['checklist_item_id'],
                    ],
                    [
                        'inspector_id' => Auth::id(),
                        'compliance_status' => $responseData['compliance_status'],
                        'score' => $score,
                        'notes' => $responseData['notes'] ?? null,
                        'photo_evidence' => $photoPath,
                        'corrective_action' => $responseData['corrective_action'] ?? null,
                        'deadline' => $responseData['deadline'] ?? null,
                    ]
                );
            }

            // Update inspection status and score
            $inspection->update([
                'status' => 'in_progress',
                'score' => $totalScore,
            ]);
        });

        return redirect()->route('inspector.inspections.checklist', $inspection)
            ->with('success', 'Checklist responses saved successfully.');
    }

    /**
     * Complete inspection
     */
    public function complete(Request $request, Inspection $inspection)
    {
        // Check if inspector is assigned to this inspection
        if ($inspection->inspector_id !== Auth::id()) {
            abort(403, 'You are not authorized to complete this inspection.');
        }

        $validated = $request->validate([
            'findings' => 'required|string',
            'recommendations' => 'required|string',
            'inspector_notes' => 'nullable|string',
            'compliance_rating' => 'required|in:A,B,C,D,F',
        ]);

        // Check if all checklist items have responses
        $totalItems = InspectionChecklistItem::active()->count();
        $responseCount = InspectionChecklistResponse::where('inspection_id', $inspection->id)->count();

        if ($responseCount < $totalItems) {
            return back()->with('error', 'Please complete all checklist items before finishing the inspection.');
        }

        // Update inspection
        $inspection->update([
            'status' => 'completed',
            'completed_date' => now(),
            'findings' => $validated['findings'],
            'recommendations' => $validated['recommendations'],
            'inspector_notes' => $validated['inspector_notes'],
            'compliance_rating' => $validated['compliance_rating'],
            'verification_status' => 'pending',
        ]);

        // Update business compliance status based on rating
        $complianceStatus = match($validated['compliance_rating']) {
            'A', 'B' => 'compliant',
            'C' => 'pending_review',
            'D', 'F' => 'non_compliant',
        };

        $inspection->business->update([
            'compliance_status' => $complianceStatus,
            'last_inspection_date' => now(),
        ]);

        // TODO: Send notification to admin about completed inspection
        // TODO: Send notification to business owner about inspection completion

        return redirect()->route('inspector.inspections.show', $inspection)
            ->with('success', 'Inspection completed successfully and submitted for verification.');
    }

    /**
     * Show inspection report
     */
    public function report(Inspection $inspection)
    {
        // Check if inspector is assigned to this inspection
        if ($inspection->inspector_id !== Auth::id()) {
            abort(403, 'You are not authorized to view this report.');
        }

        if ($inspection->status !== 'completed') {
            return redirect()->route('inspector.inspections.show', $inspection)
                ->with('error', 'Report is only available for completed inspections.');
        }

        $inspection->load([
            'business.barangay.district',
            'business.category',
            'checklistResponses.checklistItem.category',
            'assignedBy',
            'verifiedBy'
        ]);

        // Group responses by category
        $responsesByCategory = $inspection->checklistResponses
            ->groupBy('checklistItem.category.name');

        // Calculate statistics
        $stats = [
            'total_items' => $inspection->checklistResponses->count(),
            'compliant' => $inspection->checklistResponses->where('compliance_status', 'compliant')->count(),
            'needs_improvement' => $inspection->checklistResponses->where('compliance_status', 'needs_improvement')->count(),
            'non_compliant' => $inspection->checklistResponses->where('compliance_status', 'non_compliant')->count(),
            'not_applicable' => $inspection->checklistResponses->where('compliance_status', 'not_applicable')->count(),
            'critical_violations' => $inspection->getCriticalViolationsCount(),
            'score_percentage' => $inspection->getScorePercentage(),
        ];

        return view('inspector.inspections.report', compact('inspection', 'responsesByCategory', 'stats'));
    }
}
