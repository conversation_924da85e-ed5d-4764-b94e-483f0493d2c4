<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    /**
     * Upload evidence file for business checklist
     */
    public function uploadEvidenceFile(UploadedFile $file, string $businessId, string $checklistItemId): array
    {
        // Validate file
        $this->validateFile($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        
        // Determine storage path
        $path = "evidence/{$businessId}/{$checklistItemId}";
        
        // Store file
        $filePath = $file->storeAs($path, $filename, 'public');
        
        // Process image if it's an image file
        if ($this->isImage($file)) {
            $this->processImage(storage_path("app/public/{$filePath}"));
        }

        return [
            'file_path' => $filePath,
            'file_name' => $file->getClientOriginalName(),
            'file_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'stored_filename' => $filename,
        ];
    }

    /**
     * Upload inspection photo evidence
     */
    public function uploadInspectionPhoto(UploadedFile $file, string $inspectionId, string $checklistItemId): array
    {
        // Validate file
        $this->validateFile($file, ['image']);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        
        // Determine storage path
        $path = "inspections/{$inspectionId}/photos";
        
        // Store file
        $filePath = $file->storeAs($path, $filename, 'public');
        
        // Process image
        $this->processImage(storage_path("app/public/{$filePath}"));

        return [
            'file_path' => $filePath,
            'file_name' => $file->getClientOriginalName(),
            'file_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'stored_filename' => $filename,
        ];
    }

    /**
     * Upload chat attachment
     */
    public function uploadChatAttachment(UploadedFile $file, string $chatRoomId): array
    {
        // Validate file
        $this->validateFile($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        
        // Determine storage path
        $path = "chat/{$chatRoomId}/attachments";
        
        // Store file
        $filePath = $file->storeAs($path, $filename, 'public');
        
        // Process image if it's an image file
        if ($this->isImage($file)) {
            $this->processImage(storage_path("app/public/{$filePath}"));
        }

        return [
            'file_path' => $filePath,
            'file_name' => $file->getClientOriginalName(),
            'file_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'stored_filename' => $filename,
        ];
    }

    /**
     * Upload website images (admin settings)
     */
    public function uploadWebsiteImage(UploadedFile $file, string $type): array
    {
        // Validate image file
        $this->validateFile($file, ['image']);

        // Generate unique filename
        $filename = $type . '_' . time() . '.' . $file->getClientOriginalExtension();
        
        // Determine storage path
        $path = "website/images";
        
        // Store file
        $filePath = $file->storeAs($path, $filename, 'public');
        
        // Process image with specific dimensions based on type
        $this->processWebsiteImage(storage_path("app/public/{$filePath}"), $type);

        return [
            'file_path' => $filePath,
            'file_name' => $file->getClientOriginalName(),
            'file_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'stored_filename' => $filename,
            'url' => Storage::url($filePath),
        ];
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file, array $allowedTypes = ['image', 'document']): void
    {
        // Check file size (max 10MB)
        if ($file->getSize() > 10 * 1024 * 1024) {
            throw new \InvalidArgumentException('File size cannot exceed 10MB.');
        }

        // Check file type
        $mimeType = $file->getMimeType();
        $isValid = false;

        foreach ($allowedTypes as $type) {
            if ($type === 'image' && str_starts_with($mimeType, 'image/')) {
                $allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (in_array($mimeType, $allowedImageTypes)) {
                    $isValid = true;
                    break;
                }
            } elseif ($type === 'document') {
                $allowedDocTypes = [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ];
                if (in_array($mimeType, $allowedDocTypes)) {
                    $isValid = true;
                    break;
                }
            }
        }

        if (!$isValid) {
            throw new \InvalidArgumentException('Invalid file type. Only images and documents are allowed.');
        }

        // Check for malicious files
        $extension = strtolower($file->getClientOriginalExtension());
        $dangerousExtensions = ['php', 'exe', 'bat', 'cmd', 'scr', 'pif', 'vbs', 'js'];
        
        if (in_array($extension, $dangerousExtensions)) {
            throw new \InvalidArgumentException('File type not allowed for security reasons.');
        }
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);
        
        return "{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Check if file is an image
     */
    private function isImage(UploadedFile $file): bool
    {
        return str_starts_with($file->getMimeType(), 'image/');
    }

    /**
     * Process uploaded image (resize, optimize)
     */
    private function processImage(string $filePath): void
    {
        try {
            $image = Image::make($filePath);
            
            // Resize if too large (max 1920x1080)
            if ($image->width() > 1920 || $image->height() > 1080) {
                $image->resize(1920, 1080, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }
            
            // Optimize quality
            $image->save($filePath, 85);
            
        } catch (\Exception $e) {
            // Log error but don't fail the upload
            \Log::warning('Image processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Process website images with specific dimensions
     */
    private function processWebsiteImage(string $filePath, string $type): void
    {
        try {
            $image = Image::make($filePath);
            
            // Define dimensions for different image types
            $dimensions = [
                'logo' => [200, 80],
                'banner' => [1200, 400],
                'hero' => [1920, 600],
                'thumbnail' => [300, 200],
            ];
            
            if (isset($dimensions[$type])) {
                [$width, $height] = $dimensions[$type];
                $image->resize($width, $height, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }
            
            // Optimize quality
            $image->save($filePath, 90);
            
        } catch (\Exception $e) {
            \Log::warning('Website image processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Delete file from storage
     */
    public function deleteFile(string $filePath): bool
    {
        try {
            return Storage::disk('public')->delete($filePath);
        } catch (\Exception $e) {
            \Log::error('File deletion failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get file URL
     */
    public function getFileUrl(string $filePath): string
    {
        return Storage::url($filePath);
    }

    /**
     * Get file size in human readable format
     */
    public function getFormattedFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Create thumbnail for image
     */
    public function createThumbnail(string $filePath, int $width = 150, int $height = 150): string
    {
        try {
            $image = Image::make(storage_path("app/public/{$filePath}"));
            
            // Create thumbnail
            $thumbnail = $image->fit($width, $height);
            
            // Generate thumbnail path
            $pathInfo = pathinfo($filePath);
            $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
            
            // Save thumbnail
            $thumbnail->save(storage_path("app/public/{$thumbnailPath}"), 80);
            
            return $thumbnailPath;
            
        } catch (\Exception $e) {
            \Log::warning('Thumbnail creation failed: ' . $e->getMessage());
            return $filePath; // Return original if thumbnail creation fails
        }
    }

    /**
     * Validate and clean filename
     */
    public function sanitizeFilename(string $filename): string
    {
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Limit length
        if (strlen($filename) > 100) {
            $pathInfo = pathinfo($filename);
            $name = substr($pathInfo['filename'], 0, 90);
            $filename = $name . '.' . $pathInfo['extension'];
        }
        
        return $filename;
    }

    /**
     * Get storage statistics
     */
    public function getStorageStats(): array
    {
        $publicPath = storage_path('app/public');
        
        return [
            'total_files' => $this->countFiles($publicPath),
            'total_size' => $this->getDirectorySize($publicPath),
            'formatted_size' => $this->getFormattedFileSize($this->getDirectorySize($publicPath)),
        ];
    }

    /**
     * Count files in directory recursively
     */
    private function countFiles(string $directory): int
    {
        if (!is_dir($directory)) {
            return 0;
        }
        
        $count = 0;
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
            }
        }
        
        return $count;
    }

    /**
     * Get directory size in bytes
     */
    private function getDirectorySize(string $directory): int
    {
        if (!is_dir($directory)) {
            return 0;
        }
        
        $size = 0;
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }
}
