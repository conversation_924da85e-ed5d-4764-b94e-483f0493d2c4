<?php

use App\Http\Controllers\Api\ApiController;
use App\Http\Controllers\ChatController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::post('/login', [ApiController::class, 'login']);
Route::post('/logout', [ApiController::class, 'logout'])->middleware('auth:sanctum');

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User info
    Route::get('/me', [ApiController::class, 'me']);
    
    // Dashboard data
    Route::get('/dashboard', [ApiController::class, 'getDashboardData']);
    
    // Inspections
    Route::get('/inspections', [ApiController::class, 'getInspections']);
    Route::get('/inspections/{id}', [ApiController::class, 'getInspection']);
    Route::get('/inspections/{id}/checklist', [ApiController::class, 'getChecklistItems']);
    Route::post('/inspections/{id}/checklist', [ApiController::class, 'submitChecklistResponse']);
    
    // Evidence
    Route::post('/evidence/upload', [ApiController::class, 'uploadEvidence']);
    Route::get('/evidence', [ApiController::class, 'getBusinessEvidence']);
    
    // Chat
    Route::get('/chat/rooms', [ApiController::class, 'getChatRooms']);
    Route::get('/chat/rooms/{id}/messages', [ApiController::class, 'getChatMessages']);
    Route::post('/chat/rooms/{id}/messages', [ChatController::class, 'sendMessage']);
    Route::post('/chat/rooms/{id}/typing', [ChatController::class, 'typing']);
    Route::post('/chat/rooms/{id}/read', [ChatController::class, 'markAsRead']);
    Route::get('/chat/rooms/{id}/participants', [ChatController::class, 'getOnlineUsers']);
    
    // File uploads
    Route::post('/upload/evidence', [ApiController::class, 'uploadEvidence']);
    Route::post('/upload/inspection-photo', [ApiController::class, 'uploadInspectionPhoto']);
    
    // Notifications
    Route::get('/notifications', function (Request $request) {
        return $request->user()->notifications()->paginate(20);
    });
    Route::post('/notifications/{id}/read', function (Request $request, $id) {
        $request->user()->notifications()->where('id', $id)->update(['read_at' => now()]);
        return response()->json(['success' => true]);
    });
});

// Public API routes (if needed)
Route::get('/system/status', function () {
    return response()->json([
        'status' => 'online',
        'version' => '2.0.0',
        'timestamp' => now()->toISOString(),
    ]);
});

Route::get('/system/health', function () {
    return response()->json([
        'database' => 'connected',
        'cache' => 'working',
        'storage' => 'accessible',
        'timestamp' => now()->toISOString(),
    ]);
});
