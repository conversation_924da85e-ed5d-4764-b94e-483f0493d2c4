-- Database cleanup script
-- Truncates all tables except users, districts, barangays, and business_categories
-- Also removes duplicate categories

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Truncate inspection-related tables
TRUNCATE TABLE inspection_checklist_items;
TRUNCATE TABLE inspection_checklist_responses;
TRUNCATE TABLE inspections;
TRUNCATE TABLE inspection_assignments;
TRUNCATE TABLE inspector_district_assignments;
TRUNCATE TABLE inspector_barangay_assignments;

-- Truncate business-related tables (except categories)
TRUNCATE TABLE businesses;
TRUNCATE TABLE business_checklist_evidence;
TRUNCATE TABLE business_inspection_queue;
TRUNCATE TABLE compliance_evidences;

-- Truncate communication tables
TRUNCATE TABLE chat_messages;
TRUNCATE TABLE chat_rooms;
TRUNCATE TABLE chatbot_conversations;
TRUNCATE TABLE chatbot_escalations;
TRUNCATE TABLE notifications;

-- Truncate other data tables
TRUNCATE TABLE announcements;
TRUNCATE TABLE contact_messages;
TRUNCATE TABLE homepage_announcements;

-- Clean up duplicate categories
-- First, let's see what categories we have
SELECT id, name, COUNT(*) as count
FROM business_categories
GROUP BY name
HAVING COUNT(*) > 1;

-- Remove duplicate categories, keeping only the first occurrence of each name
DELETE c1 FROM business_categories c1
INNER JOIN business_categories c2
WHERE c1.id > c2.id AND c1.name = c2.name;

-- Insert clean category data
DELETE FROM business_categories;
INSERT INTO business_categories (id, name, description, created_at) VALUES
(UUID(), 'Construction', 'Construction and building-related businesses', NOW()),
(UUID(), 'Food & Beverage', 'Restaurants, cafes, food processing, and beverage businesses', NOW()),
(UUID(), 'Manufacturing', 'Manufacturing and production facilities', NOW()),
(UUID(), 'Retail', 'Retail stores and commercial establishments', NOW()),
(UUID(), 'Healthcare', 'Medical facilities, clinics, and healthcare services', NOW()),
(UUID(), 'Education', 'Schools, training centers, and educational institutions', NOW()),
(UUID(), 'Transportation', 'Transportation and logistics companies', NOW()),
(UUID(), 'Technology', 'IT companies, software development, and tech services', NOW()),
(UUID(), 'Hospitality', 'Hotels, resorts, and accommodation services', NOW()),
(UUID(), 'Agriculture', 'Farming, livestock, and agricultural businesses', NOW()),
(UUID(), 'Financial Services', 'Banks, insurance, and financial institutions', NOW()),
(UUID(), 'Entertainment', 'Entertainment venues, gaming, and recreational facilities', NOW()),
(UUID(), 'Automotive', 'Car dealerships, repair shops, and automotive services', NOW()),
(UUID(), 'Beauty & Wellness', 'Salons, spas, gyms, and wellness centers', NOW()),
(UUID(), 'Professional Services', 'Legal, accounting, consulting, and other professional services', NOW());

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify the cleanup
SELECT 'Users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'Districts' as table_name, COUNT(*) as record_count FROM districts
UNION ALL
SELECT 'Barangays' as table_name, COUNT(*) as record_count FROM barangays
UNION ALL
SELECT 'Business Categories' as table_name, COUNT(*) as record_count FROM business_categories
UNION ALL
SELECT 'Businesses' as table_name, COUNT(*) as record_count FROM businesses
UNION ALL
SELECT 'Inspections' as table_name, COUNT(*) as record_count FROM inspections
UNION ALL
SELECT 'Chat Rooms' as table_name, COUNT(*) as record_count FROM chat_rooms;

-- Show clean categories
SELECT id, name, description FROM business_categories ORDER BY name;
