#!/usr/bin/env python3
"""
OHS Management System - Laravel File Transfer Script
This script copies all Laravel files to the proper directory structure
"""

import os
import shutil
import sys
from pathlib import Path

def create_directory_structure():
    """Create the Laravel directory structure"""
    directories = [
        'ohs-management-system/app/Http/Controllers/Admin',
        'ohs-management-system/app/Http/Controllers/Inspector', 
        'ohs-management-system/app/Http/Controllers/BusinessOwner',
        'ohs-management-system/app/Http/Controllers/Api',
        'ohs-management-system/app/Http/Middleware',
        'ohs-management-system/app/Http/Requests',
        'ohs-management-system/app/Models',
        'ohs-management-system/app/Services',
        'ohs-management-system/app/Events',
        'ohs-management-system/app/Exports',
        'ohs-management-system/database/migrations',
        'ohs-management-system/database/seeders',
        'ohs-management-system/database/factories',
        'ohs-management-system/resources/views/layouts',
        'ohs-management-system/resources/views/admin',
        'ohs-management-system/resources/views/inspector',
        'ohs-management-system/resources/views/business-owner',
        'ohs-management-system/resources/views/emails',
        'ohs-management-system/resources/views/reports',
        'ohs-management-system/resources/views/components',
        'ohs-management-system/routes',
        'ohs-management-system/tests/Feature',
        'ohs-management-system/tests/Unit',
        'ohs-management-system/config',
        'ohs-management-system/public/css',
        'ohs-management-system/public/js',
        'ohs-management-system/public/images',
        'ohs-management-system/storage/app/public',
        'ohs-management-system/storage/logs',
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def copy_files():
    """Copy all Laravel files to proper locations"""
    
    file_mappings = {
        # Migrations
        'laravel-migrations/': 'ohs-management-system/database/migrations/',
        
        # Models
        'laravel-models/': 'ohs-management-system/app/Models/',
        
        # Controllers
        'laravel-controllers/': 'ohs-management-system/app/Http/Controllers/',
        
        # Middleware
        'laravel-middleware/': 'ohs-management-system/app/Http/Middleware/',
        
        # Requests
        'laravel-requests/': 'ohs-management-system/app/Http/Requests/',
        
        # Services
        'laravel-services/': 'ohs-management-system/app/Services/',
        
        # Events
        'laravel-events/': 'ohs-management-system/app/Events/',
        
        # Views
        'laravel-views/': 'ohs-management-system/resources/views/',
        
        # API Controllers
        'laravel-api/': 'ohs-management-system/app/Http/Controllers/Api/',
        
        # Broadcasting
        'laravel-broadcasting/': 'ohs-management-system/app/Http/Controllers/',
        
        # Tests
        'laravel-tests/': 'ohs-management-system/tests/',
        
        # Migration Scripts
        'laravel-migration-scripts/': 'ohs-management-system/app/Services/',
    }
    
    for source_dir, dest_dir in file_mappings.items():
        if os.path.exists(source_dir):
            print(f"📁 Copying {source_dir} to {dest_dir}")
            
            # Create destination directory if it doesn't exist
            Path(dest_dir).mkdir(parents=True, exist_ok=True)
            
            # Copy all files from source to destination
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    source_file = os.path.join(root, file)
                    
                    # Calculate relative path
                    rel_path = os.path.relpath(source_file, source_dir)
                    dest_file = os.path.join(dest_dir, rel_path)
                    
                    # Create subdirectories if needed
                    os.makedirs(os.path.dirname(dest_file), exist_ok=True)
                    
                    # Copy file
                    shutil.copy2(source_file, dest_file)
                    print(f"  ✅ Copied: {rel_path}")
        else:
            print(f"⚠️  Source directory not found: {source_dir}")

def create_additional_files():
    """Create additional required Laravel files"""
    
    # Create artisan file
    artisan_content = '''#!/usr/bin/env php
<?php

define('LARAVEL_START', microtime(true));

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\\Contracts\\Console\\Kernel::class);

$status = $kernel->handle(
    $input = new Symfony\\Component\\Console\\Input\\ArgvInput,
    new Symfony\\Component\\Console\\Output\\ConsoleOutput
);

$kernel->terminate($input, $status);

exit($status);
'''
    
    with open('ohs-management-system/artisan', 'w') as f:
        f.write(artisan_content)
    
    # Make artisan executable
    os.chmod('ohs-management-system/artisan', 0o755)
    print("✅ Created artisan file")
    
    # Create bootstrap/app.php
    Path('ohs-management-system/bootstrap').mkdir(exist_ok=True)
    
    bootstrap_content = '''<?php

$app = new Illuminate\\Foundation\\Application(
    $_ENV['APP_BASE_PATH'] ?? dirname(__DIR__)
);

$app->singleton(
    Illuminate\\Contracts\\Http\\Kernel::class,
    App\\Http\\Kernel::class
);

$app->singleton(
    Illuminate\\Contracts\\Console\\Kernel::class,
    App\\Console\\Kernel::class
);

$app->singleton(
    Illuminate\\Contracts\\Debug\\ExceptionHandler::class,
    App\\Exceptions\\Handler::class
);

return $app;
'''
    
    with open('ohs-management-system/bootstrap/app.php', 'w') as f:
        f.write(bootstrap_content)
    print("✅ Created bootstrap/app.php")
    
    # Create public/index.php
    index_content = '''<?php

use Illuminate\\Contracts\\Http\\Kernel;
use Illuminate\\Http\\Request;

define('LARAVEL_START', microtime(true));

if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
    require $maintenance;
}

require_once __DIR__.'/../vendor/autoload.php';

$app = require_once __DIR__.'/../bootstrap/app.php';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
'''
    
    with open('ohs-management-system/public/index.php', 'w') as f:
        f.write(index_content)
    print("✅ Created public/index.php")

def main():
    """Main function to execute the file transfer"""
    print("🚀 Starting OHS Management System Laravel File Transfer")
    print("=" * 60)
    
    try:
        # Step 1: Create directory structure
        print("\n📁 Creating directory structure...")
        create_directory_structure()
        
        # Step 2: Copy files
        print("\n📋 Copying Laravel files...")
        copy_files()
        
        # Step 3: Create additional files
        print("\n🔧 Creating additional Laravel files...")
        create_additional_files()
        
        print("\n" + "=" * 60)
        print("🎉 File transfer completed successfully!")
        print("\n📋 Next steps:")
        print("1. cd ohs-management-system")
        print("2. composer install")
        print("3. npm install")
        print("4. cp .env.example .env")
        print("5. php artisan key:generate")
        print("6. php artisan migrate")
        print("7. php artisan serve")
        print("\n📖 See SETUP_INSTRUCTIONS.md for detailed setup guide")
        
    except Exception as e:
        print(f"❌ Error during file transfer: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
