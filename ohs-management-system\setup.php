<?php
/**
 * OHS Management System - Auto Setup Script
 * Run this once to automatically setup the entire system
 */

echo "<h1>🚀 OHS Management System - Auto Setup</h1>";
echo "<p>Setting up your system automatically...</p>";

// Check if already setup
if (file_exists('storage/app/setup_complete.txt')) {
    echo "<div style='color: green; font-weight: bold;'>✅ System already setup! <a href='public/index.php'>Go to System</a></div>";
    exit;
}

try {
    echo "<p>📦 Step 1: Generating application key...</p>";
    
    // Generate APP_KEY
    $key = 'base64:' . base64_encode(random_bytes(32));
    
    // Update .env file with generated key
    $envContent = file_get_contents('.env');
    $envContent = preg_replace('/APP_KEY=.*/', 'APP_KEY=' . $key, $envContent);
    file_put_contents('.env', $envContent);
    
    echo "<p style='color: green;'>✅ Application key generated</p>";
    
    echo "<p>🗄️ Step 2: Setting up database...</p>";
    
    // Database connection
    $host = '127.0.0.1';
    $username = 'root';
    $password = '';
    $dbname = 'ohs_management_system';
    
    // Create database if not exists
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    echo "<p style='color: green;'>✅ Database created</p>";
    
    echo "<p>📋 Step 3: Creating tables...</p>";
    
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute migration files
    $migrationFiles = glob('database/migrations/*.php');
    sort($migrationFiles);
    
    foreach ($migrationFiles as $file) {
        $content = file_get_contents($file);
        
        // Extract SQL from migration file (simplified)
        if (strpos($file, 'create_users_table') !== false) {
            $sql = "CREATE TABLE IF NOT EXISTS `users` (
                `id` char(36) NOT NULL,
                `full_name` varchar(255) NOT NULL,
                `email` varchar(255) NOT NULL,
                `email_verified_at` timestamp NULL DEFAULT NULL,
                `password` varchar(255) NOT NULL,
                `role` enum('admin','inspector','business_owner') NOT NULL DEFAULT 'business_owner',
                `status` enum('active','inactive') NOT NULL DEFAULT 'active',
                `phone` varchar(50) DEFAULT NULL,
                `address` text,
                `preferences` json DEFAULT NULL,
                `last_activity_at` timestamp NULL DEFAULT NULL,
                `created_by` char(36) DEFAULT NULL,
                `remember_token` varchar(100) DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                UNIQUE KEY `users_email_unique` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $pdo->exec($sql);
        }
        
        // Add other essential tables
        if (strpos($file, 'create_districts_table') !== false) {
            $sql = "CREATE TABLE IF NOT EXISTS `districts` (
                `id` char(36) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $pdo->exec($sql);
        }
        
        if (strpos($file, 'create_barangays_table') !== false) {
            $sql = "CREATE TABLE IF NOT EXISTS `barangays` (
                `id` char(36) NOT NULL,
                `district_id` char(36) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `barangays_district_id_foreign` (`district_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $pdo->exec($sql);
        }
        
        if (strpos($file, 'create_business_categories_table') !== false) {
            $sql = "CREATE TABLE IF NOT EXISTS `business_categories` (
                `id` char(36) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $pdo->exec($sql);
        }
    }
    
    echo "<p style='color: green;'>✅ Database tables created</p>";
    
    echo "<p>👤 Step 4: Creating admin user...</p>";
    
    // Create admin user
    $adminId = bin2hex(random_bytes(16));
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("INSERT INTO users (id, full_name, email, password, role, status, phone, address, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
    $stmt->execute([
        $adminId,
        'System Administrator',
        '<EMAIL>',
        $adminPassword,
        'admin',
        'active',
        '09123456789',
        'Bacoor City Hall, Cavite'
    ]);
    
    echo "<p style='color: green;'>✅ Admin user created</p>";
    
    echo "<p>🏢 Step 5: Adding sample data...</p>";
    
    // Add districts
    $districts = [
        ['id' => bin2hex(random_bytes(16)), 'name' => 'District 1 - West', 'description' => 'Western District of Bacoor'],
        ['id' => bin2hex(random_bytes(16)), 'name' => 'District 2 - East', 'description' => 'Eastern District of Bacoor']
    ];
    
    foreach ($districts as $district) {
        $stmt = $pdo->prepare("INSERT INTO districts (id, name, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
        $stmt->execute([$district['id'], $district['name'], $district['description']]);
    }
    
    // Add sample barangays for District 1
    $barangays = [
        'Alima', 'Aniban I', 'Aniban II', 'Aniban III', 'Aniban IV', 'Aniban V',
        'Banalo', 'Bayanan', 'Campo Santo', 'Dasmarinas Bagong Bayan',
        'Digman', 'Dulong Bayan', 'Habay I', 'Habay II', 'Kaingin',
        'Ligas I', 'Ligas II', 'Ligas III', 'Longos', 'Maliksi I',
        'Maliksi II', 'Maliksi III', 'Mambog I', 'Mambog II', 'Mambog III',
        'Mambog IV', 'Mambog V', 'Molino I', 'Molino II', 'Molino III',
        'Molino IV', 'Molino V', 'Molino VI', 'Molino VII', 'Niog I',
        'Niog II', 'Niog III', 'Panapaan I', 'Panapaan II', 'Panapaan III',
        'Panapaan IV', 'Panapaan V', 'Panapaan VI', 'Panapaan VII', 'Panapaan VIII',
        'Queens Row Central', 'Queens Row East', 'Queens Row West'
    ];
    
    foreach ($barangays as $index => $barangayName) {
        $districtId = $index < 33 ? $districts[0]['id'] : $districts[1]['id'];
        $stmt = $pdo->prepare("INSERT INTO barangays (id, district_id, name, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
        $stmt->execute([bin2hex(random_bytes(16)), $districtId, $barangayName]);
    }
    
    // Add business categories
    $categories = [
        'Restaurant/Food Service',
        'Retail Store',
        'Manufacturing',
        'Construction',
        'Healthcare Facility',
        'Educational Institution',
        'Office/Professional Services',
        'Automotive Services',
        'Beauty/Personal Care',
        'Entertainment/Recreation'
    ];

    foreach ($categories as $category) {
        $stmt = $pdo->prepare("INSERT INTO business_categories (id, name, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
        $stmt->execute([bin2hex(random_bytes(16)), $category]);
    }

    echo "<p>📋 Step 6: Setting up inspection checklist...</p>";

    // Create inspection checklist tables
    $pdo->exec("CREATE TABLE IF NOT EXISTS inspection_checklist_categories (
        id CHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    $pdo->exec("CREATE TABLE IF NOT EXISTS inspection_checklist_items (
        id CHAR(36) PRIMARY KEY,
        category_id CHAR(36) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        points INT DEFAULT 1,
        is_required BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES inspection_checklist_categories(id) ON DELETE CASCADE
    )");

    // Add default checklist categories
    $checklist_categories = [
        ['id' => bin2hex(random_bytes(16)), 'name' => 'Fire Safety', 'description' => 'Fire prevention and safety measures', 'sort_order' => 1],
        ['id' => bin2hex(random_bytes(16)), 'name' => 'Workplace Safety', 'description' => 'General workplace safety requirements', 'sort_order' => 2],
        ['id' => bin2hex(random_bytes(16)), 'name' => 'Health & Sanitation', 'description' => 'Health and sanitation standards', 'sort_order' => 3],
        ['id' => bin2hex(random_bytes(16)), 'name' => 'Emergency Preparedness', 'description' => 'Emergency response and preparedness', 'sort_order' => 4],
        ['id' => bin2hex(random_bytes(16)), 'name' => 'Documentation', 'description' => 'Required permits and documentation', 'sort_order' => 5],
    ];

    foreach ($checklist_categories as $cat) {
        $stmt = $pdo->prepare("INSERT INTO inspection_checklist_categories (id, name, description, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
        $stmt->execute([$cat['id'], $cat['name'], $cat['description'], $cat['sort_order']]);
    }

    // Add default checklist items
    $checklist_items = [
        // Fire Safety
        ['category_id' => $checklist_categories[0]['id'], 'title' => 'Fire Extinguishers Available', 'description' => 'Adequate number of fire extinguishers properly placed and maintained', 'points' => 5, 'is_required' => 1, 'sort_order' => 1],
        ['category_id' => $checklist_categories[0]['id'], 'title' => 'Emergency Exit Signs', 'description' => 'Clear and visible emergency exit signs installed', 'points' => 3, 'is_required' => 1, 'sort_order' => 2],
        ['category_id' => $checklist_categories[0]['id'], 'title' => 'Fire Alarm System', 'description' => 'Functional fire alarm system installed and tested', 'points' => 5, 'is_required' => 1, 'sort_order' => 3],
        ['category_id' => $checklist_categories[0]['id'], 'title' => 'Sprinkler System', 'description' => 'Automatic sprinkler system installed and functional', 'points' => 4, 'is_required' => 0, 'sort_order' => 4],

        // Workplace Safety
        ['category_id' => $checklist_categories[1]['id'], 'title' => 'Safety Signage', 'description' => 'Appropriate safety signs and warnings posted', 'points' => 3, 'is_required' => 1, 'sort_order' => 1],
        ['category_id' => $checklist_categories[1]['id'], 'title' => 'First Aid Kit', 'description' => 'Complete and accessible first aid kit available', 'points' => 3, 'is_required' => 1, 'sort_order' => 2],
        ['category_id' => $checklist_categories[1]['id'], 'title' => 'Personal Protective Equipment', 'description' => 'Adequate PPE provided for employees', 'points' => 4, 'is_required' => 1, 'sort_order' => 3],
        ['category_id' => $checklist_categories[1]['id'], 'title' => 'Safety Training Records', 'description' => 'Employee safety training documented', 'points' => 3, 'is_required' => 1, 'sort_order' => 4],

        // Health & Sanitation
        ['category_id' => $checklist_categories[2]['id'], 'title' => 'Clean Restroom Facilities', 'description' => 'Clean and well-maintained restroom facilities', 'points' => 3, 'is_required' => 1, 'sort_order' => 1],
        ['category_id' => $checklist_categories[2]['id'], 'title' => 'Waste Management', 'description' => 'Proper waste segregation and disposal system', 'points' => 4, 'is_required' => 1, 'sort_order' => 2],
        ['category_id' => $checklist_categories[2]['id'], 'title' => 'Ventilation System', 'description' => 'Adequate ventilation and air circulation', 'points' => 3, 'is_required' => 1, 'sort_order' => 3],
        ['category_id' => $checklist_categories[2]['id'], 'title' => 'Water Quality', 'description' => 'Safe and clean water supply', 'points' => 4, 'is_required' => 1, 'sort_order' => 4],

        // Emergency Preparedness
        ['category_id' => $checklist_categories[3]['id'], 'title' => 'Emergency Action Plan', 'description' => 'Written emergency action plan available', 'points' => 4, 'is_required' => 1, 'sort_order' => 1],
        ['category_id' => $checklist_categories[3]['id'], 'title' => 'Emergency Contact List', 'description' => 'Updated emergency contact information posted', 'points' => 2, 'is_required' => 1, 'sort_order' => 2],
        ['category_id' => $checklist_categories[3]['id'], 'title' => 'Evacuation Routes', 'description' => 'Clear evacuation routes marked and unobstructed', 'points' => 4, 'is_required' => 1, 'sort_order' => 3],

        // Documentation
        ['category_id' => $checklist_categories[4]['id'], 'title' => 'Business Permit', 'description' => 'Valid business permit displayed', 'points' => 5, 'is_required' => 1, 'sort_order' => 1],
        ['category_id' => $checklist_categories[4]['id'], 'title' => 'Fire Safety Certificate', 'description' => 'Valid fire safety certificate', 'points' => 5, 'is_required' => 1, 'sort_order' => 2],
        ['category_id' => $checklist_categories[4]['id'], 'title' => 'Sanitary Permit', 'description' => 'Valid sanitary permit for applicable businesses', 'points' => 4, 'is_required' => 1, 'sort_order' => 3],
        ['category_id' => $checklist_categories[4]['id'], 'title' => 'Environmental Compliance', 'description' => 'Environmental compliance certificate if required', 'points' => 3, 'is_required' => 0, 'sort_order' => 4],
    ];

    foreach ($checklist_items as $item) {
        $stmt = $pdo->prepare("INSERT INTO inspection_checklist_items (id, category_id, title, description, points, is_required, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->execute([
            bin2hex(random_bytes(16)),
            $item['category_id'],
            $item['title'],
            $item['description'],
            $item['points'],
            $item['is_required'],
            $item['sort_order']
        ]);
    }
    
    echo "<p style='color: green;'>✅ Sample data added</p>";
    
    echo "<p>📁 Step 7: Setting up storage...</p>";
    
    // Create storage directories
    $directories = [
        'storage/app/public',
        'storage/app/public/business-evidence',
        'storage/app/public/inspection-evidence',
        'storage/app/public/homepage-content',
        'storage/app/public/announcements',
        'storage/app/public/website-images',
        'storage/app/public/chat-files',
        'storage/framework/cache',
        'storage/framework/sessions',
        'storage/framework/views',
        'storage/logs',
        'bootstrap/cache'
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // Create storage link
    if (!file_exists('public/storage')) {
        if (PHP_OS_FAMILY === 'Windows') {
            exec('mklink /D "public\storage" "..\storage\app\public"');
        } else {
            symlink('../storage/app/public', 'public/storage');
        }
    }
    
    echo "<p style='color: green;'>✅ Storage setup complete</p>";
    
    // Mark setup as complete
    file_put_contents('storage/app/setup_complete.txt', 'Setup completed on ' . date('Y-m-d H:i:s'));
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Setup Complete!</h3>";
    echo "<p><strong>Your OHS Management System is ready to use!</strong></p>";
    echo "<p><strong>Admin Login:</strong></p>";
    echo "<ul>";
    echo "<li><strong>URL:</strong> <a href='public/index.php'>Click here to access system</a></li>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Setup Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
    echo "</div>";
}
?>
