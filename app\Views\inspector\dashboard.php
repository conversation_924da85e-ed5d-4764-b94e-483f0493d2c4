<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">Inspector Dashboard</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item active">Dashboard</li>
    </ol>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= $stats['total_inspections'] ?? 0 ?></h4>
                            <div class="small">Total Inspections</div>
                        </div>
                        <i class="fas fa-clipboard-list fa-2x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link text-decoration-none" href="<?= BASE_URL ?>inspector/schedule">View Schedule</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= $stats['total_completed'] ?? 0 ?></h4>
                            <div class="small">Completed Inspections</div>
                        </div>
                        <i class="fas fa-check fa-2x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link text-decoration-none" href="<?= BASE_URL ?>inspector/inspections?status=completed">View Completed</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= $stats['total_pending'] ?? 0 ?></h4>
                            <div class="small">Scheduled Inspections</div>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link text-decoration-none" href="<?= BASE_URL ?>inspector/schedule">View Schedule</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= $stats['pending_evidence_count'] ?? 0 ?></h4>
                            <div class="small">Pending Evidence</div>
                        </div>
                        <i class="fas fa-file-alt fa-2x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link text-decoration-none" href="<?= BASE_URL ?>inspector/compliance">Review Evidence</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions and Recent Activity -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-calendar-alt me-1"></i>
                        Next Scheduled Inspections
                    </div>
                    <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-sm btn-primary">
                        <i class="fas fa-calendar me-1"></i>View Full Schedule
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($stats['upcoming_inspections'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No upcoming inspections scheduled. Check with your administrator for new assignments.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Business</th>
                                        <th>District</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($stats['upcoming_inspections'] as $inspection): ?>
                                    <tr>
                                        <td>
                                            <strong><?= $inspection['scheduled_date'] ? date('M d, Y', strtotime($inspection['scheduled_date'])) : 'N/A' ?></strong>
                                            <br><small class="text-muted"><?= $inspection['scheduled_date'] ? date('h:i A', strtotime($inspection['scheduled_date'])) : '' ?></small>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address'] ?? '') ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></td>
                                        <td>
                                            <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-clipboard-check me-1"></i>Start Inspection
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-check-double me-1"></i>
                    Recent Completions
                </div>
                <div class="card-body">
                    <?php if (empty($stats['recent_inspections'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No completed inspections yet.
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($stats['recent_inspections'] as $inspection): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                        <br><small class="text-muted">
                                            <?= $inspection['completed_date'] ? date('M d, Y', strtotime($inspection['completed_date'])) : 'N/A' ?>
                                        </small>
                                    </div>
                                    <div>
                                        <?php if (isset($inspection['compliance_rating'])): ?>
                                            <span class="badge bg-<?=
                                                $inspection['compliance_rating'] === 'A' ? 'success' :
                                                ($inspection['compliance_rating'] === 'B' ? 'info' :
                                                ($inspection['compliance_rating'] === 'C' ? 'warning' : 'danger'))
                                            ?>">
                                                Grade <?= $inspection['compliance_rating'] ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No Grade</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="<?= BASE_URL ?>inspector/inspections?status=completed" class="btn btn-sm btn-outline-primary">
                                View All Completed
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.card .stretched-link:hover {
    text-decoration: none !important;
}

.card-footer {
    background-color: rgba(255,255,255,0.1);
    border-top: 1px solid rgba(255,255,255,0.2);
}
</style>

<?php $this->endSection(); ?>