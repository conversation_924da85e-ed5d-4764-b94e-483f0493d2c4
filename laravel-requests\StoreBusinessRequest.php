<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreBusinessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'owner_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\.\-\']+$/'
            ],
            'owner_email' => [
                'required',
                'email',
                'max:255',
                'unique:users,email'
            ],
            'business_name' => [
                'required',
                'string',
                'max:255',
                'min:3'
            ],
            'category_id' => [
                'required',
                'exists:business_categories,id'
            ],
            'barangay_id' => [
                'required',
                'exists:barangays,id'
            ],
            'address' => [
                'required',
                'string',
                'max:500',
                'min:10'
            ],
            'contact_number' => [
                'required',
                'string',
                'regex:/^(\+63|0)[0-9]{10}$/',
                'max:20'
            ],
            'employee_count' => [
                'required',
                'integer',
                'min:1',
                'max:10000'
            ],
            'date_established' => [
                'nullable',
                'date',
                'before_or_equal:today',
                'after:1900-01-01'
            ],
            'business_permit' => 'boolean',
            'sanitary_permit' => 'boolean',
            'fire_safety_certificate' => 'boolean',
            'environmental_permit' => 'boolean',
            'safety_officer_count' => [
                'nullable',
                'integer',
                'min:0',
                'max:100'
            ],
            'has_safety_signage' => 'boolean',
            'has_first_aid' => 'boolean',
            'has_fire_extinguishers' => 'boolean',
            'has_cctv' => 'boolean',
            'has_waste_segregation' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'owner_name.required' => 'Business owner name is required.',
            'owner_name.regex' => 'Business owner name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'owner_email.required' => 'Business owner email is required.',
            'owner_email.email' => 'Please provide a valid email address.',
            'owner_email.unique' => 'This email address is already registered in the system.',
            'business_name.required' => 'Business name is required.',
            'business_name.min' => 'Business name must be at least 3 characters long.',
            'category_id.required' => 'Please select a business category.',
            'category_id.exists' => 'Selected business category is invalid.',
            'barangay_id.required' => 'Please select a barangay.',
            'barangay_id.exists' => 'Selected barangay is invalid.',
            'address.required' => 'Business address is required.',
            'address.min' => 'Business address must be at least 10 characters long.',
            'contact_number.required' => 'Contact number is required.',
            'contact_number.regex' => 'Please provide a valid Philippine phone number (e.g., +639123456789 or ***********).',
            'employee_count.required' => 'Employee count is required.',
            'employee_count.min' => 'Employee count must be at least 1.',
            'employee_count.max' => 'Employee count cannot exceed 10,000.',
            'date_established.date' => 'Please provide a valid establishment date.',
            'date_established.before_or_equal' => 'Establishment date cannot be in the future.',
            'date_established.after' => 'Establishment date must be after 1900.',
            'safety_officer_count.max' => 'Safety officer count cannot exceed 100.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'owner_name' => 'business owner name',
            'owner_email' => 'business owner email',
            'business_name' => 'business name',
            'category_id' => 'business category',
            'barangay_id' => 'barangay',
            'contact_number' => 'contact number',
            'employee_count' => 'employee count',
            'date_established' => 'date established',
            'safety_officer_count' => 'safety officer count',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and format contact number
        if ($this->has('contact_number')) {
            $contactNumber = preg_replace('/[^0-9+]/', '', $this->contact_number);
            
            // Convert 09 format to +639 format
            if (str_starts_with($contactNumber, '09')) {
                $contactNumber = '+63' . substr($contactNumber, 1);
            }
            
            $this->merge([
                'contact_number' => $contactNumber,
            ]);
        }

        // Convert boolean strings to actual booleans
        $booleanFields = [
            'business_permit',
            'sanitary_permit', 
            'fire_safety_certificate',
            'environmental_permit',
            'has_safety_signage',
            'has_first_aid',
            'has_fire_extinguishers',
            'has_cctv',
            'has_waste_segregation'
        ];

        foreach ($booleanFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->input($field), FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }

        // Ensure employee_count and safety_officer_count are integers
        if ($this->has('employee_count')) {
            $this->merge([
                'employee_count' => (int) $this->employee_count
            ]);
        }

        if ($this->has('safety_officer_count')) {
            $this->merge([
                'safety_officer_count' => (int) $this->safety_officer_count
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation: Check if safety officer count is reasonable for employee count
            if ($this->has(['employee_count', 'safety_officer_count'])) {
                $employeeCount = (int) $this->employee_count;
                $safetyOfficerCount = (int) $this->safety_officer_count;
                
                if ($safetyOfficerCount > $employeeCount) {
                    $validator->errors()->add(
                        'safety_officer_count',
                        'Safety officer count cannot exceed total employee count.'
                    );
                }
            }

            // Custom validation: Check business name uniqueness within the same barangay
            if ($this->has(['business_name', 'barangay_id'])) {
                $existingBusiness = \App\Models\Business::where('name', $this->business_name)
                    ->where('barangay_id', $this->barangay_id)
                    ->where('status', '!=', 'inactive')
                    ->first();

                if ($existingBusiness) {
                    $validator->errors()->add(
                        'business_name',
                        'A business with this name already exists in the selected barangay.'
                    );
                }
            }
        });
    }
}
