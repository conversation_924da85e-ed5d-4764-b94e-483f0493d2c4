<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>Login - OHS System</title>
    
    <!-- Custom fonts for this template-->
    <link href="{{ asset('vendor/fontawesome-free/css/all.min.css') }}" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    
    <!-- Custom styles for this template-->
    <link href="{{ asset('css/sb-admin-2.min.css') }}" rel="stylesheet">
</head>

<body class="bg-gradient-primary">
    <div class="container">
        <!-- Outer Row -->
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-12 col-md-9">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <!-- Nested Row within Card Body -->
                        <div class="row">
                            <div class="col-lg-6 d-none d-lg-block bg-login-image">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4">Welcome to</h1>
                                        <h1 class="h2 text-primary mb-4">OHS Management System</h1>
                                        <p class="mb-4">Occupational Health and Safety Management System for Bacoor City</p>
                                        <div class="text-center">
                                            <i class="fas fa-shield-alt fa-5x text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4">Welcome Back!</h1>
                                    </div>
                                    
                                    @if($errors->any())
                                        <div class="alert alert-danger">
                                            @foreach($errors->all() as $error)
                                                <div>{{ $error }}</div>
                                            @endforeach
                                        </div>
                                    @endif

                                    @if(session('success'))
                                        <div class="alert alert-success">
                                            {{ session('success') }}
                                        </div>
                                    @endif
                                    
                                    <form class="user" method="POST" action="{{ route('login') }}">
                                        @csrf
                                        <div class="form-group">
                                            <input type="email" 
                                                   class="form-control form-control-user @error('email') is-invalid @enderror" 
                                                   id="email" 
                                                   name="email" 
                                                   placeholder="Enter Email Address..." 
                                                   value="{{ old('email') }}" 
                                                   required>
                                        </div>
                                        <div class="form-group">
                                            <input type="password" 
                                                   class="form-control form-control-user @error('password') is-invalid @enderror" 
                                                   id="password" 
                                                   name="password" 
                                                   placeholder="Password" 
                                                   required>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox small">
                                                <input type="checkbox" class="custom-control-input" id="remember" name="remember">
                                                <label class="custom-control-label" for="remember">Remember Me</label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary btn-user btn-block">
                                            Login
                                        </button>
                                    </form>
                                    <hr>
                                    <div class="text-center">
                                        <a class="small" href="{{ route('password.request') }}">Forgot Password?</a>
                                    </div>
                                    <div class="text-center">
                                        <a class="small" href="{{ route('register') }}">Create an Account!</a>
                                    </div>
                                    
                                    <!-- Demo Accounts -->
                                    <hr>
                                    <div class="text-center">
                                        <small class="text-muted">Demo Accounts:</small>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <button type="button" class="btn btn-sm btn-outline-primary btn-block" onclick="fillDemo('admin')">
                                                Admin Demo
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row mt-1">
                                        <div class="col-6">
                                            <button type="button" class="btn btn-sm btn-outline-success btn-block" onclick="fillDemo('inspector')">
                                                Inspector
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button type="button" class="btn btn-sm btn-outline-info btn-block" onclick="fillDemo('business')">
                                                Business
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{{ asset('vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{{ asset('vendor/jquery-easing/jquery.easing.min.js') }}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{{ asset('js/sb-admin-2.min.js') }}"></script>

    <script>
        function fillDemo(type) {
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            
            switch(type) {
                case 'admin':
                    emailField.value = '<EMAIL>';
                    passwordField.value = 'admin123';
                    break;
                case 'inspector':
                    emailField.value = '<EMAIL>';
                    passwordField.value = 'inspector123';
                    break;
                case 'business':
                    emailField.value = '<EMAIL>';
                    passwordField.value = 'business123';
                    break;
            }
        }
    </script>
</body>
</html>
