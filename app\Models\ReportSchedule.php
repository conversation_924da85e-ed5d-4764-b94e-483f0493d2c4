<?php
require_once __DIR__ . '/../Config/Database.php';

class ReportSchedule {
    private $db;
    private $table = 'report_schedules';

    public function __construct() {
        $this->db = (new Database())->connect();
    }

    public function getActiveSchedules() {
        $query = "SELECT * FROM {$this->table} 
                 WHERE is_active = TRUE
                 AND next_run <= NOW()";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function updateSchedule($scheduleId, $lastRun, $nextRun) {
        $query = "UPDATE {$this->table} 
                 SET last_run = :last_run,
                     next_run = :next_run,
                     run_count = run_count + 1
                 WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            'id' => $scheduleId,
            'last_run' => $lastRun,
            'next_run' => $nextRun
        ]);
    }

    public function getRecipients($scheduleId) {
        $query = "SELECT u.email, u.full_name
                 FROM report_recipients r
                 JOIN users u ON r.user_id = u.id
                 WHERE r.schedule_id = :schedule_id
                 AND u.is_active = TRUE";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute(['schedule_id' => $scheduleId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function create($data) {
        $query = "INSERT INTO {$this->table} 
                (id, name, frequency, email_subject, email_body, is_active, next_run)
                VALUES (:id, :name, :frequency, :subject, :body, :is_active, :next_run)";
        
        $stmt = $this->db->prepare($query);
        $data['id'] = bin2hex(random_bytes(16));
        $success = $stmt->execute($data);
        
        return $success ? $data['id'] : false;
    }

    public function update($id, $data) {
        $query = "UPDATE {$this->table} 
                SET name = :name,
                    frequency = :frequency,
                    email_subject = :subject,
                    email_body = :body,
                    is_active = :is_active,
                    next_run = :next_run
                WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        $data['id'] = $id;
        return $stmt->execute($data);
    }

    public function getById($id) {
        $query = "SELECT * FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute(['id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function clearRecipients($scheduleId) {
        $query = "DELETE FROM report_recipients WHERE schedule_id = :schedule_id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute(['schedule_id' => $scheduleId]);
    }

    public function addRecipient($scheduleId, $userId) {
        $query = "INSERT INTO report_recipients 
                (id, schedule_id, user_id)
                VALUES (:id, :schedule_id, :user_id)";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            'id' => bin2hex(random_bytes(16)),
            'schedule_id' => $scheduleId,
            'user_id' => $userId
        ]);
    }


}