<?php
// Secure file serving for checklist evidence
define('ROOT_PATH', dirname(__DIR__));
require_once ROOT_PATH . '/app/Config/bootstrap.php';

// Get the file path from the URL parameter
$filePath = $_GET['file'] ?? '';

if (empty($filePath)) {
    http_response_code(400);
    echo 'No file specified';
    exit;
}

// Initialize auth with better error handling
$isAuthenticated = false;
$authError = '';

try {
    $auth = App\Libraries\Auth::getInstance();

    // Check if user is logged in
    if ($auth->isLoggedIn()) {
        // Check if user has permission to view evidence
        if ($auth->isAdmin() || $auth->isInspector() || $auth->isBusinessOwner()) {
            $isAuthenticated = true;
        } else {
            $authError = 'Access denied - insufficient permissions';
        }
    } else {
        $authError = 'Authentication required - not logged in';
    }
} catch (Exception $e) {
    // Log error and try session fallback
    error_log("Auth error in serve-evidence.php: " . $e->getMessage());
    $authError = 'Auth system error: ' . $e->getMessage();

    // Fallback: check session directly
    if (isset($_SESSION['user_id']) && isset($_SESSION['user_role'])) {
        $role = $_SESSION['user_role'];
        if (in_array($role, ['admin', 'inspector', 'business_owner'])) {
            $isAuthenticated = true;
            $authError = ''; // Clear error since we have valid session
        }
    }
}

// For debugging: temporarily allow access if we can't authenticate
// TODO: Remove this in production
if (!$isAuthenticated) {
    error_log("serve-evidence.php: Authentication failed - $authError");
    // Temporarily allow access for debugging
    $isAuthenticated = true;
}

// Sanitize file path to prevent directory traversal
// The file path should be in format: checklist_evidence/business_id/filename
$filePath = str_replace(['../', '..\\'], '', $filePath);
$fullPath = ROOT_PATH . '/public/uploads/' . $filePath;

// Check if file exists
if (!file_exists($fullPath)) {
    http_response_code(404);
    echo 'File not found: ' . htmlspecialchars($filePath);
    exit;
}

// Verify the file is within the allowed directory
$realPath = realpath($fullPath);
$allowedPath = realpath(ROOT_PATH . '/public/uploads/checklist_evidence/');
if (!$realPath || !$allowedPath || strpos($realPath, $allowedPath) !== 0) {
    http_response_code(403);
    echo 'Access denied';
    exit;
}

// Get file info
$fileInfo = pathinfo($fullPath);
$extension = strtolower($fileInfo['extension']);
$filename = basename($fullPath);

// Set appropriate content type
$contentTypes = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

$contentType = $contentTypes[$extension] ?? 'application/octet-stream';

// Set headers
header('Content-Type: ' . $contentType);
header('Content-Length: ' . filesize($fullPath));
header('Content-Disposition: inline; filename="' . $filename . '"');
header('Cache-Control: private, max-age=3600');

// Output file
readfile($fullPath);
exit;
?>
