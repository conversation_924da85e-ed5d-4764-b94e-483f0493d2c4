<?php
namespace App\Services;

/**
 * File Upload Service
 * 
 * Centralized file upload handling with validation, security, and storage management
 */
class FileUploadService {
    
    private $uploadBasePath;
    private $allowedTypes;
    private $maxFileSize;
    private $errors = [];
    
    public function __construct() {
        $this->uploadBasePath = ROOT_PATH . '/public/uploads/';
        $this->allowedTypes = [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'document' => ['pdf', 'doc', 'docx', 'txt'],
            'compliance' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
            'all' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'txt']
        ];
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB default
    }
    
    /**
     * Upload a single file
     */
    public function upload($file, $directory = 'general', $options = []) {
        $this->errors = [];
        
        // Validate file input
        if (!$this->validateFileInput($file)) {
            return false;
        }
        
        // Set options
        $allowedTypes = $options['allowed_types'] ?? 'all';
        $maxSize = $options['max_size'] ?? $this->maxFileSize;
        $customName = $options['custom_name'] ?? null;
        $overwrite = $options['overwrite'] ?? false;
        
        // Validate file
        if (!$this->validateFile($file, $allowedTypes, $maxSize)) {
            return false;
        }
        
        // Create upload directory
        $uploadDir = $this->uploadBasePath . $directory . '/';
        if (!$this->createDirectory($uploadDir)) {
            return false;
        }
        
        // Generate filename
        $filename = $this->generateFilename($file, $customName);
        $filePath = $uploadDir . $filename;
        
        // Check if file exists and handle overwrite
        if (file_exists($filePath) && !$overwrite) {
            $filename = $this->generateUniqueFilename($uploadDir, $filename);
            $filePath = $uploadDir . $filename;
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return [
                'success' => true,
                'filename' => $filename,
                'path' => $filePath,
                'relative_path' => 'uploads/' . $directory . '/' . $filename,
                'url' => BASE_URL . 'uploads/' . $directory . '/' . $filename,
                'size' => $file['size'],
                'type' => $file['type'],
                'extension' => $this->getFileExtension($file['name'])
            ];
        } else {
            $this->errors[] = 'Failed to move uploaded file';
            return false;
        }
    }
    
    /**
     * Upload multiple files
     */
    public function uploadMultiple($files, $directory = 'general', $options = []) {
        $results = [];
        $errors = [];
        
        foreach ($files as $index => $file) {
            $result = $this->upload($file, $directory, $options);
            if ($result) {
                $results[] = $result;
            } else {
                $errors[$index] = $this->getErrors();
            }
        }
        
        return [
            'success' => !empty($results),
            'uploaded' => $results,
            'errors' => $errors,
            'count' => count($results)
        ];
    }
    
    /**
     * Delete uploaded file
     */
    public function delete($filePath) {
        $fullPath = $this->uploadBasePath . ltrim($filePath, '/');
        
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        
        return false;
    }
    
    /**
     * Validate file input
     */
    private function validateFileInput($file) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $this->errors[] = 'No file uploaded or invalid file';
            return false;
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->errors[] = $this->getUploadErrorMessage($file['error']);
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate file type and size
     */
    private function validateFile($file, $allowedTypes, $maxSize) {
        // Check file size
        if ($file['size'] > $maxSize) {
            $this->errors[] = 'File size exceeds maximum allowed size of ' . $this->formatBytes($maxSize);
            return false;
        }
        
        // Check file type
        $extension = strtolower($this->getFileExtension($file['name']));
        $allowedExtensions = $this->allowedTypes[$allowedTypes] ?? $this->allowedTypes['all'];
        
        if (!in_array($extension, $allowedExtensions)) {
            $this->errors[] = 'File type not allowed. Allowed types: ' . implode(', ', $allowedExtensions);
            return false;
        }
        
        // Additional MIME type validation
        if (!$this->validateMimeType($file, $extension)) {
            $this->errors[] = 'File content does not match file extension';
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate MIME type
     */
    private function validateMimeType($file, $extension) {
        $mimeTypes = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'webp' => ['image/webp'],
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'txt' => ['text/plain']
        ];
        
        if (!isset($mimeTypes[$extension])) {
            return true; // Allow if not in our strict list
        }
        
        $fileMime = mime_content_type($file['tmp_name']);
        return in_array($fileMime, $mimeTypes[$extension]);
    }
    
    /**
     * Create directory if it doesn't exist
     */
    private function createDirectory($path) {
        if (!file_exists($path)) {
            if (!mkdir($path, 0755, true)) {
                $this->errors[] = 'Failed to create upload directory';
                return false;
            }
        }
        return true;
    }
    
    /**
     * Generate filename
     */
    private function generateFilename($file, $customName = null) {
        $extension = $this->getFileExtension($file['name']);
        
        if ($customName) {
            return $customName . '.' . $extension;
        }
        
        return uniqid() . '_' . time() . '.' . $extension;
    }
    
    /**
     * Generate unique filename if file exists
     */
    private function generateUniqueFilename($directory, $filename) {
        $pathInfo = pathinfo($filename);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        $counter = 1;
        
        while (file_exists($directory . $filename)) {
            $filename = $baseName . '_' . $counter . '.' . $extension;
            $counter++;
        }
        
        return $filename;
    }
    
    /**
     * Get file extension
     */
    private function getFileExtension($filename) {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }
    
    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($errorCode) {
        $messages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        return $messages[$errorCode] ?? 'Unknown upload error';
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Check if there are errors
     */
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    /**
     * Set allowed file types for a category
     */
    public function setAllowedTypes($category, array $types) {
        $this->allowedTypes[$category] = $types;
    }
    
    /**
     * Set maximum file size
     */
    public function setMaxFileSize($size) {
        $this->maxFileSize = $size;
    }
    
    /**
     * Get file info without uploading
     */
    public function getFileInfo($file) {
        return [
            'name' => $file['name'],
            'size' => $file['size'],
            'type' => $file['type'],
            'extension' => $this->getFileExtension($file['name']),
            'size_formatted' => $this->formatBytes($file['size'])
        ];
    }
}
