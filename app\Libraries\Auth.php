<?php
namespace App\Libraries;

use App\Models\User;

class Auth {
    private static $instance = null;
    private $user = null;
    private $userModel;

    public function __construct() {
        $this->userModel = new User();
        $this->checkSession();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function checkSession() {
        if (isset($_SESSION['user_id'])) {
            try {
                $this->user = $this->userModel->find($_SESSION['user_id']);
                if (!$this->user || $this->user['status'] !== 'active') {
                    $this->logout();
                }
            } catch (\Exception $e) {
                error_log("Error checking session: " . $e->getMessage());
                $this->logout();
            }
        }
    }

    public function login($email, $password) {
        try {
            $user = $this->userModel->login($email, $password);
            
            if ($user) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['user_name'] = $user['full_name'];
                $this->user = $user;
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            error_log("Error in login: " . $e->getMessage());
            return false;
        }
    }

    public function logout() {
        $this->user = null;
        session_unset();
        session_destroy();
        session_start();
    }

    public function isLoggedIn() {
        return $this->user !== null;
    }

    public function isAdmin() {
        return $this->isLoggedIn() && $this->user['role'] === 'admin';
    }

    public function isInspector() {
        return $this->isLoggedIn() && $this->user['role'] === 'inspector';
    }

    public function isBusinessOwner() {
        return $this->isLoggedIn() && $this->user['role'] === 'business_owner';
    }

    public function isSafetyOfficer() {
        return $this->isLoggedIn() && $this->user['role'] === 'safety_officer';
    }

    public function getUser() {
        return $this->user;
    }

    public function getUserId() {
        return $this->user ? $this->user['id'] : null;
    }

    public function getUserRole() {
        return $this->user ? $this->user['role'] : null;
    }

    public function getRole() {
        return $this->getUserRole();
    }

    public function getUserName() {
        return $this->user ? $this->user['full_name'] : null;
    }

    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            $_SESSION['redirect_url'] = $_SERVER['REQUEST_URI'];
            header('Location: ' . BASE_URL . 'login');
            exit();
        }
    }

    public function requireAdmin() {
        $this->requireLogin();
        if (!$this->isAdmin()) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit();
        }
    }

    public function requireInspector() {
        $this->requireLogin();
        if (!$this->isInspector()) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit();
        }
    }

    public function requireBusinessOwner() {
        $this->requireLogin();
        if (!$this->isBusinessOwner()) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit();
        }
    }

    public function requireSafetyOfficer() {
        $this->requireLogin();
        if (!$this->isSafetyOfficer()) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit();
        }
    }
}