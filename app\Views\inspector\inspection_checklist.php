<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<style>
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-check"></i> Inspection Checklist
        </h1>
        <div>
            <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Schedule
            </a>
            <button type="button" class="btn btn-success btn-lg shadow-sm" id="completeInspectionBtn"
                    style="display: none; animation: pulse 2s infinite;">
                <i class="fas fa-check-circle"></i> Complete Inspection
            </button>
            <!-- Completion Alert -->
            <div class="alert alert-success alert-dismissible fade show" id="completionAlert" style="display: none;" role="alert">
                <h5 class="alert-heading"><i class="fas fa-check-circle"></i> Checklist Complete!</h5>
                <p class="mb-2">You have completed all checklist items with a score of <strong id="alertScore">100%</strong> and grade <strong id="alertGrade">A</strong>.</p>
                <p class="mb-0">Click the <strong>"Complete Inspection"</strong> button above to finalize this inspection.</p>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Inspection Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-building"></i> Inspection Details
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= htmlspecialchars($inspection['business_name']) ?></h5>
                    <p class="text-muted mb-1">
                        <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($inspection['business_address']) ?>
                    </p>
                    <p class="text-muted mb-1">
                        <i class="fas fa-map"></i> District: <?= htmlspecialchars($inspection['district_name']) ?>
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1">
                        <strong>Inspection Date:</strong> <?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?>
                    </p>
                    <p class="mb-1">
                        <strong>Type:</strong> <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'])) ?>
                    </p>
                    <p class="mb-1">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                            <?= ucfirst($inspection['status']) ?>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-chart-line"></i> Inspection Progress
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="progress mb-2" style="height: 20px;">
                            <div class="progress-bar bg-info" role="progressbar" 
                                 style="width: <?= $completion_status['completion_percentage'] ?>%"
                                 id="progressBar">
                                <?= $completion_status['completion_percentage'] ?>%
                            </div>
                        </div>
                        <small class="text-muted">
                            <span id="completedItems"><?= $completion_status['completed_items'] ?></span> of
                            <?= $completion_status['total_items'] ?> items completed
                        </small>
                        <div id="completionStatus" style="display: none;" class="mt-2">
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle"></i> Ready to Complete
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4" id="scoreSection" style="<?= $current_score ? '' : 'display: none;' ?>">
                    <div class="text-center">
                        <h4 class="mb-1" id="currentScore">
                            <?= $current_score ? $current_score['percentage'] . '%' : '0%' ?>
                        </h4>
                        <small class="text-muted">Current Score</small>
                        <br>
                        <span class="badge bg-<?= $current_score && $current_score['grade'] === 'A' ? 'success' : ($current_score && in_array($current_score['grade'], ['B', 'C']) ? 'warning' : 'danger') ?>" id="currentGrade">
                            Grade: <?= $current_score ? $current_score['grade'] : 'N/A' ?>
                        </span>
                    </div>
                </div>
                <div class="col-md-4" id="violationsSection" style="<?= $current_score && $current_score['critical_violations'] > 0 ? '' : 'display: none;' ?>">
                    <div class="text-center">
                        <h4 class="mb-1 text-danger" id="criticalViolations">
                            <?= $current_score ? $current_score['critical_violations'] : 0 ?>
                        </h4>
                        <small class="text-muted">Critical Violations</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Checklist Categories -->
    <?php foreach ($checklist_categories as $category): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3" data-bs-toggle="collapse" data-bs-target="#category-<?= $category['id'] ?>" style="cursor: pointer;">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chevron-down me-2"></i>
                    <?= htmlspecialchars($category['name']) ?>
                    <span class="badge bg-secondary ms-2">Weight: <?= $category['weight'] ?></span>
                    <span class="badge bg-info ms-1" id="category-progress-<?= $category['id'] ?>">
                        0/<?= count($category['items']) ?> completed
                    </span>
                </h6>
                <?php if ($category['description']): ?>
                    <small class="text-muted"><?= htmlspecialchars($category['description']) ?></small>
                <?php endif; ?>
            </div>
            <div class="collapse show" id="category-<?= $category['id'] ?>">
                <div class="card-body">
                    <?php foreach ($category['items'] as $item): ?>
                        <?php 
                        $existingResponse = $existing_responses[$item['id']] ?? null;
                        $isCompleted = $existingResponse !== null;
                        ?>
                        <div class="checklist-item border rounded p-3 mb-3 <?= $isCompleted ? 'bg-light' : '' ?>" 
                             data-item-id="<?= $item['id'] ?>" data-category-id="<?= $category['id'] ?>">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-2">
                                        <?= htmlspecialchars($item['item_name']) ?>
                                        <?php if ($item['is_critical']): ?>
                                            <span class="badge bg-danger ms-2">CRITICAL</span>
                                        <?php endif; ?>
                                        <span class="badge bg-secondary ms-1"><?= $item['points'] ?> pts</span>
                                    </h6>
                                    <p class="text-muted mb-2"><?= htmlspecialchars($item['description']) ?></p>
                                    <small class="text-info">
                                        <strong>Requirement:</strong> <?= htmlspecialchars($item['compliance_requirement']) ?>
                                    </small>

                                    <!-- Business Evidence Section -->
                                    <?php if (!empty($item['business_evidence'])): ?>
                                        <div class="business-evidence mt-3 p-2 bg-light border rounded">
                                            <h6 class="mb-2 text-success">
                                                <i class="fas fa-file-check me-1"></i>Business Evidence (<?= count($item['business_evidence']) ?>)
                                                <small class="text-muted">- Optional Reference</small>
                                            </h6>
                                            <div class="evidence-list">
                                                <?php foreach ($item['business_evidence'] as $evidence): ?>
                                                    <div class="evidence-item d-flex justify-content-between align-items-center mb-2 p-2 bg-white border rounded">
                                                        <div class="evidence-info">
                                                            <strong><?= htmlspecialchars($evidence['file_name']) ?></strong>
                                                            <br>
                                                            <small class="text-muted">
                                                                <i class="fas fa-clock me-1"></i>Uploaded: <?= date('M d, Y g:i A', strtotime($evidence['created_at'])) ?>
                                                                by <?= htmlspecialchars($evidence['uploaded_by_name']) ?>
                                                            </small>
                                                            <?php if ($evidence['notes']): ?>
                                                                <br><small class="text-info"><?= htmlspecialchars($evidence['notes']) ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="evidence-actions">
                                                            <span class="badge bg-<?=
                                                                $evidence['status'] === 'approved' ? 'success' :
                                                                ($evidence['status'] === 'rejected' ? 'danger' : 'warning')
                                                            ?> me-2">
                                                                <?= ucfirst($evidence['status']) ?>
                                                            </span>
                                                            <?php
                                                            $fileExtension = strtolower(pathinfo($evidence['file_name'], PATHINFO_EXTENSION));
                                                            $isImage = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif']);
                                                            ?>

                                                            <?php if ($isImage): ?>
                                                                <button type="button" class="btn btn-outline-primary btn-sm view-image-btn"
                                                                        data-image-src="<?= BASE_URL ?>public/serve-evidence.php?file=<?= urlencode($evidence['file_path']) ?>"
                                                                        data-image-name="<?= htmlspecialchars($evidence['file_name']) ?>">
                                                                    <i class="fas fa-eye"></i> View
                                                                </button>
                                                            <?php else: ?>
                                                                <a href="<?= BASE_URL ?>public/serve-evidence.php?file=<?= urlencode($evidence['file_path']) ?>" target="_blank"
                                                                   class="btn btn-outline-primary btn-sm">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="business-evidence mt-3 p-2 bg-light border rounded">
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>No evidence uploaded by business owner (Optional - assess based on physical inspection)
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4">
                                    <div class="compliance-controls">
                                        <label class="form-label"><strong>Compliance Status:</strong></label>
                                        <select class="form-select form-select-sm mb-2 compliance-status" 
                                                data-item-id="<?= $item['id'] ?>">
                                            <option value="">Select Status</option>
                                            <option value="compliant" <?= $existingResponse && $existingResponse['compliance_status'] === 'compliant' ? 'selected' : '' ?>>
                                                ✅ Compliant
                                            </option>
                                            <option value="needs_improvement" <?= $existingResponse && $existingResponse['compliance_status'] === 'needs_improvement' ? 'selected' : '' ?>>
                                                ⚠️ Needs Improvement
                                            </option>
                                            <option value="non_compliant" <?= $existingResponse && $existingResponse['compliance_status'] === 'non_compliant' ? 'selected' : '' ?>>
                                                ❌ Non-Compliant
                                            </option>
                                            <option value="not_applicable" <?= $existingResponse && $existingResponse['compliance_status'] === 'not_applicable' ? 'selected' : '' ?>>
                                                ➖ Not Applicable
                                            </option>
                                        </select>
                                        
                                        <div class="additional-fields" style="<?= $existingResponse ? '' : 'display: none;' ?>">
                                            <textarea class="form-control form-control-sm mb-2 notes" 
                                                      placeholder="Notes/Observations" 
                                                      rows="2"><?= $existingResponse ? htmlspecialchars($existingResponse['notes']) : '' ?></textarea>
                                            
                                            <div class="corrective-action-section" style="<?= $existingResponse && in_array($existingResponse['compliance_status'], ['needs_improvement', 'non_compliant']) ? '' : 'display: none;' ?>">
                                                <textarea class="form-control form-control-sm mb-2 corrective-action" 
                                                          placeholder="Corrective Action Required" 
                                                          rows="2"><?= $existingResponse ? htmlspecialchars($existingResponse['corrective_action']) : '' ?></textarea>
                                                <input type="date" class="form-control form-control-sm mb-2 deadline" 
                                                       placeholder="Deadline" 
                                                       value="<?= $existingResponse ? $existingResponse['deadline'] : '' ?>">
                                            </div>
                                            
                                            <button type="button" class="btn btn-primary btn-sm save-response" 
                                                    data-item-id="<?= $item['id'] ?>">
                                                <i class="fas fa-save"></i> Save Response
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- Image Viewer Modal -->
<div class="modal fade" id="imageViewerModal" tabindex="-1" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="imageViewerModalLabel">
                    <i class="fas fa-image me-2"></i>Evidence Image
                </h5>
                <div class="d-flex align-items-center">
                    <!-- Zoom Controls -->
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-outline-light btn-sm" id="zoomOutBtn" title="Zoom Out">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button type="button" class="btn btn-outline-light btn-sm" id="resetZoomBtn" title="Reset Zoom">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button type="button" class="btn btn-outline-light btn-sm" id="zoomInBtn" title="Zoom In">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-3 text-center bg-light" style="min-height: 500px;">
                <div id="imageContainer" style="overflow: auto; max-height: 75vh; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); cursor: grab; position: relative; min-height: 400px; display: flex; align-items: center; justify-content: center;">
                    <div id="imageLoader" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); display: none; z-index: 10;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2 text-muted">Loading image...</div>
                    </div>
                    <img id="modalImage" src="" alt="Evidence Image"
                         style="max-width: 100%; max-height: 100%; height: auto; transition: transform 0.3s ease; transform-origin: center; display: none; object-fit: contain;"
                         onload="document.getElementById('imageLoader').style.display='none'; this.style.display='block'; this.parentElement.style.alignItems='flex-start'; this.parentElement.style.justifyContent='flex-start';"
                         onerror="document.getElementById('imageLoader').innerHTML='<div class=\'text-danger text-center\'><i class=\'fas fa-exclamation-triangle fa-2x\'></i><br><br>Failed to load image<br><small>Please check if the file exists and is accessible</small></div>';">
                </div>
            </div>
            <div class="modal-footer bg-light justify-content-center">
                <div class="text-muted">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Use zoom controls or scroll wheel to zoom. Click and drag to pan when zoomed.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const inspectionId = '<?= $inspection['id'] ?>';
    
    // Handle compliance status change
    document.querySelectorAll('.compliance-status').forEach(select => {
        select.addEventListener('change', function() {
            const additionalFields = this.closest('.compliance-controls').querySelector('.additional-fields');
            const correctiveSection = additionalFields.querySelector('.corrective-action-section');
            
            if (this.value) {
                additionalFields.style.display = 'block';
                
                // Show corrective action for non-compliant items
                if (['needs_improvement', 'non_compliant'].includes(this.value)) {
                    correctiveSection.style.display = 'block';
                } else {
                    correctiveSection.style.display = 'none';
                }
            } else {
                additionalFields.style.display = 'none';
            }
        });
    });
    
    // Handle save response
    document.querySelectorAll('.save-response').forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            const itemContainer = document.querySelector(`[data-item-id="${itemId}"]`);
            
            const complianceStatus = itemContainer.querySelector('.compliance-status').value;
            const notes = itemContainer.querySelector('.notes').value;
            const correctiveAction = itemContainer.querySelector('.corrective-action').value;
            const deadline = itemContainer.querySelector('.deadline').value;
            
            if (!complianceStatus) {
                alert('Please select a compliance status');
                return;
            }
            
            // Show loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            this.disabled = true;
            
            // Save response
            fetch('<?= BASE_URL ?>inspector/save-checklist-response', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    inspection_id: inspectionId,
                    checklist_item_id: itemId,
                    compliance_status: complianceStatus,
                    notes: notes,
                    corrective_action: correctiveAction,
                    deadline: deadline
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI
                    itemContainer.classList.add('bg-light');
                    this.innerHTML = '<i class="fas fa-check"></i> Saved';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-success');
                    
                    // Update progress
                    updateProgress(data.completion_status, data.current_score);
                    
                    // Update category progress
                    updateCategoryProgress();
                    
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-save"></i> Save Response';
                        this.classList.remove('btn-success');
                        this.classList.add('btn-primary');
                        this.disabled = false;
                    }, 2000);
                } else {
                    alert('Error: ' + data.message);
                    this.innerHTML = '<i class="fas fa-save"></i> Save Response';
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving');
                this.innerHTML = '<i class="fas fa-save"></i> Save Response';
                this.disabled = false;
            });
        });
    });

    // Check if inspection is already complete on page load
    function checkCompletionStatus() {
        const completionPercentage = <?= $completion_status['completion_percentage'] ?>;
        const isComplete = <?= $completion_status['is_complete'] ? 'true' : 'false' ?>;

        if (completionPercentage >= 100 || isComplete) {
            showCompletionElements();
        }
    }

    function showCompletionElements() {
        const btn = document.getElementById('completeInspectionBtn');
        const alert = document.getElementById('completionAlert');
        const status = document.getElementById('completionStatus');

        if (btn) {
            btn.style.display = 'inline-block';
        }
        if (alert) {
            alert.style.display = 'block';
            // Update alert with current score
            const scoreElement = document.getElementById('currentScore');
            const gradeElement = document.getElementById('currentGrade');

            if (scoreElement && gradeElement) {
                document.getElementById('alertScore').textContent = scoreElement.textContent;
                document.getElementById('alertGrade').textContent = gradeElement.textContent.replace('Grade: ', '');
            }
        }
        if (status) {
            status.style.display = 'block';
        }
    }

    // Call on page load
    checkCompletionStatus();

    function updateProgress(completionStatus, currentScore) {
        // Update progress bar
        document.getElementById('progressBar').style.width = completionStatus.completion_percentage + '%';
        document.getElementById('progressBar').textContent = completionStatus.completion_percentage + '%';
        document.getElementById('completedItems').textContent = completionStatus.completed_items;
        
        // Update score section
        if (currentScore) {
            document.getElementById('scoreSection').style.display = 'block';
            document.getElementById('currentScore').textContent = currentScore.percentage + '%';
            document.getElementById('currentGrade').textContent = 'Grade: ' + currentScore.grade;
            
            // Update grade badge color
            const gradeBadge = document.getElementById('currentGrade');
            gradeBadge.className = 'badge bg-' + (currentScore.grade === 'A' ? 'success' : 
                                                  ['B', 'C'].includes(currentScore.grade) ? 'warning' : 'danger');
            
            // Update violations
            if (currentScore.critical_violations > 0) {
                document.getElementById('violationsSection').style.display = 'block';
                document.getElementById('criticalViolations').textContent = currentScore.critical_violations;
            }
        }
        
        // Show complete button and alert if inspection is complete
        if (completionStatus.is_complete || completionStatus.completion_percentage >= 100) {
            showCompletionElements();
        }
    }
    
    function updateCategoryProgress() {
        document.querySelectorAll('[data-category-id]').forEach(categoryElement => {
            const categoryId = categoryElement.dataset.categoryId;
            const categoryItems = document.querySelectorAll(`[data-category-id="${categoryId}"]`);
            const completedItems = Array.from(categoryItems).filter(item =>
                item.classList.contains('bg-light')
            ).length;

            const progressBadge = document.getElementById(`category-progress-${categoryId}`);
            if (progressBadge) {
                progressBadge.textContent = `${completedItems}/${categoryItems.length} completed`;
            }
        });
    }

    // Handle complete inspection button click
    document.getElementById('completeInspectionBtn').addEventListener('click', function() {
        if (confirm('Are you sure you want to complete this inspection? This action cannot be undone.')) {
            // Get current score data
            const scoreElement = document.getElementById('currentScore');
            const gradeElement = document.getElementById('currentGrade');

            let scoreData = '100';
            let gradeData = 'A';

            if (scoreElement && scoreElement.textContent) {
                scoreData = scoreElement.textContent.replace('%', '').trim();
            }
            if (gradeElement && gradeElement.textContent) {
                gradeData = gradeElement.textContent.replace('Grade: ', '').trim();
            }

            console.log('Submitting completion with:', {
                score: scoreData,
                grade: gradeData
            });

            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?= BASE_URL ?>inspector/inspection/<?= $inspection['id'] ?>/complete';

            // Add hidden fields for completion data
            const scoreInput = document.createElement('input');
            scoreInput.type = 'hidden';
            scoreInput.name = 'score';
            scoreInput.value = scoreData;
            form.appendChild(scoreInput);

            const gradeInput = document.createElement('input');
            gradeInput.type = 'hidden';
            gradeInput.name = 'compliance_rating';
            gradeInput.value = gradeData;
            form.appendChild(gradeInput);

            // Add findings and recommendations
            const findingsInput = document.createElement('input');
            findingsInput.type = 'hidden';
            findingsInput.name = 'findings';
            findingsInput.value = 'Inspection completed via checklist with ' + scoreData + '% compliance score';
            form.appendChild(findingsInput);

            const recommendationsInput = document.createElement('input');
            recommendationsInput.type = 'hidden';
            recommendationsInput.name = 'recommendations';
            recommendationsInput.value = 'Continue maintaining compliance standards';
            form.appendChild(recommendationsInput);

            document.body.appendChild(form);
            form.submit();
        }
    });
    
    // Initialize category progress
    updateCategoryProgress();

    // Image Viewer Variables
    let currentZoom = 1;
    let isDragging = false;
    let startX, startY, scrollLeft, scrollTop;

    // Handle image viewer button clicks
    document.querySelectorAll('.view-image-btn').forEach(button => {
        button.addEventListener('click', function() {
            const imageSrc = this.dataset.imageSrc;
            const imageName = this.dataset.imageName;

            // Show loading state
            const loader = document.getElementById('imageLoader');
            const modalImage = document.getElementById('modalImage');

            loader.style.display = 'block';
            modalImage.style.display = 'none';
            modalImage.src = ''; // Clear previous image

            // Set image source and title
            document.getElementById('imageViewerModalLabel').innerHTML =
                '<i class="fas fa-image me-2"></i>' + imageName;

            // Reset zoom and container
            currentZoom = 1;
            modalImage.style.transform = 'scale(1)';
            const container = document.getElementById('imageContainer');
            container.scrollLeft = 0;
            container.scrollTop = 0;

            // Show modal first
            const modal = new bootstrap.Modal(document.getElementById('imageViewerModal'));
            modal.show();

            // Load image after modal is shown
            setTimeout(() => {
                modalImage.src = imageSrc;
            }, 100);
        });
    });

    // Zoom Controls
    document.getElementById('zoomInBtn').addEventListener('click', function() {
        currentZoom = Math.min(currentZoom * 1.2, 5); // Max zoom 5x
        document.getElementById('modalImage').style.transform = `scale(${currentZoom})`;
    });

    document.getElementById('zoomOutBtn').addEventListener('click', function() {
        currentZoom = Math.max(currentZoom / 1.2, 0.1); // Min zoom 0.1x
        document.getElementById('modalImage').style.transform = `scale(${currentZoom})`;
    });

    document.getElementById('resetZoomBtn').addEventListener('click', function() {
        currentZoom = 1;
        const modalImage = document.getElementById('modalImage');
        const container = document.getElementById('imageContainer');

        modalImage.style.transform = 'scale(1)';
        container.scrollLeft = 0;
        container.scrollTop = 0;

        // Reset container alignment for normal view
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';
    });

    // Mouse wheel zoom
    document.getElementById('imageContainer').addEventListener('wheel', function(e) {
        e.preventDefault();

        if (e.deltaY < 0) {
            // Zoom in
            currentZoom = Math.min(currentZoom * 1.1, 5);
        } else {
            // Zoom out
            currentZoom = Math.max(currentZoom / 1.1, 0.1);
        }

        document.getElementById('modalImage').style.transform = `scale(${currentZoom})`;
    });

    // Pan functionality
    const imageContainer = document.getElementById('imageContainer');
    const modalImage = document.getElementById('modalImage');

    imageContainer.addEventListener('mousedown', function(e) {
        if (currentZoom > 1) {
            isDragging = true;
            startX = e.pageX - imageContainer.offsetLeft;
            startY = e.pageY - imageContainer.offsetTop;
            scrollLeft = imageContainer.scrollLeft;
            scrollTop = imageContainer.scrollTop;
            imageContainer.style.cursor = 'grabbing';
        }
    });

    imageContainer.addEventListener('mouseleave', function() {
        isDragging = false;
        imageContainer.style.cursor = 'grab';
    });

    imageContainer.addEventListener('mouseup', function() {
        isDragging = false;
        imageContainer.style.cursor = 'grab';
    });

    imageContainer.addEventListener('mousemove', function(e) {
        if (!isDragging) return;
        e.preventDefault();

        const x = e.pageX - imageContainer.offsetLeft;
        const y = e.pageY - imageContainer.offsetTop;
        const walkX = (x - startX) * 2;
        const walkY = (y - startY) * 2;

        imageContainer.scrollLeft = scrollLeft - walkX;
        imageContainer.scrollTop = scrollTop - walkY;
    });

    // Reset zoom when modal is closed
    document.getElementById('imageViewerModal').addEventListener('hidden.bs.modal', function() {
        currentZoom = 1;
        const modalImage = document.getElementById('modalImage');
        const container = document.getElementById('imageContainer');

        modalImage.style.transform = 'scale(1)';
        modalImage.style.display = 'none';
        modalImage.src = '';
        container.scrollLeft = 0;
        container.scrollTop = 0;
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';

        // Hide loader
        document.getElementById('imageLoader').style.display = 'none';
    });
});
</script>

<?php $this->endSection(); ?>
