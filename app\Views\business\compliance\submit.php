<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h4>Submit Compliance Evidence for Business: <?= htmlspecialchars($business['name'] ?? '') ?></h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger">
                            <?= $_SESSION['error'] ?>
                            <?php unset($_SESSION['error']); ?>
                        </div>
                    <?php endif; ?>
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success">
                            <?= $_SESSION['success'] ?>
                            <?php unset($_SESSION['success']); ?>
                        </div>
                    <?php endif; ?>

                    <form action="<?= BASE_URL ?>business/compliance/submit/<?= $business['id'] ?? '' ?>" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="compliance_type" class="form-label">Compliance Type</label>
                                    <select class="form-select" id="compliance_type" name="compliance_type" required>
                                        <option value="">Select Compliance Type</option>
                                        <?php foreach ($compliance_types as $key => $value): ?>
                                            <option value="<?= htmlspecialchars($key) ?>"><?= htmlspecialchars($value) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="evidence_file" class="form-label">Upload Evidence (JPG, JPEG, PNG, PDF)</label>
                                    <input type="file" class="form-control" id="evidence_file" name="evidence_file" accept=".jpg,.jpeg,.png,.pdf" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Submit Evidence</button>
                                <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?? '' ?>" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>
