<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class District extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get barangays in this district
     */
    public function barangays()
    {
        return $this->hasMany(Barangay::class);
    }

    /**
     * Get businesses in this district through barangays
     */
    public function businesses()
    {
        return $this->hasManyThrough(Business::class, Barangay::class);
    }

    /**
     * Get inspector assignments for this district
     */
    public function inspectorAssignments()
    {
        return $this->hasMany(InspectorDistrictAssignment::class);
    }

    /**
     * Get assigned inspectors
     */
    public function assignedInspectors()
    {
        return $this->belongsToMany(User::class, 'inspector_district_assignments', 'district_id', 'inspector_id')
                    ->where('is_active', true);
    }
}
