<?php
namespace App\Models;

use App\Config\Database;
use PDO;

class BusinessCategory {
    private $db;
    private $table = 'business_categories';

    public function __construct() {
        $this->db = (new Database())->getConnection();
    }

    public function getAll() {
        $query = "SELECT * FROM {$this->table} ORDER BY name";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}