<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-map-marker-alt text-primary me-2"></i>
            <?= htmlspecialchars($district['name']) ?> - Inspector Assignments
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspector-assignments/assign/<?= $district['id'] ?>" class="btn btn-success">
                <i class="fas fa-user-plus"></i> Assign Inspectors
            </a>
            <a href="<?= BASE_URL ?>admin/inspector-assignments" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Assignments
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- District Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>District Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted">District Name</h6>
                        <p class="mb-0"><?= htmlspecialchars($district['name']) ?></p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">District ID</h6>
                        <p class="mb-0"><code><?= htmlspecialchars($district['id']) ?></code></p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Assigned Inspectors</h6>
                        <p class="mb-0">
                            <span class="badge bg-primary fs-6"><?= count($inspectors) ?></span>
                        </p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Businesses in District</h6>
                        <p class="mb-0">
                            <span class="badge bg-info fs-6"><?= count($businesses) ?></span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/inspector-assignments/assign/<?= $district['id'] ?>" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> Assign More Inspectors
                        </a>
                        <a href="<?= BASE_URL ?>admin/inspections?district=<?= $district['id'] ?>" class="btn btn-primary">
                            <i class="fas fa-clipboard-list"></i> View District Inspections
                        </a>
                        <a href="<?= BASE_URL ?>admin/businesses?district=<?= $district['id'] ?>" class="btn btn-info">
                            <i class="fas fa-building"></i> View District Businesses
                        </a>
                        <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#bulkScheduleModal">
                            <i class="fas fa-calendar-plus"></i> Schedule Inspections
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assigned Inspectors -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>Assigned Inspectors
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($inspectors)): ?>
                        <div class="row">
                            <?php foreach ($inspectors as $inspector): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card border-left-success h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h5 class="card-title text-success mb-1">
                                                        <i class="fas fa-user-check me-2"></i>
                                                        <?= htmlspecialchars($inspector['full_name'] ?? $inspector['inspector_name'] ?? 'Unknown Inspector') ?>
                                                    </h5>
                                                    <p class="text-muted mb-0">
                                                        <?= htmlspecialchars($inspector['email'] ?? $inspector['inspector_email'] ?? 'No Email') ?>
                                                    </p>
                                                </div>
                                                <span class="badge bg-success">Active</span>
                                            </div>

                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    <strong>Assigned by:</strong> <?= htmlspecialchars($inspector['assigned_by_name']) ?>
                                                </small>
                                                <br>
                                                <small class="text-muted">
                                                    <strong>Date:</strong> <?= date('M d, Y', strtotime($inspector['assigned_at'])) ?>
                                                </small>
                                            </div>

                                            <?php if ($inspector['notes']): ?>
                                                <div class="mb-3">
                                                    <h6 class="text-muted">Assignment Notes:</h6>
                                                    <p class="text-secondary mb-0">
                                                        <i class="fas fa-sticky-note text-info me-1"></i>
                                                        <?= htmlspecialchars($inspector['notes']) ?>
                                                    </p>
                                                </div>
                                            <?php endif; ?>

                                            <div class="d-flex gap-2">
                                                <a href="<?= BASE_URL ?>admin/inspector-assignments/inspector/<?= $inspector['inspector_id'] ?>" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> View Inspector
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="removeInspector('<?= $inspector['inspector_id'] ?>', '<?= htmlspecialchars($inspector['full_name'] ?? $inspector['inspector_name'] ?? 'Unknown Inspector') ?>')"
                                                        title="Remove from district">
                                                    <i class="fas fa-times"></i> Remove
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-3">No Inspectors Assigned</h5>
                            <p class="text-muted mb-3">
                                This district doesn't have any inspectors assigned yet.
                            </p>
                            <a href="<?= BASE_URL ?>admin/inspector-assignments/assign/<?= $district['id'] ?>" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Assign Inspectors
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Businesses in District -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-building me-2"></i>Businesses in District
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($businesses)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Business Name</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($businesses as $business): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($business['name']) ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($business['address'] ?? '') ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= htmlspecialchars($business['category_name'] ?? 'N/A') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $business['status'] === 'approved' ? 'success' : ($business['status'] === 'pending' ? 'warning' : 'danger') ?>">
                                                    <?= ucfirst($business['status'] ?? 'unknown') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= BASE_URL ?>admin/businesses/view/<?= $business['id'] ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-building fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No businesses found in this district.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Schedule Inspections Modal -->
<div class="modal fade" id="bulkScheduleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-plus me-2"></i>Schedule Inspections for <?= htmlspecialchars($district['name']) ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= BASE_URL ?>admin/inspector-assignments/bulk-schedule" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="district_id" value="<?= $district['id'] ?>">

                    <!-- Inspection Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="inspection_type" class="form-label">Inspection Type</label>
                            <select class="form-select" name="inspection_type" id="inspection_type" required>
                                <option value="routine">Routine Inspection</option>
                                <option value="follow_up">Follow-up Inspection</option>
                                <option value="complaint">Complaint Investigation</option>
                                <option value="emergency">Emergency Inspection</option>
                                <option value="annual">Annual Compliance Review</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="scheduled_date" class="form-label">Scheduled Date</label>
                            <input type="datetime-local" class="form-control" name="scheduled_date" id="scheduled_date" required>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <label for="bulk_notes" class="form-label">Inspection Notes</label>
                            <textarea class="form-control" name="bulk_notes" id="bulk_notes" rows="3"
                                      placeholder="Enter notes that will apply to all scheduled inspections..."></textarea>
                        </div>
                    </div>

                    <!-- Assignment Strategy -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <label class="form-label">Assignment Strategy</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="assignment_strategy" id="auto_assign" value="auto" checked>
                                <label class="form-check-label" for="auto_assign">
                                    <strong>Auto-assign</strong> - Distribute evenly among available inspectors
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="assignment_strategy" id="manual_assign" value="manual">
                                <label class="form-check-label" for="manual_assign">
                                    <strong>Manual assignment</strong> - Choose specific inspector for each business
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Business Selection -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-building me-2"></i>Select Businesses for Inspection
                                </h6>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllBusinesses()">
                                        <i class="fas fa-check-square me-1"></i>Select All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllBusinesses()">
                                        <i class="fas fa-square me-1"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <?php if (!empty($businesses)): ?>
                                <div class="row">
                                    <?php foreach ($businesses as $business): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card border">
                                                <div class="card-body py-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input business-checkbox" type="checkbox"
                                                               name="business_ids[]" value="<?= $business['id'] ?>"
                                                               id="business_<?= $business['id'] ?>">
                                                        <label class="form-check-label w-100" for="business_<?= $business['id'] ?>">
                                                            <div class="d-flex justify-content-between align-items-start">
                                                                <div>
                                                                    <strong><?= htmlspecialchars($business['name']) ?></strong>
                                                                    <br><small class="text-muted"><?= htmlspecialchars($business['address'] ?? '') ?></small>
                                                                    <br><span class="badge bg-secondary"><?= htmlspecialchars($business['category_name'] ?? 'N/A') ?></span>
                                                                </div>
                                                                <span class="badge bg-<?= $business['status'] === 'approved' ? 'success' : 'warning' ?>">
                                                                    <?= ucfirst($business['status'] ?? 'unknown') ?>
                                                                </span>
                                                            </div>

                                                            <!-- Manual Inspector Assignment (hidden by default) -->
                                                            <div class="manual-assignment mt-2" style="display: none;">
                                                                <select class="form-select form-select-sm" name="inspector_assignments[<?= $business['id'] ?>]">
                                                                    <option value="">Auto-assign</option>
                                                                    <?php foreach ($inspectors as $inspector): ?>
                                                                        <option value="<?= $inspector['inspector_id'] ?>">
                                                                            <?= htmlspecialchars($inspector['full_name'] ?? $inspector['inspector_name'] ?? 'Unknown Inspector') ?>
                                                                        </option>
                                                                    <?php endforeach; ?>
                                                                </select>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-building fa-2x text-muted mb-3"></i>
                                    <p class="text-muted mb-0">No businesses found in this district.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Inspector Availability -->
                    <?php if (!empty($inspectors)): ?>
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2"></i>Available Inspectors (<?= count($inspectors) ?>)
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($inspectors as $inspector): ?>
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-success-subtle rounded-circle me-2">
                                                    <span class="avatar-title text-success">
                                                        <?= strtoupper(substr($inspector['full_name'] ?? $inspector['inspector_name'] ?? 'U', 0, 2)) ?>
                                                    </span>
                                                </div>
                                                <div>
                                                    <small class="fw-bold"><?= htmlspecialchars($inspector['full_name'] ?? $inspector['inspector_name'] ?? 'Unknown') ?></small>
                                                    <br><small class="text-muted">Available</small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>No inspectors assigned to this district!</strong>
                            Please <a href="<?= BASE_URL ?>admin/inspector-assignments/assign/<?= $district['id'] ?>">assign inspectors</a> before scheduling inspections.
                        </div>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" <?= empty($inspectors) ? 'disabled' : '' ?>>
                        <i class="fas fa-calendar-plus me-1"></i>Schedule Selected Inspections
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Remove Inspector Modal -->
<div class="modal fade" id="removeInspectorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Inspector</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove <strong id="removeInspectorName"></strong> from <?= htmlspecialchars($district['name']) ?>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="removeInspectorForm" method="POST" action="<?= BASE_URL ?>admin/inspector-assignments/remove" style="display: inline;">
                    <input type="hidden" name="inspector_id" id="removeInspectorIdInput">
                    <input type="hidden" name="district_id" value="<?= $district['id'] ?>">
                    <button type="submit" class="btn btn-danger">Remove Inspector</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function removeInspector(inspectorId, inspectorName) {
    document.getElementById('removeInspectorName').textContent = inspectorName;
    document.getElementById('removeInspectorIdInput').value = inspectorId;

    new bootstrap.Modal(document.getElementById('removeInspectorModal')).show();
}

// Bulk scheduling functions
function selectAllBusinesses() {
    const checkboxes = document.querySelectorAll('.business-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function clearAllBusinesses() {
    const checkboxes = document.querySelectorAll('.business-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Toggle manual assignment visibility
document.addEventListener('DOMContentLoaded', function() {
    const autoAssign = document.getElementById('auto_assign');
    const manualAssign = document.getElementById('manual_assign');
    const manualAssignments = document.querySelectorAll('.manual-assignment');

    function toggleManualAssignments() {
        const showManual = manualAssign.checked;
        manualAssignments.forEach(element => {
            element.style.display = showManual ? 'block' : 'none';
        });
    }

    autoAssign.addEventListener('change', toggleManualAssignments);
    manualAssign.addEventListener('change', toggleManualAssignments);

    // Set minimum date to today
    const dateInput = document.getElementById('scheduled_date');
    if (dateInput) {
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        dateInput.min = now.toISOString().slice(0, 16);
    }

    // Form validation
    const form = document.querySelector('#bulkScheduleModal form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const checkedBusinesses = document.querySelectorAll('.business-checkbox:checked');
            if (checkedBusinesses.length === 0) {
                e.preventDefault();
                alert('Please select at least one business for inspection.');
                return false;
            }

            const scheduledDate = document.getElementById('scheduled_date').value;
            if (!scheduledDate) {
                e.preventDefault();
                alert('Please select a scheduled date.');
                return false;
            }

            // Confirm bulk scheduling
            const confirmMessage = `Are you sure you want to schedule ${checkedBusinesses.length} inspection(s) for ${new Date(scheduledDate).toLocaleDateString()}?`;
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-title {
    font-size: 12px;
    font-weight: 600;
}

.form-check-label {
    cursor: pointer;
}

.card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.manual-assignment {
    border-top: 1px solid #dee2e6;
    padding-top: 8px;
}
</style>
<?php $this->endSection() ?>
