// Sidebar toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // Sidebar Toggle
    const toggleSidebar = document.getElementById('toggleSidebar');
    const wrapper = document.querySelector('.wrapper');
    
    if (toggleSidebar) {
        toggleSidebar.addEventListener('click', function() {
            wrapper.classList.toggle('sidebar-collapsed');
        });
    }

    // Initialize DataTables
    const tables = document.querySelectorAll('.datatable');
    tables.forEach(table => {
        new DataTable(table, {
            responsive: true,
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search..."
            }
        });
    });

    // Initialize Tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // File Input Custom Text
    document.querySelectorAll('.custom-file-input').forEach(input => {
        input.addEventListener('change', function(e) {
            let fileName = e.target.files[0].name;
            let nextSibling = e.target.nextElementSibling;
            nextSibling.innerText = fileName;
        });
    });

    // Alert Auto-close
    document.querySelectorAll('.alert').forEach(alert => {
        if (!alert.classList.contains('alert-permanent')) {
            setTimeout(() => {
                alert.classList.add('fade');
                setTimeout(() => alert.remove(), 150);
            }, 3000);
        }
    });

    // Form Validation
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Active Link Highlighting
    const currentPath = window.location.pathname;
    document.querySelectorAll('.sidebar-nav a').forEach(link => {
        if (currentPath.includes(link.getAttribute('href'))) {
            link.parentElement.classList.add('active');
        }
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        if (window.innerWidth <= 991.98) {
            if (!wrapper.contains(event.target) && !toggleSidebar.contains(event.target) && wrapper.classList.contains('sidebar-collapsed')) {
                wrapper.classList.remove('sidebar-collapsed');
            }
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 991.98) {
            wrapper.classList.remove('sidebar-collapsed');
        }
    });
}); 