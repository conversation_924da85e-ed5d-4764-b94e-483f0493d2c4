<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\BusinessController as AdminBusinessController;
use App\Http\Controllers\Admin\InspectionController as AdminInspectionController;
use App\Http\Controllers\Admin\InspectorController as AdminInspectorController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\ChecklistController as AdminChecklistController;
use App\Http\Controllers\Admin\ChatController as AdminChatController;
use App\Http\Controllers\Admin\SettingsController as AdminSettingsController;
use App\Http\Controllers\Admin\HomepageController as AdminHomepageController;
use App\Http\Controllers\Admin\ComplianceEvidenceController as AdminComplianceEvidenceController;
use App\Http\Controllers\Admin\InspectorAssignmentController;
use App\Http\Controllers\Inspector\InspectorController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\Inspector\DashboardController as InspectorDashboardController;
use App\Http\Controllers\Inspector\InspectionController as InspectorInspectionController;
use App\Http\Controllers\Inspector\BusinessController as InspectorBusinessController;
use App\Http\Controllers\Inspector\ChatController as InspectorChatController;
use App\Http\Controllers\BusinessOwner\DashboardController as BusinessOwnerDashboardController;
use App\Http\Controllers\BusinessOwner\BusinessController as BusinessOwnerBusinessController;
use App\Http\Controllers\BusinessOwner\InspectionController as BusinessOwnerInspectionController;
use App\Http\Controllers\BusinessOwner\ChecklistController as BusinessOwnerChecklistController;
use App\Http\Controllers\BusinessOwner\ChatController as BusinessOwnerChatController;
use App\Http\Controllers\PublicController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\ChatbotController;
use App\Http\Controllers\NotificationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', [PublicController::class, 'home'])->name('home');
Route::get('/about', [PublicController::class, 'about'])->name('about');
Route::get('/contact', [PublicController::class, 'contact'])->name('contact');
Route::post('/contact', [PublicController::class, 'submitContact'])->name('contact.submit');
Route::get('/services', [PublicController::class, 'services'])->name('services');
Route::get('/business-registration', [PublicController::class, 'businessRegistration'])->name('business-registration');
Route::post('/business-registration', [PublicController::class, 'submitBusinessRegistration'])->name('business-registration.submit');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('password.email');
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Chat routes (accessible to all authenticated users)
Route::middleware('auth')->prefix('chat')->name('chat.')->group(function () {
    Route::get('/', [ChatController::class, 'index'])->name('index');
    Route::get('/room/{chatRoom}', [ChatController::class, 'room'])->name('room');
    Route::post('/room/{chatRoom}/message', [ChatController::class, 'sendMessage'])->name('send-message');
    Route::get('/room/{chatRoom}/messages', [ChatController::class, 'getMessages'])->name('get-messages');
    Route::post('/create-room', [ChatController::class, 'createOrGetRoom'])->name('create-room');
    Route::post('/room/{chatRoom}/close', [ChatController::class, 'closeRoom'])->name('close-room');
    Route::post('/room/{chatRoom}/assign-inspector', [ChatController::class, 'assignInspector'])->name('assign-inspector');
});

// Chatbot routes
Route::middleware('auth')->prefix('chatbot')->name('chatbot.')->group(function () {
    Route::post('/room/{chatRoom}/process', [ChatbotController::class, 'processMessage'])->name('process');
    Route::post('/room/{chatRoom}/escalate', [ChatbotController::class, 'escalateToHuman'])->name('escalate');
    Route::get('/room/{chatRoom}/status', [ChatbotController::class, 'getStatus'])->name('status');
});

// Notification routes
Route::middleware('auth')->prefix('notifications')->name('notifications.')->group(function () {
    Route::get('/', [NotificationController::class, 'index'])->name('index');
    Route::get('/count', [NotificationController::class, 'getUnreadCount'])->name('count');
    Route::get('/recent', [NotificationController::class, 'getRecent'])->name('recent');
    Route::post('/{notification}/read', [NotificationController::class, 'markAsRead'])->name('mark-read');
    Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
    Route::delete('/{notification}', [NotificationController::class, 'destroy'])->name('destroy');
    Route::delete('/read/all', [NotificationController::class, 'deleteAllRead'])->name('delete-all-read');
});

// File serving routes
Route::middleware('auth')->prefix('files')->name('files.')->group(function () {
    Route::get('/evidence/{filename}', [FileController::class, 'serveEvidence'])->name('serve-evidence');
    Route::get('/inspection-evidence/{filename}', [FileController::class, 'serveInspectionEvidence'])->name('serve-inspection-evidence');
    Route::get('/homepage-image/{filename}', [FileController::class, 'serveHomepageImage'])->name('serve-homepage-image');
    Route::get('/announcement-image/{filename}', [FileController::class, 'serveAnnouncementImage'])->name('serve-announcement-image');
    Route::get('/website-image/{type}/{filename}', [FileController::class, 'serveWebsiteImage'])->name('serve-website-image');
    Route::get('/download/evidence/{evidence}', [FileController::class, 'downloadEvidence'])->name('download-evidence');
    Route::get('/download/inspection-evidence/{response}', [FileController::class, 'downloadInspectionEvidence'])->name('download-inspection-evidence');
    Route::post('/info', [FileController::class, 'getFileInfo'])->name('info');
    Route::post('/validate', [FileController::class, 'validateUpload'])->name('validate');
    Route::post('/cleanup', [FileController::class, 'cleanupOrphanedFiles'])->name('cleanup');
});

// API routes for AJAX
Route::prefix('api')->middleware('auth')->group(function () {
    Route::get('/auth/check', [AuthController::class, 'checkAuth']);
    Route::get('/districts/{district}/barangays', [AdminBusinessController::class, 'getBarangays']);
});

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [AdminDashboardController::class, 'getData'])->name('dashboard.data');
    
    // User Management
    Route::resource('users', AdminUserController::class);
    Route::post('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])->name('users.toggle-status');
    
    // Business Management
    Route::resource('businesses', AdminBusinessController::class);
    Route::get('businesses/export', [AdminBusinessController::class, 'export'])->name('businesses.export');
    
    // Inspector Management
    Route::resource('inspectors', AdminInspectorController::class);
    Route::get('inspectors/{inspector}/assignments', [AdminInspectorController::class, 'assignments'])->name('inspectors.assignments');
    Route::post('inspectors/{inspector}/assign-district', [AdminInspectorController::class, 'assignDistrict'])->name('inspectors.assign-district');
    Route::post('inspectors/{inspector}/assign-barangay', [AdminInspectorController::class, 'assignBarangay'])->name('inspectors.assign-barangay');
    Route::delete('inspector-assignments/{assignment}', [AdminInspectorController::class, 'removeAssignment'])->name('inspector-assignments.destroy');
    
    // Inspection Management
    Route::resource('inspections', AdminInspectionController::class);
    Route::get('inspections/calendar', [AdminInspectionController::class, 'calendar'])->name('inspections.calendar');
    Route::post('inspections/{inspection}/verify', [AdminInspectionController::class, 'verify'])->name('inspections.verify');
    Route::post('inspections/{inspection}/reject', [AdminInspectionController::class, 'reject'])->name('inspections.reject');
    Route::get('pending-verification', [AdminInspectionController::class, 'pendingVerification'])->name('inspections.pending-verification');
    Route::get('inspection-results', [AdminInspectionController::class, 'results'])->name('inspections.results');
    Route::get('inspection-assignments', [AdminInspectionController::class, 'assignments'])->name('inspections.assignments');
    Route::get('inspection-assignments/integrated', [AdminInspectionController::class, 'integratedAssignments'])->name('inspections.assignments.integrated');
    
    // Checklist Management
    Route::resource('checklist-categories', AdminChecklistController::class);
    Route::resource('checklist-items', AdminChecklistController::class, ['except' => ['index', 'show']]);
    Route::get('checklist', [AdminChecklistController::class, 'index'])->name('checklist.index');
    Route::get('checklist/{category}', [AdminChecklistController::class, 'show'])->name('checklist.show');
    Route::post('checklist-items/{item}/toggle-status', [AdminChecklistController::class, 'toggleItemStatus'])->name('checklist-items.toggle-status');
    
    // Chat Management
    Route::get('chat', [AdminChatController::class, 'index'])->name('chat.index');
    Route::get('chat/{chatRoom}', [AdminChatController::class, 'show'])->name('chat.show');
    Route::post('chat/{chatRoom}/message', [AdminChatController::class, 'sendMessage'])->name('chat.send-message');
    Route::get('chat/{chatRoom}/messages', [AdminChatController::class, 'getMessages'])->name('chat.messages');
    
    // Settings
    Route::get('settings', [AdminSettingsController::class, 'index'])->name('settings.index');
    Route::post('settings/update-general', [AdminSettingsController::class, 'updateGeneral'])->name('settings.update-general');
    Route::post('settings/update-colors', [AdminSettingsController::class, 'updateColors'])->name('settings.update-colors');
    Route::post('settings/apply-color-scheme', [AdminSettingsController::class, 'applyColorScheme'])->name('settings.apply-color-scheme');
    Route::post('settings/upload-image', [AdminSettingsController::class, 'uploadImage'])->name('settings.upload-image');
    Route::post('settings/reset-defaults', [AdminSettingsController::class, 'resetToDefaults'])->name('settings.reset-defaults');
    Route::post('settings/clear-cache', [AdminSettingsController::class, 'clearCache'])->name('settings.clear-cache');

    // Homepage content management
    Route::prefix('homepage')->name('homepage.')->group(function () {
        Route::get('/', [AdminHomepageController::class, 'index'])->name('index');

        // Content sections
        Route::get('/content/create', [AdminHomepageController::class, 'createContent'])->name('create-content');
        Route::post('/content', [AdminHomepageController::class, 'storeContent'])->name('store-content');
        Route::get('/content/{content}/edit', [AdminHomepageController::class, 'editContent'])->name('edit-content');
        Route::put('/content/{content}', [AdminHomepageController::class, 'updateContent'])->name('update-content');
        Route::delete('/content/{content}', [AdminHomepageController::class, 'destroyContent'])->name('destroy-content');
        Route::post('/content/bulk-actions', [AdminHomepageController::class, 'bulkContentActions'])->name('bulk-content-actions');
        Route::post('/content/reorder', [AdminHomepageController::class, 'reorderContent'])->name('reorder-content');

        // Announcements
        Route::get('/announcements', [AdminHomepageController::class, 'announcements'])->name('announcements');
        Route::get('/announcements/create', [AdminHomepageController::class, 'createAnnouncement'])->name('create-announcement');
        Route::post('/announcements', [AdminHomepageController::class, 'storeAnnouncement'])->name('store-announcement');
        Route::get('/announcements/{announcement}/edit', [AdminHomepageController::class, 'editAnnouncement'])->name('edit-announcement');
        Route::put('/announcements/{announcement}', [AdminHomepageController::class, 'updateAnnouncement'])->name('update-announcement');
        Route::delete('/announcements/{announcement}', [AdminHomepageController::class, 'destroyAnnouncement'])->name('destroy-announcement');

        // Contact messages
        Route::get('/contact-messages', [AdminHomepageController::class, 'contactMessages'])->name('contact-messages');
        Route::post('/contact-messages/{message}/read', [AdminHomepageController::class, 'markMessageRead'])->name('mark-message-read');
        Route::post('/contact-messages/{message}/replied', [AdminHomepageController::class, 'markMessageReplied'])->name('mark-message-replied');
        Route::delete('/contact-messages/{message}', [AdminHomepageController::class, 'destroyContactMessage'])->name('destroy-contact-message');
    });

    // Compliance evidence management
    Route::prefix('compliance-evidence')->name('compliance-evidence.')->group(function () {
        Route::get('/', [AdminComplianceEvidenceController::class, 'index'])->name('index');
        Route::get('/business/{business}', [AdminComplianceEvidenceController::class, 'businessEvidence'])->name('business');
        Route::get('/{evidence}', [AdminComplianceEvidenceController::class, 'show'])->name('show');
        Route::post('/{evidence}/verify', [AdminComplianceEvidenceController::class, 'verify'])->name('verify');
        Route::post('/bulk-verify', [AdminComplianceEvidenceController::class, 'bulkVerify'])->name('bulk-verify');
        Route::delete('/{evidence}', [AdminComplianceEvidenceController::class, 'destroy'])->name('destroy');
        Route::get('/{evidence}/download', [AdminComplianceEvidenceController::class, 'download'])->name('download');
        Route::get('/reports/compliance', [AdminComplianceEvidenceController::class, 'complianceReport'])->name('compliance-report');
        Route::post('/export/compliance', [AdminComplianceEvidenceController::class, 'exportCompliance'])->name('export-compliance');
    });

    // Inspector assignment management
    Route::prefix('inspector-assignments')->name('inspector-assignments.')->group(function () {
        Route::get('/', [InspectorAssignmentController::class, 'index'])->name('index');
        Route::get('/integrated', [InspectorAssignmentController::class, 'integratedView'])->name('integrated');
        Route::get('/district/{district}/assign', [InspectorAssignmentController::class, 'assignToDistrict'])->name('assign-district');
        Route::post('/district/{district}/assign', [InspectorAssignmentController::class, 'assignToDistrict'])->name('store-district-assignment');
        Route::post('/remove-assignment', [InspectorAssignmentController::class, 'removeFromDistrict'])->name('remove-assignment');
        Route::get('/district/{district}', [InspectorAssignmentController::class, 'viewDistrict'])->name('view-district');
        Route::get('/inspector/{inspector}', [InspectorAssignmentController::class, 'viewInspector'])->name('view-inspector');
        Route::post('/schedule-inspection', [InspectorAssignmentController::class, 'scheduleInspection'])->name('schedule-inspection');
        Route::post('/bulk-schedule', [InspectorAssignmentController::class, 'bulkScheduleInspections'])->name('bulk-schedule');
        Route::post('/schedule-barangay', [InspectorAssignmentController::class, 'scheduleBarangayInspections'])->name('schedule-barangay');
        Route::get('/barangay/{barangay}/businesses', [InspectorAssignmentController::class, 'getBarangayBusinesses'])->name('barangay-businesses');
        Route::post('/check-conflicts', [InspectorAssignmentController::class, 'checkConflicts'])->name('check-conflicts');
        Route::get('/inspections', [InspectorAssignmentController::class, 'inspectionAssignments'])->name('inspections');
        Route::post('/reassign-inspection', [InspectorAssignmentController::class, 'reassignInspection'])->name('reassign-inspection');
    });
});

// Inspector routes
Route::prefix('inspector')->name('inspector.')->middleware(['auth', 'role:inspector'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [InspectorController::class, 'dashboard'])->name('dashboard');

    // Inspection Management
    Route::get('inspections', [InspectorController::class, 'inspections'])->name('inspections.index');
    Route::get('inspections/{inspection}', [InspectorController::class, 'inspection'])->name('inspections.show');
    Route::get('inspections/{inspection}/checklist', [InspectorController::class, 'startInspectionChecklist'])->name('inspections.checklist');
    Route::post('inspections/{inspection}/start', [InspectorController::class, 'startInspection'])->name('inspections.start');
    Route::post('inspections/{inspection}/confirm', [InspectorController::class, 'confirmInspection'])->name('inspections.confirm');
    Route::post('inspections/{inspection}/cancel', [InspectorController::class, 'cancelInspection'])->name('inspections.cancel');
    Route::post('inspections/{inspection}/complete', [InspectorController::class, 'completeInspection'])->name('inspections.complete');
    Route::get('inspections/{inspection}/report', [InspectorController::class, 'inspectionReport'])->name('inspections.report');
    Route::post('checklist/save-response', [InspectorController::class, 'saveChecklistResponse'])->name('checklist.save-response');

    // Profile and Settings
    Route::get('profile', [InspectorController::class, 'profile'])->name('profile');
    Route::put('profile', [InspectorController::class, 'updateProfile'])->name('profile.update');
    Route::get('settings', [InspectorController::class, 'settings'])->name('settings');
    Route::put('settings', [InspectorController::class, 'updateSettings'])->name('settings.update');
});

// Business Owner routes
Route::prefix('business-owner')->name('business-owner.')->middleware(['auth', 'role:business_owner'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [BusinessOwnerBusinessController::class, 'dashboard'])->name('dashboard');

    // Business Management
    Route::get('business', [BusinessOwnerBusinessController::class, 'index'])->name('business.index');
    Route::get('business/create', [BusinessOwnerBusinessController::class, 'create'])->name('business.create');
    Route::post('business', [BusinessOwnerBusinessController::class, 'store'])->name('business.store');
    Route::get('business/{business}', [BusinessOwnerBusinessController::class, 'view'])->name('business.view');
    Route::get('business/profile', [BusinessOwnerBusinessController::class, 'profile'])->name('business.profile');
    Route::get('business/edit', [BusinessOwnerBusinessController::class, 'edit'])->name('business.edit');
    Route::put('business', [BusinessOwnerBusinessController::class, 'update'])->name('business.update');
    Route::get('business/settings', [BusinessOwnerBusinessController::class, 'settings'])->name('business.settings');
    Route::put('business/settings', [BusinessOwnerBusinessController::class, 'updateSettings'])->name('business.settings.update');

    // Inspection Management
    Route::get('inspections', [BusinessOwnerBusinessController::class, 'inspections'])->name('inspections.index');
    Route::get('inspections/{inspection}', [BusinessOwnerBusinessController::class, 'inspectionReport'])->name('inspections.show');

    // Checklist & Evidence
    Route::get('checklist', [BusinessOwnerBusinessController::class, 'checklist'])->name('checklist.index');
    Route::post('checklist/{item}/evidence', [BusinessOwnerBusinessController::class, 'uploadEvidence'])->name('checklist.upload-evidence');
    Route::delete('evidence/{evidence}', [BusinessOwnerBusinessController::class, 'deleteEvidence'])->name('evidence.delete');
    Route::get('evidence/{business}/{checklistItem}', [BusinessOwnerBusinessController::class, 'viewEvidence'])->name('evidence.view');

    // Compliance
    Route::get('compliance', [BusinessOwnerBusinessController::class, 'compliance'])->name('compliance');
});
