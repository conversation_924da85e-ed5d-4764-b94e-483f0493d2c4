<?php $this->extend('layouts/app'); ?>

<?php $this->section('styles'); ?>
<style>
.bg-gradient-primary {
    background: linear-gradient(45deg, #4e73df, #224abe);
}
.bg-gradient-warning {
    background: linear-gradient(45deg, #f6c23e, #dda20a);
}
.bg-gradient-info {
    background: linear-gradient(45deg, #36b9cc, #258391);
}
.bg-gradient-success {
    background: linear-gradient(45deg, #1cc88a, #13855c);
}
.card-body h3 {
    font-weight: 600;
}
.border-3 {
    border-width: 3px !important;
}
</style>
<?php $this->endSection(); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-clipboard-list"></i> My Inspections & Schedule
            </h1>
            <p class="text-muted mb-0">Manage your assigned inspections and view your schedule</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#helpModal">
                <i class="fas fa-question-circle"></i> Help
            </button>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel">
                        <i class="fas fa-info-circle"></i> How to Use Your Inspection Dashboard
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-play-circle text-primary"></i> Workflow Steps:</h6>
                            <ol>
                                <li><strong>Confirm</strong> scheduled inspections</li>
                                <li><strong>Start</strong> inspection checklist</li>
                                <li><strong>Complete</strong> the inspection</li>
                                <li><strong>View</strong> reports and scores</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-palette text-success"></i> Status Colors:</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-warning">Scheduled</span> - Waiting for confirmation</li>
                                <li><span class="badge bg-secondary">Confirmed</span> - Ready to start</li>
                                <li><span class="badge bg-info">In Progress</span> - Currently inspecting</li>
                                <li><span class="badge bg-success">Completed</span> - Finished</li>
                                <li><span class="badge bg-danger">Cancelled</span> - Cancelled</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Got it!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Inspection Overview Dashboard -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <!-- Main Statistics Cards -->
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white mb-3 shadow-sm">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?= $stats['total'] ?></h3>
                            <div class="small">Total Inspections</div>
                            <i class="fas fa-clipboard-list fa-2x mt-2 opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white mb-3 shadow-sm">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?= $stats['scheduled'] + $stats['confirmed'] ?></h3>
                            <div class="small">Upcoming</div>
                            <i class="fas fa-calendar-alt fa-2x mt-2 opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white mb-3 shadow-sm">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?= $stats['in_progress'] ?></h3>
                            <div class="small">In Progress</div>
                            <i class="fas fa-play-circle fa-2x mt-2 opacity-75"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white mb-3 shadow-sm">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?= $stats['completed'] ?></h3>
                            <div class="small">Completed</div>
                            <i class="fas fa-check-circle fa-2x mt-2 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status Breakdown -->
            <div class="card shadow-sm h-100">
                <div class="card-header py-2">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-1"></i>Status Breakdown
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-2">
                            <div class="border-start border-warning border-3 ps-2">
                                <h5 class="text-warning mb-0"><?= $stats['scheduled'] ?></h5>
                                <small class="text-muted">Scheduled</small>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="border-start border-secondary border-3 ps-2">
                                <h5 class="text-secondary mb-0"><?= $stats['confirmed'] ?></h5>
                                <small class="text-muted">Confirmed</small>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="border-start border-danger border-3 ps-2">
                                <h5 class="text-danger mb-0"><?= $stats['cancelled'] ?></h5>
                                <small class="text-muted">Cancelled</small>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="border-start border-dark border-3 ps-2">
                                <h5 class="text-dark mb-0"><?= $stats['rejected'] ?></h5>
                                <small class="text-muted">Rejected</small>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="border-start border-primary border-3 ps-2">
                                <h5 class="text-primary mb-0"><?= isset($stats['overdue']) ? $stats['overdue'] : 0 ?></h5>
                                <small class="text-muted">Overdue</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Smart Filters & Quick Actions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>Filter & Quick Actions
                </h6>
                <?php if ($current_status_filter || $current_district_filter): ?>
                    <span class="badge bg-info">
                        <i class="fas fa-filter"></i> Filters Active
                    </span>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <!-- Quick Filter Buttons -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex flex-wrap gap-2" role="group" aria-label="Quick filters">
                        <a href="<?= BASE_URL ?>inspector/inspections"
                           class="btn btn-<?= !$current_status_filter ? 'primary' : 'outline-primary' ?> btn-sm">
                            <i class="fas fa-list"></i> All (<?= $stats['total'] ?>)
                        </a>
                        <a href="<?= BASE_URL ?>inspector/inspections?status=scheduled"
                           class="btn btn-<?= $current_status_filter === 'scheduled' ? 'warning' : 'outline-warning' ?> btn-sm">
                            <i class="fas fa-calendar-check"></i> Scheduled (<?= $stats['scheduled'] ?>)
                        </a>
                        <a href="<?= BASE_URL ?>inspector/inspections?status=confirmed"
                           class="btn btn-<?= $current_status_filter === 'confirmed' ? 'secondary' : 'outline-secondary' ?> btn-sm">
                            <i class="fas fa-thumbs-up"></i> Confirmed (<?= $stats['confirmed'] ?>)
                        </a>
                        <a href="<?= BASE_URL ?>inspector/inspections?status=in_progress"
                           class="btn btn-<?= $current_status_filter === 'in_progress' ? 'info' : 'outline-info' ?> btn-sm">
                            <i class="fas fa-play-circle"></i> In Progress (<?= $stats['in_progress'] ?>)
                        </a>
                        <a href="<?= BASE_URL ?>inspector/inspections?status=completed"
                           class="btn btn-<?= $current_status_filter === 'completed' ? 'success' : 'outline-success' ?> btn-sm">
                            <i class="fas fa-check-circle"></i> Completed (<?= $stats['completed'] ?>)
                        </a>
                        <?php if ($stats['rejected'] > 0): ?>
                        <a href="<?= BASE_URL ?>inspector/inspections?status=rejected"
                           class="btn btn-<?= $current_status_filter === 'rejected' ? 'dark' : 'outline-dark' ?> btn-sm">
                            <i class="fas fa-ban"></i> Rejected (<?= $stats['rejected'] ?>)
                        </a>
                        <?php endif; ?>
                        <?php if ($stats['cancelled'] > 0): ?>
                        <a href="<?= BASE_URL ?>inspector/inspections?status=cancelled"
                           class="btn btn-<?= $current_status_filter === 'cancelled' ? 'danger' : 'outline-danger' ?> btn-sm">
                            <i class="fas fa-times-circle"></i> Cancelled (<?= $stats['cancelled'] ?>)
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters -->
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">
                        <i class="fas fa-tasks"></i> Status Filter
                    </label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="scheduled" <?= $current_status_filter === 'scheduled' ? 'selected' : '' ?>>Scheduled</option>
                        <option value="confirmed" <?= $current_status_filter === 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                        <option value="in_progress" <?= $current_status_filter === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                        <option value="completed" <?= $current_status_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                        <option value="rejected" <?= $current_status_filter === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                        <option value="cancelled" <?= $current_status_filter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="district" class="form-label">
                        <i class="fas fa-map-marker-alt"></i> District Filter
                    </label>
                    <select name="district" id="district" class="form-select">
                        <option value="">All Districts</option>
                        <?php if (!empty($assigned_districts)): ?>
                            <?php foreach ($assigned_districts as $district): ?>
                                <option value="<?= $district['district_id'] ?>" <?= $current_district_filter === $district['district_id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($district['district_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <a href="<?= BASE_URL ?>inspector/inspections" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear All
                        </a>
                    </div>
                </div>
            </form>

            <!-- Current Filter Display -->
            <?php if ($current_status_filter || $current_district_filter): ?>
                <div class="mt-3 pt-3 border-top">
                    <h6 class="text-muted mb-2">Active Filters:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <?php if ($current_status_filter): ?>
                            <span class="badge bg-primary fs-6">
                                Status: <?= ucfirst(str_replace('_', ' ', $current_status_filter)) ?>
                                <a href="<?= BASE_URL ?>inspector/inspections<?= $current_district_filter ? '?district=' . $current_district_filter : '' ?>"
                                   class="text-white ms-1" title="Remove status filter">
                                    <i class="fas fa-times"></i>
                                </a>
                            </span>
                        <?php endif; ?>
                        <?php if ($current_district_filter): ?>
                            <?php
                            $districtName = 'Unknown District';
                            if (!empty($assigned_districts)) {
                                foreach ($assigned_districts as $district) {
                                    if ($district['district_id'] === $current_district_filter) {
                                        $districtName = $district['district_name'];
                                        break;
                                    }
                                }
                            }
                            ?>
                            <span class="badge bg-success fs-6">
                                District: <?= htmlspecialchars($districtName) ?>
                                <a href="<?= BASE_URL ?>inspector/inspections<?= $current_status_filter ? '?status=' . $current_status_filter : '' ?>"
                                   class="text-white ms-1" title="Remove district filter">
                                    <i class="fas fa-times"></i>
                                </a>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Businesses Needing Attention (Information Only) -->
    <?php if (!empty($businesses_needing_inspection) || !empty($overdue_inspections)): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-2"></i>Businesses in My Districts Requiring Attention
                    <span class="badge bg-info ms-2"><?= count($businesses_needing_inspection) + count($overdue_inspections) ?></span>
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> These businesses need inspection scheduling. Please contact your administrator to schedule inspections for these businesses.
                </div>

                <?php if (!empty($overdue_inspections)): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> Overdue Inspections (<?= count($overdue_inspections) ?>)</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Business</th>
                                        <th>District</th>
                                        <th>Due Date</th>
                                        <th>Days Overdue</th>
                                        <th>Priority</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($overdue_inspections, 0, 5) as $item): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($item['business_name']) ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($item['address']) ?></small>
                                            </td>
                                            <td><?= htmlspecialchars($item['district_name']) ?></td>
                                            <td><?= date('M d, Y', strtotime($item['due_date'])) ?></td>
                                            <td>
                                                <span class="badge bg-danger"><?= $item['days_overdue'] ?> days</span>
                                            </td>
                                            <td>
                                                <?php
                                                $priorityClasses = [
                                                    'urgent' => 'danger',
                                                    'high' => 'warning',
                                                    'medium' => 'info',
                                                    'low' => 'secondary'
                                                ];
                                                $priorityClass = $priorityClasses[$item['priority']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $priorityClass ?>"><?= ucfirst($item['priority']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($overdue_inspections) > 5): ?>
                            <div class="mt-2">
                                <small class="text-muted">Showing 5 of <?= count($overdue_inspections) ?> overdue inspections</small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($businesses_needing_inspection)): ?>
                    <h6><i class="fas fa-building"></i> Businesses Needing Inspection (<?= count($businesses_needing_inspection) ?>)</h6>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Business</th>
                                    <th>District</th>
                                    <th>Type</th>
                                    <th>Due Date</th>
                                    <th>Priority</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($businesses_needing_inspection, 0, 5) as $item): ?>
                                    <tr class="<?= $item['days_until_due'] < 0 ? 'table-danger' : ($item['days_until_due'] <= 7 ? 'table-warning' : '') ?>">
                                        <td>
                                            <strong><?= htmlspecialchars($item['business_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($item['address']) ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($item['district_name']) ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= ucfirst(str_replace('_', ' ', $item['inspection_type'])) ?></span>
                                        </td>
                                        <td>
                                            <?= date('M d, Y', strtotime($item['due_date'])) ?>
                                            <?php if ($item['days_until_due'] < 0): ?>
                                                <br><small class="text-danger"><?= abs($item['days_until_due']) ?> days overdue</small>
                                            <?php elseif ($item['days_until_due'] <= 7): ?>
                                                <br><small class="text-warning"><?= $item['days_until_due'] ?> days left</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $priorityClasses = [
                                                'urgent' => 'danger',
                                                'high' => 'warning',
                                                'medium' => 'info',
                                                'low' => 'secondary'
                                            ];
                                            $priorityClass = $priorityClasses[$item['priority']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?= $priorityClass ?>"><?= ucfirst($item['priority']) ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if (count($businesses_needing_inspection) > 5): ?>
                        <div class="mt-2">
                            <small class="text-muted">Showing 5 of <?= count($businesses_needing_inspection) ?> businesses needing inspection</small>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Inspections by Status -->
    <?php foreach ($inspections_by_status as $status => $inspections): ?>
        <?php if (!empty($inspections) || !$current_status_filter): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3" data-bs-toggle="collapse" data-bs-target="#<?= $status ?>-inspections" style="cursor: pointer;">
                    <h6 class="m-0 font-weight-bold text-<?=
                        $status === 'completed' ? 'success' :
                        ($status === 'in_progress' ? 'info' :
                        ($status === 'cancelled' ? 'danger' :
                        ($status === 'rejected' ? 'dark' : 'primary')))
                    ?>">
                        <i class="fas fa-chevron-down me-2"></i>
                        <?= ucfirst(str_replace('_', ' ', $status)) ?> Inspections
                        <span class="badge bg-<?=
                            $status === 'completed' ? 'success' :
                            ($status === 'in_progress' ? 'info' :
                            ($status === 'cancelled' ? 'danger' :
                            ($status === 'rejected' ? 'dark' : 'primary')))
                        ?> ms-2"><?= count($inspections) ?></span>
                    </h6>
                </div>
                <div class="collapse <?= $current_status_filter === $status || !$current_status_filter ? 'show' : '' ?>" id="<?= $status ?>-inspections">
                    <div class="card-body">
                        <?php if (!empty($inspections)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Inspection ID</th>
                                            <th>Business</th>
                                            <th>District</th>
                                            <th>Scheduled Date</th>
                                            <th>Type</th>
                                            <th>Checklist Progress</th>
                                            <th>Score</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($inspections as $inspection): ?>
                                            <tr <?= $status === 'rejected' ? 'class="table-warning"' : '' ?>>
                                                <td>
                                                    <code><?= substr($inspection['id'], 0, 8) ?>...</code>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                                </td>
                                                <td><?= htmlspecialchars($inspection['district_name']) ?></td>
                                                <td><?= $inspection['scheduled_date'] ? date('M d, Y g:i A', strtotime($inspection['scheduled_date'])) : 'N/A' ?></td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'])) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($inspection['checklist_completion'] && $inspection['checklist_completion']['is_started']): ?>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar bg-<?= $inspection['checklist_completion']['completion_percentage'] >= 100 ? 'success' : 'info' ?>"
                                                                 role="progressbar"
                                                                 style="width: <?= $inspection['checklist_completion']['completion_percentage'] ?>%">
                                                                <?= $inspection['checklist_completion']['completion_percentage'] ?>%
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <?php if ($inspection['checklist_completion']['completed_items'] > 1): ?>
                                                                <?= $inspection['checklist_completion']['completed_items'] ?>/<?= $inspection['checklist_completion']['total_items'] ?> items
                                                            <?php else: ?>
                                                                Inspection started
                                                            <?php endif; ?>
                                                        </small>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not started</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($inspection['checklist_score']): ?>
                                                        <span class="badge bg-<?= 
                                                            $inspection['checklist_score']['grade'] === 'A' ? 'success' : 
                                                            (in_array($inspection['checklist_score']['grade'], ['B', 'C']) ? 'warning' : 'danger') 
                                                        ?> fs-6">
                                                            <?= $inspection['checklist_score']['percentage'] ?>% (<?= $inspection['checklist_score']['grade'] ?>)
                                                        </span>
                                                        <?php if ($inspection['checklist_score']['critical_violations'] > 0): ?>
                                                            <br><small class="text-danger">
                                                                <i class="fas fa-exclamation-triangle"></i> 
                                                                <?= $inspection['checklist_score']['critical_violations'] ?> critical violations
                                                            </small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">No score</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?= BASE_URL ?>inspector/inspection/<?= $inspection['id'] ?>"
                                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </a>

                                                        <?php if ($inspection['status'] === 'scheduled'): ?>
                                                            <form method="POST" action="<?= BASE_URL ?>inspector/confirm-inspection/<?= $inspection['id'] ?>" class="d-inline">
                                                                <button type="submit" class="btn btn-sm btn-primary" title="Confirm Inspection">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>

                                                        <?php if (in_array($inspection['status'], ['scheduled', 'confirmed', 'in_progress'])): ?>
                                                            <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                                                               class="btn btn-sm btn-success" title="Inspection Checklist">
                                                                <i class="fas fa-clipboard-check"></i>
                                                            </a>

                                                            <button type="button" class="btn btn-sm btn-danger"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#cancelModal<?= $inspection['id'] ?>"
                                                                    title="Cancel Inspection">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        <?php endif; ?>

                                                        <?php if ($inspection['status'] === 'completed' && $inspection['checklist_score']): ?>
                                                            <a href="<?= BASE_URL ?>inspector/inspection-report/<?= $inspection['id'] ?>"
                                                               class="btn btn-sm btn-info" title="View Report">
                                                                <i class="fas fa-file-alt"></i>
                                                            </a>
                                                        <?php endif; ?>

                                                        <?php if ($status === 'rejected'): ?>
                                                            <span class="btn btn-sm btn-outline-dark" title="Rejected by Admin">
                                                                <i class="fas fa-ban"></i> Rejected
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No <?= ucfirst(str_replace('_', ' ', $status)) ?> Inspections</h5>
                                <p class="text-muted">You don't have any <?= str_replace('_', ' ', $status) ?> inspections at the moment.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>

    <!-- Cancel Inspection Modals -->
    <?php foreach ($inspections_by_status as $status => $inspections): ?>
        <?php foreach ($inspections as $inspection): ?>
            <?php if (in_array($inspection['status'], ['scheduled', 'confirmed', 'in_progress'])): ?>
                <div class="modal fade" id="cancelModal<?= $inspection['id'] ?>" tabindex="-1" aria-labelledby="cancelModalLabel<?= $inspection['id'] ?>" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="cancelModalLabel<?= $inspection['id'] ?>">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> Cancel Inspection
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form method="POST" action="<?= BASE_URL ?>inspector/cancel-inspection/<?= $inspection['id'] ?>">
                                <div class="modal-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Warning:</strong> This action will cancel the inspection for <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>.
                                        The admin will be notified of this cancellation.
                                    </div>

                                    <div class="mb-3">
                                        <label for="cancellation_reason<?= $inspection['id'] ?>" class="form-label">
                                            <i class="fas fa-comment"></i> Reason for Cancellation <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control" id="cancellation_reason<?= $inspection['id'] ?>" name="cancellation_reason"
                                                  rows="3" required placeholder="Please provide a detailed reason for cancelling this inspection..."></textarea>
                                        <div class="form-text">This reason will be recorded and sent to the admin.</div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="fas fa-times"></i> Keep Inspection
                                    </button>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-ban"></i> Cancel Inspection
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php endforeach; ?>
</div>
<?php $this->endSection(); ?>
