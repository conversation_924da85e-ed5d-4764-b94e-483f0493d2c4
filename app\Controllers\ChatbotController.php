<?php
namespace App\Controllers;

use App\Models\Chatbot;
use App\Libraries\Auth;
use App\Core\Controller;

class ChatbotController extends Controller {
    private $chatbotModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->chatbotModel = new Chatbot();
    }

    /**
     * Admin chatbot management dashboard
     */
    public function adminDashboard() {
        $this->auth->requireAdmin();

        $analytics = $this->chatbotModel->getAnalytics();
        $settings = $this->getChatbotSettings();

        return $this->render('admin/chatbot/dashboard', [
            'title' => 'Chatbot Management',
            'active_page' => 'chatbot',
            'analytics' => $analytics,
            'settings' => $settings
        ]);
    }

    /**
     * Update chatbot settings
     */
    public function updateSettings() {
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $this->redirect('admin/chatbot');
            return;
        }

        $settings = [
            'chatbot_enabled' => isset($_POST['chatbot_enabled']) ? 'true' : 'false',
            'auto_respond_when_offline' => isset($_POST['auto_respond_when_offline']) ? 'true' : 'false',
            'response_delay' => $_POST['response_delay'] ?? '2',
            'escalation_threshold' => $_POST['escalation_threshold'] ?? '3',
            'greeting_message' => $_POST['greeting_message'] ?? '',
            'offline_message' => $_POST['offline_message'] ?? '',
            'escalation_message' => $_POST['escalation_message'] ?? '',
            'fallback_message' => $_POST['fallback_message'] ?? ''
        ];

        $success = true;
        foreach ($settings as $key => $value) {
            if (!$this->chatbotModel->updateSetting($key, $value)) {
                $success = false;
            }
        }

        if ($success) {
            $_SESSION['success'] = 'Chatbot settings updated successfully.';
        } else {
            $_SESSION['error'] = 'Failed to update some settings.';
        }

        $this->redirect('admin/chatbot');
    }

    /**
     * Manage chatbot responses
     */
    public function manageResponses() {
        $this->auth->requireAdmin();

        $responses = $this->getChatbotResponses();

        return $this->render('admin/chatbot/responses', [
            'title' => 'Manage Chatbot Responses',
            'active_page' => 'chatbot',
            'responses' => $responses
        ]);
    }

    /**
     * Add new chatbot response
     */
    public function addResponse() {
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            return $this->render('admin/chatbot/add_response', [
                'title' => 'Add Chatbot Response',
                'active_page' => 'chatbot'
            ]);
        }

        $data = [
            'trigger_keywords' => $_POST['trigger_keywords'] ?? '',
            'response_text' => $_POST['response_text'] ?? '',
            'response_type' => $_POST['response_type'] ?? 'text',
            'category' => $_POST['category'] ?? 'general',
            'priority' => (int)($_POST['priority'] ?? 1)
        ];

        if (empty($data['trigger_keywords']) || empty($data['response_text'])) {
            $_SESSION['error'] = 'Keywords and response text are required.';
            return $this->render('admin/chatbot/add_response', [
                'title' => 'Add Chatbot Response',
                'active_page' => 'chatbot',
                'data' => $data
            ]);
        }

        if ($this->addChatbotResponse($data)) {
            $_SESSION['success'] = 'Chatbot response added successfully.';
            $this->redirect('admin/chatbot/responses');
        } else {
            $_SESSION['error'] = 'Failed to add chatbot response.';
            return $this->render('admin/chatbot/add_response', [
                'title' => 'Add Chatbot Response',
                'active_page' => 'chatbot',
                'data' => $data
            ]);
        }
    }

    /**
     * Edit chatbot response
     */
    public function editResponse($id) {
        $this->auth->requireAdmin();

        $response = $this->getChatbotResponse($id);
        if (!$response) {
            $_SESSION['error'] = 'Response not found.';
            $this->redirect('admin/chatbot/responses');
            return;
        }

        if (!$this->isPost()) {
            return $this->render('admin/chatbot/edit_response', [
                'title' => 'Edit Chatbot Response',
                'active_page' => 'chatbot',
                'response' => $response
            ]);
        }

        $data = [
            'trigger_keywords' => $_POST['trigger_keywords'] ?? '',
            'response_text' => $_POST['response_text'] ?? '',
            'response_type' => $_POST['response_type'] ?? 'text',
            'category' => $_POST['category'] ?? 'general',
            'priority' => (int)($_POST['priority'] ?? 1),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        if ($this->updateChatbotResponse($id, $data)) {
            $_SESSION['success'] = 'Chatbot response updated successfully.';
            $this->redirect('admin/chatbot/responses');
        } else {
            $_SESSION['error'] = 'Failed to update chatbot response.';
            return $this->render('admin/chatbot/edit_response', [
                'title' => 'Edit Chatbot Response',
                'active_page' => 'chatbot',
                'response' => array_merge($response, $data)
            ]);
        }
    }

    /**
     * Delete chatbot response
     */
    public function deleteResponse($id) {
        $this->auth->requireAdmin();

        if ($this->deleteChatbotResponse($id)) {
            $_SESSION['success'] = 'Chatbot response deleted successfully.';
        } else {
            $_SESSION['error'] = 'Failed to delete chatbot response.';
        }

        $this->redirect('admin/chatbot/responses');
    }

    /**
     * Manage knowledge base
     */
    public function manageKnowledge() {
        $this->auth->requireAdmin();

        $knowledge = $this->getKnowledgeBase();

        return $this->render('admin/chatbot/knowledge', [
            'title' => 'Manage Knowledge Base',
            'active_page' => 'chatbot',
            'knowledge' => $knowledge
        ]);
    }

    /**
     * Check admin status for auto-activation (AJAX endpoint)
     */
    public function checkAdminStatus() {
        header('Content-Type: application/json');

        if (!$this->isPost()) {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $roomId = $_POST['room_id'] ?? '';
        if (empty($roomId)) {
            echo json_encode(['success' => false, 'message' => 'Room ID required']);
            return;
        }

        // Check if auto-respond is enabled
        $autoRespond = $this->chatbotModel->getSetting('auto_respond_when_offline') === 'true';

        // Check if any admin is online
        $adminsOnline = $this->chatbotModel->areAdminsOnline();

        // Check if chatbot is enabled
        $chatbotEnabled = $this->chatbotModel->isEnabled();

        $shouldActivate = $chatbotEnabled && $autoRespond && !$adminsOnline;

        echo json_encode([
            'success' => true,
            'should_activate_chatbot' => $shouldActivate,
            'admins_online' => $adminsOnline,
            'chatbot_enabled' => $chatbotEnabled,
            'auto_respond' => $autoRespond
        ]);
    }

    /**
     * Process chatbot message (AJAX endpoint)
     */
    public function processMessage() {
        header('Content-Type: application/json');

        if (!$this->isPost()) {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $chatRoomId = $_POST['chat_room_id'] ?? '';
        $message = $_POST['message'] ?? '';
        $userId = $this->auth->getUserId();

        // Debug logging (can be removed in production)
        // error_log("Chatbot processMessage - chatRoomId: $chatRoomId, message: $message, userId: " . ($userId ?: 'NULL'));

        if (empty($chatRoomId) || empty($message)) {
            echo json_encode(['success' => false, 'message' => 'Missing chat room ID or message']);
            return;
        }

        if (!$userId) {
            // Try to get user ID from session directly as fallback
            $userId = $_SESSION['user_id'] ?? null;
            if (!$userId) {
                // For chatbot testing, allow anonymous users with a default ID
                $userId = 'anonymous_' . session_id();
            }
        }

        // Check if chatbot is enabled and should respond
        if (!$this->chatbotModel->isEnabled()) {
            echo json_encode(['success' => false, 'message' => 'Chatbot is disabled']);
            return;
        }

        // For manual activation, we don't need to check if chatbot is active in room
        // The frontend will only call this if the user manually activated the chatbot

        $response = $this->chatbotModel->processMessage($chatRoomId, $userId, $message);

        if ($response['response_type'] === 'escalate') {
            // Escalate to human
            $this->chatbotModel->escalateToHuman($chatRoomId, $userId, 'User requested human assistance');
            $response['escalated'] = true;
        }

        echo json_encode([
            'success' => true,
            'response' => $response['response_text'],
            'type' => $response['response_type'],
            'confidence' => $response['confidence_score'] ?? 0,
            'escalated' => $response['escalated'] ?? false
        ]);
    }

    // Helper methods
    private function getChatbotSettings() {
        $db = $this->chatbotModel->db ?? (new \App\Config\Database())->getConnection();
        $stmt = $db->query("SELECT setting_name, setting_value, description FROM chatbot_settings ORDER BY setting_name");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    private function getChatbotResponses() {
        $db = (new \App\Config\Database())->getConnection();
        $stmt = $db->query("SELECT * FROM chatbot_responses ORDER BY priority DESC, category, created_at DESC");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    private function getChatbotResponse($id) {
        $db = (new \App\Config\Database())->getConnection();
        $stmt = $db->prepare("SELECT * FROM chatbot_responses WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    private function addChatbotResponse($data) {
        $db = (new \App\Config\Database())->getConnection();
        $stmt = $db->prepare("
            INSERT INTO chatbot_responses (id, trigger_keywords, response_text, response_type, category, priority, created_by) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?)
        ");
        return $stmt->execute([
            $data['trigger_keywords'],
            $data['response_text'],
            $data['response_type'],
            $data['category'],
            $data['priority'],
            $this->auth->getUserId()
        ]);
    }

    private function updateChatbotResponse($id, $data) {
        $db = (new \App\Config\Database())->getConnection();
        $stmt = $db->prepare("
            UPDATE chatbot_responses 
            SET trigger_keywords = ?, response_text = ?, response_type = ?, category = ?, priority = ?, is_active = ?, updated_at = NOW()
            WHERE id = ?
        ");
        return $stmt->execute([
            $data['trigger_keywords'],
            $data['response_text'],
            $data['response_type'],
            $data['category'],
            $data['priority'],
            $data['is_active'],
            $id
        ]);
    }

    private function deleteChatbotResponse($id) {
        $db = (new \App\Config\Database())->getConnection();
        $stmt = $db->prepare("DELETE FROM chatbot_responses WHERE id = ?");
        return $stmt->execute([$id]);
    }

    private function getKnowledgeBase() {
        $db = (new \App\Config\Database())->getConnection();
        $stmt = $db->query("SELECT * FROM chatbot_knowledge_base ORDER BY category, topic");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}
