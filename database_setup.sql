-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS ohs_system;
USE ohs_system;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HAR(255) NOT NULL,
    role ENUM('admin', 'inspector', 'business_owner') NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create districts table
CREATE TABLE IF NOT EXISTS districts (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create barangays table
CREATE TABLE IF NOT EXISTS barangays (
    id VARCHAR(36) PRIMARY KEY,
    district_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE
);

-- Create business categories table
CREATE TABLE IF NOT EXISTS business_categories (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create businesses table
CREATE TABLE IF NOT EXISTS businesses (
    id VARCHAR(36) PRIMARY KEY,
    owner_id VARCHAR(36) NOT NULL,
    owner_name VARCHAR(255) NOT NULL,
    category_id VARCHAR(36) NOT NULL,
    barangay_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    registration_number VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    contact_number VARCHAR(50) NOT NULL,
    address TEXT NOT NULL,
    employee_count INT UNSIGNED NOT NULL DEFAULT 1,
    date_established DATE DEFAULT NULL,
    status ENUM('pending', 'active', 'suspended', 'inactive') DEFAULT 'pending',
    compliance_status ENUM('compliant', 'non_compliant', 'pending_review') DEFAULT 'pending_review',
    business_permit TINYINT(1) NOT NULL DEFAULT 0,
    sanitary_permit TINYINT(1) NOT NULL DEFAULT 0,
    fire_safety_certificate TINYINT(1) NOT NULL DEFAULT 0,
    environmental_permit TINYINT(1) NOT NULL DEFAULT 0,
    safety_officer_count INT UNSIGNED NOT NULL DEFAULT 0,
    has_safety_signage TINYINT(1) NOT NULL DEFAULT 0,
    has_first_aid TINYINT(1) NOT NULL DEFAULT 0,
    has_fire_extinguishers TINYINT(1) NOT NULL DEFAULT 0,
    has_cctv TINYINT(1) NOT NULL DEFAULT 0,
    has_waste_segregation TINYINT(1) NOT NULL DEFAULT 0,
    last_inspection_date DATE DEFAULT NULL,
    next_inspection_date DATE DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES business_categories(id),
    FOREIGN KEY (barangay_id) REFERENCES barangays(id)
);

-- Create compliance_evidences table to replace documents
CREATE TABLE IF NOT EXISTS compliance_evidences (
    id VARCHAR(36) PRIMARY KEY,
    business_id VARCHAR(36) NOT NULL,
    compliance_type VARCHAR(50) NOT NULL,
    photo_path VARCHAR(255) NOT NULL,
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    verification_notes TEXT,
    verified_by VARCHAR(36) DEFAULT NULL,
    verified_at DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_id) REFERENCES businesses(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create inspections table
CREATE TABLE IF NOT EXISTS inspections (
    id VARCHAR(36) PRIMARY KEY,
    business_id VARCHAR(36) NOT NULL,
    inspector_id VARCHAR(36) NOT NULL,
    assigned_by VARCHAR(36) NOT NULL,
    scheduled_date DATETIME NOT NULL,
    completed_date DATETIME DEFAULT NULL,
    status ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled') DEFAULT 'scheduled',
    inspection_type ENUM('routine', 'follow_up', 'complaint', 'initial') DEFAULT 'routine',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    score SMALLINT UNSIGNED DEFAULT NULL,
    notes TEXT,
    findings TEXT,
    recommendations TEXT,
    compliance_rating ENUM('A', 'B', 'C', 'D', 'F') DEFAULT NULL,
    inspector_notes TEXT,
    admin_notes TEXT,
    admin_verified TINYINT(1) DEFAULT 0,
    verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    verified_by VARCHAR(36) DEFAULT NULL,
    verified_at DATETIME DEFAULT NULL,
    verification_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_id) REFERENCES businesses(id) ON DELETE CASCADE,
    FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create chat_rooms table
CREATE TABLE IF NOT EXISTS chat_rooms (
    id VARCHAR(36) PRIMARY KEY,
    business_id VARCHAR(36) NOT NULL,
    business_owner_id VARCHAR(36) NOT NULL,
    admin_id VARCHAR(36) DEFAULT NULL,
    inspector_id VARCHAR(36) DEFAULT NULL,
    status ENUM('active', 'closed', 'archived') DEFAULT 'active',
    subject VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (business_id) REFERENCES businesses(id) ON DELETE CASCADE,
    FOREIGN KEY (business_owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    id VARCHAR(36) PRIMARY KEY,
    chat_room_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('text', 'file', 'image', 'system') DEFAULT 'text',
    file_path VARCHAR(255) DEFAULT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error', 'inspection', 'compliance', 'chat') DEFAULT 'info',
    related_id VARCHAR(36) DEFAULT NULL,
    related_type ENUM('inspection', 'business', 'compliance_evidence', 'chat_room') DEFAULT NULL,
    is_read TINYINT(1) DEFAULT 0,
    action_url VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create inspection_assignments table (for multiple inspectors per inspection)
CREATE TABLE IF NOT EXISTS inspection_assignments (
    id VARCHAR(36) PRIMARY KEY,
    inspection_id VARCHAR(36) NOT NULL,
    inspector_id VARCHAR(36) NOT NULL,
    assigned_by VARCHAR(36) NOT NULL,
    role ENUM('primary', 'secondary', 'observer') DEFAULT 'primary',
    status ENUM('assigned', 'accepted', 'declined', 'completed') DEFAULT 'assigned',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    notes TEXT,
    FOREIGN KEY (inspection_id) REFERENCES inspections(id) ON DELETE CASCADE,
    FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inspection_inspector (inspection_id, inspector_id)
);

-- Create inspector_district_assignments table
CREATE TABLE IF NOT EXISTS inspector_district_assignments (
    id VARCHAR(36) PRIMARY KEY,
    inspector_id VARCHAR(36) NOT NULL,
    district_id VARCHAR(36) NOT NULL,
    assigned_by VARCHAR(36) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    notes TEXT,
    FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inspector_district (inspector_id, district_id)
);

-- Create inspector_barangay_assignments table
CREATE TABLE IF NOT EXISTS inspector_barangay_assignments (
    id VARCHAR(36) PRIMARY KEY,
    inspector_id VARCHAR(36) NOT NULL,
    barangay_id VARCHAR(36) NOT NULL,
    assigned_by VARCHAR(36) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (barangay_id) REFERENCES barangays(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inspector_barangay (inspector_id, barangay_id)
);

-- Add new columns to inspections table if they don't exist
ALTER TABLE inspections
ADD COLUMN IF NOT EXISTS assigned_by VARCHAR(36),
ADD COLUMN IF NOT EXISTS inspection_type ENUM('routine', 'follow_up', 'complaint', 'initial') DEFAULT 'routine',
ADD COLUMN IF NOT EXISTS priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS inspector_notes TEXT,
ADD COLUMN IF NOT EXISTS admin_notes TEXT,
ADD COLUMN IF NOT EXISTS admin_verified TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS verified_by VARCHAR(36) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS verified_at DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS verification_notes TEXT;

-- Add foreign key for assigned_by if it doesn't exist
ALTER TABLE inspections
ADD CONSTRAINT fk_inspections_assigned_by
FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add foreign key for verified_by if it doesn't exist
ALTER TABLE inspections
ADD CONSTRAINT fk_inspections_verified_by
FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL;

-- Insert Bacoor City Districts
INSERT INTO districts (id, name) VALUES
('district-1', 'District 1 - Bacoor West'),
('district-2', 'District 2 - Bacoor East');

-- Insert Bacoor City Barangays (1st and 2nd Legislative Districts)
INSERT INTO barangays (id, district_id, name) VALUES
-- 1st Legislative District of Bacoor (District 1 - Bacoor West)
(UUID(), 'district-1', 'Bayan (Poblacion)'),
(UUID(), 'district-1', 'San Nicolas'),
(UUID(), 'district-1', 'Panapaan I'),
(UUID(), 'district-1', 'Panapaan II'),
(UUID(), 'district-1', 'Panapaan III'),
(UUID(), 'district-1', 'Panapaan IV'),
(UUID(), 'district-1', 'Panapaan V'),
(UUID(), 'district-1', 'Panapaan VI'),
(UUID(), 'district-1', 'Panapaan VII'),
(UUID(), 'district-1', 'Panapaan VIII'),
(UUID(), 'district-1', 'Alima'),
(UUID(), 'district-1', 'Aniban I'),
(UUID(), 'district-1', 'Aniban II'),
(UUID(), 'district-1', 'Aniban III'),
(UUID(), 'district-1', 'Aniban IV'),
(UUID(), 'district-1', 'Aniban V'),
(UUID(), 'district-1', 'Banalo'),
(UUID(), 'district-1', 'Camposanto'),
(UUID(), 'district-1', 'Daang Bukid'),
(UUID(), 'district-1', 'Digman'),
(UUID(), 'district-1', 'Dulong Bayan'),
(UUID(), 'district-1', 'Habay I'),
(UUID(), 'district-1', 'Habay II'),
(UUID(), 'district-1', 'Kaingin'),
(UUID(), 'district-1', 'Ligas I'),
(UUID(), 'district-1', 'Ligas II'),
(UUID(), 'district-1', 'Ligas III'),
(UUID(), 'district-1', 'Maliksi I'),
(UUID(), 'district-1', 'Maliksi II'),
(UUID(), 'district-1', 'Maliksi III'),
(UUID(), 'district-1', 'Mambog I'),
(UUID(), 'district-1', 'Mambog II'),
(UUID(), 'district-1', 'Mambog III'),
(UUID(), 'district-1', 'Mambog IV'),
(UUID(), 'district-1', 'Mambog V'),
(UUID(), 'district-1', 'Molino I'),
(UUID(), 'district-1', 'Molino II'),
(UUID(), 'district-1', 'Molino III'),
(UUID(), 'district-1', 'Molino IV'),
(UUID(), 'district-1', 'Molino V'),
(UUID(), 'district-1', 'Molino VI'),
(UUID(), 'district-1', 'Molino VII'),
(UUID(), 'district-1', 'Niog I'),
(UUID(), 'district-1', 'Niog II'),
(UUID(), 'district-1', 'Niog III'),
(UUID(), 'district-1', 'P.F. Espiritu I (Poblacion)'),
(UUID(), 'district-1', 'P.F. Espiritu II (Poblacion)'),
(UUID(), 'district-1', 'Queens Row Central'),
(UUID(), 'district-1', 'Queens Row East'),
(UUID(), 'district-1', 'Queens Row West'),
(UUID(), 'district-1', 'Real I'),
(UUID(), 'district-1', 'Real II'),
(UUID(), 'district-1', 'Salinas I'),
(UUID(), 'district-1', 'Salinas II'),
(UUID(), 'district-1', 'Salinas III'),
(UUID(), 'district-1', 'Salinas IV'),
(UUID(), 'district-1', 'San Antonio I'),
(UUID(), 'district-1', 'San Antonio II'),
(UUID(), 'district-1', 'Talaba I'),
(UUID(), 'district-1', 'Talaba II'),
(UUID(), 'district-1', 'Talaba III'),
(UUID(), 'district-1', 'Talaba IV'),
(UUID(), 'district-1', 'Talaba V'),
(UUID(), 'district-1', 'Talaba VI'),
(UUID(), 'district-1', 'Talaba VII'),
(UUID(), 'district-1', 'Zapote I'),
(UUID(), 'district-1', 'Zapote II'),
(UUID(), 'district-1', 'Zapote III'),
(UUID(), 'district-1', 'Zapote IV'),
(UUID(), 'district-1', 'Zapote V'),
-- 2nd Legislative District of Bacoor (District 2 - Bacoor East)
(UUID(), 'district-2', 'Springville'),
(UUID(), 'district-2', 'City Heights'),
(UUID(), 'district-2', 'Grand Royale');

-- Insert sample data for business categories
INSERT INTO business_categories (id, name, description) VALUES
(UUID(), 'Retail', 'Retail businesses selling goods to consumers'),
(UUID(), 'Food & Beverage', 'Restaurants, cafes, and food service businesses'),
(UUID(), 'Manufacturing', 'Production and manufacturing facilities'),
(UUID(), 'Services', 'Service-based businesses'),
(UUID(), 'Construction', 'Building and construction companies');

-- Insert admin user (password: admin123)
INSERT INTO users (id, email, password_hash, full_name, role, status) VALUES 
(UUID(), '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin', 'active');

-- Insert inspector user (password: inspector123)
INSERT INTO users (id, email, password_hash, full_name, role, status) VALUES 
(UUID(), '<EMAIL>', '$2y$10$JDe6hxOYGINB8MX/DDq4NOiYCXzHv.cRX0QauL/7TqTRba.SSl7.O', 'Safety Inspector', 'inspector', 'active'); 