<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Carbon\Carbon;

class Announcement extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'title',
        'content',
        'image_url',
        'author_id',
        'publish_date',
        'expire_date',
        'status',
        'is_featured',
        'views_count',
    ];

    protected $casts = [
        'publish_date' => 'datetime',
        'expire_date' => 'datetime',
        'is_featured' => 'boolean',
        'views_count' => 'integer',
    ];

    /**
     * Get the author of the announcement
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Scope for active announcements
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for published announcements
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'active')
            ->where(function($q) {
                $q->whereNull('publish_date')
                  ->orWhere('publish_date', '<=', now());
            })
            ->where(function($q) {
                $q->whereNull('expire_date')
                  ->orWhere('expire_date', '>', now());
            });
    }

    /**
     * Scope for featured announcements
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for scheduled announcements
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled')
            ->where('publish_date', '>', now());
    }

    /**
     * Scope for expired announcements
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('expire_date')
            ->where('expire_date', '<=', now());
    }

    /**
     * Get announcements for homepage
     */
    public static function getHomepageAnnouncements($limit = 5)
    {
        return static::published()
            ->latest('publish_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Get featured announcements
     */
    public static function getFeaturedAnnouncements($limit = 3)
    {
        return static::published()
            ->featured()
            ->latest('publish_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent announcements
     */
    public static function getRecentAnnouncements($limit = 10)
    {
        return static::published()
            ->latest('publish_date')
            ->limit($limit)
            ->get();
    }

    /**
     * Check if announcement is published
     */
    public function isPublished()
    {
        if ($this->status !== 'active') {
            return false;
        }

        $now = now();

        // Check publish date
        if ($this->publish_date && $this->publish_date > $now) {
            return false;
        }

        // Check expire date
        if ($this->expire_date && $this->expire_date <= $now) {
            return false;
        }

        return true;
    }

    /**
     * Check if announcement is scheduled
     */
    public function isScheduled()
    {
        return $this->status === 'scheduled' || 
               ($this->status === 'active' && $this->publish_date && $this->publish_date > now());
    }

    /**
     * Check if announcement is expired
     */
    public function isExpired()
    {
        return $this->expire_date && $this->expire_date <= now();
    }

    /**
     * Get status label
     */
    public function getStatusLabel()
    {
        if ($this->isExpired()) {
            return 'Expired';
        }

        if ($this->isScheduled()) {
            return 'Scheduled';
        }

        if ($this->isPublished()) {
            return 'Published';
        }

        return ucfirst($this->status);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor()
    {
        if ($this->isExpired()) {
            return 'secondary';
        }

        if ($this->isScheduled()) {
            return 'warning';
        }

        if ($this->isPublished()) {
            return 'success';
        }

        return 'danger';
    }

    /**
     * Check if announcement has image
     */
    public function hasImage()
    {
        return !empty($this->image_url);
    }

    /**
     * Get full image URL
     */
    public function getImageUrl()
    {
        if (!$this->hasImage()) {
            return null;
        }

        if (str_starts_with($this->image_url, 'http')) {
            return $this->image_url;
        }

        return asset('storage/' . $this->image_url);
    }

    /**
     * Get excerpt of content
     */
    public function getExcerpt($length = 200)
    {
        $content = strip_tags($this->content);
        
        return strlen($content) > $length 
            ? substr($content, 0, $length) . '...'
            : $content;
    }

    /**
     * Get reading time estimate
     */
    public function getReadingTime()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $minutes = ceil($wordCount / 200); // Average reading speed
        
        return $minutes . ' min read';
    }

    /**
     * Increment views count
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Get formatted publish date
     */
    public function getFormattedPublishDate()
    {
        if (!$this->publish_date) {
            return $this->created_at->format('M j, Y');
        }

        return $this->publish_date->format('M j, Y');
    }

    /**
     * Get time ago for publish date
     */
    public function getPublishTimeAgo()
    {
        $date = $this->publish_date ?? $this->created_at;
        
        return $date->diffForHumans();
    }

    /**
     * Auto-update status based on dates
     */
    public function updateStatus()
    {
        $now = now();

        // If expired, mark as inactive
        if ($this->expire_date && $this->expire_date <= $now && $this->status === 'active') {
            $this->update(['status' => 'inactive']);
            return;
        }

        // If scheduled and publish date has passed, mark as active
        if ($this->status === 'scheduled' && 
            $this->publish_date && 
            $this->publish_date <= $now) {
            $this->update(['status' => 'active']);
        }
    }

    /**
     * Get announcements that need status updates
     */
    public static function getAnnouncementsNeedingStatusUpdate()
    {
        $now = now();

        return static::where(function($query) use ($now) {
            // Scheduled announcements that should be active
            $query->where('status', 'scheduled')
                  ->where('publish_date', '<=', $now);
        })->orWhere(function($query) use ($now) {
            // Active announcements that should be expired
            $query->where('status', 'active')
                  ->whereNotNull('expire_date')
                  ->where('expire_date', '<=', $now);
        })->get();
    }

    /**
     * Update all announcement statuses
     */
    public static function updateAllStatuses()
    {
        $announcements = static::getAnnouncementsNeedingStatusUpdate();
        
        foreach ($announcements as $announcement) {
            $announcement->updateStatus();
        }

        return $announcements->count();
    }
}
