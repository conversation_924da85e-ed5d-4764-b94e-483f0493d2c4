<?php

namespace App\Models;

use App\Core\Model;

class BusinessEvidence extends Model
{
    protected $table = 'business_evidence';
    protected $primaryKey = 'id';

    /**
     * Get evidence statistics for a business
     */
    public function getBusinessEvidenceStats($businessId)
    {
        try {
            // Get all checklist items
            $checklistModel = new InspectionChecklist();
            $allItems = $checklistModel->getAllChecklistItems();
            $totalItems = count($allItems);

            // Get evidence for this business
            $sql = "SELECT checklist_item_id, COUNT(*) as file_count 
                    FROM {$this->table} 
                    WHERE business_id = ? 
                    GROUP BY checklist_item_id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$businessId]);
            $evidenceByItem = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            // Count items with evidence
            $itemsWithEvidence = count($evidenceByItem);

            // Count total evidence files
            $totalEvidenceFiles = 0;
            foreach ($evidenceByItem as $item) {
                $totalEvidenceFiles += $item['file_count'];
            }

            return [
                'total_items' => $totalItems,
                'items_with_evidence' => $itemsWithEvidence,
                'total_evidence_files' => $totalEvidenceFiles,
                'preparation_percentage' => $totalItems > 0 ? round(($itemsWithEvidence / $totalItems) * 100) : 0
            ];

        } catch (\Exception $e) {
            error_log("Error getting business evidence stats: " . $e->getMessage());
            return [
                'total_items' => 0,
                'items_with_evidence' => 0,
                'total_evidence_files' => 0,
                'preparation_percentage' => 0
            ];
        }
    }

    /**
     * Get evidence for a specific business and checklist item
     */
    public function getBusinessItemEvidence($businessId, $checklistItemId)
    {
        try {
            $sql = "SELECT * FROM {$this->table} 
                    WHERE business_id = ? AND checklist_item_id = ? 
                    ORDER BY uploaded_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$businessId, $checklistItemId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("Error getting business item evidence: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Add evidence for a checklist item
     */
    public function addEvidence($businessId, $checklistItemId, $filename, $originalFilename, $fileType)
    {
        try {
            $data = [
                'business_id' => $businessId,
                'checklist_item_id' => $checklistItemId,
                'filename' => $filename,
                'original_filename' => $originalFilename,
                'file_type' => $fileType,
                'uploaded_at' => date('Y-m-d H:i:s')
            ];

            return $this->create($data);

        } catch (\Exception $e) {
            error_log("Error adding evidence: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete evidence file
     */
    public function deleteEvidence($evidenceId)
    {
        try {
            // Get evidence details first
            $evidence = $this->find($evidenceId);
            if (!$evidence) {
                return false;
            }

            // Delete file from filesystem
            $filePath = 'uploads/business_evidence/' . $evidence['filename'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Delete from database
            return $this->delete($evidenceId);

        } catch (\Exception $e) {
            error_log("Error deleting evidence: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all evidence for a business grouped by checklist item
     */
    public function getBusinessEvidenceGrouped($businessId)
    {
        try {
            $sql = "SELECT be.*, ci.item_text, ci.category_id, cc.name as category_name
                    FROM {$this->table} be
                    JOIN checklist_items ci ON be.checklist_item_id = ci.id
                    JOIN checklist_categories cc ON ci.category_id = cc.id
                    WHERE be.business_id = ?
                    ORDER BY cc.sort_order, ci.sort_order, be.uploaded_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$businessId]);
            $evidence = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            // Group by checklist item
            $grouped = [];
            foreach ($evidence as $item) {
                $itemId = $item['checklist_item_id'];
                if (!isset($grouped[$itemId])) {
                    $grouped[$itemId] = [
                        'item_text' => $item['item_text'],
                        'category_name' => $item['category_name'],
                        'evidence' => []
                    ];
                }
                $grouped[$itemId]['evidence'][] = $item;
            }

            return $grouped;

        } catch (\Exception $e) {
            error_log("Error getting grouped business evidence: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if business has evidence for specific checklist item
     */
    public function hasEvidence($businessId, $checklistItemId)
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                    WHERE business_id = ? AND checklist_item_id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$businessId, $checklistItemId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return $result['count'] > 0;

        } catch (\Exception $e) {
            error_log("Error checking evidence existence: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get evidence count for a business
     */
    public function getEvidenceCount($businessId)
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE business_id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$businessId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return $result['count'] ?? 0;

        } catch (\Exception $e) {
            error_log("Error getting evidence count: " . $e->getMessage());
            return 0;
        }
    }
}
