<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'ohs_system');
define('DB_USER', 'root');
define('DB_PASS', '');

// Path configuration
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(__DIR__)));
}

if (!defined('BASE_URL')) {
    $scriptName = str_replace('\\', '/', dirname($_SERVER['SCRIPT_NAME']));
    $scriptName = rtrim($scriptName, '/');
    
    // Always remove /public from the URL if present
    $scriptName = preg_replace('#/public$#', '', $scriptName);
    
    define('BASE_URL', $scriptName . '/');
}

define('APP_PATH', ROOT_PATH . '/app');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('UPLOAD_PATH', PUBLIC_PATH . '/uploads');

// Application configuration
define('APP_ENV', 'development');
define('APP_DEBUG', true);
define('APP_NAME', 'OHS System');

// Session configuration
define('SESSION_LIFETIME', 7200); // 2 hours
define('SESSION_PATH', '/');
define('SESSION_DOMAIN', '');
define('SESSION_SECURE', false);
define('SESSION_HTTP_ONLY', true);

// Create required directories if they don't exist
$directories = [
    STORAGE_PATH,
    STORAGE_PATH . '/logs',
    STORAGE_PATH . '/cache',
    UPLOAD_PATH,
    UPLOAD_PATH . '/documents'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0777, true);
    }
} 