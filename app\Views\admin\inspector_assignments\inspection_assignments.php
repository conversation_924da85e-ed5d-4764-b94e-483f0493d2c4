<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 text-gray-800">
                <i class="fas fa-user-cog me-2 text-warning"></i><?= $title ?>
            </h1>
            <p class="text-muted mb-0">Manage individual inspection assignments and inspector workloads</p>
        </div>
        <div>
            <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="btn btn-outline-primary me-2">
                <i class="fas fa-calendar-check me-2"></i>Integrated Management
            </a>
            <button type="button" class="btn btn-info me-2" onclick="findDuplicates()">
                <i class="fas fa-search me-2"></i>Find Duplicates
            </button>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#bulkReassignModal">
                <i class="fas fa-exchange-alt me-2"></i>Bulk Reassign
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Inspections</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_inspections'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Assigned</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['assigned_inspections'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Unassigned</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['unassigned_inspections'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Overdue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['overdue_inspections'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Inspectors</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_inspectors'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Active Inspectors</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['active_inspectors'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column: Inspector Workloads -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Inspector Workloads
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshWorkloads()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="inspector-workloads" style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($inspectors as $inspector): ?>
                            <?php $workload = $inspector_workloads[$inspector['id']] ?? ['total_inspections' => 0, 'scheduled' => 0, 'in_progress' => 0, 'completed' => 0, 'overdue' => 0]; ?>
                            <div class="inspector-workload-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1"><?= htmlspecialchars($inspector['full_name']) ?></h6>
                                        <small class="text-muted"><?= htmlspecialchars($inspector['email']) ?></small>
                                    </div>
                                    <span class="badge bg-<?= $inspector['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst($inspector['status']) ?>
                                    </span>
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-3">
                                        <div class="text-primary font-weight-bold"><?= $workload['total_inspections'] ?></div>
                                        <small class="text-muted">Total</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-warning font-weight-bold"><?= $workload['scheduled'] ?></div>
                                        <small class="text-muted">Scheduled</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-info font-weight-bold"><?= $workload['in_progress'] ?></div>
                                        <small class="text-muted">In Progress</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-<?= $workload['overdue'] > 0 ? 'danger' : 'success' ?> font-weight-bold"><?= $workload['overdue'] ?></div>
                                        <small class="text-muted">Overdue</small>
                                    </div>
                                </div>
                                
                                <?php if ($workload['total_inspections'] > 0): ?>
                                    <div class="progress mt-2" style="height: 6px;">
                                        <?php 
                                        $completedPercent = ($workload['completed'] / $workload['total_inspections']) * 100;
                                        $inProgressPercent = ($workload['in_progress'] / $workload['total_inspections']) * 100;
                                        $scheduledPercent = ($workload['scheduled'] / $workload['total_inspections']) * 100;
                                        ?>
                                        <div class="progress-bar bg-success" style="width: <?= $completedPercent ?>%"></div>
                                        <div class="progress-bar bg-info" style="width: <?= $inProgressPercent ?>%"></div>
                                        <div class="progress-bar bg-warning" style="width: <?= $scheduledPercent ?>%"></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Inspection Assignments -->
        <div class="col-lg-8">
            <!-- Unassigned Inspections Alert -->
            <?php if (!empty($unassigned_inspections)): ?>
                <div class="alert alert-warning mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong><?= count($unassigned_inspections) ?> inspection(s)</strong> need to be assigned to inspectors.
                        </div>
                        <button type="button" class="btn btn-sm btn-warning" onclick="showUnassignedInspections()">
                            <i class="fas fa-eye me-1"></i>View Unassigned
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Overdue Inspections Alert -->
            <?php if (!empty($overdue_inspections)): ?>
                <div class="alert alert-danger mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-clock me-2"></i>
                            <strong><?= count($overdue_inspections) ?> inspection(s)</strong> are overdue and need immediate attention.
                        </div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="showOverdueInspections()">
                            <i class="fas fa-eye me-1"></i>View Overdue
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <!-- All Inspections Table -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>All Inspection Assignments
                    </h6>
                    <div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="filterByStatus('all')">All</button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="filterByStatus('unassigned')">Unassigned</button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="filterByStatus('overdue')">Overdue</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="assignmentsTable">
                            <thead>
                                <tr>
                                    <th>Business</th>
                                    <th>Location</th>
                                    <th>Inspector</th>
                                    <th>Scheduled Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($inspections as $inspection): ?>
                                    <tr data-status="<?= empty($inspection['inspector_id']) ? 'unassigned' : 'assigned' ?>" 
                                        data-overdue="<?= ($inspection['status'] === 'scheduled' && strtotime($inspection['scheduled_date']) < time()) ? 'true' : 'false' ?>">
                                        <td>
                                            <div>
                                                <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                                <strong><?= htmlspecialchars($inspection['barangay_name'] ?? 'N/A') ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($inspection['inspector_name']): ?>
                                                <div>
                                                    <i class="fas fa-user-shield text-primary me-1"></i>
                                                    <?= htmlspecialchars($inspection['inspector_name']) ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="badge bg-warning-subtle text-warning">
                                                    <i class="fas fa-user-times me-1"></i>Unassigned
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="fas fa-calendar text-muted me-1"></i>
                                                <?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?>
                                                <br><small class="text-muted"><?= date('h:i A', strtotime($inspection['scheduled_date'])) ?></small>
                                                <?php if ($inspection['status'] === 'scheduled' && strtotime($inspection['scheduled_date']) < time()): ?>
                                                    <br><small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Overdue</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClasses = [
                                                'scheduled' => 'warning',
                                                'in_progress' => 'primary',
                                                'completed' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $statusClass = $statusClasses[$inspection['status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?= $statusClass ?>-subtle text-<?= $statusClass ?>">
                                                <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-warning"
                                                        onclick="showReassignModal('<?= $inspection['id'] ?>', '<?= htmlspecialchars($inspection['business_name']) ?>', '<?= $inspection['inspector_id'] ?? '' ?>')"
                                                        title="Reassign Inspector">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </button>
                                                <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>"
                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDeleteInspection('<?= $inspection['id'] ?>', '<?= htmlspecialchars($inspection['business_name']) ?>')"
                                                        title="Delete Inspection">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reassign Inspector Modal -->
<div class="modal fade" id="reassignModal" tabindex="-1" aria-labelledby="reassignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="reassignModalLabel">
                    <i class="fas fa-exchange-alt me-2"></i>Reassign Inspector
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= BASE_URL ?>admin/inspector-assignments/reassign" method="POST" id="reassignForm">
                <div class="modal-body">
                    <input type="hidden" id="reassign_inspection_id" name="inspection_id">

                    <div class="mb-3">
                        <label class="form-label">Business</label>
                        <div class="form-control-plaintext" id="reassign_business_name"></div>
                    </div>

                    <div class="mb-3">
                        <label for="new_inspector_id" class="form-label">New Inspector <span class="text-danger">*</span></label>
                        <select class="form-select" id="new_inspector_id" name="new_inspector_id" required>
                            <option value="">Select Inspector</option>
                            <?php foreach ($inspectors as $inspector): ?>
                                <option value="<?= $inspector['id'] ?>"><?= htmlspecialchars($inspector['full_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason for Reassignment</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3"
                                  placeholder="Optional: Explain why this inspection is being reassigned..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-exchange-alt me-1"></i>Reassign Inspector
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Reassign Modal -->
<div class="modal fade" id="bulkReassignModal" tabindex="-1" aria-labelledby="bulkReassignModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="bulkReassignModalLabel">
                    <i class="fas fa-exchange-alt me-2"></i>Bulk Reassign Inspections
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This feature allows you to reassign multiple inspections from one inspector to another.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <label for="from_inspector" class="form-label">From Inspector</label>
                        <select class="form-select" id="from_inspector">
                            <option value="">Select Inspector</option>
                            <?php foreach ($inspectors as $inspector): ?>
                                <option value="<?= $inspector['id'] ?>"><?= htmlspecialchars($inspector['full_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="to_inspector" class="form-label">To Inspector</label>
                        <select class="form-select" id="to_inspector">
                            <option value="">Select Inspector</option>
                            <?php foreach ($inspectors as $inspector): ?>
                                <option value="<?= $inspector['id'] ?>"><?= htmlspecialchars($inspector['full_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="mt-3">
                    <div id="bulk_inspections_list" style="display: none;">
                        <h6>Inspections to Reassign:</h6>
                        <div id="bulk_inspections_content"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-info" onclick="loadBulkInspections()">
                    <i class="fas fa-search me-1"></i>Load Inspections
                </button>
                <button type="button" class="btn btn-warning" onclick="executeBulkReassign()" style="display: none;" id="bulk_reassign_btn">
                    <i class="fas fa-exchange-alt me-1"></i>Reassign All
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteInspectionModal" tabindex="-1" aria-labelledby="deleteInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteInspectionModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                    <h5>Are you sure you want to delete this inspection?</h5>
                    <p class="text-muted mb-3">This action cannot be undone. The inspection assignment will be permanently removed.</p>
                    <div class="alert alert-warning">
                        <strong>Business:</strong> <span id="deleteBusinessName"></span><br>
                        <strong>Action:</strong> Complete removal of inspection assignment
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Yes, Delete Inspection
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Duplicate Detection Modal -->
<div class="modal fade" id="duplicatesModal" tabindex="-1" aria-labelledby="duplicatesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="duplicatesModalLabel">
                    <i class="fas fa-search me-2"></i>Duplicate Inspections Found
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="duplicatesContent">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-info mb-3"></i>
                        <p>Scanning for duplicate inspections...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Close
                </button>
                <button type="button" class="btn btn-danger" id="cleanupDuplicatesBtn" style="display: none;">
                    <i class="fas fa-broom me-1"></i>Clean Up Selected Duplicates
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Store data for JavaScript use
const inspectorsData = <?= json_encode($inspectors) ?>;
const inspectionsData = <?= json_encode($inspections) ?>;

// Show reassign modal
function showReassignModal(inspectionId, businessName, currentInspectorId) {
    document.getElementById('reassign_inspection_id').value = inspectionId;
    document.getElementById('reassign_business_name').textContent = businessName;

    // Reset and populate inspector dropdown
    const inspectorSelect = document.getElementById('new_inspector_id');
    inspectorSelect.value = '';

    // Disable current inspector option
    Array.from(inspectorSelect.options).forEach(option => {
        option.disabled = option.value === currentInspectorId;
    });

    new bootstrap.Modal(document.getElementById('reassignModal')).show();
}

// Filter inspections by status
function filterByStatus(status) {
    const rows = document.querySelectorAll('#assignmentsTable tbody tr');

    rows.forEach(row => {
        let show = false;

        if (status === 'all') {
            show = true;
        } else if (status === 'unassigned') {
            show = row.dataset.status === 'unassigned';
        } else if (status === 'overdue') {
            show = row.dataset.overdue === 'true';
        }

        row.style.display = show ? '' : 'none';
    });

    // Update button states
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// Show unassigned inspections
function showUnassignedInspections() {
    filterByStatus('unassigned');
}

// Show overdue inspections
function showOverdueInspections() {
    filterByStatus('overdue');
}

// Refresh workloads
function refreshWorkloads() {
    location.reload();
}

// Load bulk inspections
function loadBulkInspections() {
    const fromInspectorId = document.getElementById('from_inspector').value;

    if (!fromInspectorId) {
        alert('Please select an inspector to reassign from.');
        return;
    }

    // Filter inspections for the selected inspector
    const inspectorInspections = inspectionsData.filter(inspection =>
        inspection.inspector_id === fromInspectorId &&
        (inspection.status === 'scheduled' || inspection.status === 'in_progress')
    );

    const listContainer = document.getElementById('bulk_inspections_list');
    const contentContainer = document.getElementById('bulk_inspections_content');

    if (inspectorInspections.length === 0) {
        contentContainer.innerHTML = '<div class="alert alert-info">No reassignable inspections found for this inspector.</div>';
        listContainer.style.display = 'block';
        document.getElementById('bulk_reassign_btn').style.display = 'none';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Business</th><th>Date</th><th>Status</th></tr></thead><tbody>';

    inspectorInspections.forEach(inspection => {
        html += `<tr>
            <td>${inspection.business_name}</td>
            <td>${new Date(inspection.scheduled_date).toLocaleDateString()}</td>
            <td><span class="badge bg-warning">${inspection.status}</span></td>
        </tr>`;
    });

    html += '</tbody></table></div>';
    html += `<div class="alert alert-success mt-2"><strong>${inspectorInspections.length}</strong> inspection(s) will be reassigned.</div>`;

    contentContainer.innerHTML = html;
    listContainer.style.display = 'block';
    document.getElementById('bulk_reassign_btn').style.display = 'inline-block';
}

// Execute bulk reassign
function executeBulkReassign() {
    const fromInspectorId = document.getElementById('from_inspector').value;
    const toInspectorId = document.getElementById('to_inspector').value;

    if (!fromInspectorId || !toInspectorId) {
        alert('Please select both inspectors.');
        return;
    }

    if (fromInspectorId === toInspectorId) {
        alert('Cannot reassign to the same inspector.');
        return;
    }

    if (confirm('Are you sure you want to reassign all inspections? This action cannot be undone.')) {
        // In a real implementation, this would make an AJAX call
        alert('Bulk reassignment feature will be implemented with AJAX calls.');
    }
}

// Delete inspection functionality
let inspectionToDelete = null;

function confirmDeleteInspection(inspectionId, businessName) {
    inspectionToDelete = inspectionId;
    document.getElementById('deleteBusinessName').textContent = businessName;

    const modal = new bootstrap.Modal(document.getElementById('deleteInspectionModal'));
    modal.show();
}

// Duplicate detection functionality
function findDuplicates() {
    const modal = new bootstrap.Modal(document.getElementById('duplicatesModal'));
    modal.show();

    // Reset content
    document.getElementById('duplicatesContent').innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-info mb-3"></i>
            <p>Scanning for duplicate inspections...</p>
        </div>
    `;

    // Analyze current table data for duplicates
    const table = document.getElementById('assignmentsTable');
    const rows = table.querySelectorAll('tbody tr');
    const inspections = [];

    // Extract inspection data from table
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 6) {
            const businessName = cells[0].textContent.trim();
            const inspector = cells[2].textContent.trim();
            const scheduledDate = cells[3].textContent.trim();
            const inspectionId = row.querySelector('.btn-outline-danger')?.getAttribute('onclick')?.match(/'([^']+)'/)?.[1];

            if (inspectionId) {
                inspections.push({
                    id: inspectionId,
                    business: businessName,
                    inspector: inspector,
                    date: scheduledDate,
                    row: row
                });
            }
        }
    });

    // Find duplicates
    const duplicates = [];
    const seen = new Map();

    inspections.forEach(inspection => {
        const key = `${inspection.business}|${inspection.inspector}|${inspection.date}`;
        if (seen.has(key)) {
            // Found duplicate
            const existing = seen.get(key);
            if (!duplicates.find(d => d.key === key)) {
                duplicates.push({
                    key: key,
                    business: inspection.business,
                    inspector: inspection.inspector,
                    date: inspection.date,
                    inspections: [existing, inspection]
                });
            } else {
                // Add to existing duplicate group
                const duplicateGroup = duplicates.find(d => d.key === key);
                duplicateGroup.inspections.push(inspection);
            }
        } else {
            seen.set(key, inspection);
        }
    });

    // Display results
    setTimeout(() => {
        displayDuplicateResults(duplicates);
    }, 1000);
}

function displayDuplicateResults(duplicates) {
    const content = document.getElementById('duplicatesContent');
    const cleanupBtn = document.getElementById('cleanupDuplicatesBtn');

    if (duplicates.length === 0) {
        content.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-success">No Duplicates Found!</h5>
                <p class="text-muted">All inspection assignments appear to be unique.</p>
            </div>
        `;
        cleanupBtn.style.display = 'none';
        return;
    }

    let html = `
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Found ${duplicates.length} duplicate group(s)</strong> affecting ${duplicates.reduce((sum, d) => sum + d.inspections.length, 0)} inspections.
        </div>
        <div class="mb-3">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllDuplicates()">
                <i class="fas fa-check-double me-1"></i>Select All Duplicates
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearDuplicateSelection()">
                <i class="fas fa-times me-1"></i>Clear Selection
            </button>
        </div>
    `;

    duplicates.forEach((duplicate, index) => {
        html += `
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-copy text-warning me-2"></i>
                        Duplicate Group ${index + 1}: ${duplicate.business} - ${duplicate.inspector} - ${duplicate.date}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">
                        <i class="fas fa-info-circle me-1"></i>
                        The following ${duplicate.inspections.length} inspections are identical. Select which ones to delete:
                    </p>
                    <div class="row">
        `;

        duplicate.inspections.forEach((inspection, inspIndex) => {
            html += `
                <div class="col-md-6 mb-2">
                    <div class="form-check">
                        <input class="form-check-input duplicate-checkbox" type="checkbox"
                               value="${inspection.id}" id="duplicate_${inspection.id}"
                               ${inspIndex > 0 ? 'checked' : ''}>
                        <label class="form-check-label" for="duplicate_${inspection.id}">
                            <div class="border rounded p-2 ${inspIndex > 0 ? 'bg-danger-subtle' : 'bg-success-subtle'}">
                                <strong>Inspection #${inspIndex + 1}</strong><br>
                                <small class="text-muted">ID: ${inspection.id}</small><br>
                                ${inspIndex === 0 ? '<span class="badge bg-success">Keep This One</span>' : '<span class="badge bg-danger">Delete</span>'}
                            </div>
                        </label>
                    </div>
                </div>
            `;
        });

        html += `
                    </div>
                </div>
            </div>
        `;
    });

    content.innerHTML = html;
    cleanupBtn.style.display = 'inline-block';
}

function selectAllDuplicates() {
    document.querySelectorAll('.duplicate-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function clearDuplicateSelection() {
    document.querySelectorAll('.duplicate-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Handle cleanup of selected duplicates
document.getElementById('cleanupDuplicatesBtn').addEventListener('click', function() {
    const selectedIds = Array.from(document.querySelectorAll('.duplicate-checkbox:checked')).map(cb => cb.value);

    if (selectedIds.length === 0) {
        alert('Please select at least one duplicate inspection to delete.');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedIds.length} duplicate inspection(s)? This action cannot be undone.`)) {
        return;
    }

    // Show loading state
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Cleaning up...';
    this.disabled = true;

    // Delete each selected inspection
    let deletedCount = 0;
    const totalToDelete = selectedIds.length;

    selectedIds.forEach((inspectionId, index) => {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= BASE_URL ?>admin/inspections/delete/' + inspectionId;
        form.style.display = 'none';

        document.body.appendChild(form);

        // For the last deletion, reload the page
        if (index === totalToDelete - 1) {
            form.addEventListener('submit', () => {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        }

        form.submit();
    });
});

// Handle delete confirmation
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (!inspectionToDelete) return;

    // Show loading state
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
    this.disabled = true;

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= BASE_URL ?>admin/inspections/delete/' + inspectionToDelete;

    // Add CSRF token if needed
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_method';
    csrfInput.value = 'DELETE';
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
});

// Initialize DataTable
$(document).ready(function() {
    $('#assignmentsTable').DataTable({
        order: [[3, 'asc']], // Sort by scheduled date
        pageLength: 25,
        language: {
            search: '<i class="fas fa-search"></i>',
            searchPlaceholder: 'Search assignments...'
        },
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting for actions column (now 6 due to added delete button)
        ]
    });
});
</script>

<?php $this->endSection(); ?>
