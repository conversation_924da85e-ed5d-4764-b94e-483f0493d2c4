<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BusinessChecklistEvidence;
use App\Models\Business;
use App\Models\InspectionChecklistItem;
use App\Models\User;
use App\Services\FileUploadService;
use App\Http\Controllers\NotificationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class ComplianceEvidenceController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Display compliance evidence dashboard
     */
    public function index(Request $request)
    {
        $query = BusinessChecklistEvidence::with(['business', 'checklistItem', 'uploadedBy', 'verifiedBy']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        if ($request->filled('checklist_item_id')) {
            $query->where('checklist_item_id', $request->checklist_item_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $evidence = $query->latest()->paginate(20);

        // Get filter options
        $businesses = Business::where('status', 'active')->orderBy('name')->get();
        $checklistItems = InspectionChecklistItem::active()->orderBy('item_name')->get();

        // Get statistics
        $stats = [
            'total_evidence' => BusinessChecklistEvidence::count(),
            'pending_evidence' => BusinessChecklistEvidence::where('status', 'pending')->count(),
            'approved_evidence' => BusinessChecklistEvidence::where('status', 'approved')->count(),
            'rejected_evidence' => BusinessChecklistEvidence::where('status', 'rejected')->count(),
            'evidence_this_month' => BusinessChecklistEvidence::whereMonth('created_at', now()->month)->count(),
        ];

        return view('admin.compliance-evidence.index', compact(
            'evidence', 
            'businesses', 
            'checklistItems', 
            'stats'
        ));
    }

    /**
     * Display evidence for specific business
     */
    public function businessEvidence(Business $business)
    {
        $evidence = BusinessChecklistEvidence::where('business_id', $business->id)
            ->with(['checklistItem.category', 'uploadedBy', 'verifiedBy'])
            ->latest()
            ->paginate(15);

        // Group evidence by category
        $evidenceByCategory = $evidence->getCollection()->groupBy('checklistItem.category.name');

        // Get compliance statistics for this business
        $stats = [
            'total_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)->count(),
            'pending_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'pending')->count(),
            'approved_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'approved')->count(),
            'rejected_evidence' => BusinessChecklistEvidence::where('business_id', $business->id)
                ->where('status', 'rejected')->count(),
        ];

        // Calculate compliance percentage
        $totalChecklistItems = InspectionChecklistItem::active()->count();
        $approvedEvidence = $stats['approved_evidence'];
        $compliancePercentage = $totalChecklistItems > 0 
            ? round(($approvedEvidence / $totalChecklistItems) * 100, 1)
            : 0;

        return view('admin.compliance-evidence.business', compact(
            'business',
            'evidence',
            'evidenceByCategory',
            'stats',
            'compliancePercentage'
        ));
    }

    /**
     * Display specific evidence details
     */
    public function show(BusinessChecklistEvidence $evidence)
    {
        $evidence->load(['business', 'checklistItem.category', 'uploadedBy', 'verifiedBy']);

        return view('admin.compliance-evidence.show', compact('evidence'));
    }

    /**
     * Verify evidence (approve/reject)
     */
    public function verify(Request $request, BusinessChecklistEvidence $evidence)
    {
        $validated = $request->validate([
            'status' => 'required|in:approved,rejected',
            'verification_notes' => 'nullable|string|max:1000',
        ]);

        $evidence->update([
            'status' => $validated['status'],
            'verified_by' => Auth::id(),
            'verified_at' => now(),
            'verification_notes' => $validated['verification_notes'],
        ]);

        // Update business compliance status
        $this->updateBusinessComplianceStatus($evidence->business_id);

        // Send notification to business owner
        NotificationController::createNotification(
            $evidence->business->owner_id,
            'Evidence ' . ucfirst($validated['status']),
            "Your evidence for '{$evidence->checklistItem->item_name}' has been {$validated['status']}.",
            $validated['status'] === 'approved' ? 'success' : 'warning',
            $evidence->id,
            'evidence',
            route('business-owner.business.checklist')
        );

        return back()->with('success', 'Evidence verification completed successfully.');
    }

    /**
     * Bulk verify evidence
     */
    public function bulkVerify(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:approve,reject',
            'evidence_ids' => 'required|array',
            'evidence_ids.*' => 'exists:business_checklist_evidence,id',
            'verification_notes' => 'nullable|string|max:1000',
        ]);

        $status = $validated['action'] === 'approve' ? 'approved' : 'rejected';
        $evidenceItems = BusinessChecklistEvidence::whereIn('id', $validated['evidence_ids'])->get();

        DB::transaction(function () use ($evidenceItems, $status, $validated) {
            foreach ($evidenceItems as $evidence) {
                $evidence->update([
                    'status' => $status,
                    'verified_by' => Auth::id(),
                    'verified_at' => now(),
                    'verification_notes' => $validated['verification_notes'],
                ]);

                // Update business compliance status
                $this->updateBusinessComplianceStatus($evidence->business_id);

                // Send notification to business owner
                NotificationController::createNotification(
                    $evidence->business->owner_id,
                    'Evidence ' . ucfirst($status),
                    "Your evidence for '{$evidence->checklistItem->item_name}' has been {$status}.",
                    $status === 'approved' ? 'success' : 'warning',
                    $evidence->id,
                    'evidence',
                    route('business-owner.business.checklist')
                );
            }
        });

        $count = count($validated['evidence_ids']);
        $action = $validated['action'] === 'approve' ? 'approved' : 'rejected';

        return back()->with('success', "{$count} evidence items {$action} successfully.");
    }

    /**
     * Delete evidence
     */
    public function destroy(BusinessChecklistEvidence $evidence)
    {
        // Delete file from storage
        if ($evidence->file_path && Storage::disk('public')->exists($evidence->file_path)) {
            Storage::disk('public')->delete($evidence->file_path);
        }

        $businessId = $evidence->business_id;
        $evidence->delete();

        // Update business compliance status
        $this->updateBusinessComplianceStatus($businessId);

        return back()->with('success', 'Evidence deleted successfully.');
    }

    /**
     * Download evidence file
     */
    public function download(BusinessChecklistEvidence $evidence)
    {
        if (!$evidence->file_path || !Storage::disk('public')->exists($evidence->file_path)) {
            return back()->with('error', 'Evidence file not found.');
        }

        return Storage::disk('public')->download($evidence->file_path, $evidence->file_name);
    }

    /**
     * Generate compliance report
     */
    public function complianceReport(Request $request)
    {
        $filters = $request->only(['business_id', 'status', 'date_from', 'date_to']);

        // Get compliance statistics by business
        $businessCompliance = Business::with(['checklistEvidence' => function($query) use ($filters) {
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            if (!empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }
            if (!empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }
        }])
        ->where('status', 'active')
        ->get()
        ->map(function($business) {
            $totalItems = InspectionChecklistItem::active()->count();
            $approvedEvidence = $business->checklistEvidence->where('status', 'approved')->count();
            $pendingEvidence = $business->checklistEvidence->where('status', 'pending')->count();
            $rejectedEvidence = $business->checklistEvidence->where('status', 'rejected')->count();

            return [
                'business' => $business,
                'total_items' => $totalItems,
                'approved_evidence' => $approvedEvidence,
                'pending_evidence' => $pendingEvidence,
                'rejected_evidence' => $rejectedEvidence,
                'compliance_percentage' => $totalItems > 0 
                    ? round(($approvedEvidence / $totalItems) * 100, 1)
                    : 0,
            ];
        });

        // Get overall statistics
        $overallStats = [
            'total_businesses' => Business::where('status', 'active')->count(),
            'compliant_businesses' => Business::where('compliance_status', 'compliant')->count(),
            'non_compliant_businesses' => Business::where('compliance_status', 'non_compliant')->count(),
            'pending_review_businesses' => Business::where('compliance_status', 'pending_review')->count(),
        ];

        return view('admin.compliance-evidence.report', compact(
            'businessCompliance',
            'overallStats',
            'filters'
        ));
    }

    /**
     * Export compliance data
     */
    public function exportCompliance(Request $request)
    {
        $filters = $request->only(['business_id', 'status', 'date_from', 'date_to']);

        $query = BusinessChecklistEvidence::with(['business', 'checklistItem.category']);

        // Apply filters
        if (!empty($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        $evidence = $query->get();

        $exportData = $evidence->map(function($item) {
            return [
                'Business Name' => $item->business->name,
                'Owner' => $item->business->owner_name,
                'Category' => $item->checklistItem->category->name,
                'Checklist Item' => $item->checklistItem->item_name,
                'Status' => ucfirst($item->status),
                'Upload Date' => $item->created_at->format('Y-m-d H:i:s'),
                'Verified Date' => $item->verified_at ? $item->verified_at->format('Y-m-d H:i:s') : 'Not verified',
                'Verification Notes' => $item->verification_notes,
                'File Name' => $item->file_name,
                'File Size' => $this->formatFileSize($item->file_size),
            ];
        })->toArray();

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'filename' => 'compliance_evidence_' . now()->format('Y-m-d') . '.xlsx'
        ]);
    }

    /**
     * Update business compliance status based on evidence
     */
    private function updateBusinessComplianceStatus($businessId)
    {
        $business = Business::find($businessId);
        if (!$business) return;

        $totalItems = InspectionChecklistItem::active()->count();
        $approvedEvidence = BusinessChecklistEvidence::where('business_id', $businessId)
            ->where('status', 'approved')
            ->count();

        $compliancePercentage = $totalItems > 0 ? ($approvedEvidence / $totalItems) * 100 : 0;

        if ($compliancePercentage >= 80) {
            $complianceStatus = 'compliant';
        } elseif ($compliancePercentage >= 50) {
            $complianceStatus = 'pending_review';
        } else {
            $complianceStatus = 'non_compliant';
        }

        $business->update(['compliance_status' => $complianceStatus]);
    }

    /**
     * Format file size for display
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
