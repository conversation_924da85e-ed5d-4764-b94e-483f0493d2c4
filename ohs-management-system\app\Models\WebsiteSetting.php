<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class WebsiteSetting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'description'
    ];

    protected $casts = [
        'value' => 'string'
    ];

    /**
     * Get a setting value by key
     */
    public static function getSetting($key, $default = null)
    {
        $setting = Cache::remember("setting_{$key}", 3600, function () use ($key) {
            return static::where('key', $key)->first();
        });

        return $setting ? $setting->value : $default;
    }

    /**
     * Update or create a setting
     */
    public static function updateSetting($key, $value)
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );

        // Clear cache
        Cache::forget("setting_{$key}");
        Cache::forget('all_settings');

        return $setting;
    }

    /**
     * Get all settings as key-value pairs
     */
    public static function getAllSettings()
    {
        return Cache::remember('all_settings', 3600, function () {
            return static::pluck('value', 'key')->toArray();
        });
    }

    /**
     * Reset all settings to defaults
     */
    public static function resetToDefaults()
    {
        $defaultSettings = [
            // General settings
            'site_name' => 'OHS Management System',
            'site_tagline' => 'Occupational Health and Safety Management for Bacoor City',
            'site_description' => 'Professional OHS management system for businesses in Bacoor City',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+63 ************',
            'office_address' => 'Bacoor City Hall, Cavite, Philippines',
            'office_hours' => 'Monday - Friday: 8:00 AM - 5:00 PM',
            'facebook_url' => '',
            'twitter_url' => '',
            'youtube_url' => '',
            
            // Colors
            'primary_color' => '#2563eb',
            'secondary_color' => '#1e40af',
            'accent_color' => '#3b82f6',
            'success_color' => '#059669',
            'warning_color' => '#d97706',
            'danger_color' => '#dc2626',
            'dark_color' => '#1f2937',
            'light_color' => '#f8fafc',
            
            // Images
            'hero_bg' => '/images/hero-bg.jpg',
            'logo' => '/images/logo.png',
            'about_image' => '/images/about-us.jpg',
            'services_image' => '/images/services.jpg',
            'contact_image' => '/images/contact.jpg'
        ];

        foreach ($defaultSettings as $key => $value) {
            static::updateSetting($key, $value);
        }

        // Clear all cache
        Cache::flush();

        return true;
    }

    /**
     * Get settings by category
     */
    public static function getSettingsByCategory($category)
    {
        $allSettings = static::getAllSettings();
        $categorySettings = [];
        
        switch ($category) {
            case 'general':
                $keys = ['site_name', 'site_tagline', 'site_description', 'contact_email', 'contact_phone', 'office_address', 'office_hours'];
                break;
            case 'social':
                $keys = ['facebook_url', 'twitter_url', 'youtube_url'];
                break;
            case 'colors':
                $keys = ['primary_color', 'secondary_color', 'accent_color', 'success_color', 'warning_color', 'danger_color', 'dark_color', 'light_color'];
                break;
            case 'images':
                $keys = ['hero_bg', 'logo', 'about_image', 'services_image', 'contact_image'];
                break;
            default:
                return $allSettings;
        }

        foreach ($keys as $key) {
            if (isset($allSettings[$key])) {
                $categorySettings[$key] = $allSettings[$key];
            }
        }

        return $categorySettings;
    }

    /**
     * Get current color palette
     */
    public static function getCurrentColorPalette()
    {
        $colorKeys = ['primary_color', 'secondary_color', 'accent_color', 'success_color', 'warning_color', 'danger_color', 'dark_color', 'light_color'];
        $palette = [];
        
        foreach ($colorKeys as $key) {
            $palette[$key] = static::getSetting($key, '#2563eb');
        }
        
        return $palette;
    }

    /**
     * Validate color value
     */
    public static function validateColor($color)
    {
        return preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color);
    }

    /**
     * Get image settings with full URLs
     */
    public static function getImageSettings()
    {
        $imageKeys = ['hero_bg', 'logo', 'about_image', 'services_image', 'contact_image'];
        $images = [];
        
        foreach ($imageKeys as $key) {
            $path = static::getSetting($key);
            $images[$key] = [
                'path' => $path,
                'url' => $path ? asset($path) : '',
                'exists' => $path ? file_exists(public_path($path)) : false
            ];
        }
        
        return $images;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings are updated
        static::saved(function ($setting) {
            Cache::forget("setting_{$setting->key}");
            Cache::forget('all_settings');
        });

        static::deleted(function ($setting) {
            Cache::forget("setting_{$setting->key}");
            Cache::forget('all_settings');
        });
    }
}
