<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Business Details</h1>
        <div>
            <a href="<?= BASE_URL ?>business/edit/<?= $business['id'] ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?= BASE_URL ?>chat/create/<?= $business['id'] ?>" class="btn btn-success">
                <i class="fas fa-comments"></i> Contact Support
            </a>
            <a href="<?= BASE_URL ?>business" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Business Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Business Name</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['name']) ?></p>

                            <h6 class="text-muted">Registration Number</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['registration_number']) ?></p>

                            <h6 class="text-muted">Email</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['email']) ?></p>

                            <h6 class="text-muted">Contact Number</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['contact_number']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Address</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['address']) ?></p>

                            <h6 class="text-muted">Category</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['category_name'] ?? 'N/A') ?></p>

                            <h6 class="text-muted">District</h6>
                            <p class="mb-3"><?= htmlspecialchars($business['district_name'] ?? 'N/A') ?></p>

                            <h6 class="text-muted">Employee Count</h6>
                            <p class="mb-3"><?= $business['employee_count'] ?? 'N/A' ?></p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Date Established</h6>
                            <p class="mb-3"><?= $business['date_established'] ? date('F d, Y', strtotime($business['date_established'])) : 'N/A' ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Registration Date</h6>
                            <p class="mb-3">
                                <?php if ($business['created_at']): ?>
                                    <?= date('F d, Y', strtotime($business['created_at'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">Unknown</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Business Status</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="mb-3">
                            <h5 class="mb-1">Status</h5>
                            <?php
                            $businessStatus = $business['status'] ?? 'pending';
                            $statusClass = ($businessStatus === 'active' || $businessStatus === 'approved') ? 'success' : 'warning';
                            ?>
                            <span class="badge bg-<?= $statusClass ?> fs-6">
                                <?= ucfirst($businessStatus) ?>
                            </span>
                        </div>

                        <div class="mb-3">
                            <h5 class="mb-1">Compliance Status</h5>
                            <span class="badge bg-<?=
                                $business['compliance_status'] == 'compliant' ? 'success' :
                                ($business['compliance_status'] == 'non_compliant' ? 'danger' : 'warning')
                            ?> fs-6">
                                <?= ucfirst(str_replace('_', ' ', $business['compliance_status'] ?? 'pending')) ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>business/edit/<?= $business['id'] ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Business
                        </a>
                        <a href="<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>" class="btn btn-success">
                            <i class="fas fa-clipboard-list"></i> View Checklist
                        </a>
                        <form method="POST" action="<?= BASE_URL ?>business/delete/<?= $business['id'] ?>"
                              onsubmit="return confirm('Are you sure you want to delete this business? This action cannot be undone.')">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash"></i> Delete Business
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?>
