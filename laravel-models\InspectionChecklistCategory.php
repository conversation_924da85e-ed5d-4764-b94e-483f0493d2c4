<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InspectionChecklistCategory extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'weight',
        'sort_order',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'weight' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get checklist items in this category
     */
    public function checklistItems()
    {
        return $this->hasMany(InspectionChecklistItem::class, 'category_id')->orderBy('sort_order');
    }

    /**
     * Get active checklist items
     */
    public function activeChecklistItems()
    {
        return $this->hasMany(InspectionChecklistItem::class, 'category_id')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get total points for this category
     */
    public function getTotalPointsAttribute(): int
    {
        return $this->activeChecklistItems()->sum('points');
    }
}
