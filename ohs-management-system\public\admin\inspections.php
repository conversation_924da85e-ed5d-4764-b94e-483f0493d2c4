<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /ohs-management-system/public/login');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed.');
}

// Create inspections table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS inspections (
        id CHAR(36) PRIMARY KEY,
        business_id CHAR(36) NOT NULL,
        inspector_id CHAR(36) NOT NULL,
        assigned_by CHAR(36) NOT NULL,
        scheduled_date DATE NOT NULL,
        scheduled_time TIME DEFAULT '09:00:00',
        completed_date DATETIME NULL,
        status ENUM('scheduled', 'in_progress', 'completed', 'cancelled', 'rescheduled') DEFAULT 'scheduled',
        inspection_type ENUM('routine', 'follow_up', 'complaint', 'initial') DEFAULT 'routine',
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        score INT NULL,
        notes TEXT,
        findings TEXT,
        recommendations TEXT,
        compliance_rating ENUM('compliant', 'non_compliant', 'pending') DEFAULT 'pending',
        inspector_notes TEXT,
        admin_notes TEXT,
        admin_verified BOOLEAN DEFAULT FALSE,
        verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        verified_by CHAR(36) NULL,
        verified_at DATETIME NULL,
        verification_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (business_id) REFERENCES businesses(id) ON DELETE CASCADE,
        FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL
    )");
} catch (Exception $e) {
    // Table might already exist
}

// Handle form submissions
if ($_POST) {
    $success = false;
    $error = '';

    try {
        if ($_POST['action'] === 'schedule_inspection') {
            // Check for conflicts
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM inspections WHERE inspector_id = ? AND scheduled_date = ? AND status != 'cancelled'");
            $stmt->execute([$_POST['inspector_id'], $_POST['scheduled_date']]);
            $conflict = $stmt->fetchColumn();

            if ($conflict > 0) {
                $error = 'Inspector already has an inspection scheduled for this date.';
            } else {
                $stmt = $pdo->prepare("INSERT INTO inspections (id, business_id, inspector_id, assigned_by, scheduled_date, scheduled_time, inspection_type, priority, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    bin2hex(random_bytes(16)),
                    $_POST['business_id'],
                    $_POST['inspector_id'],
                    $_SESSION['user_id'],
                    $_POST['scheduled_date'],
                    $_POST['scheduled_time'] ?? '09:00:00',
                    $_POST['inspection_type'],
                    $_POST['priority'],
                    $_POST['notes']
                ]);
                $success = "Inspection scheduled successfully!";
            }
        }

        elseif ($_POST['action'] === 'bulk_schedule') {
            $business_ids = $_POST['business_ids'] ?? [];
            $inspector_id = $_POST['inspector_id'];
            $start_date = $_POST['start_date'];
            $inspection_type = $_POST['inspection_type'];
            $priority = $_POST['priority'];

            $scheduled_count = 0;
            $current_date = new DateTime($start_date);

            foreach ($business_ids as $business_id) {
                // Check for conflicts
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM inspections WHERE inspector_id = ? AND scheduled_date = ? AND status != 'cancelled'");
                $stmt->execute([$inspector_id, $current_date->format('Y-m-d')]);
                $conflict = $stmt->fetchColumn();

                if ($conflict == 0) {
                    $stmt = $pdo->prepare("INSERT INTO inspections (id, business_id, inspector_id, assigned_by, scheduled_date, inspection_type, priority, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'scheduled')");
                    $stmt->execute([
                        bin2hex(random_bytes(16)),
                        $business_id,
                        $inspector_id,
                        $_SESSION['user_id'],
                        $current_date->format('Y-m-d'),
                        $inspection_type,
                        $priority
                    ]);
                    $scheduled_count++;
                }

                // Move to next day
                $current_date->add(new DateInterval('P1D'));
            }

            $success = "{$scheduled_count} inspections scheduled successfully!";
        }

        elseif ($_POST['action'] === 'update_status') {
            $stmt = $pdo->prepare("UPDATE inspections SET status = ?, admin_notes = ? WHERE id = ?");
            $stmt->execute([$_POST['status'], $_POST['admin_notes'] ?? '', $_POST['inspection_id']]);
            $success = "Inspection status updated successfully!";
        }

        elseif ($_POST['action'] === 'reschedule') {
            // Check for conflicts
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM inspections WHERE inspector_id = ? AND scheduled_date = ? AND status != 'cancelled' AND id != ?");
            $stmt->execute([$_POST['inspector_id'], $_POST['new_date'], $_POST['inspection_id']]);
            $conflict = $stmt->fetchColumn();

            if ($conflict > 0) {
                $error = 'Inspector already has an inspection scheduled for this date.';
            } else {
                $stmt = $pdo->prepare("UPDATE inspections SET scheduled_date = ?, scheduled_time = ?, status = 'rescheduled', admin_notes = ? WHERE id = ?");
                $stmt->execute([$_POST['new_date'], $_POST['new_time'], $_POST['reschedule_reason'], $_POST['inspection_id']]);
                $success = "Inspection rescheduled successfully!";
            }
        }

    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get filters
$status_filter = $_GET['status'] ?? '';
$inspector_filter = $_GET['inspector'] ?? '';
$date_filter = $_GET['date'] ?? '';
$type_filter = $_GET['type'] ?? '';

// Build query conditions
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "i.status = ?";
    $params[] = $status_filter;
}

if ($inspector_filter) {
    $where_conditions[] = "i.inspector_id = ?";
    $params[] = $inspector_filter;
}

if ($date_filter) {
    $where_conditions[] = "i.scheduled_date = ?";
    $params[] = $date_filter;
}

if ($type_filter) {
    $where_conditions[] = "i.inspection_type = ?";
    $params[] = $type_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get inspections with business and inspector details
$inspections_query = "
    SELECT i.*,
           b.name as business_name,
           b.address as business_address,
           br.name as barangay_name,
           d.name as district_name,
           u.full_name as inspector_name,
           u.email as inspector_email,
           a.full_name as assigned_by_name
    FROM inspections i
    LEFT JOIN businesses b ON i.business_id = b.id
    LEFT JOIN barangays br ON b.barangay_id = br.id
    LEFT JOIN districts d ON br.district_id = d.id
    LEFT JOIN users u ON i.inspector_id = u.id
    LEFT JOIN users a ON i.assigned_by = a.id
    $where_clause
    ORDER BY i.scheduled_date DESC, i.scheduled_time ASC
";

$inspections = $pdo->prepare($inspections_query);
$inspections->execute($params);
$inspections = $inspections->fetchAll(PDO::FETCH_ASSOC);

// Get inspectors for dropdowns
$inspectors = $pdo->query("SELECT id, full_name, email FROM users WHERE role = 'inspector' AND status = 'active' ORDER BY full_name")->fetchAll(PDO::FETCH_ASSOC);

// Get businesses for scheduling
$businesses = $pdo->query("
    SELECT b.id, b.name, b.address, br.name as barangay_name, d.name as district_name
    FROM businesses b
    LEFT JOIN barangays br ON b.barangay_id = br.id
    LEFT JOIN districts d ON br.district_id = d.id
    WHERE b.status = 'active'
    ORDER BY b.name
")->fetchAll(PDO::FETCH_ASSOC);

// Get barangays for bulk scheduling
$barangays = $pdo->query("
    SELECT br.id, br.name, d.name as district_name
    FROM barangays br
    LEFT JOIN districts d ON br.district_id = d.id
    ORDER BY d.name, br.name
")->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [
    'total' => $pdo->query("SELECT COUNT(*) FROM inspections")->fetchColumn(),
    'scheduled' => $pdo->query("SELECT COUNT(*) FROM inspections WHERE status = 'scheduled'")->fetchColumn(),
    'completed' => $pdo->query("SELECT COUNT(*) FROM inspections WHERE status = 'completed'")->fetchColumn(),
    'overdue' => $pdo->query("SELECT COUNT(*) FROM inspections WHERE status = 'scheduled' AND scheduled_date < CURDATE()")->fetchColumn(),
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspection Management - OHS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 280px;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover { background: rgba(255,255,255,0.1); color: white; }
        .sidebar .nav-link.active { background: rgba(255,255,255,0.2); color: white; }
        .main-content { margin-left: 280px; padding: 2rem; }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
        }
        .stat-card.total { border-left-color: #007bff; }
        .stat-card.scheduled { border-left-color: #28a745; }
        .stat-card.completed { border-left-color: #17a2b8; }
        .stat-card.overdue { border-left-color: #dc3545; }
        .inspection-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        .inspection-card.scheduled { border-left-color: #28a745; }
        .inspection-card.completed { border-left-color: #17a2b8; }
        .inspection-card.overdue { border-left-color: #dc3545; }
        .inspection-card.cancelled { border-left-color: #6c757d; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="p-3">
                    <h4><i class="fas fa-shield-alt me-2"></i>OHS Admin</h4>
                    <hr class="text-white">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/ohs-management-system/public/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="/ohs-management-system/public/admin/users">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="/ohs-management-system/public/admin/businesses">
                            <i class="fas fa-building me-2"></i>Businesses
                        </a>
                        <a class="nav-link active" href="/ohs-management-system/public/admin/inspections">
                            <i class="fas fa-clipboard-check me-2"></i>Inspections
                        </a>
                        <a class="nav-link" href="/ohs-management-system/public/admin/inspection-checklist">
                            <i class="fas fa-list-check me-2"></i>Inspection Checklist
                        </a>
                        <a class="nav-link" href="/ohs-management-system/public/admin/chat">
                            <i class="fas fa-comments me-2"></i>Live Chat
                        </a>
                        <a class="nav-link" href="/ohs-management-system/public/admin/website-settings">
                            <i class="fas fa-cog me-2"></i>Website Settings
                        </a>
                        <hr class="text-white">
                        <a class="nav-link" href="/ohs-management-system/public/?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-clipboard-check me-2"></i>Inspection Management</h2>
                        <p class="text-muted mb-0">Schedule, manage, and track safety inspections</p>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                            <i class="fas fa-calendar-plus me-2"></i>Schedule Inspection
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkScheduleModal">
                            <i class="fas fa-calendar-week me-2"></i>Bulk Schedule
                        </button>
                    </div>
                </div>

                <?php if (isset($success) && $success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?= $success ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error) && $error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?= $error ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card total">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total'] ?></h3>
                                    <p class="text-muted mb-0">Total Inspections</p>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-clipboard-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card scheduled">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['scheduled'] ?></h3>
                                    <p class="text-muted mb-0">Scheduled</p>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-calendar-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card completed">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['completed'] ?></h3>
                                    <p class="text-muted mb-0">Completed</p>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card overdue">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['overdue'] ?></h3>
                                    <p class="text-muted mb-0">Overdue</p>
                                </div>
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="scheduled" <?= $status_filter === 'scheduled' ? 'selected' : '' ?>>Scheduled</option>
                                    <option value="in_progress" <?= $status_filter === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                                    <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                                    <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                    <option value="rescheduled" <?= $status_filter === 'rescheduled' ? 'selected' : '' ?>>Rescheduled</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="inspector">
                                    <option value="">All Inspectors</option>
                                    <?php foreach ($inspectors as $inspector): ?>
                                    <option value="<?= $inspector['id'] ?>" <?= $inspector_filter === $inspector['id'] ? 'selected' : '' ?>><?= htmlspecialchars($inspector['full_name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="date" value="<?= htmlspecialchars($date_filter) ?>">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="type">
                                    <option value="">All Types</option>
                                    <option value="routine" <?= $type_filter === 'routine' ? 'selected' : '' ?>>Routine</option>
                                    <option value="follow_up" <?= $type_filter === 'follow_up' ? 'selected' : '' ?>>Follow-up</option>
                                    <option value="complaint" <?= $type_filter === 'complaint' ? 'selected' : '' ?>>Complaint</option>
                                    <option value="initial" <?= $type_filter === 'initial' ? 'selected' : '' ?>>Initial</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="/ohs-management-system/public/admin/inspections" class="btn btn-secondary w-100">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Inspections List -->
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($inspections)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                                <h4>No Inspections Found</h4>
                                <p class="text-muted">Start by scheduling your first inspection.</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                                    <i class="fas fa-calendar-plus me-2"></i>Schedule First Inspection
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date & Time</th>
                                            <th>Business</th>
                                            <th>Inspector</th>
                                            <th>Type</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($inspections as $inspection): ?>
                                        <tr class="<?= $inspection['status'] === 'scheduled' && $inspection['scheduled_date'] < date('Y-m-d') ? 'table-danger' : '' ?>">
                                            <td>
                                                <div>
                                                    <strong><?= date('M j, Y', strtotime($inspection['scheduled_date'])) ?></strong>
                                                    <br><small class="text-muted"><?= date('g:i A', strtotime($inspection['scheduled_time'])) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($inspection['barangay_name'] . ', ' . $inspection['district_name']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?= htmlspecialchars($inspection['inspector_name']) ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($inspection['inspector_email']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?=
                                                    $inspection['inspection_type'] === 'routine' ? 'primary' :
                                                    ($inspection['inspection_type'] === 'follow_up' ? 'warning' :
                                                    ($inspection['inspection_type'] === 'complaint' ? 'danger' : 'info'))
                                                ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?=
                                                    $inspection['priority'] === 'urgent' ? 'danger' :
                                                    ($inspection['priority'] === 'high' ? 'warning' :
                                                    ($inspection['priority'] === 'medium' ? 'info' : 'secondary'))
                                                ?>">
                                                    <?= ucfirst($inspection['priority']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?=
                                                    $inspection['status'] === 'completed' ? 'success' :
                                                    ($inspection['status'] === 'scheduled' ? 'primary' :
                                                    ($inspection['status'] === 'in_progress' ? 'warning' :
                                                    ($inspection['status'] === 'cancelled' ? 'secondary' : 'info')))
                                                ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                                </span>
                                                <?php if ($inspection['status'] === 'scheduled' && $inspection['scheduled_date'] < date('Y-m-d')): ?>
                                                    <br><small class="text-danger">Overdue</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewInspection('<?= $inspection['id'] ?>')" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($inspection['status'] === 'scheduled'): ?>
                                                    <button class="btn btn-outline-warning" onclick="rescheduleInspection('<?= $inspection['id'] ?>', '<?= $inspection['scheduled_date'] ?>', '<?= $inspection['scheduled_time'] ?>', '<?= $inspection['inspector_id'] ?>')" title="Reschedule">
                                                        <i class="fas fa-calendar-alt"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="cancelInspection('<?= $inspection['id'] ?>')" title="Cancel">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Inspection Modal -->
    <div class="modal fade" id="scheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Schedule New Inspection</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="schedule_inspection">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_id" class="form-label">Business</label>
                                    <select class="form-select" id="business_id" name="business_id" required>
                                        <option value="">Select Business</option>
                                        <?php foreach ($businesses as $business): ?>
                                        <option value="<?= $business['id'] ?>"><?= htmlspecialchars($business['name'] . ' - ' . $business['barangay_name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="inspector_id" class="form-label">Inspector</label>
                                    <select class="form-select" id="inspector_id" name="inspector_id" required>
                                        <option value="">Select Inspector</option>
                                        <?php foreach ($inspectors as $inspector): ?>
                                        <option value="<?= $inspector['id'] ?>"><?= htmlspecialchars($inspector['full_name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="scheduled_date" class="form-label">Date</label>
                                    <input type="date" class="form-control" id="scheduled_date" name="scheduled_date" required min="<?= date('Y-m-d', strtotime('+1 day')) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="scheduled_time" class="form-label">Time</label>
                                    <input type="time" class="form-control" id="scheduled_time" name="scheduled_time" value="09:00">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="inspection_type" class="form-label">Type</label>
                                    <select class="form-select" id="inspection_type" name="inspection_type" required>
                                        <option value="routine">Routine</option>
                                        <option value="follow_up">Follow-up</option>
                                        <option value="complaint">Complaint</option>
                                        <option value="initial">Initial</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">Priority</label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Additional notes or instructions..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Schedule Inspection</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bulk Schedule Modal -->
    <div class="modal fade" id="bulkScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Schedule Inspections</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="bulk_schedule">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bulk_inspector_id" class="form-label">Inspector</label>
                                    <select class="form-select" id="bulk_inspector_id" name="inspector_id" required>
                                        <option value="">Select Inspector</option>
                                        <?php foreach ($inspectors as $inspector): ?>
                                        <option value="<?= $inspector['id'] ?>"><?= htmlspecialchars($inspector['full_name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required min="<?= date('Y-m-d', strtotime('+1 day')) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bulk_inspection_type" class="form-label">Type</label>
                                    <select class="form-select" id="bulk_inspection_type" name="inspection_type" required>
                                        <option value="routine">Routine</option>
                                        <option value="follow_up">Follow-up</option>
                                        <option value="complaint">Complaint</option>
                                        <option value="initial">Initial</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bulk_priority" class="form-label">Priority</label>
                                    <select class="form-select" id="bulk_priority" name="priority" required>
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Select Businesses</label>
                            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px;">
                                <?php foreach ($businesses as $business): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="business_ids[]" value="<?= $business['id'] ?>" id="business_<?= $business['id'] ?>">
                                    <label class="form-check-label" for="business_<?= $business['id'] ?>">
                                        <?= htmlspecialchars($business['name']) ?> - <?= htmlspecialchars($business['barangay_name']) ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <small class="text-muted">Inspections will be scheduled on consecutive days starting from the selected date.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Schedule Inspections</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reschedule Modal -->
    <div class="modal fade" id="rescheduleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reschedule Inspection</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="rescheduleForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reschedule">
                        <input type="hidden" name="inspection_id" id="reschedule_inspection_id">
                        <input type="hidden" name="inspector_id" id="reschedule_inspector_id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="new_date" class="form-label">New Date</label>
                                    <input type="date" class="form-control" id="new_date" name="new_date" required min="<?= date('Y-m-d', strtotime('+1 day')) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="new_time" class="form-label">New Time</label>
                                    <input type="time" class="form-control" id="new_time" name="new_time" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="reschedule_reason" class="form-label">Reason for Rescheduling</label>
                            <textarea class="form-control" id="reschedule_reason" name="reschedule_reason" rows="3" required placeholder="Please provide a reason for rescheduling..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Reschedule</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hidden forms for actions -->
    <form method="POST" id="statusForm" style="display: none;">
        <input type="hidden" name="action" value="update_status">
        <input type="hidden" name="inspection_id" id="status_inspection_id">
        <input type="hidden" name="status" id="status_value">
        <input type="hidden" name="admin_notes" id="status_notes">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewInspection(inspectionId) {
            // Implement view inspection details
            alert('View inspection details for ID: ' + inspectionId);
        }

        function rescheduleInspection(inspectionId, currentDate, currentTime, inspectorId) {
            document.getElementById('reschedule_inspection_id').value = inspectionId;
            document.getElementById('reschedule_inspector_id').value = inspectorId;
            document.getElementById('new_date').value = currentDate;
            document.getElementById('new_time').value = currentTime;

            new bootstrap.Modal(document.getElementById('rescheduleModal')).show();
        }

        function cancelInspection(inspectionId) {
            if (confirm('Are you sure you want to cancel this inspection?')) {
                document.getElementById('status_inspection_id').value = inspectionId;
                document.getElementById('status_value').value = 'cancelled';
                document.getElementById('status_notes').value = 'Cancelled by admin';
                document.getElementById('statusForm').submit();
            }
        }

        // Auto-set minimum date for scheduling
        document.addEventListener('DOMContentLoaded', function() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];

            document.getElementById('scheduled_date').min = tomorrowStr;
            document.getElementById('start_date').min = tomorrowStr;
            document.getElementById('new_date').min = tomorrowStr;
        });
    </script>
</body>
</html>