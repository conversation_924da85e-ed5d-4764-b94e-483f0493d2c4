<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BusinessCategory extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * Get businesses in this category
     */
    public function businesses()
    {
        return $this->hasMany(Business::class, 'category_id');
    }

    /**
     * Get business count for this category
     */
    public function getBusinessCountAttribute(): int
    {
        return $this->businesses()->count();
    }
}
