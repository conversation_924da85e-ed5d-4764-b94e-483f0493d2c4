<?php
$auth = \App\Libraries\Auth::getInstance();

if (!$auth->isLoggedIn()) {
    header('Location: ' . BASE_URL . 'login');
    exit();
}

$user = $auth->getUser();
$title = 'Dashboard';

ob_start();
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">Welcome, <?= htmlspecialchars($user['full_name']) ?></h1>
        </div>
    </div>

    <?php if ($auth->isInspector()): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Upcoming Inspections</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Use consistent method for getting upcoming inspections
                        $inspections = (new \App\Models\Inspection())->getByInspector($user['id'], ['scheduled', 'confirmed']);
                        // Limit to next 5 for dashboard overview
                        $inspections = array_slice($inspections, 0, 5);
                        if (empty($inspections)):
                        ?>
                            <div class="text-center py-4">
                                <img src="<?= BASE_URL ?>assets/img/empty-state.svg" alt="No inspections" class="mb-3" style="max-width: 200px;">
                                <p class="text-muted">No upcoming inspections scheduled.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Business</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($inspections as $inspection): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($inspection['business_name']) ?></td>
                                                <td><?= date('M j, Y', strtotime($inspection['scheduled_date'])) ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $inspection['status'] === 'completed' ? 'success' : 'warning' ?>">
                                                        <?= ucfirst($inspection['status']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>" class="btn btn-sm btn-success">
                                                        <i class="fas fa-clipboard-check me-1"></i>Start Inspection
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
require_once __DIR__ . '/layouts/app.php';
?>