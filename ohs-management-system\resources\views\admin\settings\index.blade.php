@extends('layouts.admin')

@section('title', 'Website Settings')

@section('content')
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Website Settings</h1>
</div>

<!-- Settings Navigation -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog me-2"></i>General
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="colors-tab" data-bs-toggle="tab" data-bs-target="#colors" type="button" role="tab">
                            <i class="fas fa-palette me-2"></i>Colors & Theme
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="images-tab" data-bs-toggle="tab" data-bs-target="#images" type="button" role="tab">
                            <i class="fas fa-images me-2"></i>Images
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="settingsTabContent">
                    <!-- General Settings Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <form action="{{ route('admin.settings.update-general') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" 
                                               value="{{ $settings['site_name'] ?? 'OHS Management System' }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="site_tagline" class="form-label">Site Tagline</label>
                                        <input type="text" class="form-control" id="site_tagline" name="site_tagline" 
                                               value="{{ $settings['site_tagline'] ?? '' }}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_description" class="form-label">Site Description</label>
                                <textarea class="form-control" id="site_description" name="site_description" rows="3">{{ $settings['site_description'] ?? '' }}</textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="{{ $settings['contact_email'] ?? '' }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="{{ $settings['contact_phone'] ?? '' }}">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="office_address" class="form-label">Office Address</label>
                                <textarea class="form-control" id="office_address" name="office_address" rows="2">{{ $settings['office_address'] ?? '' }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="office_hours" class="form-label">Office Hours</label>
                                <input type="text" class="form-control" id="office_hours" name="office_hours" 
                                       value="{{ $settings['office_hours'] ?? '' }}">
                            </div>

                            <h5 class="mt-4 mb-3">Social Media Links</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="facebook_url" class="form-label">Facebook URL</label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                               value="{{ $settings['facebook_url'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="twitter_url" class="form-label">Twitter URL</label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                               value="{{ $settings['twitter_url'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="youtube_url" class="form-label">YouTube URL</label>
                                        <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                                               value="{{ $settings['youtube_url'] ?? '' }}">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save General Settings
                            </button>
                        </form>
                    </div>

                    <!-- Colors & Theme Tab -->
                    <div class="tab-pane fade" id="colors" role="tabpanel">
                        <!-- Predefined Color Schemes -->
                        <div class="mb-4">
                            <h5>Predefined Color Schemes</h5>
                            <div class="row">
                                @foreach($colorSchemes as $schemeId => $scheme)
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6 class="card-title">{{ $scheme['name'] }}</h6>
                                            <p class="card-text small">{{ $scheme['description'] }}</p>
                                            <div class="d-flex justify-content-center mb-3">
                                                @foreach(array_slice($scheme['colors'], 0, 4) as $color)
                                                <div class="color-preview me-1" style="background-color: {{ $color }}; width: 20px; height: 20px; border-radius: 3px;"></div>
                                                @endforeach
                                            </div>
                                            <form action="{{ route('admin.settings.apply-color-scheme') }}" method="POST" class="d-inline">
                                                @csrf
                                                <input type="hidden" name="scheme_id" value="{{ $schemeId }}">
                                                <button type="submit" class="btn btn-sm btn-outline-primary">Apply</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Custom Colors -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Custom Colors</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('admin.settings.update-colors') }}" method="POST">
                                    @csrf
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="primary_color" class="form-label">Primary Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" 
                                                       value="{{ $settings['primary_color'] ?? '#2563eb' }}">
                                                <input type="text" class="form-control" value="{{ $settings['primary_color'] ?? '#2563eb' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="secondary_color" class="form-label">Secondary Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" 
                                                       value="{{ $settings['secondary_color'] ?? '#1e40af' }}">
                                                <input type="text" class="form-control" value="{{ $settings['secondary_color'] ?? '#1e40af' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="success_color" class="form-label">Success Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="success_color" name="success_color" 
                                                       value="{{ $settings['success_color'] ?? '#059669' }}">
                                                <input type="text" class="form-control" value="{{ $settings['success_color'] ?? '#059669' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="warning_color" class="form-label">Warning Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="warning_color" name="warning_color" 
                                                       value="{{ $settings['warning_color'] ?? '#d97706' }}">
                                                <input type="text" class="form-control" value="{{ $settings['warning_color'] ?? '#d97706' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="danger_color" class="form-label">Danger Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color" 
                                                       value="{{ $settings['danger_color'] ?? '#dc2626' }}">
                                                <input type="text" class="form-control" value="{{ $settings['danger_color'] ?? '#dc2626' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="accent_color" class="form-label">Accent Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="accent_color" name="accent_color" 
                                                       value="{{ $settings['accent_color'] ?? '#3b82f6' }}">
                                                <input type="text" class="form-control" value="{{ $settings['accent_color'] ?? '#3b82f6' }}" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-palette me-2"></i>Update Colors
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Images Tab -->
                    <div class="tab-pane fade" id="images" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Logo</h6>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ route('admin.settings.upload-image') }}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <input type="hidden" name="image_type" value="logo">
                                            <div class="mb-3">
                                                <input type="file" class="form-control" name="image" accept="image/*" required>
                                            </div>
                                            <button type="submit" class="btn btn-sm btn-primary">Upload Logo</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Hero Background</h6>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ route('admin.settings.upload-image') }}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <input type="hidden" name="image_type" value="hero_bg">
                                            <div class="mb-3">
                                                <input type="file" class="form-control" name="image" accept="image/*" required>
                                            </div>
                                            <button type="submit" class="btn btn-sm btn-primary">Upload Background</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset to Defaults -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <h5 class="card-title text-danger">Reset Settings</h5>
                <p class="card-text">Reset all website settings to their default values. This action cannot be undone.</p>
                <form action="{{ route('admin.settings.reset-defaults') }}" method="POST" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')">
                        <i class="fas fa-undo me-2"></i>Reset to Defaults
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Update color text inputs when color picker changes
document.querySelectorAll('input[type="color"]').forEach(function(colorInput) {
    colorInput.addEventListener('change', function() {
        const textInput = this.parentNode.querySelector('input[type="text"]');
        if (textInput) {
            textInput.value = this.value;
        }
    });
});
</script>
@endpush
