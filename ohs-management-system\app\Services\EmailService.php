<?php

namespace App\Services;

use App\Models\User;
use App\Models\Business;
use App\Models\Inspection;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailService
{
    /**
     * Send business owner credentials email
     */
    public function sendBusinessOwnerCredentials(User $owner, string $password, Business $business): bool
    {
        try {
            $data = [
                'owner' => $owner,
                'password' => $password,
                'business' => $business,
                'login_url' => route('login'),
            ];

            Mail::send('emails.business-owner-credentials', $data, function ($message) use ($owner, $business) {
                $message->to($owner->email, $owner->full_name)
                        ->subject('OHS System - Business Registration Successful')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Business owner credentials email sent to: {$owner->email}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send business owner credentials email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send inspection scheduled notification
     */
    public function sendInspectionScheduledNotification(Inspection $inspection): bool
    {
        try {
            $business = $inspection->business;
            $inspector = $inspection->inspector;
            $owner = $business->owner;

            // Send to business owner
            $ownerData = [
                'inspection' => $inspection,
                'business' => $business,
                'inspector' => $inspector,
                'owner' => $owner,
            ];

            Mail::send('emails.inspection-scheduled-owner', $ownerData, function ($message) use ($owner, $inspection) {
                $message->to($owner->email, $owner->full_name)
                        ->subject('OHS Inspection Scheduled - ' . $inspection->scheduled_date->format('M j, Y'))
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Send to inspector
            $inspectorData = [
                'inspection' => $inspection,
                'business' => $business,
                'inspector' => $inspector,
            ];

            Mail::send('emails.inspection-scheduled-inspector', $inspectorData, function ($message) use ($inspector, $inspection) {
                $message->to($inspector->email, $inspector->full_name)
                        ->subject('New Inspection Assignment - ' . $inspection->business->name)
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Inspection scheduled emails sent for inspection: {$inspection->id}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send inspection scheduled emails: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send inspection completed notification
     */
    public function sendInspectionCompletedNotification(Inspection $inspection): bool
    {
        try {
            $business = $inspection->business;
            $inspector = $inspection->inspector;
            $owner = $business->owner;
            $admin = $inspection->assignedBy;

            // Send to business owner
            $ownerData = [
                'inspection' => $inspection,
                'business' => $business,
                'inspector' => $inspector,
                'owner' => $owner,
                'report_url' => route('business-owner.inspections.report', $inspection),
            ];

            Mail::send('emails.inspection-completed-owner', $ownerData, function ($message) use ($owner, $inspection) {
                $message->to($owner->email, $owner->full_name)
                        ->subject('Inspection Completed - Results Pending Verification')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Send to admin
            $adminData = [
                'inspection' => $inspection,
                'business' => $business,
                'inspector' => $inspector,
                'admin' => $admin,
                'verification_url' => route('admin.inspections.show', $inspection),
            ];

            Mail::send('emails.inspection-completed-admin', $adminData, function ($message) use ($admin, $inspection) {
                $message->to($admin->email, $admin->full_name)
                        ->subject('Inspection Completed - Verification Required')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Inspection completed emails sent for inspection: {$inspection->id}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send inspection completed emails: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send inspection verified notification
     */
    public function sendInspectionVerifiedNotification(Inspection $inspection): bool
    {
        try {
            $business = $inspection->business;
            $inspector = $inspection->inspector;
            $owner = $business->owner;
            $verifier = $inspection->verifiedBy;

            $status = $inspection->verification_status === 'approved' ? 'Approved' : 'Rejected';
            $statusClass = $inspection->verification_status === 'approved' ? 'success' : 'danger';

            // Send to business owner
            $ownerData = [
                'inspection' => $inspection,
                'business' => $business,
                'inspector' => $inspector,
                'owner' => $owner,
                'verifier' => $verifier,
                'status' => $status,
                'status_class' => $statusClass,
                'report_url' => route('business-owner.inspections.report', $inspection),
            ];

            Mail::send('emails.inspection-verified-owner', $ownerData, function ($message) use ($owner, $inspection, $status) {
                $message->to($owner->email, $owner->full_name)
                        ->subject("Inspection Results {$status} - " . $inspection->business->name)
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Send to inspector
            $inspectorData = [
                'inspection' => $inspection,
                'business' => $business,
                'inspector' => $inspector,
                'verifier' => $verifier,
                'status' => $status,
                'status_class' => $statusClass,
            ];

            Mail::send('emails.inspection-verified-inspector', $inspectorData, function ($message) use ($inspector, $inspection, $status) {
                $message->to($inspector->email, $inspector->full_name)
                        ->subject("Inspection {$status} - " . $inspection->business->name)
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Inspection verified emails sent for inspection: {$inspection->id}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send inspection verified emails: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send evidence review notification
     */
    public function sendEvidenceReviewNotification(Business $business, string $status, string $itemName): bool
    {
        try {
            $owner = $business->owner;
            $statusText = ucfirst($status);
            $statusClass = $status === 'approved' ? 'success' : 'danger';

            $data = [
                'business' => $business,
                'owner' => $owner,
                'item_name' => $itemName,
                'status' => $statusText,
                'status_class' => $statusClass,
                'checklist_url' => route('business-owner.checklist.index'),
            ];

            Mail::send('emails.evidence-reviewed', $data, function ($message) use ($owner, $statusText, $itemName) {
                $message->to($owner->email, $owner->full_name)
                        ->subject("Evidence {$statusText} - {$itemName}")
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Evidence review email sent to: {$owner->email}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send evidence review email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail(User $user, string $token): bool
    {
        try {
            $data = [
                'user' => $user,
                'token' => $token,
                'reset_url' => route('password.reset', ['token' => $token, 'email' => $user->email]),
                'expire_minutes' => config('auth.passwords.users.expire'),
            ];

            Mail::send('emails.password-reset', $data, function ($message) use ($user) {
                $message->to($user->email, $user->full_name)
                        ->subject('OHS System - Password Reset Request')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Password reset email sent to: {$user->email}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send password reset email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration(string $testEmail): bool
    {
        try {
            $data = [
                'test_time' => now()->format('M j, Y g:i A'),
                'app_name' => config('app.name'),
            ];

            Mail::send('emails.test-email', $data, function ($message) use ($testEmail) {
                $message->to($testEmail)
                        ->subject('OHS System - Email Configuration Test')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            Log::info("Test email sent to: {$testEmail}");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to send test email: " . $e->getMessage());
            return false;
        }
    }
}
