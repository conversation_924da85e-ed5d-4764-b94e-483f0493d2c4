<?php
require_once __DIR__ . '/../../layouts/header.php';
?>

<div class="container">
    <h1><?= htmlspecialchars($business['name']); ?></h1>
    
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success"><?= htmlspecialchars($_SESSION['success']); ?></div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($_SESSION['error']); ?></div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Business Details</h3>
                </div>
                <div class="card-body">
                    <p><strong>Registration Number:</strong> <?= htmlspecialchars($business['registration_number']); ?></p>
                    <p><strong>Category:</strong> <?= htmlspecialchars($business['category_name']); ?></p>
                    <p><strong>District:</strong> <?= htmlspecialchars($business['district_name']); ?></p>
                    <p><strong>Address:</strong> <?= htmlspecialchars($business['address']); ?></p>
                    <p><strong>Contact Email:</strong> <?= htmlspecialchars($business['contact_email']); ?></p>
                    <p><strong>Contact Phone:</strong> <?= htmlspecialchars($business['contact_phone']); ?></p>
                    <p><strong>Employees:</strong> <?= htmlspecialchars($business['employee_count']); ?></p>
                    <p><strong>Status:</strong> 
                        <span class="badge badge-<?= 
                            $business['compliance_status'] === 'COMPLIANT' ? 'success' :
                            ($business['compliance_status'] === 'NON_COMPLIANT' ? 'danger' :
                            ($business['compliance_status'] === 'WARNING' ? 'warning' : 'info'))
                        ?>">
                            <?= htmlspecialchars($business['compliance_status']); ?>
                        </span>
                    </p>
                    <?php if ($business['compliance_status'] === 'GRACE_PERIOD' && $business['grace_period_end']): ?>
                        <p><strong>Grace Period Ends:</strong> <?= date('M d, Y', strtotime($business['grace_period_end'])); ?></p>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="/business/<?= htmlspecialchars($business['id']); ?>/edit" class="btn btn-primary">Edit Business</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3>Documents</h3>
                </div>
                <div class="card-body">
                    <p>Document management will be implemented in the next phase.</p>
                    <a href="#" class="btn btn-secondary disabled">Upload Document</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once __DIR__ . '/../../layouts/footer.php';