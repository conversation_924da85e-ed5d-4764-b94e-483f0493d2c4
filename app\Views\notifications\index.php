<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Notifications</h1>
        <div>
            <button id="mark-all-read" class="btn btn-outline-primary">
                <i class="fas fa-check-double"></i> Mark All as Read
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bell me-2"></i>Your Notifications
                    </h6>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($notifications)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($notifications as $notification): ?>
                                <div class="list-group-item list-group-item-action notification-item <?= $notification['is_read'] ? '' : 'unread' ?>" 
                                     data-notification-id="<?= $notification['id'] ?>"
                                     <?= $notification['action_url'] ? 'onclick="handleNotificationClick(\'' . $notification['id'] . '\', \'' . BASE_URL . $notification['action_url'] . '\')"' : '' ?>>
                                    <div class="d-flex w-100 justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-<?= 
                                                    $notification['type'] === 'inspection' ? 'clipboard-check text-primary' : 
                                                    ($notification['type'] === 'compliance' ? 'file-alt text-success' : 
                                                    ($notification['type'] === 'chat' ? 'comments text-info' : 
                                                    ($notification['type'] === 'warning' ? 'exclamation-triangle text-warning' : 
                                                    ($notification['type'] === 'error' ? 'times-circle text-danger' : 'info-circle text-secondary')))) 
                                                ?> me-2"></i>
                                                <h6 class="mb-0 <?= $notification['is_read'] ? 'text-muted' : 'text-dark' ?>">
                                                    <?= htmlspecialchars($notification['title']) ?>
                                                </h6>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-primary ms-2">New</span>
                                                <?php endif; ?>
                                            </div>
                                            <p class="mb-1 <?= $notification['is_read'] ? 'text-muted' : 'text-secondary' ?>">
                                                <?= htmlspecialchars($notification['message']) ?>
                                            </p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?= date('M d, Y h:i A', strtotime($notification['created_at'])) ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <?php if ($notification['action_url']): ?>
                                                <i class="fas fa-external-link-alt text-muted"></i>
                                            <?php endif; ?>
                                            <?php if (!$notification['is_read']): ?>
                                                <button class="btn btn-sm btn-outline-primary ms-2" 
                                                        onclick="markAsRead('<?= $notification['id'] ?>', event)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-3">No Notifications</h5>
                            <p class="text-muted mb-3">
                                You're all caught up! You'll receive notifications here when there are updates about your 
                                <?php if ($user['role'] === 'business_owner'): ?>
                                    businesses, inspections, or compliance status.
                                <?php elseif ($user['role'] === 'inspector'): ?>
                                    assigned inspections and tasks.
                                <?php else: ?>
                                    system activities and tasks.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark single notification as read
    window.markAsRead = function(notificationId, event) {
        event.stopPropagation();
        
        fetch(`<?= BASE_URL ?>notifications/mark-read/${notificationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
                notificationItem.classList.remove('unread');
                notificationItem.querySelector('.badge')?.remove();
                notificationItem.querySelector('.btn-outline-primary')?.remove();
                
                // Update text colors
                const title = notificationItem.querySelector('h6');
                const message = notificationItem.querySelector('p');
                title.classList.remove('text-dark');
                title.classList.add('text-muted');
                message.classList.remove('text-secondary');
                message.classList.add('text-muted');
            } else {
                alert('Failed to mark notification as read');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to mark notification as read');
        });
    };

    // Mark all notifications as read
    document.getElementById('mark-all-read').addEventListener('click', function() {
        fetch('<?= BASE_URL ?>notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to mark all notifications as read');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to mark all notifications as read');
        });
    });

    // Handle notification click
    window.handleNotificationClick = function(notificationId, actionUrl) {
        // Mark as read first
        fetch(`<?= BASE_URL ?>notifications/mark-read/${notificationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && actionUrl) {
                window.location.href = actionUrl;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (actionUrl) {
                window.location.href = actionUrl;
            }
        });
    };
});
</script>

<style>
.notification-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.notification-item.unread {
    background-color: #f8f9fa;
    border-left-color: #007bff;
}

.notification-item:hover {
    background-color: #e9ecef;
}

.notification-item.unread:hover {
    background-color: #e3f2fd;
}

.badge {
    font-size: 0.7rem;
}
</style>
<?php $this->endSection() ?>
