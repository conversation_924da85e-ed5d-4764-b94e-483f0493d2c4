<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                    <h6><?= $title ?></h6>
                    <a href="<?= BASE_URL ?>documents/upload/<?= $business['id'] ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-upload me-2"></i>Upload Document
                    </a>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <?php if (empty($documents)): ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No documents found.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive p-0">
                            <table class="table align-items-center mb-0">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Title</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Type</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Uploaded</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($documents as $document): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex px-3 py-1">
                                                <div class="d-flex flex-column justify-content-center">
                                                    <h6 class="mb-0 text-sm"><?= htmlspecialchars($document['title']) ?></h6>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <p class="text-sm font-weight-bold mb-0"><?= htmlspecialchars($document['type']) ?></p>
                                        </td>
                                        <td>
                                            <span class="badge badge-sm bg-<?= $document['status'] === 'approved' ? 'success' : ($document['status'] === 'rejected' ? 'danger' : 'warning') ?>">
                                                <?= ucfirst($document['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <p class="text-sm font-weight-bold mb-0">
                                                <?php if ($document['created_at']): ?>
                                                    <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Unknown</span>
                                                <?php endif; ?>
                                            </p>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <a href="<?= BASE_URL ?>documents/view/<?= $document['id'] ?>" class="btn btn-link text-dark px-3 mb-0">
                                                    <i class="fas fa-eye text-dark me-2"></i>View
                                                </a>
                                                <?php if ($document['status'] === 'pending'): ?>
                                                <form action="<?= BASE_URL ?>documents/delete/<?= $document['id'] ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this document?');">
                                                    <button type="submit" class="btn btn-link text-danger px-3 mb-0">
                                                        <i class="fas fa-trash text-danger me-2"></i>Delete
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection() ?> 