<?php
namespace App\Models;

use App\Core\Model;
use PDO;

class ComplianceEvidence extends Model {
    protected $table = 'compliance_evidences';
    protected $fillable = [
        'id', 'business_id', 'compliance_type', 'photo_path', 'notes', 'status',
        'verification_notes', 'verified_by', 'verified_at', 'created_at', 'updated_at'
    ];

    public function __construct() {
        parent::__construct();
    }

    public function all($orderBy = null, $direction = 'DESC') {
        $query = "SELECT ce.*, b.name as business_name, u.full_name as verified_by_name
                 FROM compliance_evidences ce
                 LEFT JOIN businesses b ON ce.business_id = b.id
                 LEFT JOIN users u ON ce.verified_by = u.id
                 ORDER BY ce.created_at DESC";
        $stmt = $this->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function create(array $data) {
        // Set default status if not provided
        if (!isset($data['status'])) {
            $data['status'] = 'pending';
        }

        return parent::create($data);
    }

    public function getByBusinessId($businessId) {
        $query = "SELECT ce.*, u.full_name as verifier_name
                 FROM compliance_evidences ce
                 LEFT JOIN users u ON ce.verified_by = u.id 
                 WHERE ce.business_id = :business_id 
                 ORDER BY ce.created_at DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':business_id' => $businessId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $query = "SELECT ce.*, u.full_name as verifier_name
                 FROM compliance_evidences ce
                 LEFT JOIN users u ON ce.verified_by = u.id 
                 WHERE ce.id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByType($businessId, $complianceType) {
        $query = "SELECT ce.*, u.full_name as verifier_name
                 FROM compliance_evidences ce
                 LEFT JOIN users u ON ce.verified_by = u.id 
                 WHERE ce.business_id = :business_id AND ce.compliance_type = :compliance_type
                 ORDER BY ce.created_at DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            ':business_id' => $businessId,
            ':compliance_type' => $complianceType
        ]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getLatestByType($businessId, $complianceType) {
        $query = "SELECT ce.*, u.full_name as verifier_name
                 FROM compliance_evidences ce
                 LEFT JOIN users u ON ce.verified_by = u.id 
                 WHERE ce.business_id = :business_id AND ce.compliance_type = :compliance_type
                 ORDER BY ce.created_at DESC
                 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            ':business_id' => $businessId,
            ':compliance_type' => $complianceType
        ]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function verify($id, $userId, $status, $notes = '') {
        $query = "UPDATE compliance_evidences SET 
                  status = :status, 
                  verification_notes = :notes,
                  verified_by = :verified_by,
                  verified_at = NOW(),
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':status' => $status,
            ':notes' => $notes,
            ':verified_by' => $userId
        ]);
    }

    public function delete($id) {
        $query = "DELETE FROM compliance_evidences WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $id]);
    }

    public function getAll($limit = null) {
        $query = "SELECT ce.*, b.name as business_name, u.full_name as verifier_name
                 FROM compliance_evidences ce
                 LEFT JOIN businesses b ON ce.business_id = b.id
                 LEFT JOIN users u ON ce.verified_by = u.id
                 ORDER BY ce.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
        } else {
            $stmt = $this->db->query($query);
        }
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getPending($limit = null) {
        $query = "SELECT ce.*, b.name as business_name
                 FROM compliance_evidences ce
                 LEFT JOIN businesses b ON ce.business_id = b.id
                 WHERE ce.status = 'pending'
                 ORDER BY ce.created_at ASC";
        
        if ($limit) {
            $query .= " LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
        } else {
            $stmt = $this->db->query($query);
        }
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function countPending() {
        $query = "SELECT COUNT(*) FROM compliance_evidences WHERE status = 'pending'";
        return $this->db->query($query)->fetchColumn();
    }

    public function countByStatus($status) {
        $query = "SELECT COUNT(*) FROM compliance_evidences WHERE status = :status";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':status' => $status]);
        return $stmt->fetchColumn();
    }

    public function getComplianceTypes() {
        return [
            'business_permit' => 'Business Permit',
            'sanitary_permit' => 'Sanitary Permit',
            'fire_safety_certificate' => 'Fire Safety Certificate',
            'environmental_permit' => 'Environmental Permit',
            'safety_signage' => 'Safety Signage',
            'first_aid' => 'First Aid Kit',
            'fire_extinguishers' => 'Fire Extinguishers',
            'cctv' => 'CCTV System',
            'waste_segregation' => 'Waste Segregation System'
        ];
    }
}