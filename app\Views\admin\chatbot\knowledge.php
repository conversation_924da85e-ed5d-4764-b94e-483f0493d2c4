<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-brain text-primary me-2"></i>Knowledge Base Management
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/chatbot/knowledge/add" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Knowledge
            </a>
            <a href="<?= BASE_URL ?>admin/chatbot" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <?php 
        $categories = ['general', 'safety', 'compliance', 'inspection', 'permits'];
        $categoryColors = [
            'general' => 'primary',
            'safety' => 'danger', 
            'compliance' => 'success',
            'inspection' => 'warning',
            'permits' => 'info'
        ];
        
        foreach ($categories as $category): 
            $categoryKnowledge = array_filter($knowledge, function($item) use ($category) {
                return $item['category'] === $category;
            });
        ?>
            <div class="col-lg-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-<?= $categoryColors[$category] ?> text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-<?= $category === 'safety' ? 'shield-alt' : ($category === 'compliance' ? 'check-circle' : ($category === 'inspection' ? 'search' : ($category === 'permits' ? 'file-alt' : 'info-circle'))) ?> me-2"></i>
                            <?= ucfirst($category) ?> (<?= count($categoryKnowledge) ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($categoryKnowledge)): ?>
                            <?php foreach ($categoryKnowledge as $item): ?>
                                <div class="border-bottom pb-3 mb-3">
                                    <h6 class="text-primary"><?= htmlspecialchars($item['topic']) ?></h6>
                                    <p class="text-muted mb-2">
                                        <strong>Q:</strong> <?= htmlspecialchars(substr($item['question'], 0, 80)) ?>...
                                    </p>
                                    <p class="small mb-2">
                                        <strong>A:</strong> <?= htmlspecialchars(substr($item['answer'], 0, 120)) ?>...
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-secondary">Views: <?= $item['view_count'] ?></span>
                                            <span class="badge bg-<?= $item['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $item['is_active'] ? 'Active' : 'Inactive' ?>
                                            </span>
                                        </div>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewKnowledge('<?= $item['id'] ?>')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" onclick="editKnowledge('<?= $item['id'] ?>')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p>No knowledge items in this category yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Knowledge Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>Knowledge Base Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary"><?= count($knowledge) ?></h4>
                                <p class="text-muted">Total Items</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">
                                    <?= count(array_filter($knowledge, function($item) { return $item['is_active']; })) ?>
                                </h4>
                                <p class="text-muted">Active Items</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">
                                    <?= array_sum(array_column($knowledge, 'view_count')) ?>
                                </h4>
                                <p class="text-muted">Total Views</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning"><?= count($categories) ?></h4>
                                <p class="text-muted">Categories</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Knowledge View Modal -->
<div class="modal fade" id="knowledgeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Knowledge Item Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="knowledgeModalBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewKnowledge(id) {
    // Find the knowledge item
    const knowledge = <?= json_encode($knowledge) ?>;
    const item = knowledge.find(k => k.id === id);
    
    if (item) {
        document.getElementById('knowledgeModalBody').innerHTML = `
            <div class="mb-3">
                <h6 class="text-primary">Topic</h6>
                <p>${item.topic}</p>
            </div>
            <div class="mb-3">
                <h6 class="text-primary">Question</h6>
                <p>${item.question}</p>
            </div>
            <div class="mb-3">
                <h6 class="text-primary">Answer</h6>
                <p>${item.answer}</p>
            </div>
            <div class="mb-3">
                <h6 class="text-primary">Keywords</h6>
                <p><code>${item.keywords}</code></p>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">Category</h6>
                    <span class="badge bg-primary">${item.category}</span>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">Views</h6>
                    <span class="badge bg-info">${item.view_count}</span>
                </div>
            </div>
        `;
        
        new bootstrap.Modal(document.getElementById('knowledgeModal')).show();
    }
}

function editKnowledge(id) {
    // Redirect to edit page (to be implemented)
    alert('Edit functionality will be implemented in the next update.');
}
</script>
<?php $this->endSection() ?>
