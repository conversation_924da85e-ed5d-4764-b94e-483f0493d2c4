<?php

namespace App\Services;

use App\Models\Business;
use App\Models\Inspection;
use App\Models\User;
use App\Models\InspectionChecklistResponse;
use App\Models\BusinessChecklistEvidence;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsService
{
    /**
     * Generate comprehensive dashboard analytics
     */
    public function getDashboardAnalytics($dateRange = 30)
    {
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        return [
            'overview' => $this->getOverviewStats(),
            'trends' => $this->getTrendAnalytics($startDate, $endDate),
            'performance' => $this->getPerformanceMetrics($startDate, $endDate),
            'compliance' => $this->getComplianceAnalytics(),
            'geographic' => $this->getGeographicAnalytics(),
            'predictions' => $this->getPredictiveAnalytics(),
        ];
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats()
    {
        return [
            'total_businesses' => Business::count(),
            'active_businesses' => Business::where('status', 'active')->count(),
            'total_inspectors' => User::where('role', 'inspector')->count(),
            'active_inspectors' => User::where('role', 'inspector')->where('status', 'active')->count(),
            'total_inspections' => Inspection::count(),
            'completed_inspections' => Inspection::where('status', 'completed')->count(),
            'pending_inspections' => Inspection::where('status', 'scheduled')->count(),
            'compliance_rate' => $this->calculateOverallComplianceRate(),
            'average_inspection_score' => Inspection::where('status', 'completed')->avg('score'),
        ];
    }

    /**
     * Get trend analytics
     */
    private function getTrendAnalytics($startDate, $endDate)
    {
        // Monthly trends for the past year
        $monthlyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();

            $monthlyData[] = [
                'month' => $month->format('M Y'),
                'businesses_registered' => Business::whereBetween('created_at', [$monthStart, $monthEnd])->count(),
                'inspections_completed' => Inspection::where('status', 'completed')
                    ->whereBetween('completed_date', [$monthStart, $monthEnd])->count(),
                'compliance_rate' => $this->getMonthlyComplianceRate($monthStart, $monthEnd),
                'average_score' => Inspection::where('status', 'completed')
                    ->whereBetween('completed_date', [$monthStart, $monthEnd])->avg('score'),
            ];
        }

        // Weekly trends for the past 12 weeks
        $weeklyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $week = Carbon::now()->subWeeks($i);
            $weekStart = $week->copy()->startOfWeek();
            $weekEnd = $week->copy()->endOfWeek();

            $weeklyData[] = [
                'week' => $week->format('M j'),
                'inspections_scheduled' => Inspection::whereBetween('scheduled_date', [$weekStart, $weekEnd])->count(),
                'inspections_completed' => Inspection::where('status', 'completed')
                    ->whereBetween('completed_date', [$weekStart, $weekEnd])->count(),
                'evidence_submitted' => BusinessChecklistEvidence::whereBetween('created_at', [$weekStart, $weekEnd])->count(),
                'chat_messages' => ChatMessage::whereBetween('created_at', [$weekStart, $weekEnd])->count(),
            ];
        }

        return [
            'monthly' => $monthlyData,
            'weekly' => $weeklyData,
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics($startDate, $endDate)
    {
        // Inspector performance
        $inspectorPerformance = User::where('role', 'inspector')
            ->where('status', 'active')
            ->withCount([
                'assignedInspections as total_assigned' => function($query) use ($startDate, $endDate) {
                    $query->whereBetween('scheduled_date', [$startDate, $endDate]);
                },
                'assignedInspections as completed' => function($query) use ($startDate, $endDate) {
                    $query->where('status', 'completed')
                          ->whereBetween('completed_date', [$startDate, $endDate]);
                }
            ])
            ->get()
            ->map(function($inspector) {
                $completionRate = $inspector->total_assigned > 0 
                    ? round(($inspector->completed / $inspector->total_assigned) * 100, 1)
                    : 0;
                
                $avgScore = $inspector->assignedInspections()
                    ->where('status', 'completed')
                    ->avg('score');

                return [
                    'name' => $inspector->full_name,
                    'total_assigned' => $inspector->total_assigned,
                    'completed' => $inspector->completed,
                    'completion_rate' => $completionRate,
                    'average_score' => round($avgScore, 1),
                ];
            });

        // Business category performance
        $categoryPerformance = DB::table('businesses')
            ->join('business_categories', 'businesses.category_id', '=', 'business_categories.id')
            ->select(
                'business_categories.name',
                DB::raw('COUNT(businesses.id) as total_businesses'),
                DB::raw('SUM(CASE WHEN businesses.compliance_status = "compliant" THEN 1 ELSE 0 END) as compliant_businesses'),
                DB::raw('AVG(CASE WHEN businesses.compliance_status = "compliant" THEN 100 ELSE 0 END) as compliance_rate')
            )
            ->groupBy('business_categories.id', 'business_categories.name')
            ->get();

        return [
            'inspector_performance' => $inspectorPerformance,
            'category_performance' => $categoryPerformance,
        ];
    }

    /**
     * Get compliance analytics
     */
    private function getComplianceAnalytics()
    {
        // Compliance by business category
        $complianceByCategory = DB::table('businesses')
            ->join('business_categories', 'businesses.category_id', '=', 'business_categories.id')
            ->select(
                'business_categories.name',
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN compliance_status = "compliant" THEN 1 ELSE 0 END) as compliant'),
                DB::raw('SUM(CASE WHEN compliance_status = "non_compliant" THEN 1 ELSE 0 END) as non_compliant'),
                DB::raw('SUM(CASE WHEN compliance_status = "pending_review" THEN 1 ELSE 0 END) as pending')
            )
            ->groupBy('business_categories.id', 'business_categories.name')
            ->get();

        // Compliance by district
        $complianceByDistrict = DB::table('businesses')
            ->join('barangays', 'businesses.barangay_id', '=', 'barangays.id')
            ->join('districts', 'barangays.district_id', '=', 'districts.id')
            ->select(
                'districts.name',
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN compliance_status = "compliant" THEN 1 ELSE 0 END) as compliant'),
                DB::raw('ROUND((SUM(CASE WHEN compliance_status = "compliant" THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as compliance_rate')
            )
            ->groupBy('districts.id', 'districts.name')
            ->get();

        // Critical violations
        $criticalViolations = InspectionChecklistResponse::join('inspection_checklist_items', 'inspection_checklist_responses.checklist_item_id', '=', 'inspection_checklist_items.id')
            ->where('inspection_checklist_items.is_critical', true)
            ->where('inspection_checklist_responses.compliance_status', 'non_compliant')
            ->join('inspections', 'inspection_checklist_responses.inspection_id', '=', 'inspections.id')
            ->join('businesses', 'inspections.business_id', '=', 'businesses.id')
            ->select(
                'inspection_checklist_items.item_name',
                DB::raw('COUNT(*) as violation_count'),
                DB::raw('COUNT(DISTINCT businesses.id) as affected_businesses')
            )
            ->groupBy('inspection_checklist_items.id', 'inspection_checklist_items.item_name')
            ->orderByDesc('violation_count')
            ->limit(10)
            ->get();

        return [
            'by_category' => $complianceByCategory,
            'by_district' => $complianceByDistrict,
            'critical_violations' => $criticalViolations,
        ];
    }

    /**
     * Get geographic analytics
     */
    private function getGeographicAnalytics()
    {
        $districtStats = DB::table('businesses')
            ->join('barangays', 'businesses.barangay_id', '=', 'barangays.id')
            ->join('districts', 'barangays.district_id', '=', 'districts.id')
            ->select(
                'districts.name as district_name',
                DB::raw('COUNT(businesses.id) as total_businesses'),
                DB::raw('SUM(CASE WHEN businesses.status = "active" THEN 1 ELSE 0 END) as active_businesses'),
                DB::raw('SUM(CASE WHEN businesses.compliance_status = "compliant" THEN 1 ELSE 0 END) as compliant_businesses'),
                DB::raw('COUNT(DISTINCT barangays.id) as total_barangays')
            )
            ->groupBy('districts.id', 'districts.name')
            ->get();

        $barangayStats = DB::table('businesses')
            ->join('barangays', 'businesses.barangay_id', '=', 'barangays.id')
            ->join('districts', 'barangays.district_id', '=', 'districts.id')
            ->select(
                'barangays.name as barangay_name',
                'districts.name as district_name',
                DB::raw('COUNT(businesses.id) as total_businesses'),
                DB::raw('SUM(CASE WHEN businesses.compliance_status = "compliant" THEN 1 ELSE 0 END) as compliant_businesses'),
                DB::raw('ROUND((SUM(CASE WHEN businesses.compliance_status = "compliant" THEN 1 ELSE 0 END) / COUNT(businesses.id)) * 100, 1) as compliance_rate')
            )
            ->groupBy('barangays.id', 'barangays.name', 'districts.name')
            ->orderByDesc('total_businesses')
            ->get();

        return [
            'district_stats' => $districtStats,
            'barangay_stats' => $barangayStats,
        ];
    }

    /**
     * Get predictive analytics
     */
    private function getPredictiveAnalytics()
    {
        // Predict next month's inspection needs
        $avgMonthlyInspections = Inspection::where('created_at', '>=', Carbon::now()->subMonths(6))
            ->groupBy(DB::raw('YEAR(created_at), MONTH(created_at)'))
            ->selectRaw('COUNT(*) as count')
            ->get()
            ->avg('count');

        // Businesses due for inspection
        $businessesDueForInspection = Business::where('status', 'active')
            ->where(function($query) {
                $query->whereNull('last_inspection_date')
                      ->orWhere('last_inspection_date', '<', Carbon::now()->subMonths(6));
            })
            ->count();

        // Compliance trend prediction
        $recentComplianceRates = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();
            
            $recentComplianceRates[] = $this->getMonthlyComplianceRate($monthStart, $monthEnd);
        }

        $complianceTrend = count($recentComplianceRates) > 1 
            ? end($recentComplianceRates) - $recentComplianceRates[0]
            : 0;

        return [
            'predicted_monthly_inspections' => round($avgMonthlyInspections),
            'businesses_due_for_inspection' => $businessesDueForInspection,
            'compliance_trend' => round($complianceTrend, 1),
            'trend_direction' => $complianceTrend > 0 ? 'improving' : ($complianceTrend < 0 ? 'declining' : 'stable'),
        ];
    }

    /**
     * Calculate overall compliance rate
     */
    private function calculateOverallComplianceRate()
    {
        $totalBusinesses = Business::where('status', 'active')->count();
        
        if ($totalBusinesses === 0) {
            return 0;
        }

        $compliantBusinesses = Business::where('status', 'active')
            ->where('compliance_status', 'compliant')
            ->count();

        return round(($compliantBusinesses / $totalBusinesses) * 100, 1);
    }

    /**
     * Get monthly compliance rate
     */
    private function getMonthlyComplianceRate($startDate, $endDate)
    {
        $inspections = Inspection::where('status', 'completed')
            ->whereBetween('completed_date', [$startDate, $endDate])
            ->get();

        if ($inspections->count() === 0) {
            return 0;
        }

        $compliantInspections = $inspections->filter(function($inspection) {
            return in_array($inspection->compliance_rating, ['A', 'B']);
        });

        return round(($compliantInspections->count() / $inspections->count()) * 100, 1);
    }

    /**
     * Generate export data for analytics
     */
    public function exportAnalytics($type, $filters = [])
    {
        switch ($type) {
            case 'compliance_summary':
                return $this->exportComplianceSummary($filters);
            case 'inspector_performance':
                return $this->exportInspectorPerformance($filters);
            case 'business_analytics':
                return $this->exportBusinessAnalytics($filters);
            default:
                return [];
        }
    }

    /**
     * Export compliance summary
     */
    private function exportComplianceSummary($filters)
    {
        $businesses = Business::with(['category', 'barangay.district', 'inspections'])
            ->where('status', 'active')
            ->get();

        return $businesses->map(function($business) {
            $lastInspection = $business->inspections()->latest('completed_date')->first();
            
            return [
                'Business Name' => $business->name,
                'Owner' => $business->owner_name,
                'Category' => $business->category->name,
                'District' => $business->barangay->district->name,
                'Barangay' => $business->barangay->name,
                'Compliance Status' => ucfirst(str_replace('_', ' ', $business->compliance_status)),
                'Last Inspection' => $lastInspection ? $lastInspection->completed_date->format('Y-m-d') : 'Never',
                'Last Score' => $lastInspection ? $lastInspection->score : 'N/A',
                'Last Rating' => $lastInspection ? $lastInspection->compliance_rating : 'N/A',
            ];
        })->toArray();
    }

    /**
     * Export inspector performance
     */
    private function exportInspectorPerformance($filters)
    {
        $inspectors = User::where('role', 'inspector')
            ->where('status', 'active')
            ->with('assignedInspections')
            ->get();

        return $inspectors->map(function($inspector) {
            $inspections = $inspector->assignedInspections;
            $completed = $inspections->where('status', 'completed');
            
            return [
                'Inspector Name' => $inspector->full_name,
                'Email' => $inspector->email,
                'Total Assigned' => $inspections->count(),
                'Completed' => $completed->count(),
                'Completion Rate' => $inspections->count() > 0 
                    ? round(($completed->count() / $inspections->count()) * 100, 1) . '%'
                    : '0%',
                'Average Score' => $completed->count() > 0 
                    ? round($completed->avg('score'), 1)
                    : 'N/A',
                'Districts Assigned' => $inspector->districtAssignments->pluck('district.name')->implode(', '),
            ];
        })->toArray();
    }

    /**
     * Export business analytics
     */
    private function exportBusinessAnalytics($filters)
    {
        $analytics = $this->getDashboardAnalytics();
        
        // Convert analytics to exportable format
        $exportData = [];
        
        // Add overview stats
        foreach ($analytics['overview'] as $key => $value) {
            $exportData[] = [
                'Metric' => ucfirst(str_replace('_', ' ', $key)),
                'Value' => $value,
                'Type' => 'Overview'
            ];
        }
        
        return $exportData;
    }
}
