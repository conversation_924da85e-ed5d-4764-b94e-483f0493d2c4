<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('text');
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index('key');
        });

        // Insert default settings
        $this->insertDefaultSettings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_settings');
    }

    /**
     * Insert default website settings
     */
    private function insertDefaultSettings()
    {
        $defaultSettings = [
            // General settings
            ['key' => 'site_name', 'value' => 'OHS Management System', 'type' => 'text'],
            ['key' => 'site_tagline', 'value' => 'Occupational Health and Safety Management for Bacoor City', 'type' => 'text'],
            ['key' => 'site_description', 'value' => 'Professional OHS management system for businesses in Bacoor City', 'type' => 'textarea'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'email'],
            ['key' => 'contact_phone', 'value' => '+63 ************', 'type' => 'text'],
            ['key' => 'office_address', 'value' => 'Bacoor City Hall, Cavite, Philippines', 'type' => 'textarea'],
            ['key' => 'office_hours', 'value' => 'Monday - Friday: 8:00 AM - 5:00 PM', 'type' => 'text'],
            ['key' => 'facebook_url', 'value' => '', 'type' => 'url'],
            ['key' => 'twitter_url', 'value' => '', 'type' => 'url'],
            ['key' => 'youtube_url', 'value' => '', 'type' => 'url'],
            
            // Colors
            ['key' => 'primary_color', 'value' => '#2563eb', 'type' => 'color'],
            ['key' => 'secondary_color', 'value' => '#1e40af', 'type' => 'color'],
            ['key' => 'accent_color', 'value' => '#3b82f6', 'type' => 'color'],
            ['key' => 'success_color', 'value' => '#059669', 'type' => 'color'],
            ['key' => 'warning_color', 'value' => '#d97706', 'type' => 'color'],
            ['key' => 'danger_color', 'value' => '#dc2626', 'type' => 'color'],
            ['key' => 'dark_color', 'value' => '#1f2937', 'type' => 'color'],
            ['key' => 'light_color', 'value' => '#f8fafc', 'type' => 'color'],
            
            // Images
            ['key' => 'hero_bg', 'value' => '/images/hero-bg.jpg', 'type' => 'image'],
            ['key' => 'logo', 'value' => '/images/logo.png', 'type' => 'image'],
            ['key' => 'about_image', 'value' => '/images/about-us.jpg', 'type' => 'image'],
            ['key' => 'services_image', 'value' => '/images/services.jpg', 'type' => 'image'],
            ['key' => 'contact_image', 'value' => '/images/contact.jpg', 'type' => 'image'],
        ];

        foreach ($defaultSettings as $setting) {
            DB::table('website_settings')->insert([
                'key' => $setting['key'],
                'value' => $setting['value'],
                'type' => $setting['type'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
};
