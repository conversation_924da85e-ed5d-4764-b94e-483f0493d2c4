<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\District;
use App\Models\Barangay;
use App\Models\BusinessCategory;
use App\Models\Business;
use App\Models\Inspection;
use App\Models\InspectionChecklistCategory;
use App\Models\InspectionChecklistItem;
use App\Models\InspectionChecklistResponse;
use App\Models\BusinessChecklistEvidence;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\Notification;

class DataMigrationService
{
    private $oldConnection = 'mysql_old';
    private $newConnection = 'mysql';
    private $migrationLog = [];

    /**
     * Run complete data migration
     */
    public function migrateAllData(): array
    {
        $this->log('Starting complete data migration...');
        
        try {
            DB::transaction(function () {
                $this->migrateDistricts();
                $this->migrateBarangays();
                $this->migrateBusinessCategories();
                $this->migrateUsers();
                $this->migrateBusinesses();
                $this->migrateChecklistCategories();
                $this->migrateChecklistItems();
                $this->migrateInspections();
                $this->migrateChecklistResponses();
                $this->migrateBusinessEvidence();
                $this->migrateChatRooms();
                $this->migrateChatMessages();
                $this->migrateNotifications();
                $this->migrateInspectorAssignments();
            });
            
            $this->log('Data migration completed successfully!');
            
        } catch (\Exception $e) {
            $this->log('Migration failed: ' . $e->getMessage());
            throw $e;
        }
        
        return $this->migrationLog;
    }

    /**
     * Migrate districts
     */
    public function migrateDistricts(): void
    {
        $this->log('Migrating districts...');
        
        // Create Bacoor districts
        $districts = [
            ['name' => 'District 1-West'],
            ['name' => 'District 2-East'],
        ];
        
        foreach ($districts as $districtData) {
            District::create([
                'id' => Str::uuid(),
                'name' => $districtData['name'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        $this->log('Districts migrated: ' . count($districts));
    }

    /**
     * Migrate barangays
     */
    public function migrateBarangays(): void
    {
        $this->log('Migrating barangays...');
        
        $district1 = District::where('name', 'District 1-West')->first();
        $district2 = District::where('name', 'District 2-East')->first();
        
        // District 1-West Barangays (33 barangays)
        $district1Barangays = [
            'Alima', 'Aniban I', 'Aniban II', 'Aniban III', 'Aniban IV', 'Aniban V',
            'Banalo', 'Banlic', 'Bayanan', 'Campo Santo', 'Dasmariñas Bagong Bayan',
            'Habay I', 'Habay II', 'Kaingin', 'Ligas I', 'Ligas II', 'Ligas III',
            'Mabolo I', 'Mabolo II', 'Mabolo III', 'Maliksi I', 'Maliksi II',
            'Maliksi III', 'Molino I', 'Molino II', 'Molino III', 'Molino IV',
            'Molino V', 'Molino VI', 'Molino VII', 'Panapaan I', 'Panapaan II',
            'Panapaan III'
        ];
        
        // District 2-East Barangays (14 barangays)
        $district2Barangays = [
            'Digman', 'Dulong Bayan', 'Niog I', 'Niog II', 'Niog III',
            'Panapaan IV', 'Panapaan V', 'Panapaan VI', 'Panapaan VII',
            'Panapaan VIII', 'Queens Row Central', 'Queens Row East',
            'Queens Row West', 'Talaba I'
        ];
        
        foreach ($district1Barangays as $index => $name) {
            Barangay::create([
                'id' => Str::uuid(),
                'district_id' => $district1->id,
                'name' => $name,
                'code' => 'D1-' . str_pad($index + 1, 2, '0', STR_PAD_LEFT),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        foreach ($district2Barangays as $index => $name) {
            Barangay::create([
                'id' => Str::uuid(),
                'district_id' => $district2->id,
                'name' => $name,
                'code' => 'D2-' . str_pad($index + 1, 2, '0', STR_PAD_LEFT),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        $this->log('Barangays migrated: ' . (count($district1Barangays) + count($district2Barangays)));
    }

    /**
     * Migrate business categories
     */
    public function migrateBusinessCategories(): void
    {
        $this->log('Migrating business categories...');
        
        $categories = [
            ['name' => 'Retail Trade', 'description' => 'Retail stores, shops, and commercial establishments'],
            ['name' => 'Food & Beverage', 'description' => 'Restaurants, cafes, food stalls, and catering services'],
            ['name' => 'Manufacturing', 'description' => 'Manufacturing plants and production facilities'],
            ['name' => 'Services', 'description' => 'Service-oriented businesses and professional services'],
            ['name' => 'Healthcare', 'description' => 'Medical facilities, clinics, and healthcare services'],
            ['name' => 'Education', 'description' => 'Schools, training centers, and educational institutions'],
            ['name' => 'Construction', 'description' => 'Construction companies and building contractors'],
            ['name' => 'Transportation', 'description' => 'Transportation and logistics services'],
            ['name' => 'Technology', 'description' => 'IT services, software development, and tech companies'],
            ['name' => 'Others', 'description' => 'Other business types not covered in specific categories'],
        ];
        
        foreach ($categories as $categoryData) {
            BusinessCategory::create([
                'id' => Str::uuid(),
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        $this->log('Business categories migrated: ' . count($categories));
    }

    /**
     * Migrate users from old system
     */
    public function migrateUsers(): void
    {
        $this->log('Migrating users...');
        
        // Get users from old database
        $oldUsers = DB::connection($this->oldConnection)
            ->table('users')
            ->get();
        
        $migratedCount = 0;
        
        foreach ($oldUsers as $oldUser) {
            // Map old role to new role
            $role = $this->mapUserRole($oldUser->role ?? $oldUser->user_type ?? 'business_owner');
            
            User::create([
                'id' => Str::uuid(),
                'email' => $oldUser->email,
                'password' => $oldUser->password ?? Hash::make('password123'), // Default password if not set
                'full_name' => $oldUser->full_name ?? $oldUser->name ?? 'Unknown User',
                'role' => $role,
                'status' => $this->mapUserStatus($oldUser->status ?? 'active'),
                'last_login' => $oldUser->last_login ?? null,
                'email_verified_at' => $oldUser->email_verified_at ?? now(),
                'created_at' => $oldUser->created_at ?? now(),
                'updated_at' => $oldUser->updated_at ?? now(),
            ]);
            
            $migratedCount++;
        }
        
        $this->log('Users migrated: ' . $migratedCount);
    }

    /**
     * Migrate businesses from old system
     */
    public function migrateBusinesses(): void
    {
        $this->log('Migrating businesses...');
        
        // Get businesses from old database
        $oldBusinesses = DB::connection($this->oldConnection)
            ->table('businesses')
            ->get();
        
        $migratedCount = 0;
        $defaultCategory = BusinessCategory::first();
        $defaultBarangay = Barangay::first();
        
        foreach ($oldBusinesses as $oldBusiness) {
            // Find owner
            $owner = User::where('email', $oldBusiness->owner_email ?? $oldBusiness->email)->first();
            if (!$owner) {
                // Create owner if not exists
                $owner = User::create([
                    'id' => Str::uuid(),
                    'email' => $oldBusiness->owner_email ?? $oldBusiness->email,
                    'password' => Hash::make('password123'),
                    'full_name' => $oldBusiness->owner_name ?? 'Business Owner',
                    'role' => 'business_owner',
                    'status' => 'active',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            
            // Find category
            $category = BusinessCategory::where('name', 'like', '%' . ($oldBusiness->category ?? 'Others') . '%')->first() ?? $defaultCategory;
            
            // Find barangay
            $barangay = Barangay::where('name', 'like', '%' . ($oldBusiness->barangay ?? 'Alima') . '%')->first() ?? $defaultBarangay;
            
            Business::create([
                'id' => Str::uuid(),
                'owner_id' => $owner->id,
                'owner_name' => $oldBusiness->owner_name ?? $owner->full_name,
                'category_id' => $category->id,
                'barangay_id' => $barangay->id,
                'name' => $oldBusiness->name ?? $oldBusiness->business_name,
                'registration_number' => $oldBusiness->registration_number ?? 'BUS-' . date('Y') . '-' . str_pad($migratedCount + 1, 4, '0', STR_PAD_LEFT),
                'email' => $oldBusiness->email ?? $owner->email,
                'contact_number' => $oldBusiness->contact_number ?? $oldBusiness->phone,
                'address' => $oldBusiness->address ?? 'Address not provided',
                'employee_count' => $oldBusiness->employee_count ?? 1,
                'date_established' => $oldBusiness->date_established ?? null,
                'status' => $this->mapBusinessStatus($oldBusiness->status ?? 'active'),
                'compliance_status' => $this->mapComplianceStatus($oldBusiness->compliance_status ?? 'pending_review'),
                'business_permit' => $oldBusiness->business_permit ?? false,
                'sanitary_permit' => $oldBusiness->sanitary_permit ?? false,
                'fire_safety_certificate' => $oldBusiness->fire_safety_certificate ?? false,
                'environmental_permit' => $oldBusiness->environmental_permit ?? false,
                'safety_officer_count' => $oldBusiness->safety_officer_count ?? 0,
                'has_safety_signage' => $oldBusiness->has_safety_signage ?? false,
                'has_first_aid' => $oldBusiness->has_first_aid ?? false,
                'has_fire_extinguishers' => $oldBusiness->has_fire_extinguishers ?? false,
                'has_cctv' => $oldBusiness->has_cctv ?? false,
                'has_waste_segregation' => $oldBusiness->has_waste_segregation ?? false,
                'last_inspection_date' => $oldBusiness->last_inspection_date ?? null,
                'next_inspection_date' => $oldBusiness->next_inspection_date ?? null,
                'created_at' => $oldBusiness->created_at ?? now(),
                'updated_at' => $oldBusiness->updated_at ?? now(),
            ]);
            
            $migratedCount++;
        }
        
        $this->log('Businesses migrated: ' . $migratedCount);
    }

    /**
     * Migrate checklist categories
     */
    public function migrateChecklistCategories(): void
    {
        $this->log('Migrating checklist categories...');
        
        $categories = [
            ['name' => 'General Safety', 'description' => 'Basic safety requirements and protocols', 'weight' => 1.0, 'sort_order' => 1],
            ['name' => 'Fire Safety', 'description' => 'Fire prevention and safety measures', 'weight' => 1.2, 'sort_order' => 2],
            ['name' => 'Health & Sanitation', 'description' => 'Health and sanitation standards', 'weight' => 1.1, 'sort_order' => 3],
            ['name' => 'Environmental Compliance', 'description' => 'Environmental protection measures', 'weight' => 1.0, 'sort_order' => 4],
            ['name' => 'Emergency Preparedness', 'description' => 'Emergency response and preparedness', 'weight' => 1.3, 'sort_order' => 5],
            ['name' => 'Documentation', 'description' => 'Required permits and documentation', 'weight' => 0.8, 'sort_order' => 6],
        ];
        
        foreach ($categories as $categoryData) {
            InspectionChecklistCategory::create([
                'id' => Str::uuid(),
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'weight' => $categoryData['weight'],
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        $this->log('Checklist categories migrated: ' . count($categories));
    }

    /**
     * Helper methods for mapping old data to new format
     */
    private function mapUserRole(string $oldRole): string
    {
        return match(strtolower($oldRole)) {
            'admin', 'administrator' => 'admin',
            'inspector' => 'inspector',
            'business_owner', 'business', 'owner' => 'business_owner',
            default => 'business_owner'
        };
    }

    private function mapUserStatus(string $oldStatus): string
    {
        return match(strtolower($oldStatus)) {
            'active', '1', 'enabled' => 'active',
            'inactive', '0', 'disabled' => 'inactive',
            'suspended', 'banned' => 'suspended',
            default => 'active'
        };
    }

    private function mapBusinessStatus(string $oldStatus): string
    {
        return match(strtolower($oldStatus)) {
            'active', '1', 'approved' => 'active',
            'pending', 'review' => 'pending',
            'suspended', 'banned' => 'suspended',
            'inactive', '0', 'disabled' => 'inactive',
            default => 'active'
        };
    }

    private function mapComplianceStatus(string $oldStatus): string
    {
        return match(strtolower($oldStatus)) {
            'compliant', 'passed', 'good' => 'compliant',
            'non_compliant', 'failed', 'bad' => 'non_compliant',
            'pending', 'review', 'pending_review' => 'pending_review',
            default => 'pending_review'
        };
    }

    private function log(string $message): void
    {
        $this->migrationLog[] = '[' . now()->format('Y-m-d H:i:s') . '] ' . $message;
        \Log::info('Data Migration: ' . $message);
    }

    /**
     * Validate migration results
     */
    public function validateMigration(): array
    {
        $validation = [
            'users' => [
                'old_count' => DB::connection($this->oldConnection)->table('users')->count(),
                'new_count' => User::count(),
            ],
            'businesses' => [
                'old_count' => DB::connection($this->oldConnection)->table('businesses')->count(),
                'new_count' => Business::count(),
            ],
            'districts' => [
                'new_count' => District::count(),
                'expected' => 2,
            ],
            'barangays' => [
                'new_count' => Barangay::count(),
                'expected' => 47,
            ],
        ];
        
        return $validation;
    }

    /**
     * Rollback migration (for testing)
     */
    public function rollbackMigration(): void
    {
        $this->log('Rolling back migration...');
        
        DB::transaction(function () {
            // Delete in reverse order to respect foreign key constraints
            Notification::truncate();
            ChatMessage::truncate();
            ChatRoom::truncate();
            BusinessChecklistEvidence::truncate();
            InspectionChecklistResponse::truncate();
            Inspection::truncate();
            InspectionChecklistItem::truncate();
            InspectionChecklistCategory::truncate();
            Business::truncate();
            User::truncate();
            BusinessCategory::truncate();
            Barangay::truncate();
            District::truncate();
        });
        
        $this->log('Migration rolled back successfully');
    }
}
