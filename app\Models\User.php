<?php
namespace App\Models;

use App\Core\Model;
use PDO;
use PDOException;

class User extends Model {
    protected $table = 'users';
    protected $fillable = [
        'id', 'email', 'password_hash', 'full_name', 'role', 'status',
        'created_at', 'updated_at', 'last_login'
    ];

    public function __construct() {
        parent::__construct();
    }

    // find() method inherited from base Model class

    public function findByEmail($email) {
        return $this->whereFirst('email', $email);
    }

    /**
     * Override create to handle password hashing
     */
    public function create(array $data) {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_BCRYPT);
            unset($data['password']);
        }

        return parent::create($data);
    }

    /**
     * Override update to handle password hashing
     */
    public function update($id, array $data) {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_BCRYPT);
            unset($data['password']);
        }

        return parent::update($id, $data);
    }

    // delete() method inherited from base Model class

    public function countAll() {
        try {
            $query = "SELECT COUNT(*) as count FROM users WHERE status = 'active'";
            error_log("Executing query: " . $query);
            
            $stmt = $this->db->query($query);
            $result = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            error_log("Found " . $result . " active users");
            return $result;
        } catch (PDOException $e) {
            error_log("Error in User::countAll(): " . $e->getMessage());
            throw $e;
        }
    }

    public function getByRole($role) {
        $query = "SELECT * FROM users WHERE role = :role AND status = 'active' ORDER BY full_name ASC";
        $stmt = $this->query($query, [':role' => $role]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function register($email, $password, $fullName, $role = 'business_owner') {
        return $this->create([
            'email' => $email,
            'password' => $password,
            'full_name' => $fullName,
            'role' => $role,
            'status' => 'active'
        ]);
    }

    public function login($email, $password) {
        try {
            $query = "SELECT * FROM users WHERE email = :email AND status = 'active' LIMIT 1";
            error_log("Executing query: " . $query . " with email: " . $email);
            
            $stmt = $this->db->prepare($query);
            $stmt->execute([':email' => $email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password_hash'])) {
                $this->updateLastLogin($user['id']);
                error_log("User logged in successfully: " . $email);
                return $user;
            }
            
            error_log("Login failed for email: " . $email);
            return false;
        } catch (PDOException $e) {
            error_log("Error in User::login(): " . $e->getMessage());
            throw $e;
        }
    }

    private function updateLastLogin($userId) {
        try {
            $query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = :id";
            error_log("Executing query: " . $query . " with id: " . $userId);
            
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([':id' => $userId]);
            
            if ($result) {
                error_log("Last login updated successfully for user: " . $userId);
            } else {
                error_log("Failed to update last login for user: " . $userId);
            }
        } catch (PDOException $e) {
            error_log("Error in User::updateLastLogin(): " . $e->getMessage());
            // Don't throw the exception here as this is not a critical operation
        }
    }

    public function getBusinesses($ownerId) {
        $query = "SELECT * FROM businesses WHERE owner_id = :owner_id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':owner_id' => $ownerId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function isAdmin($userId) {
        $query = "SELECT role FROM users WHERE id = :id AND status = 'active'";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        return $user && strtolower($user['role']) === 'admin';
    }

    public function getRecent($limit = 5) {
        try {
            $query = "SELECT * FROM users WHERE status = 'active' ORDER BY created_at DESC LIMIT :limit";
            error_log("Executing query: " . $query . " with limit: " . $limit);
            
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            error_log("Found " . count($result) . " recent users");
            return $result;
        } catch (PDOException $e) {
            error_log("Error in User::getRecent(): " . $e->getMessage());
            throw $e;
        }
    }
}