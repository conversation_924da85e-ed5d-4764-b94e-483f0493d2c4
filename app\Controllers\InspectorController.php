<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Business;
use App\Models\ComplianceEvidence;
use App\Models\Inspection;
use App\Models\InspectorAssignment;
use App\Models\BusinessInspectionQueue;
use App\Models\InspectionChecklist;
use App\Models\BusinessChecklistEvidence;
use App\Models\Notification;

class InspectorController extends Controller
{
    private $inspectionModel;
    private $businessModel;
    private $evidenceModel;
    private $assignmentModel;
    private $queueModel;
    private $checklistModel;
    private $businessEvidenceModel;
    private $notificationModel;

    public function __construct()
    {
        parent::__construct();
        $this->auth = Auth::getInstance();

        $this->inspectionModel = new Inspection();
        $this->businessModel = new Business();
        $this->evidenceModel = new ComplianceEvidence();
        $this->assignmentModel = new InspectorAssignment();
        $this->queueModel = new BusinessInspectionQueue();
        $this->checklistModel = new InspectionChecklist();
        $this->businessEvidenceModel = new BusinessChecklistEvidence();
        $this->notificationModel = new Notification();
    }
    
    public function dashboard()
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Use consistent status-based counting like other views
        $allInspections = $this->inspectionModel->getByInspector($inspectorId);

        // Get counts by status (consistent with inspections page)
        $scheduledCount = $this->inspectionModel->countByInspector($inspectorId, ['scheduled', 'confirmed']);
        $completedCount = $this->inspectionModel->countByInspector($inspectorId, 'completed');
        $inProgressCount = $this->inspectionModel->countByInspector($inspectorId, 'in_progress');

        // Get upcoming inspections (scheduled/confirmed only)
        $upcomingInspections = $this->inspectionModel->getByInspector($inspectorId, ['scheduled', 'confirmed']);

        // Get recent completed inspections (last 5)
        $recentInspections = array_slice(
            $this->inspectionModel->getByInspector($inspectorId, 'completed'),
            0, 5
        );

        // Get inspector-specific pending evidence count
        $assignedDistricts = $this->assignmentModel->getInspectorDistricts($inspectorId);
        $assignedDistrictIds = array_column($assignedDistricts, 'district_id');
        $pendingEvidenceCount = $this->getInspectorPendingEvidenceCount($inspectorId, $assignedDistrictIds);

        // Prepare simplified stats array
        $stats = [
            'total_inspections' => $scheduledCount + $completedCount + $inProgressCount,
            'total_completed' => $completedCount,
            'total_pending' => $scheduledCount,
            'total_in_progress' => $inProgressCount,
            'pending_evidence_count' => $pendingEvidenceCount,
            'upcoming_inspections' => array_slice($upcomingInspections, 0, 5), // Limit to 5 for dashboard
            'recent_inspections' => $recentInspections,
            'assigned_districts' => count($assignedDistricts)
        ];

        return $this->render('inspector/dashboard', [
            'title' => 'Inspector Dashboard',
            'active_page' => 'dashboard',
            'stats' => $stats,
            'user' => $this->auth->getUser()
        ]);
    }
    
    public function schedule()
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();
        $districtFilter = $_GET['district'] ?? null;

        // Get all upcoming and completed inspections assigned to this inspector
        $upcomingInspections = $this->inspectionModel->getByInspector($inspectorId, ['scheduled', 'confirmed']);
        $completedInspections = $this->inspectionModel->getByInspector($inspectorId, 'completed');

        // Get district assignment information for filtering businesses needing inspection
        $assignedDistricts = $this->assignmentModel->getInspectorDistricts($inspectorId);
        $assignedDistrictIds = array_column($assignedDistricts, 'district_id');

        // Get businesses needing inspection in assigned districts (for new inspection suggestions)
        $businessesNeedingInspection = $this->queueModel->getBusinessesNeedingInspection($inspectorId, $assignedDistrictIds);
        $overdueInspections = $this->queueModel->getOverdueInspections($inspectorId, $assignedDistrictIds);
        $queueStats = $this->queueModel->getQueueStatistics($inspectorId, $assignedDistrictIds);

        // Filter inspections by district if specified (includes all assigned inspections)
        if ($districtFilter) {
            $upcomingInspections = $this->filterInspectionsByDistrict($upcomingInspections, $districtFilter);
            $completedInspections = $this->filterInspectionsByDistrict($completedInspections, $districtFilter);

            // Filter businesses needing inspection by district (through barangay)
            $businessesNeedingInspection = array_filter($businessesNeedingInspection, function($item) use ($districtFilter) {
                return isset($item['district_id']) && $item['district_id'] === $districtFilter;
            });
            $overdueInspections = array_filter($overdueInspections, function($item) use ($districtFilter) {
                return isset($item['district_id']) && $item['district_id'] === $districtFilter;
            });
        }

        // Categorize inspections by district assignment
        $categorizedInspections = $this->categorizeInspectionsByDistrict($upcomingInspections, $assignedDistricts);

        // Use consistent counting method (reuse from dashboard)
        $scheduledCount = count($upcomingInspections);
        $completedCount = count($completedInspections);

        return $this->render('inspector/schedule', [
            'title' => 'My Schedule',
            'active_page' => 'schedule',
            'upcoming_inspections' => $upcomingInspections,
            'completed_inspections' => $completedInspections,
            'businesses_needing_inspection' => $businessesNeedingInspection,
            'overdue_inspections' => $overdueInspections,
            'assigned_districts' => $assignedDistricts,
            'categorized_inspections' => $categorizedInspections,
            'current_district_filter' => $districtFilter,
            'queue_stats' => $queueStats,
            'stats' => [
                'total_inspections' => $scheduledCount + $completedCount,
                'total_completed' => $completedCount,
                'total_pending' => $scheduledCount,
                'businesses_needing_inspection' => count($businessesNeedingInspection),
                'overdue_inspections' => count($overdueInspections),
            ],
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Inspector inspections management page (unified with schedule functionality)
     */
    public function inspections()
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();
        $statusFilter = $_GET['status'] ?? null;
        $districtFilter = $_GET['district'] ?? null;

        // Get all inspections assigned to this inspector
        $allInspections = $this->inspectionModel->getByInspector($inspectorId);

        // Get district assignment information
        $assignedDistricts = $this->assignmentModel->getInspectorDistricts($inspectorId);
        $assignedDistrictIds = array_column($assignedDistricts, 'district_id');

        // Get businesses needing inspection and overdue inspections (from schedule functionality)
        $businessesNeedingInspection = $this->queueModel->getBusinessesNeedingInspection($inspectorId, $assignedDistrictIds);
        $overdueInspections = $this->queueModel->getOverdueInspections($inspectorId, $assignedDistrictIds);

        // Filter by district if specified
        if ($districtFilter) {
            $allInspections = array_filter($allInspections, function($inspection) use ($districtFilter) {
                return isset($inspection['district_id']) && $inspection['district_id'] === $districtFilter;
            });

            // Filter businesses needing inspection by district
            $businessesNeedingInspection = array_filter($businessesNeedingInspection, function($item) use ($districtFilter) {
                return isset($item['district_id']) && $item['district_id'] === $districtFilter;
            });
            $overdueInspections = array_filter($overdueInspections, function($item) use ($districtFilter) {
                return isset($item['district_id']) && $item['district_id'] === $districtFilter;
            });
        }

        // Filter by status if specified
        if ($statusFilter) {
            $allInspections = array_filter($allInspections, function($inspection) use ($statusFilter) {
                return $inspection['status'] === $statusFilter;
            });
        }

        // Categorize inspections by status
        $inspectionsByStatus = [
            'scheduled' => [],
            'confirmed' => [],
            'in_progress' => [],
            'completed' => [],
            'cancelled' => [],
            'rejected' => []
        ];

        foreach ($allInspections as $inspection) {
            $status = $inspection['status'] ?? 'scheduled';

            // Handle rejected inspections (completed but rejected by admin)
            if ($status === 'completed' && isset($inspection['verification_status']) && $inspection['verification_status'] === 'rejected') {
                $status = 'rejected';
            }

            if (isset($inspectionsByStatus[$status])) {
                // Get checklist completion status for each inspection
                $inspection['checklist_completion'] = $this->checklistModel->getInspectionCompletionStatus($inspection['id']);
                $inspection['checklist_score'] = null;

                if ($inspection['checklist_completion']['completed_items'] > 0) {
                    $inspection['checklist_score'] = $this->checklistModel->calculateInspectionScore($inspection['id']);
                }

                $inspectionsByStatus[$status][] = $inspection;
            }
        }

        // Get statistics (enhanced with additional data)
        $stats = [
            'total' => count($allInspections),
            'scheduled' => count($inspectionsByStatus['scheduled']),
            'confirmed' => count($inspectionsByStatus['confirmed']),
            'in_progress' => count($inspectionsByStatus['in_progress']),
            'completed' => count($inspectionsByStatus['completed']),
            'cancelled' => count($inspectionsByStatus['cancelled']),
            'rejected' => count($inspectionsByStatus['rejected']),
            'overdue' => count($overdueInspections),
            'businesses_needing_inspection' => count($businessesNeedingInspection)
        ];

        return $this->render('inspector/inspections', [
            'title' => 'My Inspections & Schedule',
            'active_page' => 'inspections',
            'inspections_by_status' => $inspectionsByStatus,
            'assigned_districts' => $assignedDistricts,
            'current_status_filter' => $statusFilter,
            'current_district_filter' => $districtFilter,
            'businesses_needing_inspection' => $businessesNeedingInspection,
            'overdue_inspections' => $overdueInspections,
            'stats' => $stats,
            'user' => $this->auth->getUser()
        ]);
    }


    
    public function inspection($id)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Use district-validated method to get inspection
        $inspection = $this->inspectionModel->getByIdForInspector($id, $inspectorId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found or you are not authorized to view it. You can only access inspections in your assigned districts.';
            $this->redirect('inspector/schedule');
            return;
        }
        
        $business = $this->businessModel->getById($inspection['business_id']);
        $complianceRatings = $this->inspectionModel->getComplianceRatings();

        // Get business evidence summary and detailed evidence for the checklist
        $evidenceSummary = null;
        $checklistEvidence = [];
        if ($business) {
            $businessEvidenceModel = new \App\Models\BusinessChecklistEvidence();
            $evidenceSummary = $this->getBusinessEvidenceStats($business['id']);

            // Get detailed checklist evidence grouped by category
            $checklistEvidence = $this->getDetailedChecklistEvidence($business['id']);
        }

        return $this->render('inspector/inspection', [
            'title' => 'Inspection Details',
            'active_page' => 'schedule',
            'inspection' => $inspection,
            'business' => $business,
            'compliance_ratings' => $complianceRatings,
            'evidence_summary' => $evidenceSummary,
            'checklist_evidence' => $checklistEvidence,
            'user' => $this->auth->getUser()
        ]);
    }
    
    public function completeInspection($id)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Get inspection directly to avoid district validation issues
        $inspection = $this->inspectionModel->getById($id);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('inspector/schedule');
            return;
        }

        // Check if inspector is assigned to this inspection
        if ($inspection['inspector_id'] !== $inspectorId) {
            $_SESSION['error'] = 'You are not authorized to complete this inspection.';
            $this->redirect('inspector/schedule');
            return;
        }
        
        if ($inspection['status'] === 'completed') {
            $_SESSION['error'] = 'This inspection is already completed.';
            $this->redirect('inspector/inspection/' . $id);
            return;
        }
        
        if ($this->isPost()) {
            $score = $_POST['score'] ?? null;
            $findings = $_POST['findings'] ?? '';
            $recommendations = $_POST['recommendations'] ?? '';
            $compliance_rating = $_POST['compliance_rating'] ?? null;



            // Allow empty findings and recommendations for checklist-based completion
            if (empty($compliance_rating)) {
                $_SESSION['error'] = 'Compliance rating is required. Received: ' . var_export($compliance_rating, true);
                $this->redirect('inspector/inspection/' . $id);
                return;
            }

            // Set default values if empty
            if (empty($findings)) {
                $findings = 'Inspection completed via checklist';
            }
            if (empty($recommendations)) {
                $recommendations = 'Continue maintaining compliance standards';
            }
            
            $data = [
                'score' => $score,
                'findings' => $findings,
                'recommendations' => $recommendations,
                'compliance_rating' => $compliance_rating
            ];
            
            if ($this->inspectionModel->completeInspection($id, $data)) {
                $business = $this->businessModel->getById($inspection['business_id']);
                
                if ($business) {
                    $complianceStatus = 'pending_review';
                    
                    if ($compliance_rating === 'A' || $compliance_rating === 'B') {
                        $complianceStatus = 'compliant';
                    } else if ($compliance_rating === 'C' || $compliance_rating === 'D' || $compliance_rating === 'F') {
                        $complianceStatus = 'non_compliant';
                    }
                    
                    $this->businessModel->updateComplianceStatus($business['id'], $complianceStatus);
                }
                
                $_SESSION['success'] = 'Inspection completed successfully.';
                $this->redirect('inspector/schedule');
            } else {
                $_SESSION['error'] = 'Failed to complete inspection.';
                $this->redirect('inspector/inspection/' . $id);
            }
            
            return;
        }
        
        $this->redirect('inspector/inspection/' . $id);
    }
    
    /**
     * @deprecated This method is deprecated. Evidence verification now happens through inspection checklist system.
     * Kept for backward compatibility but should not be used.
     */
    public function compliance()
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Get inspector's assigned districts
        $assignedDistricts = $this->assignmentModel->getInspectorDistricts($inspectorId);
        $assignedDistrictIds = array_column($assignedDistricts, 'district_id');

        // Get all compliance evidence
        $allEvidence = $this->evidenceModel->getAll();

        // Filter evidence for businesses in inspector's assigned districts (through barangay)
        $evidence = [];
        foreach ($allEvidence as $item) {
            $business = $this->businessModel->getById($item['business_id']);
            if ($business && isset($business['barangay_id'])) {
                // Get district through barangay
                $barangayModel = new \App\Models\Barangay();
                $barangay = $barangayModel->find($business['barangay_id']);
                if ($barangay && in_array($barangay['district_id'], $assignedDistrictIds)) {
                    $item['business_name'] = $business['name'];
                    $evidence[] = $item;
                }
            }
        }

        return $this->render('inspector/compliance', [
            'title' => 'Compliance Evidence Review',
            'active_page' => 'compliance',
            'evidence' => $evidence,
            'compliance_types' => $this->evidenceModel->getComplianceTypes(),
            'user' => $this->auth->getUser(),
            'auth' => $this->auth
        ]);
    }

    /**
     * @deprecated This method is deprecated. Evidence verification now happens through inspection checklist system.
     * Kept for backward compatibility but should not be used.
     */
    public function viewCompliance($businessId)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $business = $this->businessModel->getById($businessId);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('inspector/dashboard');
            return;
        }

        // Check if inspector is assigned to this business's district
        if (!$this->canInspectorAccessBusiness($this->auth->getUserId(), $business)) {
            $_SESSION['error'] = 'You are not authorized to view compliance for this business. You can only access businesses in your assigned districts.';
            $this->redirect('inspector/dashboard');
            return;
        }

        $evidence = $this->evidenceModel->getByBusinessId($businessId);

        return $this->render('business/compliance_evidence/index', [
            'title' => 'Compliance Evidence - ' . $business['name'],
            'active_page' => 'compliance',
            'business' => $business,
            'evidence' => $evidence,
            'compliance_types' => $this->evidenceModel->getComplianceTypes(),
            'user' => $this->auth->getUser(),
            'auth' => $this->auth
        ]);
    }
    
    /**
     * @deprecated This method is deprecated. Evidence verification now happens through inspection checklist system.
     * Kept for backward compatibility but should not be used.
     */
    public function verifyEvidence($id)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $evidence = $this->evidenceModel->getById($id);

        if (!$evidence) {
            $_SESSION['error'] = 'Evidence not found.';
            $this->redirect('inspector/dashboard');
            return;
        }

        $business = $this->businessModel->getById($evidence['business_id']);

        // Check if inspector is assigned to this business's district
        if (!$this->canInspectorAccessBusiness($this->auth->getUserId(), $business)) {
            $_SESSION['error'] = 'You are not authorized to verify evidence for this business. You can only access businesses in your assigned districts.';
            $this->redirect('inspector/dashboard');
            return;
        }
        
        if ($this->isPost()) {
            $status = $_POST['status'] ?? '';
            $notes = $_POST['notes'] ?? '';
            
            if (empty($status) || !in_array($status, ['verified', 'rejected'])) {
                $_SESSION['error'] = 'Please select a valid verification status.';
                $this->redirect('inspector/compliance/verify/' . $id);
                return;
            }
            
            $inspectorId = $this->auth->getUserId();
            
            if ($this->evidenceModel->verify($id, $inspectorId, $status, $notes)) {
                if ($status === 'verified') {
                    $complianceField = $this->mapComplianceTypeToField($evidence['compliance_type']);
                    if ($complianceField) {
                        $this->businessModel->update($evidence['business_id'], [$complianceField => 1]);
                    }

                    $this->updateBusinessComplianceStatus($evidence['business_id']);
                }

                // Notify admin about verification result
                $this->notifyAdminOfVerification($evidence, $business, $status, $notes);

                $_SESSION['success'] = 'Evidence has been ' . $status . '.';
                $this->redirect('inspector/compliance');
            } else {
                $_SESSION['error'] = 'Failed to update verification status.';
                $this->redirect('inspector/compliance/verify/' . $id);
            }
            
            return;
        }
        
        return $this->render('inspector/verify_evidence', [
            'title' => 'Verify Compliance Evidence',
            'active_page' => 'compliance',
            'evidence' => $evidence,
            'business' => $business,
            'compliance_types' => $this->evidenceModel->getComplianceTypes(),
            'user' => $this->auth->getUser(),
            'auth' => $this->auth
        ]);
    }
    
    private function mapComplianceTypeToField($complianceType)
    {
        $map = [
            'business_permit' => 'business_permit',
            'sanitary_permit' => 'sanitary_permit',
            'fire_safety_certificate' => 'fire_safety_certificate',
            'environmental_permit' => 'environmental_permit',
            'safety_signage' => 'has_safety_signage',
            'first_aid' => 'has_first_aid',
            'fire_extinguishers' => 'has_fire_extinguishers',
            'cctv' => 'has_cctv',
            'waste_segregation' => 'has_waste_segregation'
        ];
        
        return $map[$complianceType] ?? null;
    }
    
    private function updateBusinessComplianceStatus($businessId)
    {
        $business = $this->businessModel->getById($businessId);
        
        if (!$business) {
            return false;
        }
        
        $requiredFields = [
            'business_permit',
            'sanitary_permit',
            'fire_safety_certificate',
            'environmental_permit',
            'has_safety_signage',
            'has_first_aid',
            'has_fire_extinguishers'
        ];
        
        $compliantCount = 0;
        foreach ($requiredFields as $field) {
            if (isset($business[$field]) && $business[$field] == 1) {
                $compliantCount++;
            }
        }
        
        $complianceStatus = 'pending_review';
        
        if ($compliantCount === count($requiredFields)) {
            $complianceStatus = 'compliant';
        } else if ($compliantCount > 0) {
            $complianceStatus = 'non_compliant';
        }
        
        return $this->businessModel->updateComplianceStatus($businessId, $complianceStatus);
    }

    private function notifyAdminOfVerification($evidence, $business, $status, $notes)
    {
        try {
            $notificationModel = new \App\Models\Notification();
            $userModel = new \App\Models\User();

            // Get all admin users
            $admins = $userModel->getUsersByRole('admin');

            $inspector = $this->auth->getUser();
            $complianceTypes = $this->evidenceModel->getComplianceTypes();
            $complianceTypeName = $complianceTypes[$evidence['compliance_type']] ?? $evidence['compliance_type'];

            $title = "Compliance Evidence " . ucfirst($status);
            $message = sprintf(
                "Inspector %s has %s compliance evidence for %s.\n\nBusiness: %s\nCompliance Type: %s\nNotes: %s",
                $inspector['full_name'],
                $status,
                $business['name'],
                $business['name'],
                $complianceTypeName,
                $notes ?: 'No notes provided'
            );

            foreach ($admins as $admin) {
                $notificationModel->create([
                    'user_id' => $admin['id'],
                    'title' => $title,
                    'message' => $message,
                    'type' => 'compliance_verification',
                    'related_id' => $evidence['id'],
                    'is_read' => 0
                ]);
            }
        } catch (Exception $e) {
            error_log("Failed to send admin notification: " . $e->getMessage());
        }
    }





    public function profile() {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $user = $this->auth->getUser();
        return $this->render('inspector/profile', [
            'title' => 'My Profile',
            'active_page' => 'profile',
            'user' => $user
        ]);
    }

    public function updateProfile() {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        if (!$this->isPost()) {
            return $this->redirect('inspector/profile');
        }

        $userId = $this->auth->getUserId();
        $userModel = new \App\Models\User();
        $data = [
            'full_name' => $_POST['full_name'] ?? '',
            'email' => $_POST['email'] ?? ''
        ];

        // Validate required fields
        if (empty($data['full_name']) || empty($data['email'])) {
            $_SESSION['error'] = 'Full name and email are required.';
            return $this->redirect('inspector/profile');
        }

        // Update password if provided
        if (!empty($_POST['current_password']) && !empty($_POST['new_password'])) {
            $currentUser = $userModel->find($userId);

            if (!password_verify($_POST['current_password'], $currentUser['password_hash'])) {
                $_SESSION['error'] = 'Current password is incorrect.';
                return $this->redirect('inspector/profile');
            }

            if ($_POST['new_password'] !== $_POST['confirm_password']) {
                $_SESSION['error'] = 'New passwords do not match.';
                return $this->redirect('inspector/profile');
            }

            $data['password_hash'] = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
        }

        try {
            if ($userModel->update($userId, $data)) {
                $_SESSION['success'] = 'Profile updated successfully.';
            } else {
                $_SESSION['error'] = 'Failed to update profile.';
            }
        } catch (\Exception $e) {
            error_log("Error updating inspector profile: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred while updating your profile.';
        }

        return $this->redirect('inspector/profile');
    }

    public function settings() {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $user = $this->auth->getUser();
        return $this->render('inspector/settings', [
            'title' => 'Settings',
            'active_page' => 'settings',
            'user' => $user
        ]);
    }

    public function updateSettings() {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        if (!$this->isPost()) {
            return $this->redirect('inspector/settings');
        }

        // Handle settings updates here
        $_SESSION['success'] = 'Settings updated successfully.';
        return $this->redirect('inspector/settings');
    }

    /**
     * Get inspector-specific pending evidence count
     */
    private function getInspectorPendingEvidenceCount($inspectorId, $assignedDistrictIds) {
        if (empty($assignedDistrictIds)) {
            return 0;
        }

        $allEvidence = $this->evidenceModel->getAll();
        $count = 0;

        foreach ($allEvidence as $evidence) {
            if ($evidence['verification_status'] !== 'pending') {
                continue;
            }

            $business = $this->businessModel->getById($evidence['business_id']);
            if ($business && isset($business['barangay_id'])) {
                $barangayModel = new \App\Models\Barangay();
                $barangay = $barangayModel->find($business['barangay_id']);
                if ($barangay && in_array($barangay['district_id'], $assignedDistrictIds)) {
                    $count++;
                }
            }
        }

        return $count;
    }

    /**
     * Get business evidence statistics
     */
    private function getBusinessEvidenceStats($businessId) {
        try {
            $checklistModel = new \App\Models\InspectionChecklist();
            $businessEvidenceModel = new \App\Models\BusinessChecklistEvidence();

            // Get all checklist items
            $allItems = $checklistModel->getAllChecklistItems();
            $totalItems = count($allItems);

            // Get evidence for this business
            $evidence = $businessEvidenceModel->getByBusinessId($businessId);

            // Count items with evidence
            $itemsWithEvidence = [];
            $totalEvidenceFiles = count($evidence);

            foreach ($evidence as $item) {
                $itemsWithEvidence[$item['checklist_item_id']] = true;
            }

            $itemsWithEvidenceCount = count($itemsWithEvidence);

            return [
                'total_items' => $totalItems,
                'items_with_evidence' => $itemsWithEvidenceCount,
                'total_evidence_files' => $totalEvidenceFiles,
                'preparation_percentage' => $totalItems > 0 ? round(($itemsWithEvidenceCount / $totalItems) * 100) : 0
            ];

        } catch (\Exception $e) {
            error_log("Error getting business evidence stats: " . $e->getMessage());
            return [
                'total_items' => 0,
                'items_with_evidence' => 0,
                'total_evidence_files' => 0,
                'preparation_percentage' => 0
            ];
        }
    }

    /**
     * Get detailed checklist evidence for a business
     */
    private function getDetailedChecklistEvidence($businessId) {
        try {
            $checklistModel = new \App\Models\InspectionChecklist();
            $businessEvidenceModel = new \App\Models\BusinessChecklistEvidence();

            // Get database connection
            $database = new \App\Config\Database();
            $db = $database->getConnection();

            // Get all checklist items grouped by category using the correct table names
            $sql = "SELECT ci.id, ci.item_name as item_text, ci.category_id, cc.name as category_name, cc.sort_order as category_sort
                    FROM inspection_checklist_items ci
                    JOIN inspection_checklist_categories cc ON ci.category_id = cc.id
                    WHERE ci.is_active = 1 AND cc.is_active = 1
                    ORDER BY cc.sort_order, ci.sort_order";

            $stmt = $db->prepare($sql);
            $stmt->execute();
            $checklistItems = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            // Group items by category
            $groupedItems = [];
            foreach ($checklistItems as $item) {
                $categoryName = $item['category_name'];
                if (!isset($groupedItems[$categoryName])) {
                    $groupedItems[$categoryName] = [];
                }

                // Get evidence for this item
                $evidence = $businessEvidenceModel->getByBusinessAndItem($businessId, $item['id']);

                $groupedItems[$categoryName][] = [
                    'id' => $item['id'],
                    'item_text' => $item['item_text'],
                    'evidence' => $evidence
                ];
            }

            return $groupedItems;

        } catch (\Exception $e) {
            error_log("Error getting detailed checklist evidence: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get district-based inspections with assignment context
     */
    private function getDistrictBasedInspections($inspectorId, $inspections) {
        $assignedDistricts = $this->assignmentModel->getInspectorDistricts($inspectorId);
        $assignedDistrictIds = array_column($assignedDistricts, 'district_id');

        $districtInspections = [
            'assigned_district' => [],
            'other_district' => []
        ];

        foreach ($inspections as $inspection) {
            $business = $this->businessModel->getById($inspection['business_id']);
            if ($business) {
                // Get district through barangay
                $districtId = null;
                if (isset($business['barangay_id']) && $business['barangay_id']) {
                    $barangayModel = new \App\Models\Barangay();
                    $barangay = $barangayModel->find($business['barangay_id']);
                    if ($barangay && isset($barangay['district_id'])) {
                        $districtId = $barangay['district_id'];
                    }
                }

                if ($districtId && in_array($districtId, $assignedDistrictIds)) {
                    $inspection['district_assigned'] = true;
                    $districtInspections['assigned_district'][] = $inspection;
                } else {
                    $inspection['district_assigned'] = false;
                    $districtInspections['other_district'][] = $inspection;
                }
            }
        }

        return $districtInspections;
    }

    /**
     * Filter inspections by specific district
     */
    private function filterInspectionsByDistrict($inspections, $districtId) {
        return array_filter($inspections, function($inspection) use ($districtId) {
            $business = $this->businessModel->getById($inspection['business_id']);
            if (!$business || !isset($business['barangay_id']) || !$business['barangay_id']) {
                return false;
            }

            // Get district through barangay
            $barangayModel = new \App\Models\Barangay();
            $barangay = $barangayModel->find($business['barangay_id']);

            return $barangay && isset($barangay['district_id']) && $barangay['district_id'] === $districtId;
        });
    }

    /**
     * Categorize inspections by district assignment status
     */
    private function categorizeInspectionsByDistrict($inspections, $assignedDistricts) {
        $assignedDistrictIds = array_column($assignedDistricts, 'district_id');

        $categorized = [
            'my_districts' => [],
            'other_districts' => []
        ];

        foreach ($inspections as $inspection) {
            $business = $this->businessModel->getById($inspection['business_id']);
            if ($business) {
                $inspection['business_name'] = $business['name'];

                // Get district through barangay
                $districtId = null;
                if (isset($business['barangay_id']) && $business['barangay_id']) {
                    $barangayModel = new \App\Models\Barangay();
                    $barangay = $barangayModel->find($business['barangay_id']);
                    if ($barangay && isset($barangay['district_id'])) {
                        $districtId = $barangay['district_id'];
                    }
                }

                $inspection['business_district_id'] = $districtId;

                if ($districtId && in_array($districtId, $assignedDistrictIds)) {
                    $categorized['my_districts'][] = $inspection;
                } else {
                    $categorized['other_districts'][] = $inspection;
                }
            }
        }

        return $categorized;
    }

    /**
     * Start inspection with checklist
     */
    public function startInspectionChecklist($inspectionId)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Use district-validated method to get inspection
        $inspection = $this->inspectionModel->getByIdForInspector($inspectionId, $inspectorId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found or you are not authorized to access it.';
            $this->redirect('inspector/schedule');
            return;
        }

        // Update inspection status to in_progress if it's scheduled or confirmed (inspector has accessed the checklist)
        if ($inspection['status'] === 'scheduled' || $inspection['status'] === 'confirmed') {
            $this->inspectionModel->updateStatus($inspectionId, 'in_progress');
            $inspection['status'] = 'in_progress';
        }

        // Get checklist categories and items
        $checklistCategories = $this->checklistModel->getChecklistCategories(true);

        // Get existing responses if any
        $existingResponses = $this->checklistModel->getInspectionChecklistResponses($inspectionId);
        $responsesByItem = [];
        foreach ($existingResponses as $response) {
            $responsesByItem[$response['checklist_item_id']] = $response;
        }

        // Get business evidence for each checklist item
        $businessEvidence = [];
        foreach ($checklistCategories as &$category) {
            foreach ($category['items'] as &$item) {
                $evidence = $this->businessEvidenceModel->getEvidenceForInspection($inspectionId, $item['id']);
                $businessEvidence[$item['id']] = $evidence;
                $item['business_evidence'] = $evidence;
            }
        }

        // Get completion status
        $completionStatus = $this->checklistModel->getInspectionCompletionStatus($inspectionId);

        // Get current score if inspection has responses
        $currentScore = null;
        if (!empty($existingResponses)) {
            $currentScore = $this->checklistModel->calculateInspectionScore($inspectionId);
        }

        return $this->render('inspector/inspection_checklist', [
            'title' => 'Inspection Checklist - ' . $inspection['business_name'],
            'active_page' => 'schedule',
            'inspection' => $inspection,
            'checklist_categories' => $checklistCategories,
            'existing_responses' => $responsesByItem,
            'business_evidence' => $businessEvidence,
            'completion_status' => $completionStatus,
            'current_score' => $currentScore,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Save checklist response
     */
    public function saveChecklistResponse()
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('inspector/schedule');
            return;
        }

        $inspectorId = $this->auth->getUserId();
        $inspectionId = $_POST['inspection_id'] ?? null;
        $checklistItemId = $_POST['checklist_item_id'] ?? null;
        $complianceStatus = $_POST['compliance_status'] ?? null;
        $notes = $_POST['notes'] ?? null;
        $correctiveAction = $_POST['corrective_action'] ?? null;
        $deadline = $_POST['deadline'] ?? null;

        if (!$inspectionId || !$checklistItemId || !$complianceStatus) {
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            return;
        }

        // Verify inspector can access this inspection
        $inspection = $this->inspectionModel->getByIdForInspector($inspectionId, $inspectorId);
        if (!$inspection) {
            echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
            return;
        }

        // Get checklist item to calculate score
        $checklistItem = $this->checklistModel->getChecklistItem($checklistItemId);
        if (!$checklistItem) {
            echo json_encode(['success' => false, 'message' => 'Invalid checklist item']);
            return;
        }

        // Calculate score based on compliance status
        $score = 0;
        switch ($complianceStatus) {
            case 'compliant':
                $score = $checklistItem['points'];
                break;
            case 'needs_improvement':
                $score = $checklistItem['points'] * 0.5; // Half points
                break;
            case 'non_compliant':
            case 'not_applicable':
                $score = 0;
                break;
        }

        $data = [
            'inspection_id' => $inspectionId,
            'checklist_item_id' => $checklistItemId,
            'inspector_id' => $inspectorId,
            'compliance_status' => $complianceStatus,
            'score' => $score,
            'notes' => $notes,
            'corrective_action' => $correctiveAction,
            'deadline' => $deadline
        ];

        $result = $this->checklistModel->saveChecklistResponse($data);

        if ($result) {
            // Get updated completion status and score
            $completionStatus = $this->checklistModel->getInspectionCompletionStatus($inspectionId);
            $currentScore = $this->checklistModel->calculateInspectionScore($inspectionId);

            echo json_encode([
                'success' => true,
                'message' => 'Response saved successfully',
                'completion_status' => $completionStatus,
                'current_score' => $currentScore
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to save response']);
        }
    }

    /**
     * Start an inspection (change status to in_progress)
     */
    public function startInspection($inspectionId)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Verify inspector can access this inspection
        $inspection = $this->inspectionModel->getByIdForInspector($inspectionId, $inspectorId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found or you are not authorized to access it.';
            $this->redirect('inspector/inspections');
            return;
        }

        // Update inspection status to in_progress
        $result = $this->inspectionModel->updateStatus($inspectionId, 'in_progress');

        if ($result) {
            $_SESSION['success'] = 'Inspection started successfully.';
            $this->redirect('inspector/inspection-checklist/' . $inspectionId);
        } else {
            $_SESSION['error'] = 'Failed to start inspection.';
            $this->redirect('inspector/inspections');
        }
    }

    /**
     * Cancel an inspection
     */
    public function cancelInspection($inspectionId)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('inspector/inspections');
            return;
        }

        $inspectorId = $this->auth->getUserId();

        // Verify inspector can access this inspection
        $inspection = $this->inspectionModel->getByIdForInspector($inspectionId, $inspectorId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found or you are not authorized to access it.';
            $this->redirect('inspector/inspections');
            return;
        }

        // Check if inspection can be cancelled
        if (!in_array($inspection['status'], ['scheduled', 'confirmed', 'in_progress'])) {
            $_SESSION['error'] = 'This inspection cannot be cancelled. Current status: ' . $inspection['status'];
            $this->redirect('inspector/inspections');
            return;
        }

        $cancellationReason = $_POST['cancellation_reason'] ?? '';

        if (empty($cancellationReason)) {
            $_SESSION['error'] = 'Please provide a reason for cancellation.';
            $this->redirect('inspector/inspections');
            return;
        }

        // Update inspection status to cancelled and add cancellation notes
        $updateData = [
            'status' => 'cancelled',
            'notes' => ($inspection['notes'] ? $inspection['notes'] . "\n\n" : '') .
                      "CANCELLED by Inspector on " . date('Y-m-d H:i:s') . "\nReason: " . $cancellationReason
        ];

        if ($this->inspectionModel->update($inspectionId, $updateData)) {
            // Create notification for all admin users about cancellation
            $business = $this->businessModel->getById($inspection['business_id']);
            $userModel = new \App\Models\User();
            $admins = $userModel->getByRole('admin');

            foreach ($admins as $admin) {
                $this->notificationModel->create([
                    'user_id' => $admin['id'],
                    'title' => 'Inspection Cancelled by Inspector',
                    'message' => "Inspector cancelled inspection for {$business['name']}. Reason: {$cancellationReason}",
                    'type' => 'warning',
                    'related_id' => $inspectionId,
                    'related_type' => 'inspection',
                    'action_url' => 'admin/inspections/view/' . $inspectionId
                ]);
            }

            $_SESSION['success'] = 'Inspection cancelled successfully.';
        } else {
            $_SESSION['error'] = 'Failed to cancel inspection.';
        }

        $this->redirect('inspector/inspections');
    }

    /**
     * Confirm an inspection (change status from scheduled to confirmed)
     */
    public function confirmInspection($inspectionId)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('inspector/inspections');
            return;
        }

        $inspectorId = $this->auth->getUserId();

        // Verify inspector can access this inspection
        $inspection = $this->inspectionModel->getByIdForInspector($inspectionId, $inspectorId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found or you are not authorized to access it.';
            $this->redirect('inspector/inspections');
            return;
        }

        // Check if inspection can be confirmed (only scheduled inspections)
        if ($inspection['status'] !== 'scheduled') {
            $_SESSION['error'] = 'Only scheduled inspections can be confirmed. Current status: ' . $inspection['status'];
            $this->redirect('inspector/inspections');
            return;
        }

        // Update inspection status to confirmed
        if ($this->inspectionModel->updateStatus($inspectionId, 'confirmed')) {
            // Create notification for admin about confirmation
            $business = $this->businessModel->getById($inspection['business_id']);
            $userModel = new \App\Models\User();
            $admins = $userModel->getByRole('admin');

            foreach ($admins as $admin) {
                $this->notificationModel->create([
                    'user_id' => $admin['id'],
                    'title' => 'Inspection Confirmed by Inspector',
                    'message' => "Inspector confirmed inspection for {$business['name']} scheduled on " . date('M d, Y', strtotime($inspection['scheduled_date'])),
                    'type' => 'info',
                    'related_id' => $inspectionId,
                    'related_type' => 'inspection',
                    'action_url' => 'admin/inspections/view/' . $inspectionId
                ]);
            }

            $_SESSION['success'] = 'Inspection confirmed successfully.';
        } else {
            $_SESSION['error'] = 'Failed to confirm inspection.';
        }

        $this->redirect('inspector/inspections');
    }

    /**
     * Generate inspection report
     */
    public function inspectionReport($inspectionId)
    {
        $this->auth->requireLogin();
        $this->auth->requireInspector();

        $inspectorId = $this->auth->getUserId();

        // Verify inspector can access this inspection
        $inspection = $this->inspectionModel->getByIdForInspector($inspectionId, $inspectorId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found or you are not authorized to access it.';
            $this->redirect('inspector/inspections');
            return;
        }

        // Get checklist responses and score
        $checklistResponses = $this->checklistModel->getInspectionChecklistResponses($inspectionId);
        $inspectionScore = $this->checklistModel->calculateInspectionScore($inspectionId);
        $completionStatus = $this->checklistModel->getInspectionCompletionStatus($inspectionId);

        // Group responses by category
        $responsesByCategory = [];
        foreach ($checklistResponses as $response) {
            $categoryName = $response['category_name'];
            if (!isset($responsesByCategory[$categoryName])) {
                $responsesByCategory[$categoryName] = [];
            }
            $responsesByCategory[$categoryName][] = $response;
        }

        return $this->render('inspector/inspection_report', [
            'title' => 'Inspection Report - ' . $inspection['business_name'],
            'active_page' => 'inspections',
            'inspection' => $inspection,
            'checklist_responses' => $checklistResponses,
            'responses_by_category' => $responsesByCategory,
            'inspection_score' => $inspectionScore,
            'completion_status' => $completionStatus,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Check if inspector can access a business based on district assignment (through barangay)
     */
    private function canInspectorAccessBusiness($inspectorId, $business) {
        if (!$business || !isset($business['barangay_id'])) {
            return false;
        }

        // Get district through barangay
        $barangayModel = new \App\Models\Barangay();
        $barangay = $barangayModel->find($business['barangay_id']);

        if (!$barangay || !isset($barangay['district_id'])) {
            return false;
        }

        return $this->assignmentModel->isInspectorAssignedToDistrict($inspectorId, $barangay['district_id']);
    }
}