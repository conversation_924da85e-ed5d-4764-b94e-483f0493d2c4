<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit"></i> Edit Checklist Item
        </h1>
        <a href="<?= BASE_URL ?>admin/checklist" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Checklist
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Item Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= BASE_URL ?>admin/checklist/items/<?= $item['id'] ?>/edit">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>" 
                                                    <?= $category['id'] === $item['category_id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item_code" class="form-label">Item Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="item_code" name="item_code" required 
                                           value="<?= htmlspecialchars($item['item_code']) ?>"
                                           placeholder="e.g., SE001, BD002"
                                           pattern="[A-Z]{2}[0-9]{3}"
                                           title="Format: 2 letters + 3 numbers (e.g., SE001)">
                                    <div class="form-text">Format: 2 letters + 3 numbers</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="item_name" class="form-label">Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="item_name" name="item_name" required 
                                   value="<?= htmlspecialchars($item['item_name']) ?>"
                                   placeholder="e.g., Fire Extinguisher, Business Permit">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="2"
                                      placeholder="Brief description of what this item checks..."><?= htmlspecialchars($item['description'] ?? '') ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="compliance_requirement" class="form-label">Compliance Requirement</label>
                            <textarea class="form-control" id="compliance_requirement" name="compliance_requirement" rows="3"
                                      placeholder="Detailed requirements for compliance..."><?= htmlspecialchars($item['compliance_requirement'] ?? '') ?></textarea>
                            <div class="form-text">Specific requirements that must be met for compliance</div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="points" class="form-label">Points</label>
                                    <input type="number" class="form-control" id="points" name="points" 
                                           step="0.1" min="0" max="10" 
                                           value="<?= $item['points'] ?>"
                                           placeholder="1.0">
                                    <div class="form-text">Points for compliance</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           min="0" value="<?= $item['sort_order'] ?>"
                                           placeholder="0">
                                    <div class="form-text">Display order</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_critical" name="is_critical"
                                               <?= $item['is_critical'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_critical">
                                            Critical Item
                                        </label>
                                        <div class="form-text">Must be compliant</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                               <?= $item['is_active'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active Item
                                        </label>
                                        <div class="form-text">Include in inspections</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= BASE_URL ?>admin/checklist" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Item
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Item Information
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>Current Category:</strong> <?= htmlspecialchars($item['category_name']) ?></p>
                    <p><strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($item['created_at'])) ?></p>
                    <p><strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($item['updated_at'])) ?></p>
                    <p><strong>Current Status:</strong> 
                        <?php if ($item['is_active']): ?>
                            <span class="badge bg-success">Active</span>
                        <?php else: ?>
                            <span class="badge bg-danger">Inactive</span>
                        <?php endif; ?>
                        <?php if ($item['is_critical']): ?>
                            <span class="badge bg-warning ms-1">Critical</span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle"></i> Important Notes
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="small text-muted mb-0">
                        <li>Changing the item code may affect existing inspection data</li>
                        <li>Deactivating this item will hide it from new inspections</li>
                        <li>Critical status changes affect compliance requirements</li>
                        <li>Point changes will affect scoring calculations</li>
                        <li>Moving to a different category will reorganize the checklist</li>
                    </ul>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-lightbulb"></i> Best Practices
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="small text-muted mb-0">
                        <li>Use clear, specific item names</li>
                        <li>Provide detailed compliance requirements</li>
                        <li>Mark safety-critical items appropriately</li>
                        <li>Assign points based on importance</li>
                        <li>Keep item codes consistent within categories</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>
