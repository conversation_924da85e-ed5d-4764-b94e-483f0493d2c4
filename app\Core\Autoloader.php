<?php
namespace App\Core;

class Autoloader {
    public function register() {
        spl_autoload_register([$this, 'loadClass']);
    }

    private function loadClass($className) {
        // Convert namespace separators to directory separators
        $className = str_replace('\\', DIRECTORY_SEPARATOR, $className);

        // Remove 'App' from the beginning since our app directory is already in the path
        $className = preg_replace('/^App' . preg_quote(DIRECTORY_SEPARATOR) . '/', '', $className);

        // Build the full path to the class file
        $classFile = ROOT_PATH . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . $className . '.php';

        // --- Debugging lines ---
        error_log("Attempting to load class: " . $className);
        error_log("Looking for file: " . $classFile);
        if (file_exists($classFile)) {
            error_log("File found: " . $classFile);
        } else {
            error_log("File NOT found: " . $classFile);
        }
        // --- End Debugging lines ---

        // Check if file exists and require it
        if (file_exists($classFile)) {
            require_once $classFile;
            return true;
        }

        return false;
    }

} 