<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\Inspection;
use App\Models\User;
use App\Models\WebsiteSetting;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PublicController extends Controller
{
    protected $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Display the homepage
     */
    public function home()
    {
        // Get website settings
        $settings = WebsiteSetting::getAllSettings();
        
        // Get system statistics for homepage
        $stats = [
            'total_businesses' => Business::count(),
            'active_inspectors' => User::where('role', 'inspector')->where('status', 'active')->count(),
            'completed_inspections' => Inspection::where('status', 'completed')->count(),
            'compliance_rate' => $this->calculateComplianceRate(),
        ];

        // Get recent announcements or news (if any)
        $announcements = $this->getRecentAnnouncements();

        return view('public.home', compact('settings', 'stats', 'announcements'));
    }

    /**
     * Display the about page
     */
    public function about()
    {
        $settings = WebsiteSetting::getAllSettings();
        
        // Get additional about page data
        $aboutData = [
            'mission' => 'To ensure workplace safety and health compliance for all businesses in Bacoor City through comprehensive inspection and monitoring systems.',
            'vision' => 'A safer Bacoor City where every workplace prioritizes the health and safety of its employees.',
            'objectives' => [
                'Conduct regular safety inspections of business establishments',
                'Ensure compliance with occupational health and safety standards',
                'Provide guidance and support to business owners',
                'Maintain comprehensive records of safety compliance',
                'Promote workplace safety awareness and education'
            ],
            'services' => [
                'Business Safety Inspections',
                'Compliance Monitoring',
                'Safety Consultation',
                'Incident Investigation',
                'Safety Training Coordination'
            ]
        ];

        return view('public.about', compact('settings', 'aboutData'));
    }

    /**
     * Display the contact page
     */
    public function contact()
    {
        $settings = WebsiteSetting::getAllSettings();
        
        return view('public.contact', compact('settings'));
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'phone' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Store contact message (you can create a ContactMessage model if needed)
        $contactData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'submitted_at' => now(),
            'ip_address' => $request->ip(),
        ];

        // Send email notification to admin
        $this->sendContactNotification($contactData);

        return back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }

    /**
     * Display business registration page
     */
    public function businessRegistration()
    {
        $settings = WebsiteSetting::getAllSettings();
        
        return view('public.business-registration', compact('settings'));
    }

    /**
     * Handle business registration form submission
     */
    public function submitBusinessRegistration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'business_name' => 'required|string|max:255',
            'owner_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'contact_number' => 'required|string|max:50',
            'address' => 'required|string|max:500',
            'business_type' => 'required|string|max:255',
            'employee_count' => 'required|integer|min:1',
            'date_established' => 'nullable|date|before:today',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Create pending business registration
        $registrationData = [
            'business_name' => $request->business_name,
            'owner_name' => $request->owner_name,
            'email' => $request->email,
            'contact_number' => $request->contact_number,
            'address' => $request->address,
            'business_type' => $request->business_type,
            'employee_count' => $request->employee_count,
            'date_established' => $request->date_established,
            'status' => 'pending_approval',
            'submitted_at' => now(),
        ];

        // Send notification to admin about new registration
        $this->sendRegistrationNotification($registrationData);

        return back()->with('success', 'Your business registration has been submitted successfully! You will receive an email confirmation once your application is reviewed.');
    }

    /**
     * Calculate overall compliance rate
     */
    private function calculateComplianceRate()
    {
        $totalBusinesses = Business::where('status', 'active')->count();
        
        if ($totalBusinesses === 0) {
            return 0;
        }

        $compliantBusinesses = Business::where('status', 'active')
            ->where('compliance_status', 'compliant')
            ->count();

        return round(($compliantBusinesses / $totalBusinesses) * 100, 1);
    }

    /**
     * Get recent announcements
     */
    private function getRecentAnnouncements()
    {
        // This can be expanded to include an announcements system
        return [
            [
                'title' => 'New Safety Guidelines Released',
                'content' => 'Updated workplace safety guidelines are now available for all businesses.',
                'date' => now()->subDays(5),
                'type' => 'info'
            ],
            [
                'title' => 'Inspection Schedule Updates',
                'content' => 'Regular inspection schedules have been updated for the current quarter.',
                'date' => now()->subDays(10),
                'type' => 'announcement'
            ]
        ];
    }

    /**
     * Send contact form notification to admin
     */
    private function sendContactNotification($contactData)
    {
        try {
            // Get admin email from settings
            $adminEmail = WebsiteSetting::getSetting('contact_email', '<EMAIL>');
            
            // Send email notification (implement email template)
            // This would use your EmailService to send notification
            
            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send business registration notification to admin
     */
    private function sendRegistrationNotification($registrationData)
    {
        try {
            // Get admin email from settings
            $adminEmail = WebsiteSetting::getSetting('contact_email', '<EMAIL>');
            
            // Send email notification about new business registration
            // This would use your EmailService to send notification
            
            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to send registration notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display services page
     */
    public function services()
    {
        $settings = WebsiteSetting::getAllSettings();
        
        $services = [
            [
                'title' => 'Business Safety Inspections',
                'description' => 'Comprehensive safety inspections to ensure workplace compliance with health and safety standards.',
                'icon' => 'fas fa-clipboard-check',
                'features' => [
                    'Thorough workplace assessment',
                    'Safety equipment evaluation',
                    'Compliance documentation',
                    'Detailed inspection reports'
                ]
            ],
            [
                'title' => 'Compliance Monitoring',
                'description' => 'Ongoing monitoring and tracking of business compliance with safety regulations.',
                'icon' => 'fas fa-chart-line',
                'features' => [
                    'Regular compliance checks',
                    'Progress tracking',
                    'Compliance reporting',
                    'Improvement recommendations'
                ]
            ],
            [
                'title' => 'Safety Consultation',
                'description' => 'Expert guidance and consultation on workplace safety best practices.',
                'icon' => 'fas fa-users',
                'features' => [
                    'Safety policy development',
                    'Risk assessment guidance',
                    'Training recommendations',
                    'Implementation support'
                ]
            ],
            [
                'title' => 'Digital Management',
                'description' => 'Modern digital platform for managing all safety-related documentation and processes.',
                'icon' => 'fas fa-laptop',
                'features' => [
                    'Online inspection scheduling',
                    'Digital evidence submission',
                    'Real-time status tracking',
                    'Automated notifications'
                ]
            ]
        ];

        return view('public.services', compact('settings', 'services'));
    }
}
