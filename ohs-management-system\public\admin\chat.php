<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed.');
}

// Create chat tables if they don't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS chat_rooms (
        id CHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type ENUM('support', 'general', 'private') DEFAULT 'support',
        created_by <PERSON>AR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS chat_messages (
        id CHAR(36) PRIMARY KEY,
        room_id CHAR(36) NOT NULL,
        user_id CHAR(36) NOT NULL,
        message TEXT NOT NULL,
        message_type ENUM('text', 'file', 'system') DEFAULT 'text',
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS chat_participants (
        id CHAR(36) PRIMARY KEY,
        room_id CHAR(36) NOT NULL,
        user_id CHAR(36) NOT NULL,
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_participant (room_id, user_id)
    )");
} catch (Exception $e) {
    // Tables might already exist
}

// Get chat rooms with latest message and unread count
$chat_rooms = $pdo->query("
    SELECT cr.*, 
           u.full_name as created_by_name,
           (SELECT COUNT(*) FROM chat_messages cm WHERE cm.room_id = cr.id AND cm.is_read = FALSE AND cm.user_id != '{$_SESSION['user_id']}') as unread_count,
           (SELECT cm.message FROM chat_messages cm WHERE cm.room_id = cr.id ORDER BY cm.created_at DESC LIMIT 1) as last_message,
           (SELECT cm.created_at FROM chat_messages cm WHERE cm.room_id = cr.id ORDER BY cm.created_at DESC LIMIT 1) as last_message_time
    FROM chat_rooms cr 
    LEFT JOIN users u ON cr.created_by = u.id 
    ORDER BY last_message_time DESC, cr.created_at DESC
")->fetchAll(PDO::FETCH_ASSOC);

// Get online users
$online_users = $pdo->query("
    SELECT id, full_name, role, last_activity_at 
    FROM users 
    WHERE last_activity_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
    AND id != '{$_SESSION['user_id']}'
    ORDER BY full_name
")->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Chat - OHS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 280px;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover { background: rgba(255,255,255,0.1); color: white; }
        .sidebar .nav-link.active { background: rgba(255,255,255,0.2); color: white; }
        .main-content { margin-left: 280px; padding: 2rem; }
        .chat-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 70vh;
            overflow: hidden;
        }
        .chat-sidebar {
            border-right: 1px solid #e9ecef;
            height: 100%;
            overflow-y: auto;
        }
        .chat-room-item {
            padding: 15px;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .chat-room-item:hover {
            background-color: #f8f9fa;
        }
        .chat-room-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .chat-area {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: #fafafa;
        }
        .chat-input {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
        }
        .message {
            margin-bottom: 15px;
        }
        .message.own {
            text-align: right;
        }
        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        .message.own .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .message.other .message-bubble {
            background: white;
            border: 1px solid #e9ecef;
        }
        .online-indicator {
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .unread-badge {
            background: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="p-3">
                    <h4><i class="fas fa-shield-alt me-2"></i>OHS Admin</h4>
                    <hr class="text-white">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="businesses.php">
                            <i class="fas fa-building me-2"></i>Businesses
                        </a>
                        <a class="nav-link" href="inspections.php">
                            <i class="fas fa-clipboard-check me-2"></i>Inspections
                        </a>
                        <a class="nav-link active" href="chat.php">
                            <i class="fas fa-comments me-2"></i>Live Chat
                        </a>
                        <a class="nav-link" href="website-settings.php">
                            <i class="fas fa-cog me-2"></i>Website Settings
                        </a>
                        <hr class="text-white">
                        <a class="nav-link" href="../index.php?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-comments me-2"></i>Live Chat</h2>
                        <p class="text-muted mb-0">Real-time communication with users</p>
                    </div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newChatModal">
                        <i class="fas fa-plus me-2"></i>New Chat
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="chat-container">
                            <div class="row h-100">
                                <!-- Chat Rooms List -->
                                <div class="col-md-4 chat-sidebar">
                                    <div class="p-3 border-bottom">
                                        <h6 class="mb-0">Chat Rooms</h6>
                                    </div>
                                    
                                    <?php if (empty($chat_rooms)): ?>
                                        <div class="text-center p-4 text-muted">
                                            <i class="fas fa-comments fa-3x mb-3"></i>
                                            <p>No chat rooms yet.<br>Start a new conversation!</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($chat_rooms as $room): ?>
                                            <div class="chat-room-item" data-room-id="<?= $room['id'] ?>">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1"><?= htmlspecialchars($room['name']) ?></h6>
                                                        <p class="mb-1 text-muted small">
                                                            <?= htmlspecialchars(substr($room['last_message'] ?? 'No messages yet', 0, 50)) ?>
                                                            <?= strlen($room['last_message'] ?? '') > 50 ? '...' : '' ?>
                                                        </p>
                                                        <small class="text-muted">
                                                            <?= $room['last_message_time'] ? date('M j, g:i A', strtotime($room['last_message_time'])) : '' ?>
                                                        </small>
                                                    </div>
                                                    <?php if ($room['unread_count'] > 0): ?>
                                                        <span class="unread-badge"><?= $room['unread_count'] ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Chat Area -->
                                <div class="col-md-8">
                                    <div class="chat-area">
                                        <div class="p-3 border-bottom bg-light">
                                            <h6 class="mb-0" id="chat-title">Select a chat room</h6>
                                            <small class="text-muted" id="chat-subtitle">Choose a conversation to start chatting</small>
                                        </div>
                                        
                                        <div class="chat-messages" id="chat-messages">
                                            <div class="text-center text-muted mt-5">
                                                <i class="fas fa-comments fa-3x mb-3"></i>
                                                <p>Select a chat room to view messages</p>
                                            </div>
                                        </div>
                                        
                                        <div class="chat-input" id="chat-input" style="display: none;">
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="Type your message..." id="message-input">
                                                <button class="btn btn-primary" type="button" id="send-button">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Online Users -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-users me-2"></i>Online Users</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($online_users)): ?>
                                    <p class="text-muted mb-0">No users currently online</p>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($online_users as $user): ?>
                                            <div class="col-md-3 mb-2">
                                                <div class="d-flex align-items-center">
                                                    <span class="online-indicator"></span>
                                                    <span class="me-2"><?= htmlspecialchars($user['full_name']) ?></span>
                                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'inspector' ? 'warning' : 'info') ?> small">
                                                        <?= ucfirst(str_replace('_', ' ', $user['role'])) ?>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- New Chat Modal -->
    <div class="modal fade" id="newChatModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Start New Chat</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="newChatForm">
                        <div class="mb-3">
                            <label for="chatName" class="form-label">Chat Room Name</label>
                            <input type="text" class="form-control" id="chatName" required>
                        </div>
                        <div class="mb-3">
                            <label for="chatType" class="form-label">Chat Type</label>
                            <select class="form-select" id="chatType">
                                <option value="support">Support</option>
                                <option value="general">General</option>
                                <option value="private">Private</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="createChatBtn">Create Chat</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Chat functionality
        let currentRoomId = null;
        
        // Chat room selection
        document.querySelectorAll('.chat-room-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                document.querySelectorAll('.chat-room-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                this.classList.add('active');
                
                currentRoomId = this.dataset.roomId;
                loadChatMessages(currentRoomId);
                document.getElementById('chat-input').style.display = 'block';
            });
        });
        
        // Send message
        document.getElementById('send-button').addEventListener('click', sendMessage);
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (message && currentRoomId) {
                // Add message to chat (simulate)
                addMessageToChat('You', message, true);
                messageInput.value = '';
                
                // Here you would send the message to the server
                // For demo purposes, we'll just add it to the UI
            }
        }
        
        function loadChatMessages(roomId) {
            // Simulate loading messages
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = `
                <div class="message other">
                    <div class="message-bubble">
                        <strong>User:</strong> Hello, I need help with my inspection.
                        <br><small class="text-muted">2 minutes ago</small>
                    </div>
                </div>
                <div class="message own">
                    <div class="message-bubble">
                        <strong>You:</strong> Hello! I'd be happy to help you with your inspection. What specific assistance do you need?
                        <br><small class="text-muted">1 minute ago</small>
                    </div>
                </div>
            `;
            
            document.getElementById('chat-title').textContent = 'Support Chat';
            document.getElementById('chat-subtitle').textContent = 'Active conversation';
        }
        
        function addMessageToChat(sender, message, isOwn) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isOwn ? 'own' : 'other'}`;
            messageDiv.innerHTML = `
                <div class="message-bubble">
                    <strong>${sender}:</strong> ${message}
                    <br><small class="text-muted">Just now</small>
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Create new chat
        document.getElementById('createChatBtn').addEventListener('click', function() {
            const chatName = document.getElementById('chatName').value;
            const chatType = document.getElementById('chatType').value;
            
            if (chatName) {
                // Here you would create the chat room on the server
                alert('Chat room "' + chatName + '" created successfully!');
                bootstrap.Modal.getInstance(document.getElementById('newChatModal')).hide();
                location.reload(); // Reload to show new chat room
            }
        });
    </script>
</body>
</html>
