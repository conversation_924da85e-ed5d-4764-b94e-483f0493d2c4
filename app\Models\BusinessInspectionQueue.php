<?php

namespace App\Models;

use App\Config\Database;
use PDO;

class BusinessInspectionQueue
{
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
    }

    private function generateUUID()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Add business to inspection queue
     */
    public function addToQueue($data)
    {
        $query = "INSERT INTO business_inspection_queue
                  (id, business_id, priority, inspection_type, due_date, reason, requested_by, status)
                  VALUES (:id, :business_id, :priority, :inspection_type, :due_date, :reason, :requested_by, :status)";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $this->generateUUID(),
            ':business_id' => $data['business_id'],
            ':priority' => $data['priority'] ?? 'medium',
            ':inspection_type' => $data['inspection_type'] ?? 'routine',
            ':due_date' => $data['due_date'],
            ':reason' => $data['reason'] ?? null,
            ':requested_by' => $data['requested_by'] ?? null,
            ':status' => $data['status'] ?? 'pending'
        ]);
    }

    /**
     * Get businesses needing inspection for a specific inspector's districts
     * Excludes businesses that already have scheduled inspections
     */
    public function getBusinessesNeedingInspection($inspectorId = null, $districtIds = null)
    {
        $query = "SELECT biq.*, b.name as business_name, b.address, c.name as business_type,
                         brg.name as barangay_name, d.name as district_name,
                         u.full_name as requested_by_name,
                         inspector.full_name as assigned_inspector_name,
                         DATEDIFF(biq.due_date, CURDATE()) as days_until_due
                  FROM business_inspection_queue biq
                  LEFT JOIN businesses b ON biq.business_id = b.id
                  LEFT JOIN business_categories c ON b.category_id = c.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  LEFT JOIN users u ON biq.requested_by = u.id
                  LEFT JOIN users inspector ON biq.assigned_inspector = inspector.id
                  WHERE biq.status IN ('pending', 'scheduled')
                  AND biq.business_id NOT IN (
                      SELECT DISTINCT i.business_id
                      FROM inspections i
                      WHERE i.status IN ('scheduled', 'confirmed', 'in_progress')
                  )";

        $params = [];

        // Filter by inspector's assigned districts
        if ($districtIds && !empty($districtIds)) {
            $placeholders = str_repeat('?,', count($districtIds) - 1) . '?';
            $query .= " AND brg.district_id IN ($placeholders)";
            $params = array_merge($params, $districtIds);
        }

        // Filter by assigned inspector
        if ($inspectorId) {
            $query .= " AND (biq.assigned_inspector = ? OR biq.assigned_inspector IS NULL)";
            $params[] = $inspectorId;
        }

        $query .= " ORDER BY
                    CASE biq.priority
                        WHEN 'urgent' THEN 1
                        WHEN 'high' THEN 2
                        WHEN 'medium' THEN 3
                        WHEN 'low' THEN 4
                    END,
                    biq.due_date ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get overdue inspections
     * Excludes businesses that already have scheduled inspections
     */
    public function getOverdueInspections($inspectorId = null, $districtIds = null)
    {
        $query = "SELECT biq.*, b.name as business_name, b.address, c.name as business_type, brg.district_id,
                         brg.name as barangay_name, d.name as district_name, u.full_name as requested_by_name,
                         DATEDIFF(CURDATE(), biq.due_date) as days_overdue
                  FROM business_inspection_queue biq
                  LEFT JOIN businesses b ON biq.business_id = b.id
                  LEFT JOIN business_categories c ON b.category_id = c.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  LEFT JOIN users u ON biq.requested_by = u.id
                  WHERE biq.status IN ('pending', 'scheduled')
                  AND biq.due_date < CURDATE()
                  AND biq.business_id NOT IN (
                      SELECT DISTINCT i.business_id
                      FROM inspections i
                      WHERE i.status IN ('scheduled', 'confirmed', 'in_progress')
                  )";

        $params = [];

        if ($districtIds && !empty($districtIds)) {
            $placeholders = str_repeat('?,', count($districtIds) - 1) . '?';
            $query .= " AND brg.district_id IN ($placeholders)";
            $params = array_merge($params, $districtIds);
        }

        if ($inspectorId) {
            $query .= " AND (biq.assigned_inspector = ? OR biq.assigned_inspector IS NULL)";
            $params[] = $inspectorId;
        }

        $query .= " ORDER BY biq.due_date ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Assign inspector to inspection queue item
     */
    public function assignInspector($queueId, $inspectorId)
    {
        $query = "UPDATE business_inspection_queue 
                  SET assigned_inspector = :inspector_id, 
                      status = 'scheduled',
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :queue_id";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':inspector_id' => $inspectorId,
            ':queue_id' => $queueId
        ]);
    }

    /**
     * Update queue item status
     */
    public function updateStatus($queueId, $status)
    {
        $query = "UPDATE business_inspection_queue
                  SET status = :status, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :queue_id";

        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':status' => $status,
            ':queue_id' => $queueId
        ]);
    }

    /**
     * Mark business as scheduled (update queue status when inspection is created)
     */
    public function markBusinessAsScheduled($businessId)
    {
        $query = "UPDATE business_inspection_queue
                  SET status = 'completed', updated_at = CURRENT_TIMESTAMP
                  WHERE business_id = :business_id
                  AND status IN ('pending', 'scheduled')";

        $stmt = $this->db->prepare($query);
        return $stmt->execute([':business_id' => $businessId]);
    }

    /**
     * Get queue statistics
     */
    public function getQueueStatistics($inspectorId = null, $districtIds = null)
    {
        $baseQuery = "FROM business_inspection_queue biq
                      LEFT JOIN businesses b ON biq.business_id = b.id
                      LEFT JOIN barangays brg ON b.barangay_id = brg.id";
        
        $whereClause = " WHERE 1=1";
        $params = [];
        
        if ($districtIds && !empty($districtIds)) {
            $placeholders = str_repeat('?,', count($districtIds) - 1) . '?';
            $whereClause .= " AND brg.district_id IN ($placeholders)";
            $params = array_merge($params, $districtIds);
        }
        
        if ($inspectorId) {
            $whereClause .= " AND (biq.assigned_inspector = ? OR biq.assigned_inspector IS NULL)";
            $params[] = $inspectorId;
        }
        
        $stats = [];
        
        // Total pending
        $query = "SELECT COUNT(*) $baseQuery $whereClause AND biq.status = 'pending'";
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $stats['pending'] = $stmt->fetchColumn();
        
        // Total scheduled
        $query = "SELECT COUNT(*) $baseQuery $whereClause AND biq.status = 'scheduled'";
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $stats['scheduled'] = $stmt->fetchColumn();
        
        // Overdue
        $query = "SELECT COUNT(*) $baseQuery $whereClause AND biq.status IN ('pending', 'scheduled') AND biq.due_date < CURDATE()";
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $stats['overdue'] = $stmt->fetchColumn();
        
        // Due this week
        $query = "SELECT COUNT(*) $baseQuery $whereClause AND biq.status IN ('pending', 'scheduled') AND biq.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $stats['due_this_week'] = $stmt->fetchColumn();
        
        // By priority
        $priorities = ['urgent', 'high', 'medium', 'low'];
        foreach ($priorities as $priority) {
            $query = "SELECT COUNT(*) $baseQuery $whereClause AND biq.status IN ('pending', 'scheduled') AND biq.priority = ?";
            $priorityParams = array_merge($params, [$priority]);
            $stmt = $this->db->prepare($query);
            $stmt->execute($priorityParams);
            $stats["priority_$priority"] = $stmt->fetchColumn();
        }
        
        return $stats;
    }

    /**
     * Auto-generate inspection queue for businesses based on their last inspection
     */
    public function generateRoutineInspections()
    {
        // Get businesses that need routine inspection (6 months since last inspection)
        $query = "SELECT b.id, b.name, b.last_inspection_date, c.name as business_type
                  FROM businesses b
                  LEFT JOIN business_categories c ON b.category_id = c.id
                  WHERE b.status = 'active'
                  AND (
                      b.last_inspection_date IS NULL
                      OR b.last_inspection_date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                  )
                  AND b.id NOT IN (
                      SELECT business_id FROM business_inspection_queue
                      WHERE status IN ('pending', 'scheduled', 'in_progress')
                  )";
        
        $stmt = $this->db->query($query);
        $businesses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $addedCount = 0;
        foreach ($businesses as $business) {
            // Determine priority based on business type and last inspection
            $priority = 'medium';
            if ($business['last_inspection_date'] === null) {
                $priority = 'high'; // New business
            } elseif (strtotime($business['last_inspection_date']) < strtotime('-12 months')) {
                $priority = 'urgent'; // Very overdue
            }
            
            // Set due date based on priority
            $dueDate = date('Y-m-d', strtotime('+30 days')); // Default 30 days
            if ($priority === 'urgent') {
                $dueDate = date('Y-m-d', strtotime('+7 days'));
            } elseif ($priority === 'high') {
                $dueDate = date('Y-m-d', strtotime('+14 days'));
            }
            
            $data = [
                'business_id' => $business['id'],
                'priority' => $priority,
                'inspection_type' => 'routine',
                'due_date' => $dueDate,
                'reason' => 'Routine inspection - ' . ($business['last_inspection_date'] ? 'Follow-up' : 'Initial inspection'),
                'status' => 'pending'
            ];
            
            if ($this->addToQueue($data)) {
                $addedCount++;
            }
        }
        
        return $addedCount;
    }

    /**
     * Get queue item by ID
     */
    public function getById($id)
    {
        $query = "SELECT biq.*, b.name as business_name, b.address, c.name as business_type,
                         brg.name as barangay_name, d.name as district_name,
                         u.full_name as requested_by_name
                  FROM business_inspection_queue biq
                  LEFT JOIN businesses b ON biq.business_id = b.id
                  LEFT JOIN business_categories c ON b.category_id = c.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  LEFT JOIN users u ON biq.requested_by = u.id
                  WHERE biq.id = ?";

        $stmt = $this->db->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get queue item by ID
     */
    public function getQueueItem($queueId)
    {
        $query = "SELECT biq.*, b.name as business_name, b.address, c.name as business_type,
                         brg.name as barangay_name, d.name as district_name,
                         u.full_name as requested_by_name,
                         inspector.full_name as assigned_inspector_name
                  FROM business_inspection_queue biq
                  LEFT JOIN businesses b ON biq.business_id = b.id
                  LEFT JOIN business_categories c ON b.category_id = c.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  LEFT JOIN users u ON biq.requested_by = u.id
                  LEFT JOIN users inspector ON biq.assigned_inspector = inspector.id
                  WHERE biq.id = :queue_id";

        $stmt = $this->db->prepare($query);
        $stmt->execute([':queue_id' => $queueId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
