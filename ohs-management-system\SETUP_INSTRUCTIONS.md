# 🚀 OHS Management System - Setup Instructions

## **📋 Quick Setup Guide**

### **Step 1: Install Dependencies**
```bash
# Navigate to project directory
cd ohs-management-system

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### **Step 2: Database Setup**
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE ohs_management_system"

# Update .env file with your database credentials:
# DB_DATABASE=ohs_management_system
# DB_USERNAME=your_username
# DB_PASSWORD=your_password

# Run migrations
php artisan migrate

# Seed initial data (optional)
php artisan db:seed
```

### **Step 3: Storage Setup**
```bash
# Create storage link
php artisan storage:link

# Set permissions (Linux/Mac)
chmod -R 775 storage bootstrap/cache
```

### **Step 4: Build Assets**
```bash
# Build frontend assets
npm run build

# Or for development with hot reload
npm run dev
```

### **Step 5: Start Application**
```bash
# Start Laravel development server
php artisan serve

# Application will be available at: http://localhost:8000
```

## **🔧 Configuration**

### **Email Configuration**
Update `.env` file with your email settings:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
```

### **Broadcasting (Real-time Chat)**
For real-time chat functionality, configure Pusher:
```env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_key
PUSHER_APP_SECRET=your_secret
PUSHER_APP_CLUSTER=mt1
```

## **👤 Default Admin Account**

After running migrations, create an admin account:
```bash
php artisan tinker
```

Then in tinker:
```php
use App\Models\User;
use Illuminate\Support\Facades\Hash;

User::create([
    'email' => '<EMAIL>',
    'password' => Hash::make('admin123'),
    'full_name' => 'System Administrator',
    'role' => 'admin',
    'status' => 'active',
]);
```

## **📁 File Structure**

```
ohs-management-system/
├── app/
│   ├── Http/Controllers/
│   │   ├── Admin/
│   │   ├── Inspector/
│   │   ├── BusinessOwner/
│   │   └── Api/
│   ├── Models/
│   ├── Services/
│   └── Http/Requests/
├── database/
│   └── migrations/
├── resources/
│   └── views/
│       ├── admin/
│       ├── inspector/
│       ├── business-owner/
│       └── layouts/
├── routes/
│   ├── web.php
│   └── api.php
└── public/
```

## **🧪 Testing**

Run the test suite:
```bash
# Run all tests
php artisan test

# Run specific test
php artisan test --filter InspectionWorkflowTest
```

## **🔄 Data Migration from Old System**

If you have an existing OHS system, use the migration service:
```bash
# Configure old database connection in .env
DB_OLD_HOST=your_old_host
DB_OLD_DATABASE=your_old_database
DB_OLD_USERNAME=your_old_username
DB_OLD_PASSWORD=your_old_password

# Run migration
php artisan migrate:old-data
```

## **📊 Features Available**

### **Admin Panel**
- Dashboard with analytics
- Business registration and management
- Inspector assignment
- Inspection scheduling and verification
- Checklist management
- Chat system
- Advanced reporting

### **Inspector Panel**
- Dashboard with assigned inspections
- Mobile-friendly checklist interface
- Evidence photo capture
- Real-time chat with businesses
- Inspection reports

### **Business Owner Panel**
- Business profile management
- Evidence upload system
- Inspection history
- Compliance tracking
- Chat support

### **Mobile API**
- Complete REST API for mobile apps
- Token-based authentication
- All core functionality available

## **🚀 Production Deployment**

For production deployment, see `LARAVEL_DEPLOYMENT_GUIDE.md`

## **📞 Support**

- **Documentation**: Check the `/docs` folder
- **Issues**: Create GitHub issues for bugs
- **Features**: Submit feature requests

## **🎉 You're Ready!**

Your OHS Management System is now set up and ready to use!

**Login at**: http://localhost:8000/login
**Admin Email**: <EMAIL>
**Admin Password**: admin123

**Remember to change the default password after first login!**
