<?php
use App\Libraries\Auth;
$auth = Auth::getInstance();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>OHS System</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="<?= BASE_URL ?>assets/img/favicon.svg">
    <link rel="alternate icon" type="image/x-icon" href="<?= BASE_URL ?>assets/img/favicon.ico">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= BASE_URL ?>assets/css/style.css">

    <!-- Custom Theme CSS (loaded last to override other styles) -->
    <link href="<?= BASE_URL ?>assets/css/custom-theme.css?v=<?= time() ?>" rel="stylesheet">

    <!-- Dynamic Color Variables -->
    <?php
    $settingsModel = new \App\Models\WebsiteSettings();
    $colors = $settingsModel->getCurrentColorPalette();
    ?>
    <style id="dynamic-colors">
        :root {
            --primary-color: <?= $colors['primary_color'] ?> !important;
            --secondary-color: <?= $colors['secondary_color'] ?> !important;
            --accent-color: <?= $colors['accent_color'] ?> !important;
            --success-color: <?= $colors['success_color'] ?> !important;
            --warning-color: <?= $colors['warning_color'] ?> !important;
            --danger-color: <?= $colors['danger_color'] ?> !important;
            --dark-color: <?= $colors['dark_color'] ?> !important;
            --light-color: <?= $colors['light_color'] ?> !important;
        }
    </style>

    <!-- Additional Styles -->
    <?php $this->renderSection('styles'); ?>

    <style>
        :root {
            --sidebar-width: 280px;
            --header-height: 60px;
            --sidebar-collapsed-width: 70px;
            --primary-soft: #faf5ff;
            --background-color: #f8f9fa;
            --text-primary: #333333;
            --text-secondary: #6c757d;
        }

        body {
            min-height: 100vh;
            background: var(--background-color);
            color: var(--text-primary);
        }

        /* Sidebar Styles */
        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: #fff;
            border-right: 1px solid rgba(0,0,0,.075);
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-brand {
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            min-height: var(--header-height);
            background: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
        }

        .sidebar-brand i {
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .sidebar-brand .sidebar-brand-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-left: 0.75rem;
        }

        .sidebar-content {
            flex: 1;
            padding: 1rem 0;
        }

        .sidebar-divider {
            margin: 0.5rem 1.5rem;
            border-top: 1px solid rgba(0,0,0,.05);
        }

        .sidebar-heading {
            padding: 0.75rem 1.5rem 0.5rem;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
        }

        #sidebar .nav-item {
            padding: 0 1rem;
            margin: 0.125rem 0;
        }

        #sidebar .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--text-secondary);
            border-radius: 0.35rem;
            transition: all 0.2s ease-in-out;
            position: relative;
            font-weight: 500;
            white-space: nowrap;
        }

        #sidebar .nav-link i {
            width: 1.25rem;
            font-size: 0.9rem;
            text-align: center;
            margin-right: 0.75rem;
            transition: all 0.2s;
        }

        #sidebar .nav-link span {
            font-size: 0.875rem;
        }

        #sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        #sidebar.collapsed .sidebar-brand-text {
            display: none;
        }

        #sidebar.collapsed .nav-link span {
            display: none;
        }

        #sidebar.collapsed .nav-link {
            padding: 0.75rem;
            justify-content: center;
        }

        #sidebar.collapsed .nav-link i {
            margin: 0;
            font-size: 1.1rem;
            width: auto;
        }

        #sidebar.collapsed .sidebar-heading,
        #sidebar.collapsed .sidebar-divider {
            display: none;
        }

        /* Header Styles */
        #header {
            position: fixed;
            top: 0;
            left: var(--sidebar-width);
            right: 0;
            height: var(--header-height);
            background: #fff;
            border-bottom: 1px solid rgba(0,0,0,.075);
            transition: all 0.3s;
            z-index: 999;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
            padding: 0 1.5rem;
        }

        #header.sidebar-collapsed {
            left: var(--sidebar-collapsed-width);
        }

        #sidebarToggle {
            background: transparent;
            border: none;
            padding: 0.75rem;
            color: var(--text-secondary);
            border-radius: 0.35rem;
            transition: all 0.2s;
        }

        #sidebarToggle:hover {
            color: var(--primary-color);
            background: var(--primary-soft);
        }

        .user-dropdown .dropdown-toggle {
            padding: 0.5rem 1rem;
            color: var(--text-primary) !important;
            font-weight: 500;
        }

        .user-dropdown .dropdown-toggle:after {
            display: none;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
            border-radius: 0.35rem;
        }

        .user-dropdown .dropdown-item {
            padding: 0.5rem 1rem;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s;
        }

        .user-dropdown .dropdown-item:hover {
            color: var(--primary-color);
            background: var(--primary-soft);
        }

        /* Main Content Styles */
        #main {
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            padding: 1.5rem;
            transition: all 0.3s;
        }

        #main.sidebar-collapsed {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Card Styles */
        .card {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
            border-radius: 0.35rem;
            transition: all 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        /* Button Styles */
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: #fff;
        }

        .btn-primary:hover {
            background: #0052cc;
            border-color: #0052cc;
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: #fff;
        }

        /* Table Styles */
        .table thead th {
            background: var(--background-color);
            color: var(--text-secondary);
            font-weight: 600;
            border-bottom: 2px solid #e3e6ec;
        }

        .badge {
            font-weight: 500;
            padding: 0.5em 0.75em;
        }

        /* Enhanced Responsive Styles */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 260px;
            }
        }

        @media (max-width: 992px) {
            :root {
                --sidebar-width: 240px;
            }

            #main {
                padding: 1rem;
            }

            .card {
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 768px) {
            :root {
                --sidebar-width: 280px;
                --header-height: 56px;
            }

            #sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                z-index: 1050;
            }

            #sidebar.show {
                transform: translateX(0);
            }

            #sidebar.collapsed {
                transform: translateX(0);
                width: var(--sidebar-width);
            }

            #header {
                left: 0;
                padding: 0 1rem;
            }

            #main {
                margin-left: 0;
                margin-top: var(--header-height);
                padding: 1rem 0.75rem;
            }

            #main.sidebar-collapsed {
                margin-left: 0;
            }

            #header.sidebar-collapsed {
                left: 0;
            }

            #sidebar.collapsed .nav-link span {
                display: inline;
            }

            #sidebar.collapsed .nav-link i {
                margin-right: 0.75rem;
                font-size: 1rem;
            }

            .sidebar-heading {
                font-size: 0.65rem;
            }

            .page-title {
                font-size: 1rem !important;
                display: none;
            }

            .user-dropdown .dropdown-toggle span {
                display: none;
            }

            .table-responsive {
                font-size: 0.875rem;
            }

            .btn-group .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            .card-header h6 {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            :root {
                --header-height: 50px;
            }

            #main {
                padding: 0.75rem 0.5rem;
            }

            .container-fluid {
                padding: 0;
            }

            .card {
                border-radius: 0.25rem;
                margin-bottom: 0.75rem;
            }

            .card-header {
                padding: 0.75rem 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .btn {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }

            .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            .table {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.25rem;
            }

            .badge {
                font-size: 0.7rem;
                padding: 0.25em 0.5em;
            }

            .progress {
                height: 16px !important;
            }

            .sidebar-brand {
                padding: 0.75rem 1rem;
            }

            .sidebar-brand-text {
                font-size: 1.1rem;
            }

            #sidebarToggle {
                padding: 0.5rem;
            }

            .user-dropdown .dropdown-toggle {
                padding: 0.25rem 0.5rem;
            }

            .alert {
                padding: 0.75rem 1rem;
                margin-bottom: 1rem;
                font-size: 0.875rem;
            }

            .d-sm-flex {
                flex-direction: column !important;
                align-items: flex-start !important;
            }

            .d-sm-flex .btn {
                margin-top: 0.5rem;
                width: 100%;
            }

            .row.mb-4 {
                margin-bottom: 1rem !important;
            }

            .col-xl-3,
            .col-xl-2,
            .col-md-6,
            .col-md-4 {
                margin-bottom: 0.75rem;
            }
        }

        /* Tablet Landscape Optimizations */
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
            :root {
                --sidebar-width: 220px;
            }

            .table-responsive {
                font-size: 0.9rem;
            }

            .card-body {
                padding: 1.25rem;
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .nav-link {
                padding: 1rem;
                min-height: 48px;
                display: flex;
                align-items: center;
            }

            .btn {
                min-height: 44px;
                padding: 0.5rem 1rem;
            }

            .btn-sm {
                min-height: 36px;
                padding: 0.375rem 0.75rem;
            }

            .dropdown-item {
                padding: 0.75rem 1rem;
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }

        /* Overlay for mobile sidebar */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
        }

        @media (max-width: 768px) {
            .sidebar-overlay.show {
                display: block;
            }
        }

        #sidebar .nav-link:hover {
            color: var(--primary-color);
            background: var(--primary-soft);
        }

        #sidebar .nav-link.active {
            color: var(--primary-color);
            background: var(--primary-soft);
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <nav id="sidebar">
        <div class="sidebar-brand">
            <i class="fas fa-hard-hat"></i>
            <span class="sidebar-brand-text">OHS System</span>
        </div>

        <div class="sidebar-content">
            <!-- Dashboard Section -->
            <div class="sidebar-heading">Overview</div>
            <ul class="nav flex-column">
                <?php if ($auth->isAdmin()): ?>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/dashboard" class="nav-link <?= $active_page === 'dashboard' ? 'active' : '' ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <?php elseif ($auth->isBusinessOwner()): ?>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>business/dashboard" class="nav-link <?= $active_page === 'dashboard' ? 'active' : '' ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <?php elseif ($auth->isInspector()): ?>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>inspector/dashboard" class="nav-link <?= $active_page === 'dashboard' ? 'active' : '' ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>

            <div class="sidebar-divider"></div>

            <!-- Role-specific Main Functions -->
            <?php if ($auth->isAdmin()): ?>
            <div class="sidebar-heading">User Management</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/users" class="nav-link <?= $active_page === 'users' ? 'active' : '' ?>">
                        <i class="fas fa-users"></i>
                        <span>Manage Users</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/businesses" class="nav-link <?= $active_page === 'businesses' ? 'active' : '' ?>">
                        <i class="fas fa-building"></i>
                        <span>Manage Businesses</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-divider"></div>

            <div class="sidebar-heading">Inspection Management</div>
            <ul class="nav flex-column">

                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/inspections/pending-verification" class="nav-link <?= $active_page === 'inspections' ? 'active' : '' ?>">
                        <i class="fas fa-check-circle text-warning"></i>
                        <span>Pending Verification</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/inspector-assignments/integrated" class="nav-link <?= $active_page === 'inspector_assignments' ? 'active' : '' ?>">
                        <i class="fas fa-calendar-check"></i>
                        <span>Inspection Assignments</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/compliance" class="nav-link <?= $active_page === 'compliance' ? 'active' : '' ?>">
                        <i class="fas fa-chart-line"></i>
                        <span>Inspection Results</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-divider"></div>

            <div class="sidebar-heading">System Settings</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/website-settings" class="nav-link <?= $active_page === 'website-settings' ? 'active' : '' ?>">
                        <i class="fas fa-palette"></i>
                        <span>Website Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/checklist" class="nav-link <?= $active_page === 'checklist' ? 'active' : '' ?>">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Checklist Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>admin/chatbot" class="nav-link <?= $active_page === 'chatbot' ? 'active' : '' ?>">
                        <i class="fas fa-robot"></i>
                        <span>AI Chatbot</span>
                    </a>
                </li>
            </ul>

            <?php elseif ($auth->isInspector()): ?>
            <div class="sidebar-heading">My Work</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>inspector/inspections" class="nav-link <?= in_array($active_page, ['inspections', 'schedule']) ? 'active' : '' ?>">
                        <i class="fas fa-clipboard-list"></i>
                        <span>My Inspections & Schedule</span>
                    </a>
                </li>

            </ul>

            <?php elseif ($auth->isBusinessOwner()): ?>
            <div class="sidebar-heading">My Business</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>business" class="nav-link <?= $active_page === 'businesses' ? 'active' : '' ?>">
                        <i class="fas fa-building"></i>
                        <span>My Businesses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>business/inspections" class="nav-link <?= $active_page === 'inspections' ? 'active' : '' ?>">
                        <i class="fas fa-clipboard-check"></i>
                        <span>Inspections</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-divider"></div>

            <div class="sidebar-heading">Compliance</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="#" class="nav-link <?= $active_page === 'checklist' ? 'active' : '' ?>" id="checklistDropdown" data-bs-toggle="collapse" data-bs-target="#checklistSubmenu" aria-expanded="false">
                        <i class="fas fa-clipboard-check"></i>
                        <span>Inspection Checklist</span>
                        <i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="checklistSubmenu">
                        <ul class="nav flex-column ms-3">
                            <?php
                            $user = $auth->getUser();
                            if ($user && $auth->isBusinessOwner()) {
                                $businessModel = new \App\Models\Business();
                                $userBusinesses = $businessModel->getByOwnerId($user['id']);
                                foreach ($userBusinesses as $business): ?>
                                    <li class="nav-item">
                                        <a href="<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>" class="nav-link">
                                            <i class="fas fa-building me-2"></i>
                                            <span><?= htmlspecialchars($business['name']) ?></span>
                                        </a>
                                    </li>
                                <?php endforeach;
                            } ?>
                        </ul>
                    </div>
                </li>
            </ul>
            <?php endif; ?>

            <div class="sidebar-divider"></div>

            <div class="sidebar-heading">Communication</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>chat" class="nav-link <?= $active_page === 'chat' ? 'active' : '' ?>">
                        <i class="fas fa-comments"></i>
                        <span>Live Chat</span>
                        <span id="chat-badge" class="badge badge-danger badge-counter" style="display: none;"></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?= BASE_URL ?>notifications" class="nav-link <?= $active_page === 'notifications' ? 'active' : '' ?>">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                        <span id="notification-badge" class="badge badge-danger badge-counter" style="display: none;"></span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Header -->
    <header id="header" class="d-flex align-items-center">
        <button id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="flex-grow-1 d-flex align-items-center justify-content-between">
            <h5 class="mb-0 text-primary page-title"></h5>
            <div class="dropdown user-dropdown">
                <button class="btn btn-link dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user-circle me-2"></i>
                    <span><?= ucfirst($auth->getUserRole()) ?> - <?= htmlspecialchars($auth->getUserName()) ?></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">
                    <?php if ($auth->isAdmin()): ?>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>admin/profile"><i class="fas fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>admin/settings"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <?php elseif ($auth->isBusinessOwner()): ?>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>business/profile"><i class="fas fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>business/settings"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <?php elseif ($auth->isInspector()): ?>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>inspector/profile"><i class="fas fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>inspector/settings"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <?php endif; ?>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?= BASE_URL ?>logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main">
        <?= $this->renderSection('content') ?>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const header = document.getElementById('header');
            const main = document.getElementById('main');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            let isMobile = window.innerWidth <= 768;

            // Check for saved state
            const sidebarState = localStorage.getItem('sidebarCollapsed');

            function initializeSidebar() {
                if (isMobile) {
                    sidebar.classList.remove('collapsed');
                    sidebar.classList.remove('show');
                    header.classList.remove('sidebar-collapsed');
                    main.classList.remove('sidebar-collapsed');
                    sidebarOverlay.classList.remove('show');
                } else {
                    if (sidebarState === 'true') {
                        sidebar.classList.add('collapsed');
                        header.classList.add('sidebar-collapsed');
                        main.classList.add('sidebar-collapsed');
                    } else {
                        sidebar.classList.remove('collapsed');
                        header.classList.remove('sidebar-collapsed');
                        main.classList.remove('sidebar-collapsed');
                    }
                }
            }

            function toggleSidebar() {
                if (isMobile) {
                    // Mobile: Show/hide sidebar with overlay
                    sidebar.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');

                    // Prevent body scroll when sidebar is open
                    if (sidebar.classList.contains('show')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                } else {
                    // Desktop: Collapse/expand sidebar
                    sidebar.classList.toggle('collapsed');
                    header.classList.toggle('sidebar-collapsed');
                    main.classList.toggle('sidebar-collapsed');

                    // Save state for desktop
                    localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
                }
            }

            function closeMobileSidebar() {
                if (isMobile) {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }

            // Event listeners
            sidebarToggle.addEventListener('click', toggleSidebar);
            sidebarOverlay.addEventListener('click', closeMobileSidebar);

            // Close sidebar when clicking on nav links in mobile
            const navLinks = sidebar.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    if (isMobile) {
                        setTimeout(closeMobileSidebar, 150);
                    }
                });
            });

            // Handle window resize
            function handleResize() {
                const wasMobile = isMobile;
                isMobile = window.innerWidth <= 768;

                if (wasMobile !== isMobile) {
                    // Mode changed, reinitialize
                    initializeSidebar();
                }
            }

            window.addEventListener('resize', handleResize);
            initializeSidebar(); // Initial setup

            // Set page title
            const pageTitle = document.querySelector('.page-title');
            const activeLink = document.querySelector('.nav-link.active');
            if (activeLink && pageTitle) {
                const spanElement = activeLink.querySelector('span');
                if (spanElement) {
                    pageTitle.textContent = spanElement.textContent;
                }
            }

            // Enhanced touch support for mobile
            if ('ontouchstart' in window) {
                document.body.classList.add('touch-device');
            }

            // Check for unread notifications and chat messages
            function checkUnreadCounts() {
                // Check notifications
                fetch('<?= BASE_URL ?>notifications/count')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.count > 0) {
                            const badge = document.getElementById('notification-badge');
                            if (badge) {
                                badge.textContent = data.count;
                                badge.style.display = 'inline';
                            }
                        } else {
                            const badge = document.getElementById('notification-badge');
                            if (badge) {
                                badge.style.display = 'none';
                            }
                        }
                    })
                    .catch(error => console.error('Error checking notifications:', error));

                // Check chat messages
                fetch('<?= BASE_URL ?>chat/unread-count')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.count > 0) {
                            const badge = document.getElementById('chat-badge');
                            if (badge) {
                                badge.textContent = data.count;
                                badge.style.display = 'inline';
                            }
                        } else {
                            const badge = document.getElementById('chat-badge');
                            if (badge) {
                                badge.style.display = 'none';
                            }
                        }
                    })
                    .catch(error => console.error('Error checking chat messages:', error));
            }

            // Check every 30 seconds
            checkUnreadCounts();
            setInterval(checkUnreadCounts, 30000);

            // Add swipe gesture support for mobile
            if (isMobile) {
                let startX = 0;
                let startY = 0;

                document.addEventListener('touchstart', function(e) {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                });

                document.addEventListener('touchmove', function(e) {
                    if (!startX || !startY) return;

                    const diffX = e.touches[0].clientX - startX;
                    const diffY = e.touches[0].clientY - startY;

                    // Only handle horizontal swipes
                    if (Math.abs(diffX) > Math.abs(diffY)) {
                        // Swipe right to open sidebar
                        if (diffX > 50 && startX < 50 && !sidebar.classList.contains('show')) {
                            toggleSidebar();
                        }
                        // Swipe left to close sidebar
                        else if (diffX < -50 && sidebar.classList.contains('show')) {
                            closeMobileSidebar();
                        }
                    }

                    startX = 0;
                    startY = 0;
                });
            }
        });
    </script>
</body>
</html>
