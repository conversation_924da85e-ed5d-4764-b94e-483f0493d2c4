<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\User;
use App\Models\Business;
use App\Models\Inspection;

class BusinessOwnerProfileController extends Controller {
    protected $auth;
    protected $userModel;
    protected $businessModel;
    protected $inspectionModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->auth->requireAdmin();

        $this->userModel = new User();
        $this->businessModel = new Business();
        $this->inspectionModel = new Inspection();
    }

    public function view($id) {
        $user = $this->userModel->find($id);

        if (!$user || $user['role'] !== 'business_owner') {
            $this->setFlash('error', 'Business owner not found.');
            return $this->redirect('admin/users');
        }

        // Get owned businesses
        $businesses = $this->businessModel->getByOwnerId($id);
        
        // Get business statistics
        $businessStats = $this->getBusinessOwnerStats($id);
        
        // Get recent inspections for all businesses
        $recentInspections = $this->getRecentInspectionsForOwner($id);

        return $this->render('admin/business_owner_profile/view', [
            'title' => 'Business Owner Profile - ' . $user['full_name'],
            'user' => $user,
            'businesses' => $businesses,
            'business_stats' => $businessStats,
            'recent_inspections' => $recentInspections,
            'active_page' => 'users'
        ]);
    }

    private function getBusinessOwnerStats($ownerId) {
        try {
            $db = (new \App\Config\Database())->getConnection();
            $query = "SELECT
                        COUNT(DISTINCT b.id) as total_businesses,
                        COUNT(DISTINCT CASE WHEN b.status = 'active' THEN b.id END) as active_businesses,
                        COUNT(DISTINCT i.id) as total_inspections,
                        COUNT(DISTINCT CASE WHEN i.status = 'completed' THEN i.id END) as completed_inspections,
                        COUNT(DISTINCT CASE WHEN i.status IN ('scheduled', 'confirmed') THEN i.id END) as pending_inspections,
                        COUNT(DISTINCT CASE WHEN b.compliance_status = 'COMPLIANT' THEN b.id END) as compliant_businesses
                      FROM businesses b
                      LEFT JOIN inspections i ON b.id = i.business_id
                      WHERE b.owner_id = ?";

            $stmt = $db->prepare($query);
            $stmt->execute([$ownerId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            return $result ?: [
                'total_businesses' => 0,
                'active_businesses' => 0,
                'total_inspections' => 0,
                'completed_inspections' => 0,
                'pending_inspections' => 0,
                'compliant_businesses' => 0
            ];
        } catch (\Exception $e) {
            error_log("Error getting business owner stats: " . $e->getMessage());
            return [
                'total_businesses' => 0,
                'active_businesses' => 0,
                'total_inspections' => 0,
                'completed_inspections' => 0,
                'pending_inspections' => 0,
                'compliant_businesses' => 0
            ];
        }
    }

    private function getRecentInspectionsForOwner($ownerId) {
        try {
            $db = (new \App\Config\Database())->getConnection();
            $query = "SELECT i.*, b.name as business_name, u.full_name as inspector_name,
                             brg.name as barangay_name, d.name as district_name
                      FROM inspections i
                      LEFT JOIN businesses b ON i.business_id = b.id
                      LEFT JOIN users u ON i.inspector_id = u.id
                      LEFT JOIN barangays brg ON b.barangay_id = brg.id
                      LEFT JOIN districts d ON brg.district_id = d.id
                      WHERE b.owner_id = ?
                      ORDER BY i.scheduled_date DESC
                      LIMIT 10";

            $stmt = $db->prepare($query);
            $stmt->execute([$ownerId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\Exception $e) {
            error_log("Error getting recent inspections for owner: " . $e->getMessage());
            return [];
        }
    }
}
