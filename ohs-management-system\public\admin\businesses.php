<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=ohs_management_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed.');
}

// Create businesses table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS businesses (
        id CHAR(36) PRIMARY KEY,
        owner_id CHAR(36) NOT NULL,
        owner_name VARCHA<PERSON>(255) NOT NULL,
        owner_email VARCHAR(255) NOT NULL,
        name VA<PERSON>HA<PERSON>(255) NOT NULL,
        contact_number VARCHAR(50),
        address TEXT,
        category_id CHAR(36),
        barangay_id CHAR(36),
        employee_count INT DEFAULT 0,
        date_established DATE,
        business_permit BOOLEAN DEFAULT FALSE,
        sanitary_permit BOOLEAN DEFAULT FALSE,
        fire_safety_certificate BOOLEAN DEFAULT FALSE,
        environmental_permit BOOLEAN DEFAULT FALSE,
        safety_officer_count INT DEFAULT 0,
        has_safety_signage BOOLEAN DEFAULT FALSE,
        has_first_aid BOOLEAN DEFAULT FALSE,
        has_fire_extinguishers BOOLEAN DEFAULT FALSE,
        has_cctv BOOLEAN DEFAULT FALSE,
        has_waste_segregation BOOLEAN DEFAULT FALSE,
        status ENUM('pending_approval', 'active', 'inactive', 'suspended') DEFAULT 'pending_approval',
        compliance_status ENUM('compliant', 'non_compliant', 'pending') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES business_categories(id) ON DELETE SET NULL,
        FOREIGN KEY (barangay_id) REFERENCES barangays(id) ON DELETE SET NULL
    )");
} catch (Exception $e) {
    // Table might already exist
}

// Handle status updates
if ($_POST && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status') {
        $stmt = $pdo->prepare("UPDATE businesses SET status = ? WHERE id = ?");
        $stmt->execute([$_POST['status'], $_POST['business_id']]);
        $success = "Business status updated successfully!";
    }
}

// Get businesses with filters
$page = $_GET['page'] ?? 1;
$limit = 15;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$category_filter = $_GET['category'] ?? '';
$barangay_filter = $_GET['barangay'] ?? '';

$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(b.name LIKE ? OR b.owner_name LIKE ? OR b.owner_email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "b.status = ?";
    $params[] = $status_filter;
}

if ($category_filter) {
    $where_conditions[] = "b.category_id = ?";
    $params[] = $category_filter;
}

if ($barangay_filter) {
    $where_conditions[] = "b.barangay_id = ?";
    $params[] = $barangay_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$businesses_query = "
    SELECT b.*, 
           bc.name as category_name,
           br.name as barangay_name,
           d.name as district_name
    FROM businesses b 
    LEFT JOIN business_categories bc ON b.category_id = bc.id
    LEFT JOIN barangays br ON b.barangay_id = br.id
    LEFT JOIN districts d ON br.district_id = d.id
    $where_clause 
    ORDER BY b.created_at DESC 
    LIMIT $limit OFFSET $offset
";

$businesses = $pdo->prepare($businesses_query);
$businesses->execute($params);
$businesses = $businesses->fetchAll(PDO::FETCH_ASSOC);

// Get total count for pagination
$count_query = "SELECT COUNT(*) FROM businesses b $where_clause";
$count_stmt = $pdo->prepare($count_query);
$count_stmt->execute($params);
$total_businesses = $count_stmt->fetchColumn();
$total_pages = ceil($total_businesses / $limit);

// Get filter options
$categories = $pdo->query("SELECT * FROM business_categories ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);
$barangays = $pdo->query("SELECT br.*, d.name as district_name FROM barangays br LEFT JOIN districts d ON br.district_id = d.id ORDER BY d.name, br.name")->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [
    'total' => $pdo->query("SELECT COUNT(*) FROM businesses")->fetchColumn(),
    'active' => $pdo->query("SELECT COUNT(*) FROM businesses WHERE status = 'active'")->fetchColumn(),
    'pending' => $pdo->query("SELECT COUNT(*) FROM businesses WHERE status = 'pending_approval'")->fetchColumn(),
    'compliant' => $pdo->query("SELECT COUNT(*) FROM businesses WHERE compliance_status = 'compliant'")->fetchColumn(),
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Management - OHS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            width: 280px;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover { background: rgba(255,255,255,0.1); color: white; }
        .sidebar .nav-link.active { background: rgba(255,255,255,0.2); color: white; }
        .main-content { margin-left: 280px; padding: 2rem; }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
        }
        .stat-card.total { border-left-color: #007bff; }
        .stat-card.active { border-left-color: #28a745; }
        .stat-card.pending { border-left-color: #ffc107; }
        .stat-card.compliant { border-left-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="p-3">
                    <h4><i class="fas fa-shield-alt me-2"></i>OHS Admin</h4>
                    <hr class="text-white">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link active" href="businesses.php">
                            <i class="fas fa-building me-2"></i>Businesses
                        </a>
                        <a class="nav-link" href="inspections.php">
                            <i class="fas fa-clipboard-check me-2"></i>Inspections
                        </a>
                        <a class="nav-link" href="inspection-checklist.php">
                            <i class="fas fa-list-check me-2"></i>Inspection Checklist
                        </a>
                        <a class="nav-link" href="chat.php">
                            <i class="fas fa-comments me-2"></i>Live Chat
                        </a>
                        <a class="nav-link" href="website-settings.php">
                            <i class="fas fa-cog me-2"></i>Website Settings
                        </a>
                        <hr class="text-white">
                        <a class="nav-link" href="../index.php?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-building me-2"></i>Business Management</h2>
                        <p class="text-muted mb-0">Manage registered businesses and their compliance status</p>
                    </div>
                </div>
                
                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?= $success ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card total">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['total'] ?></h3>
                                    <p class="text-muted mb-0">Total Businesses</p>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card active">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['active'] ?></h3>
                                    <p class="text-muted mb-0">Active</p>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card pending">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['pending'] ?></h3>
                                    <p class="text-muted mb-0">Pending Approval</p>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card compliant">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="mb-0"><?= $stats['compliant'] ?></h3>
                                    <p class="text-muted mb-0">Compliant</p>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-shield-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="search" placeholder="Search businesses..." value="<?= htmlspecialchars($search) ?>">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending_approval" <?= $status_filter === 'pending_approval' ? 'selected' : '' ?>>Pending Approval</option>
                                    <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="suspended" <?= $status_filter === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                    <option value="<?= $cat['id'] ?>" <?= $category_filter === $cat['id'] ? 'selected' : '' ?>><?= htmlspecialchars($cat['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="barangay">
                                    <option value="">All Barangays</option>
                                    <?php foreach ($barangays as $barangay): ?>
                                    <option value="<?= $barangay['id'] ?>" <?= $barangay_filter === $barangay['id'] ? 'selected' : '' ?>><?= htmlspecialchars($barangay['district_name'] . ' - ' . $barangay['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="col-md-1">
                                <a href="businesses.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Businesses Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Business Name</th>
                                        <th>Owner</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Compliance</th>
                                        <th>Registered</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($businesses)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No businesses found.</p>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                        <?php foreach ($businesses as $business): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($business['name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($business['contact_number']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?= htmlspecialchars($business['owner_name']) ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($business['owner_email']) ?></small>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($business['category_name'] ?? 'N/A') ?></td>
                                            <td>
                                                <div>
                                                    <?= htmlspecialchars($business['barangay_name'] ?? 'N/A') ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($business['district_name'] ?? '') ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= 
                                                    $business['status'] === 'active' ? 'success' : 
                                                    ($business['status'] === 'pending_approval' ? 'warning' : 
                                                    ($business['status'] === 'suspended' ? 'danger' : 'secondary')) 
                                                ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $business['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= 
                                                    $business['compliance_status'] === 'compliant' ? 'success' : 
                                                    ($business['compliance_status'] === 'non_compliant' ? 'danger' : 'warning') 
                                                ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $business['compliance_status'])) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($business['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="updateStatus('<?= $business['id'] ?>', 'active')" title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning" onclick="updateStatus('<?= $business['id'] ?>', 'suspended')" title="Suspend">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="updateStatus('<?= $business['id'] ?>', 'inactive')" title="Deactivate">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&category=<?= urlencode($category_filter) ?>&barangay=<?= urlencode($barangay_filter) ?>"><?= $i ?></a>
                                </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hidden form for status updates -->
    <form method="POST" id="statusForm" style="display: none;">
        <input type="hidden" name="action" value="update_status">
        <input type="hidden" name="business_id" id="statusBusinessId">
        <input type="hidden" name="status" id="statusValue">
    </form>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateStatus(businessId, status) {
            const statusText = status.replace('_', ' ');
            if (confirm(`Are you sure you want to ${statusText} this business?`)) {
                document.getElementById('statusBusinessId').value = businessId;
                document.getElementById('statusValue').value = status;
                document.getElementById('statusForm').submit();
            }
        }
    </script>
</body>
</html>
