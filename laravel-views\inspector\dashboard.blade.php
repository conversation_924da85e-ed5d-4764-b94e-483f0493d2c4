@extends('layouts.inspector')

@section('title', 'Inspector Dashboard')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Inspector Dashboard</h1>
    <div class="d-none d-sm-inline-block">
        <span class="text-gray-600">Welcome back, {{ Auth::user()->full_name }}!</span>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Total Inspections Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Inspections
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_inspections'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Inspections Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Completed Inspections
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completed_inspections'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduled Inspections Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Scheduled Inspections
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['scheduled_inspections'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Assigned Businesses Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Assigned Businesses
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['assigned_businesses'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Upcoming Inspections -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Upcoming Inspections</h6>
                <a href="{{ route('inspector.inspections.index') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if($upcomingInspections->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Business</th>
                                    <th>Location</th>
                                    <th>Type</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($upcomingInspections as $inspection)
                                    <tr>
                                        <td>
                                            <div class="font-weight-bold">{{ $inspection->scheduled_date->format('M j, Y') }}</div>
                                            <div class="small text-gray-500">{{ $inspection->scheduled_date->format('g:i A') }}</div>
                                        </td>
                                        <td>
                                            <div class="font-weight-bold">{{ $inspection->business->name }}</div>
                                            <div class="small text-gray-500">{{ $inspection->business->owner_name }}</div>
                                        </td>
                                        <td>
                                            <div>{{ $inspection->business->barangay->name }}</div>
                                            <div class="small text-gray-500">{{ $inspection->business->barangay->district->name }}</div>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ ucfirst($inspection->inspection_type) }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('inspector.inspections.show', $inspection) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-gray-500">No upcoming inspections</h5>
                        <p class="text-gray-400">You have no scheduled inspections at this time.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Stats & Performance -->
    <div class="col-lg-4 mb-4">
        <!-- Performance Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Performance Overview</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completion Rate</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['completion_rate'] }}%</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Score</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['average_score'] }}</div>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Today</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['today_inspections'] }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">This Week</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['this_week_inspections'] }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assignment Info -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Assignment Info</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Districts</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['assigned_districts'] }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Barangays</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['assigned_barangays'] }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Inspections -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Completed Inspections</h6>
            </div>
            <div class="card-body">
                @if($recentInspections->count() > 0)
                    @foreach($recentInspections as $inspection)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-success">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small text-gray-500">{{ $inspection->completed_date->format('M j, Y') }}</div>
                                <div class="font-weight-bold">{{ $inspection->business->name }}</div>
                                <div class="small">
                                    Score: {{ $inspection->getScorePercentage() ?? 'N/A' }}% | 
                                    Rating: {{ $inspection->compliance_rating ?? 'N/A' }}
                                </div>
                            </div>
                            <div>
                                <a href="{{ route('inspector.inspections.report', $inspection) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-file-alt"></i>
                                </a>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center text-gray-500">
                        <i class="fas fa-clipboard-check fa-3x mb-3"></i>
                        <p>No completed inspections yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Assigned Businesses -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Assigned Businesses</h6>
            </div>
            <div class="card-body">
                @if($assignedBusinesses->count() > 0)
                    @foreach($assignedBusinesses->take(5) as $business)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-{{ $business->compliance_status === 'compliant' ? 'success' : ($business->compliance_status === 'pending_review' ? 'warning' : 'danger') }}">
                                    <i class="fas fa-building text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">{{ $business->name }}</div>
                                <div class="small text-gray-500">
                                    {{ $business->barangay->name }}, {{ $business->barangay->district->name }}
                                </div>
                                <div class="small">{{ $business->category->name }}</div>
                            </div>
                            <div>
                                <span class="badge badge-{{ $business->compliance_status === 'compliant' ? 'success' : ($business->compliance_status === 'pending_review' ? 'warning' : 'danger') }}">
                                    {{ ucfirst(str_replace('_', ' ', $business->compliance_status)) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                    @if($assignedBusinesses->count() > 5)
                        <div class="text-center">
                            <a href="{{ route('inspector.businesses.index') }}" class="btn btn-sm btn-primary">
                                View All ({{ $assignedBusinesses->count() }})
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center text-gray-500">
                        <i class="fas fa-building fa-3x mb-3"></i>
                        <p>No businesses assigned yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// Auto-refresh dashboard data every 5 minutes
setInterval(function() {
    // You can implement AJAX refresh here if needed
}, 300000);
</script>
@endpush
