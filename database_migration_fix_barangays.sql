-- Migration script to fix the district/barangay structure
-- This script should be run to update existing databases

-- First, create the barangays table if it doesn't exist
CREATE TABLE IF NOT EXISTS barangays (
    id VARCHAR(36) PRIMARY KEY,
    district_id VARCHAR(36) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    code VA<PERSON>HAR(50) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE
);

-- Create inspector_barangay_assignments table if it doesn't exist
CREATE TABLE IF NOT EXISTS inspector_barangay_assignments (
    id VARCHAR(36) PRIMARY KEY,
    inspector_id VARCHAR(36) NOT NULL,
    barangay_id VARCHAR(36) NOT NULL,
    assigned_by VARCHAR(36) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    FOR<PERSON><PERSON><PERSON> KEY (inspector_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (barangay_id) REFERENCES barangays(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_inspector_barangay (inspector_id, barangay_id)
);

-- Clear existing district data (which contains barangays)
DELETE FROM districts;

-- Insert proper districts
INSERT INTO districts (id, name) VALUES
('district-1', 'District 1 - Bacoor West'),
('district-2', 'District 2 - Bacoor East');

-- Insert all barangays with proper district relationships
INSERT INTO barangays (id, district_id, name) VALUES
-- 1st Legislative District of Bacoor (District 1 - Bacoor West)
(UUID(), 'district-1', 'Bayan (Poblacion)'),
(UUID(), 'district-1', 'San Nicolas'),
(UUID(), 'district-1', 'Panapaan I'),
(UUID(), 'district-1', 'Panapaan II'),
(UUID(), 'district-1', 'Panapaan III'),
(UUID(), 'district-1', 'Panapaan IV'),
(UUID(), 'district-1', 'Panapaan V'),
(UUID(), 'district-1', 'Panapaan VI'),
(UUID(), 'district-1', 'Panapaan VII'),
(UUID(), 'district-1', 'Panapaan VIII'),
(UUID(), 'district-1', 'Alima'),
(UUID(), 'district-1', 'Aniban I'),
(UUID(), 'district-1', 'Aniban II'),
(UUID(), 'district-1', 'Aniban III'),
(UUID(), 'district-1', 'Aniban IV'),
(UUID(), 'district-1', 'Aniban V'),
(UUID(), 'district-1', 'Banalo'),
(UUID(), 'district-1', 'Camposanto'),
(UUID(), 'district-1', 'Daang Bukid'),
(UUID(), 'district-1', 'Digman'),
(UUID(), 'district-1', 'Dulong Bayan'),
(UUID(), 'district-1', 'Habay I'),
(UUID(), 'district-1', 'Habay II'),
(UUID(), 'district-1', 'Kaingin'),
(UUID(), 'district-1', 'Ligas I'),
(UUID(), 'district-1', 'Ligas II'),
(UUID(), 'district-1', 'Ligas III'),
(UUID(), 'district-1', 'Maliksi I'),
(UUID(), 'district-1', 'Maliksi II'),
(UUID(), 'district-1', 'Maliksi III'),
(UUID(), 'district-1', 'Mambog I'),
(UUID(), 'district-1', 'Mambog II'),
(UUID(), 'district-1', 'Mambog III'),
(UUID(), 'district-1', 'Mambog IV'),
(UUID(), 'district-1', 'Mambog V'),
(UUID(), 'district-1', 'Molino I'),
(UUID(), 'district-1', 'Molino II'),
(UUID(), 'district-1', 'Molino III'),
(UUID(), 'district-1', 'Molino IV'),
(UUID(), 'district-1', 'Molino V'),
(UUID(), 'district-1', 'Molino VI'),
(UUID(), 'district-1', 'Molino VII'),
(UUID(), 'district-1', 'Niog I'),
(UUID(), 'district-1', 'Niog II'),
(UUID(), 'district-1', 'Niog III'),
(UUID(), 'district-1', 'P.F. Espiritu I (Poblacion)'),
(UUID(), 'district-1', 'P.F. Espiritu II (Poblacion)'),
(UUID(), 'district-1', 'Queens Row Central'),
(UUID(), 'district-1', 'Queens Row East'),
(UUID(), 'district-1', 'Queens Row West'),
(UUID(), 'district-1', 'Real I'),
(UUID(), 'district-1', 'Real II'),
(UUID(), 'district-1', 'Salinas I'),
(UUID(), 'district-1', 'Salinas II'),
(UUID(), 'district-1', 'Salinas III'),
(UUID(), 'district-1', 'Salinas IV'),
(UUID(), 'district-1', 'San Antonio I'),
(UUID(), 'district-1', 'San Antonio II'),
(UUID(), 'district-1', 'Talaba I'),
(UUID(), 'district-1', 'Talaba II'),
(UUID(), 'district-1', 'Talaba III'),
(UUID(), 'district-1', 'Talaba IV'),
(UUID(), 'district-1', 'Talaba V'),
(UUID(), 'district-1', 'Talaba VI'),
(UUID(), 'district-1', 'Talaba VII'),
(UUID(), 'district-1', 'Zapote I'),
(UUID(), 'district-1', 'Zapote II'),
(UUID(), 'district-1', 'Zapote III'),
(UUID(), 'district-1', 'Zapote IV'),
(UUID(), 'district-1', 'Zapote V'),
-- 2nd Legislative District of Bacoor (District 2 - Bacoor East)
(UUID(), 'district-2', 'Springville'),
(UUID(), 'district-2', 'City Heights'),
(UUID(), 'district-2', 'Grand Royale');

-- Check if businesses table has district_id column and needs to be updated
-- If businesses table exists with district_id, we need to add barangay_id column
-- and migrate data (this would need to be done carefully in production)

-- Add barangay_id column to businesses table if it doesn't exist
ALTER TABLE businesses ADD COLUMN IF NOT EXISTS barangay_id VARCHAR(36);

-- If there are existing businesses with district_id, you would need to:
-- 1. Map them to appropriate barangays
-- 2. Update the barangay_id values
-- 3. Drop the district_id column
-- For now, we'll just ensure the structure is correct for new installations

-- Drop the old foreign key constraint if it exists
-- ALTER TABLE businesses DROP FOREIGN KEY IF EXISTS businesses_ibfk_3;

-- Add the new foreign key constraint for barangay_id
-- ALTER TABLE businesses ADD CONSTRAINT fk_businesses_barangay 
-- FOREIGN KEY (barangay_id) REFERENCES barangays(id);

-- Note: In production, you would need to handle existing data migration carefully
-- This script assumes a fresh installation or that existing data can be cleared
