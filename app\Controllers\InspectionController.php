<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Inspection;
use App\Models\Business;
use App\Models\User;
use App\Models\Notification;
use App\Models\InspectorAssignment;
use App\Models\InspectionChecklist;
use App\Libraries\View;
use App\Middleware\AuthMiddleware;
use App\Middleware\AdminMiddleware;

class InspectionController extends Controller {
    protected $auth;
    private $inspectionModel;
    private $businessModel;
    private $userModel;
    private $notificationModel;
    private $assignmentModel;
    private $checklistModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();

        $this->inspectionModel = new Inspection();
        $this->businessModel = new Business();
        $this->userModel = new User();
        $this->notificationModel = new Notification();
        $this->assignmentModel = new InspectorAssignment();
        $this->checklistModel = new InspectionChecklist();
    }

    /**
     * @deprecated Admin inspection list removed. Use Inspection Results dashboard instead.
     */
    public function index($businessId = null) {
        $_SESSION['error'] = 'Inspection management has been moved to the Inspection Results dashboard.';
        $this->redirect('admin/compliance');
        return;
        if ($businessId) {
            $business = $this->businessModel->getById($businessId);
            
            if (!$business) {
                $_SESSION['error'] = 'Business not found.';
                $this->redirect('admin/inspections');
                return;
            }

            $inspections = $this->inspectionModel->getByBusiness($businessId);
            return $this->render('admin/inspections/index', [
                'title' => $business['name'] . ' - Inspections',
                'business' => $business,
                'inspections' => $inspections
            ]);
        } else {
            $inspections = $this->inspectionModel->getAllWithDetails();

            // Add checklist information to each inspection
            foreach ($inspections as &$inspection) {
                $inspection['checklist_completion'] = $this->checklistModel->getInspectionCompletionStatus($inspection['id']);
                $inspection['checklist_score'] = null;

                if ($inspection['checklist_completion']['completed_items'] > 0) {
                    $inspection['checklist_score'] = $this->checklistModel->calculateInspectionScore($inspection['id']);
                }
            }

            return $this->render('admin/inspections/index', [
                'title' => 'Manage Inspections',
                'inspections' => $inspections,
                'user' => $this->auth->getUser()
            ]);
        }
    }



    /**
     * @deprecated Edit functionality removed to prevent conflicts with inspector workflow.
     * Once inspections are assigned to inspectors, they should not be edited by admin
     * to maintain data integrity and avoid workflow conflicts.
     */
    public function edit($id) {
        $_SESSION['error'] = 'Inspection editing has been disabled to prevent conflicts with inspector workflow. Use the inspection assignment system to manage inspections.';
        $this->redirect('admin/inspections');
        return;
    }

    public function delete($id) {
        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspections');
            return;
        }

        $inspection = $this->inspectionModel->getById($id);
        
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspections');
            return;
        }

        try {
            if ($this->inspectionModel->delete($id)) {
                $_SESSION['success'] = 'Inspection deleted successfully.';
            } else {
                $_SESSION['error'] = 'Failed to delete inspection.';
            }
        } catch (\Exception $e) {
            error_log("Error in InspectionController::delete: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred while deleting the inspection.';
        }

        $this->redirect('admin/inspections');
    }

    public function view($id) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $inspection = $this->inspectionModel->getById($id);

        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspections');
            return;
        }

        $business = $this->businessModel->getById($inspection['business_id']);
        $inspector = $this->userModel->find($inspection['inspector_id']);

        if (!$business || !$inspector) {
            $_SESSION['error'] = 'Business or inspector not found.';
            $this->redirect('admin/inspections');
            return;
        }

        // Get checklist information
        $completionStatus = $this->checklistModel->getInspectionCompletionStatus($id);
        $inspectionScore = null;

        if ($completionStatus['completed_items'] > 0) {
            $inspectionScore = $this->checklistModel->calculateInspectionScore($id);
        }

        return $this->render('admin/inspections/view', [
            'title' => 'View Inspection',
            'active_page' => 'inspections',
            'inspection' => $inspection,
            'business' => $business,
            'inspector' => $inspector,
            'completion_status' => $completionStatus,
            'inspection_score' => $inspectionScore,
            'user' => $this->auth->getUser()
        ]);
    }

    public function assignInspectors($inspectionId) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $_SESSION['error'] = 'Invalid request method.';
            $this->redirect('admin/inspections');
            return;
        }

        $inspection = $this->inspectionModel->getById($inspectionId);
        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspections');
            return;
        }

        $inspectorIds = $_POST['inspector_ids'] ?? [];
        if (empty($inspectorIds)) {
            $_SESSION['error'] = 'Please select at least one inspector.';
            $this->redirect('admin/inspections/view/' . $inspectionId);
            return;
        }

        // Ensure inspectorIds is an array
        if (!is_array($inspectorIds)) {
            $inspectorIds = [$inspectorIds];
        }

        $assignedBy = $this->auth->getUserId();
        $business = $this->businessModel->getById($inspection['business_id']);

        try {
            // Remove existing assignments
            $this->inspectionModel->removeAssignments($inspectionId);

            // Create new assignments
            foreach ($inspectorIds as $index => $inspectorId) {
                $assignmentData = [
                    'inspection_id' => $inspectionId,
                    'inspector_id' => $inspectorId,
                    'assigned_by' => $assignedBy,
                    'role' => $index === 0 ? 'primary' : 'secondary',
                    'status' => 'assigned'
                ];

                $this->inspectionModel->createAssignment($assignmentData);

                // Create notification for inspector
                $this->notificationModel->createInspectionNotification(
                    $inspectionId,
                    $inspectorId,
                    $business['name'],
                    $inspection['scheduled_date']
                );
            }

            // Update primary inspector in inspection record
            $this->inspectionModel->update($inspectionId, ['inspector_id' => $inspectorIds[0]]);

            $_SESSION['success'] = 'Inspectors assigned successfully.';
        } catch (\Exception $e) {
            error_log("Error assigning inspectors: " . $e->getMessage());
            $_SESSION['error'] = 'Failed to assign inspectors.';
        }

        $this->redirect('admin/inspections/view/' . $inspectionId);
    }

    /**
     * Admin inspection report view
     */
    public function adminInspectionReport($id) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $inspection = $this->inspectionModel->getById($id);

        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspections');
            return;
        }

        // Get checklist responses and score
        $checklistResponses = $this->checklistModel->getInspectionChecklistResponses($id);
        $inspectionScore = $this->checklistModel->calculateInspectionScore($id);
        $completionStatus = $this->checklistModel->getInspectionCompletionStatus($id);

        // Group responses by category
        $responsesByCategory = [];
        foreach ($checklistResponses as $response) {
            $categoryName = $response['category_name'];
            if (!isset($responsesByCategory[$categoryName])) {
                $responsesByCategory[$categoryName] = [];
            }
            $responsesByCategory[$categoryName][] = $response;
        }

        return $this->render('admin/inspections/report', [
            'title' => 'Inspection Report - ' . $inspection['business_name'],
            'active_page' => 'inspections',
            'inspection' => $inspection,
            'checklist_responses' => $checklistResponses,
            'responses_by_category' => $responsesByCategory,
            'inspection_score' => $inspectionScore,
            'completion_status' => $completionStatus,
            'user' => $this->auth->getUser()
        ]);
    }



    /**
     * Get inspector workload for better assignment decisions
     */
    private function getInspectorWorkload($inspectorId) {
        return $this->assignmentModel->getInspectorWorkload($inspectorId);
    }



    /**
     * Show pending inspections that need admin verification
     */
    public function pendingVerification() {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $pendingInspections = $this->inspectionModel->getUnverifiedInspections();

        return $this->render('admin/inspections/pending_verification', [
            'title' => 'Pending Verification',
            'active_page' => 'inspections',
            'pending_inspections' => $pendingInspections,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Show inspection verification form
     */
    public function showVerifyInspection($id) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        $inspection = $this->inspectionModel->getById($id);

        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspections/pending-verification');
            return;
        }

        if ($inspection['status'] !== 'completed') {
            $_SESSION['error'] = 'Only completed inspections can be verified.';
            $this->redirect('admin/inspections/pending-verification');
            return;
        }

        $business = $this->businessModel->getById($inspection['business_id']);

        return $this->render('admin/inspections/verify', [
            'title' => 'Verify Inspection',
            'active_page' => 'inspections',
            'inspection' => $inspection,
            'business' => $business,
            'user' => $this->auth->getUser()
        ]);
    }

    /**
     * Process inspection verification
     */
    public function verifyInspection($id) {
        $this->auth->requireLogin();
        $this->auth->requireAdmin();

        if (!$this->isPost()) {
            $this->redirect('admin/inspections/verify/' . $id);
            return;
        }

        $inspection = $this->inspectionModel->getById($id);

        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('admin/inspections/pending-verification');
            return;
        }

        $verificationStatus = $_POST['verification_status'] ?? 'approved';
        $verificationNotes = $_POST['verification_notes'] ?? '';
        $verifierId = $this->auth->getUserId();

        // Validate verification status
        if (!in_array($verificationStatus, ['approved', 'rejected'])) {
            $_SESSION['error'] = 'Invalid verification status.';
            $this->redirect('admin/inspections/verify/' . $id);
            return;
        }

        if ($this->inspectionModel->verifyInspection($id, $verifierId, $verificationStatus, $verificationNotes)) {
            $statusText = ($verificationStatus === 'approved') ? 'approved' : 'rejected';
            $_SESSION['success'] = "Inspection has been {$statusText} successfully.";
            $this->redirect('admin/inspections/pending-verification');
        } else {
            $_SESSION['error'] = 'Failed to process verification.';
            $this->redirect('admin/inspections/verify/' . $id);
        }
    }
}