<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\InspectionChecklistCategory;
use App\Models\InspectionChecklistItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ChecklistController extends Controller
{
    /**
     * Display checklist management dashboard
     */
    public function index()
    {
        $categories = InspectionChecklistCategory::withCount('activeChecklistItems')
            ->ordered()
            ->get();

        $stats = [
            'total_categories' => InspectionChecklistCategory::count(),
            'active_categories' => InspectionChecklistCategory::where('is_active', true)->count(),
            'total_items' => InspectionChecklistItem::count(),
            'active_items' => InspectionChecklistItem::where('is_active', true)->count(),
            'critical_items' => InspectionChecklistItem::where('is_critical', true)->where('is_active', true)->count(),
        ];

        return view('admin.checklist.index', compact('categories', 'stats'));
    }

    /**
     * Display category management
     */
    public function categories()
    {
        $categories = InspectionChecklistCategory::withCount('checklistItems')
            ->ordered()
            ->get();

        return view('admin.checklist.categories', compact('categories'));
    }

    /**
     * Store new category
     */
    public function storeCategory(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:inspection_checklist_categories,name',
            'description' => 'nullable|string|max:1000',
            'weight' => 'required|numeric|min:0|max:100',
            'sort_order' => 'required|integer|min:0',
        ]);

        InspectionChecklistCategory::create($validated);

        return redirect()->route('admin.checklist.categories')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Update category
     */
    public function updateCategory(Request $request, InspectionChecklistCategory $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:inspection_checklist_categories,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'weight' => 'required|numeric|min:0|max:100',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category->update($validated);

        return redirect()->route('admin.checklist.categories')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Delete category
     */
    public function destroyCategory(InspectionChecklistCategory $category)
    {
        // Check if category has items
        if ($category->checklistItems()->count() > 0) {
            return back()->with('error', 'Cannot delete category with existing checklist items. Please move or delete items first.');
        }

        $category->delete();

        return redirect()->route('admin.checklist.categories')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Display checklist items for a category
     */
    public function items(InspectionChecklistCategory $category = null)
    {
        $query = InspectionChecklistItem::with('category');

        if ($category) {
            $query->where('category_id', $category->id);
        }

        $items = $query->ordered()->paginate(20);
        $categories = InspectionChecklistCategory::active()->ordered()->get();

        return view('admin.checklist.items', compact('items', 'categories', 'category'));
    }

    /**
     * Show form to create new checklist item
     */
    public function createItem(InspectionChecklistCategory $category = null)
    {
        $categories = InspectionChecklistCategory::active()->ordered()->get();

        return view('admin.checklist.create-item', compact('categories', 'category'));
    }

    /**
     * Store new checklist item
     */
    public function storeItem(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:inspection_checklist_categories,id',
            'item_code' => 'required|string|max:50|unique:inspection_checklist_items,item_code',
            'item_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'compliance_requirement' => 'nullable|string|max:2000',
            'points' => 'required|integer|min:1|max:100',
            'is_critical' => 'boolean',
            'sort_order' => 'required|integer|min:0',
        ]);

        InspectionChecklistItem::create($validated);

        return redirect()->route('admin.checklist.items')
            ->with('success', 'Checklist item created successfully.');
    }

    /**
     * Show form to edit checklist item
     */
    public function editItem(InspectionChecklistItem $item)
    {
        $categories = InspectionChecklistCategory::active()->ordered()->get();

        return view('admin.checklist.edit-item', compact('item', 'categories'));
    }

    /**
     * Update checklist item
     */
    public function updateItem(Request $request, InspectionChecklistItem $item)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:inspection_checklist_categories,id',
            'item_code' => 'required|string|max:50|unique:inspection_checklist_items,item_code,' . $item->id,
            'item_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'compliance_requirement' => 'nullable|string|max:2000',
            'points' => 'required|integer|min:1|max:100',
            'is_critical' => 'boolean',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $item->update($validated);

        return redirect()->route('admin.checklist.items')
            ->with('success', 'Checklist item updated successfully.');
    }

    /**
     * Delete checklist item
     */
    public function destroyItem(InspectionChecklistItem $item)
    {
        // Check if item has responses
        if ($item->checklistResponses()->count() > 0) {
            return back()->with('error', 'Cannot delete checklist item with existing inspection responses. Consider deactivating instead.');
        }

        $item->delete();

        return redirect()->route('admin.checklist.items')
            ->with('success', 'Checklist item deleted successfully.');
    }

    /**
     * Bulk update checklist items
     */
    public function bulkUpdateItems(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete,update_category',
            'item_ids' => 'required|array',
            'item_ids.*' => 'exists:inspection_checklist_items,id',
            'new_category_id' => 'nullable|exists:inspection_checklist_categories,id',
        ]);

        $items = InspectionChecklistItem::whereIn('id', $validated['item_ids']);

        switch ($validated['action']) {
            case 'activate':
                $items->update(['is_active' => true]);
                $message = 'Selected items activated successfully.';
                break;

            case 'deactivate':
                $items->update(['is_active' => false]);
                $message = 'Selected items deactivated successfully.';
                break;

            case 'delete':
                // Check if any items have responses
                $itemsWithResponses = $items->whereHas('checklistResponses')->count();
                if ($itemsWithResponses > 0) {
                    return back()->with('error', 'Cannot delete items with existing inspection responses.');
                }
                $items->delete();
                $message = 'Selected items deleted successfully.';
                break;

            case 'update_category':
                if (!$validated['new_category_id']) {
                    return back()->with('error', 'Please select a category.');
                }
                $items->update(['category_id' => $validated['new_category_id']]);
                $message = 'Selected items moved to new category successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Import checklist items from Excel
     */
    public function importItems(Request $request)
    {
        $request->validate([
            'import_file' => 'required|file|mimes:xlsx,xls,csv|max:2048'
        ]);

        try {
            // This would implement Excel import functionality
            // You can use Laravel Excel package for this
            
            return back()->with('success', 'Checklist items imported successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Export checklist items to Excel
     */
    public function exportItems(Request $request)
    {
        $categoryId = $request->get('category_id');
        
        $query = InspectionChecklistItem::with('category');
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        
        $items = $query->ordered()->get();

        // Prepare export data
        $exportData = $items->map(function ($item) {
            return [
                'Category' => $item->category->name,
                'Item Code' => $item->item_code,
                'Item Name' => $item->item_name,
                'Description' => $item->description,
                'Compliance Requirement' => $item->compliance_requirement,
                'Points' => $item->points,
                'Is Critical' => $item->is_critical ? 'Yes' : 'No',
                'Sort Order' => $item->sort_order,
                'Status' => $item->is_active ? 'Active' : 'Inactive',
            ];
        })->toArray();

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'filename' => 'checklist_items_' . now()->format('Y-m-d') . '.xlsx'
        ]);
    }

    /**
     * Reorder checklist items
     */
    public function reorderItems(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:inspection_checklist_items,id',
            'items.*.sort_order' => 'required|integer|min:0',
        ]);

        DB::transaction(function () use ($validated) {
            foreach ($validated['items'] as $itemData) {
                InspectionChecklistItem::where('id', $itemData['id'])
                    ->update(['sort_order' => $itemData['sort_order']]);
            }
        });

        return response()->json(['success' => true, 'message' => 'Items reordered successfully.']);
    }

    /**
     * Preview checklist for testing
     */
    public function preview()
    {
        $categories = InspectionChecklistCategory::with(['activeChecklistItems' => function($query) {
            $query->ordered();
        }])->active()->ordered()->get();

        $stats = [
            'total_items' => InspectionChecklistItem::active()->count(),
            'total_points' => InspectionChecklistItem::active()->sum('points'),
            'critical_items' => InspectionChecklistItem::active()->where('is_critical', true)->count(),
        ];

        return view('admin.checklist.preview', compact('categories', 'stats'));
    }
}
