<?php
namespace App\Controllers;

use App\Libraries\Auth;
use App\Libraries\View;
use App\Libraries\EmailService;
use App\Models\User;
use App\Core\Controller;

class UserController extends Controller {
    protected $auth;
    protected $userModel;
    protected $emailService;
    private $validRoles = ['admin', 'inspector', 'business_owner'];

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->auth->requireAdmin();

        $this->userModel = new User();
        $this->emailService = new EmailService();
    }

    public function index() {
        $users = $this->userModel->all();
        return $this->render('admin/users/index', [
            'title' => 'Manage Users',
            'users' => $users,
            'active_page' => 'users'
        ]);
    }

    public function create() {
        return $this->render('admin/users/create', [
            'title' => 'Create User',
            'active_page' => 'users'
        ]);
    }

    public function store() {
        if (!$this->isPost()) {
            return $this->redirect('admin/users');
        }

        $sendEmail = isset($_POST['send_email']) && $_POST['send_email'] === '1';
        $generatePassword = isset($_POST['generate_password']) && $_POST['generate_password'] === '1';

        $data = [
            'email' => $_POST['email'] ?? '',
            'password' => $_POST['password'] ?? '',
            'full_name' => $_POST['full_name'] ?? '',
            'role' => $_POST['role'] ?? '',
            'status' => 'active'
        ];

        // Generate random password if requested
        $plainPassword = '';
        if ($generatePassword) {
            $plainPassword = $this->emailService->generateRandomPassword();
            $data['password'] = $plainPassword;
        }

        // Validate required fields (password not required if generating)
        foreach ($data as $key => $value) {
            if (empty($value) && !($key === 'password' && $generatePassword)) {
                $this->setFlash('error', ucfirst($key) . ' is required.');
                return $this->redirect('admin/users/create');
            }
        }

        // Validate role
        if (!in_array($data['role'], $this->validRoles)) {
            $this->setFlash('error', 'Invalid role selected.');
            return $this->redirect('admin/users/create');
        }

        // Validate email
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->setFlash('error', 'Please enter a valid email address.');
            return $this->redirect('admin/users/create');
        }

        // Check if email already exists
        if ($this->userModel->findByEmail($data['email'])) {
            $this->setFlash('error', 'Email address is already taken.');
            return $this->redirect('admin/users/create');
        }

        // Store plain password for email before hashing
        if (!$generatePassword) {
            $plainPassword = $data['password'];
        }

        // Hash password
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

        try {
            if ($this->userModel->create($data)) {
                $successMessage = 'User created successfully.';

                // Send welcome email if requested
                if ($sendEmail && !empty($plainPassword)) {
                    $emailSent = $this->emailService->sendWelcomeEmail(
                        $data['email'],
                        $data['full_name'],
                        $plainPassword,
                        $data['role']
                    );

                    if ($emailSent) {
                        $successMessage .= ' Welcome email sent successfully.';
                    } else {
                        $successMessage .= ' However, failed to send welcome email.';
                    }
                }

                $this->setFlash('success', $successMessage);
                return $this->redirect('admin/users');
            } else {
                $this->setFlash('error', 'Failed to create user.');
                return $this->redirect('admin/users/create');
            }
        } catch (\Exception $e) {
            error_log("Error in UserController::store: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while creating the user.');
            return $this->redirect('admin/users/create');
        }
    }

    public function view($id) {
        $user = $this->userModel->find($id);

        if (!$user) {
            $this->setFlash('error', 'User not found.');
            return $this->redirect('admin/users');
        }

        $data = [
            'title' => 'User Details - ' . $user['full_name'],
            'user' => $user,
            'active_page' => 'users'
        ];

        // Get role-specific data
        if ($user['role'] === 'inspector') {
            // Get inspector barangay assignments and workload
            $assignmentModel = new \App\Models\InspectorAssignment();
            $inspectionModel = new \App\Models\Inspection();

            // Get barangay assignments (new system)
            $barangays = $this->getInspectorBarangays($id);

            // Get district assignments (legacy system for backward compatibility)
            $districts = $assignmentModel->getInspectorDistricts($id);

            // Get workload statistics
            $workload = $this->getInspectorWorkloadStats($id);

            // Get recent inspections
            $recentInspections = $inspectionModel->getByInspector($id);

            $data['barangays'] = $barangays;
            $data['districts'] = $districts; // Keep for backward compatibility
            $data['workload'] = $workload;
            $data['recent_inspections'] = array_slice($recentInspections, 0, 10); // Last 10 inspections

        } elseif ($user['role'] === 'business_owner') {
            // Get owned businesses
            $businessModel = new \App\Models\Business();
            $businesses = $businessModel->getByOwnerId($id);

            $data['businesses'] = $businesses;
        }

        return $this->render('admin/users/view', $data);
    }

    public function edit($id) {
        $user = $this->userModel->find($id);

        if (!$user) {
            $this->setFlash('error', 'User not found.');
            return $this->redirect('admin/users');
        }

        return $this->render('admin/users/edit', [
            'title' => 'Edit User',
            'user' => $user,
            'active_page' => 'users'
        ]);
    }

    public function update($id) {
        if (!$this->isPost()) {
            return $this->redirect('admin/users');
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            $this->setFlash('error', 'User not found.');
            return $this->redirect('admin/users');
        }

        $data = [
            'email' => $_POST['email'] ?? $user['email'],
            'full_name' => $_POST['full_name'] ?? $user['full_name'],
            'role' => $_POST['role'] ?? $user['role'],
            'status' => $_POST['status'] ?? $user['status']
        ];

        // Validate required fields
        foreach ($data as $key => $value) {
            if (empty($value)) {
                $this->setFlash('error', ucfirst($key) . ' is required.');
                return $this->redirect("admin/users/{$id}/edit");
            }
        }

        // Validate role
        if (!in_array($data['role'], $this->validRoles)) {
            $this->setFlash('error', 'Invalid role selected.');
            return $this->redirect("admin/users/{$id}/edit");
        }

        // Validate email
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->setFlash('error', 'Please enter a valid email address.');
            return $this->redirect("admin/users/{$id}/edit");
        }

        // Check if email already exists (excluding current user)
        $existingUser = $this->userModel->findByEmail($data['email']);
        if ($existingUser && $existingUser['id'] !== $id) {
            $this->setFlash('error', 'Email address is already taken.');
            return $this->redirect("admin/users/{$id}/edit");
        }

        // Update password if provided
        if (!empty($_POST['password'])) {
            $data['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
        }

        try {
            if ($this->userModel->update($id, $data)) {
                $this->setFlash('success', 'User updated successfully.');
                return $this->redirect('admin/users');
            } else {
                $this->setFlash('error', 'Failed to update user.');
                return $this->redirect("admin/users/{$id}/edit");
            }
        } catch (\Exception $e) {
            error_log("Error in UserController::update: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while updating the user.');
            return $this->redirect("admin/users/{$id}/edit");
        }
    }

    public function delete($id) {
        if (!$this->isPost()) {
            $this->setFlash('error', 'Invalid request method.');
            return $this->redirect('admin/users');
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            $this->setFlash('error', 'User not found.');
            return $this->redirect('admin/users');
        }

        // Prevent self-deletion
        if ($user['id'] === $_SESSION['user_id']) {
            $this->setFlash('error', 'You cannot delete your own account.');
            return $this->redirect('admin/users');
        }

        try {
            if ($this->userModel->delete($id)) {
                $this->setFlash('success', 'User deleted successfully.');
            } else {
                $this->setFlash('error', 'Failed to delete user.');
            }
        } catch (\Exception $e) {
            error_log("Error in UserController::delete: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while deleting the user.');
        }

        return $this->redirect('admin/users');
    }

    /**
     * Send new password to user via email
     */
    public function sendPassword($id) {
        if (!$this->isPost()) {
            $this->setFlash('error', 'Invalid request method.');
            return $this->redirect('admin/users');
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            $this->setFlash('error', 'User not found.');
            return $this->redirect('admin/users');
        }

        try {
            // Generate new random password
            $newPassword = $this->emailService->generateRandomPassword();

            // Update user password in database
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $updateData = ['password' => $hashedPassword];

            if ($this->userModel->update($id, $updateData)) {
                // Send email with new password
                $emailSent = $this->emailService->sendPasswordResetEmail(
                    $user['email'],
                    $user['full_name'],
                    $newPassword
                );

                if ($emailSent) {
                    $this->setFlash('success', 'New password generated and sent to ' . $user['email'] . ' successfully.');
                } else {
                    $this->setFlash('warning', 'Password updated but failed to send email. Please check email logs.');
                }
            } else {
                $this->setFlash('error', 'Failed to update user password.');
            }
        } catch (\Exception $e) {
            error_log("Error in UserController::sendPassword: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while generating new password.');
        }

        return $this->redirect('admin/users');
    }

    /**
     * Resend welcome email to user
     */
    public function resendWelcome($id) {
        if (!$this->isPost()) {
            $this->setFlash('error', 'Invalid request method.');
            return $this->redirect('admin/users');
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            $this->setFlash('error', 'User not found.');
            return $this->redirect('admin/users');
        }

        try {
            // Generate new password for welcome email
            $newPassword = $this->emailService->generateRandomPassword();

            // Update user password in database
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $updateData = ['password' => $hashedPassword];

            if ($this->userModel->update($id, $updateData)) {
                // Send welcome email with new password
                $emailSent = $this->emailService->sendWelcomeEmail(
                    $user['email'],
                    $user['full_name'],
                    $newPassword,
                    $user['role']
                );

                if ($emailSent) {
                    $this->setFlash('success', 'Welcome email with new password sent to ' . $user['email'] . ' successfully.');
                } else {
                    $this->setFlash('warning', 'Password updated but failed to send welcome email. Please check email logs.');
                }
            } else {
                $this->setFlash('error', 'Failed to update user password.');
            }
        } catch (\Exception $e) {
            error_log("Error in UserController::resendWelcome: " . $e->getMessage());
            $this->setFlash('error', 'An error occurred while sending welcome email.');
        }

        return $this->redirect('admin/users');
    }

    /**
     * Get inspector barangay assignments
     */
    private function getInspectorBarangays($inspectorId) {
        try {
            $db = (new \App\Config\Database())->getConnection();
            $query = "SELECT iba.*, b.name as barangay_name, d.name as district_name,
                             u.full_name as assigned_by_name
                      FROM inspector_barangay_assignments iba
                      LEFT JOIN barangays b ON iba.barangay_id = b.id
                      LEFT JOIN districts d ON b.district_id = d.id
                      LEFT JOIN users u ON iba.assigned_by = u.id
                      WHERE iba.inspector_id = ? AND iba.status = 'active'
                      ORDER BY d.name ASC, b.name ASC";

            $stmt = $db->prepare($query);
            $stmt->execute([$inspectorId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\Exception $e) {
            error_log("Error getting inspector barangays: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get inspector workload statistics
     */
    private function getInspectorWorkloadStats($inspectorId) {
        try {
            $db = (new \App\Config\Database())->getConnection();
            $query = "SELECT
                        COUNT(DISTINCT iba.barangay_id) as assigned_barangays,
                        COUNT(DISTINCT b.id) as businesses_in_barangays,
                        COUNT(DISTINCT i.id) as total_inspections,
                        COUNT(DISTINCT CASE WHEN i.status IN ('scheduled', 'confirmed') THEN i.id END) as pending_inspections,
                        COUNT(DISTINCT CASE WHEN i.status = 'completed' THEN i.id END) as completed_inspections
                      FROM inspector_barangay_assignments iba
                      LEFT JOIN businesses b ON iba.barangay_id = b.barangay_id
                      LEFT JOIN inspections i ON b.id = i.business_id AND i.inspector_id = ?
                      WHERE iba.inspector_id = ? AND iba.status = 'active'";

            $stmt = $db->prepare($query);
            $stmt->execute([$inspectorId, $inspectorId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return $result ?: [
                'assigned_barangays' => 0,
                'businesses_in_barangays' => 0,
                'total_inspections' => 0,
                'pending_inspections' => 0,
                'completed_inspections' => 0
            ];
        } catch (\Exception $e) {
            error_log("Error getting inspector workload: " . $e->getMessage());
            return [
                'assigned_barangays' => 0,
                'businesses_in_barangays' => 0,
                'total_inspections' => 0,
                'pending_inspections' => 0,
                'completed_inspections' => 0
            ];
        }
    }
}