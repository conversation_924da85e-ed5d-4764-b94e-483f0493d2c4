<?php
namespace App\Core;

use App\Config\Database;
use PDO;
use PDOException;

/**
 * Base Model Class
 * 
 * Provides common functionality for all models including:
 * - Database connection management
 * - UUID generation
 * - Standard CRUD operations
 * - Query building utilities
 * - Error handling
 */
abstract class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $timestamps = true;
    
    public function __construct() {
        try {
            $this->db = (new Database())->getConnection();
            // Only set table name if not already set by child class
            if (empty($this->table)) {
                $this->table = $this->getTableName();
            }
        } catch (PDOException $e) {
            error_log("Error initializing " . get_class($this) . " model: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get the table name for this model
     * Override this method in child classes if needed
     */
    protected function getTableName() {
        $className = (new \ReflectionClass($this))->getShortName();
        return strtolower($className) . 's';
    }
    
    /**
     * Generate UUID v4
     */
    protected function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * Get all records
     */
    public function all($orderBy = null, $direction = 'DESC') {
        try {
            $orderClause = '';
            if ($orderBy) {
                $orderClause = " ORDER BY {$orderBy} {$direction}";
            } elseif ($this->timestamps) {
                $orderClause = " ORDER BY created_at DESC";
            }
            
            $query = "SELECT * FROM {$this->table}{$orderClause}";
            $stmt = $this->db->query($query);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::all(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Find record by ID
     */
    public function find($id) {
        try {
            $query = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->execute([':id' => $id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::find(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create new record
     */
    public function create(array $data) {
        try {
            // Generate UUID if not provided
            if (!isset($data[$this->primaryKey])) {
                $data[$this->primaryKey] = $this->generateUUID();
            }
            
            // Add timestamps if enabled
            if ($this->timestamps) {
                $data['created_at'] = date('Y-m-d H:i:s');
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
            
            // Filter only fillable fields
            if (!empty($this->fillable)) {
                $data = array_intersect_key($data, array_flip($this->fillable));
            }
            
            $fields = array_keys($data);
            $placeholders = ':' . implode(', :', $fields);
            
            $query = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
            $stmt = $this->db->prepare($query);
            
            $params = [];
            foreach ($data as $key => $value) {
                $params[':' . $key] = $value;
            }
            
            $result = $stmt->execute($params);
            return $result ? $data[$this->primaryKey] : false;
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::create(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Update record
     */
    public function update($id, array $data) {
        try {
            // Add updated timestamp if enabled
            if ($this->timestamps) {
                $data['updated_at'] = date('Y-m-d H:i:s');
            }
            
            // Filter only fillable fields
            if (!empty($this->fillable)) {
                $data = array_intersect_key($data, array_flip($this->fillable));
            }
            
            $fields = [];
            $params = [':id' => $id];
            
            foreach ($data as $key => $value) {
                $fields[] = "{$key} = :{$key}";
                $params[":{$key}"] = $value;
            }
            
            $query = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE {$this->primaryKey} = :id";
            $stmt = $this->db->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::update(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Delete record
     */
    public function delete($id) {
        try {
            $query = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
            $stmt = $this->db->prepare($query);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::delete(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Count all records
     */
    public function countAll() {
        try {
            $query = "SELECT COUNT(*) FROM {$this->table}";
            $stmt = $this->db->query($query);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::countAll(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get records with WHERE clause
     */
    public function where($column, $operator, $value = null) {
        try {
            if ($value === null) {
                $value = $operator;
                $operator = '=';
            }
            
            $query = "SELECT * FROM {$this->table} WHERE {$column} {$operator} :value";
            $stmt = $this->db->prepare($query);
            $stmt->execute([':value' => $value]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::where(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get first record with WHERE clause
     */
    public function whereFirst($column, $operator, $value = null) {
        try {
            if ($value === null) {
                $value = $operator;
                $operator = '=';
            }
            
            $query = "SELECT * FROM {$this->table} WHERE {$column} {$operator} :value LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->execute([':value' => $value]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::whereFirst(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get recent records
     */
    public function getRecent($limit = 5) {
        try {
            $orderBy = $this->timestamps ? 'created_at' : $this->primaryKey;
            $query = "SELECT * FROM {$this->table} ORDER BY {$orderBy} DESC LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::getRecent(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Execute raw query
     */
    protected function query($sql, $params = []) {
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Error in " . get_class($this) . "::query(): " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }

    /**
     * Get database connection
     */
    public function getDb() {
        return $this->db;
    }
}
