<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-list"></i> Checklist Management
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/checklist/categories/create" class="btn btn-success me-2">
                <i class="fas fa-plus"></i> Add Category
            </a>
            <a href="<?= BASE_URL ?>admin/checklist/items/create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Item
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Categories</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_categories'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-folder fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Items</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_items'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Critical Items</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['critical_items'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Active Items</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['active_items'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories and Items -->
    <?php if (!empty($categories)): ?>
        <?php foreach ($categories as $category): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-folder me-2"></i>
                        <?= htmlspecialchars($category['name']) ?>
                        <span class="badge bg-secondary ms-2"><?= $category['item_count'] ?> items</span>
                        <?php if ($category['critical_count'] > 0): ?>
                            <span class="badge bg-warning ms-1"><?= $category['critical_count'] ?> critical</span>
                        <?php endif; ?>
                        <?php if (!$category['is_active']): ?>
                            <span class="badge bg-danger ms-1">Inactive</span>
                        <?php endif; ?>
                    </h6>
                    <div>
                        <a href="<?= BASE_URL ?>admin/checklist/categories/<?= $category['id'] ?>/edit" 
                           class="btn btn-sm btn-outline-primary me-1">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                onclick="confirmDeleteCategory('<?= $category['id'] ?>', '<?= htmlspecialchars($category['name']) ?>')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                
                <?php if (!empty($category['description'])): ?>
                    <div class="card-body py-2">
                        <p class="text-muted mb-0"><?= htmlspecialchars($category['description']) ?></p>
                    </div>
                <?php endif; ?>

                <!-- Category Items -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Code</th>
                                    <th>Item Name</th>
                                    <th>Points</th>
                                    <th>Critical</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                // Get items for this category
                                $categoryItems = array_filter($categories, function($cat) use ($category) {
                                    return $cat['id'] === $category['id'];
                                });
                                
                                if (!empty($category['items'])): 
                                    foreach ($category['items'] as $item): 
                                ?>
                                    <tr>
                                        <td><code><?= htmlspecialchars($item['item_code']) ?></code></td>
                                        <td>
                                            <strong><?= htmlspecialchars($item['item_name']) ?></strong>
                                            <?php if (!empty($item['description'])): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($item['description']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $item['points'] ?></td>
                                        <td>
                                            <?php if ($item['is_critical']): ?>
                                                <span class="badge bg-warning">Critical</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Normal</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($item['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= BASE_URL ?>admin/checklist/items/<?= $item['id'] ?>/edit" 
                                               class="btn btn-sm btn-outline-primary me-1">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-warning me-1" 
                                                    onclick="toggleItemStatus('<?= $item['id'] ?>')">
                                                <i class="fas fa-toggle-<?= $item['is_active'] ? 'on' : 'off' ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDeleteItem('<?= $item['id'] ?>', '<?= htmlspecialchars($item['item_name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php 
                                    endforeach; 
                                else: 
                                ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">No items in this category</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No checklist categories found</h5>
                <p class="text-muted">Start by creating your first checklist category.</p>
                <a href="<?= BASE_URL ?>admin/checklist/categories/create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Category
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category "<span id="categoryName"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This will also delete all items in this category.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteCategoryForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Category</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Item Modal -->
<div class="modal fade" id="deleteItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the item "<span id="itemName"></span>"?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteItemForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Item</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeleteCategory(categoryId, categoryName) {
    document.getElementById('categoryName').textContent = categoryName;
    document.getElementById('deleteCategoryForm').action = '<?= BASE_URL ?>admin/checklist/categories/' + categoryId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteCategoryModal')).show();
}

function confirmDeleteItem(itemId, itemName) {
    document.getElementById('itemName').textContent = itemName;
    document.getElementById('deleteItemForm').action = '<?= BASE_URL ?>admin/checklist/items/' + itemId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteItemModal')).show();
}

function toggleItemStatus(itemId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= BASE_URL ?>admin/checklist/items/' + itemId + '/toggle';
    document.body.appendChild(form);
    form.submit();
}
</script>

<?php $this->endSection(); ?>
