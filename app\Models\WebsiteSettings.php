<?php
namespace App\Models;

use App\Config\Database;
use PDO;

class WebsiteSettings {
    public $db;

    public function __construct() {
        $this->db = (new Database())->getConnection();
    }

    /**
     * Get all website settings
     */
    public function getAllSettings() {
        $stmt = $this->db->query("SELECT * FROM website_settings ORDER BY setting_key ASC");
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $settings = [];
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }

    /**
     * Get a specific setting
     */
    public function getSetting($key, $default = '') {
        $stmt = $this->db->prepare("SELECT setting_value FROM website_settings WHERE setting_key = :key");
        $stmt->execute([':key' => $key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['setting_value'] : $default;
    }

    /**
     * Update or create a setting
     */
    public function updateSetting($key, $value) {
        $stmt = $this->db->prepare("
            INSERT INTO website_settings (setting_key, setting_value, updated_at) 
            VALUES (:key, :value, NOW()) 
            ON DUPLICATE KEY UPDATE 
            setting_value = :value, updated_at = NOW()
        ");
        
        return $stmt->execute([
            ':key' => $key,
            ':value' => $value
        ]);
    }

    /**
     * Get predefined color schemes
     */
    public function getColorSchemes() {
        return [
            'default' => [
                'name' => 'Default Blue',
                'description' => 'Professional blue theme',
                'colors' => [
                    'primary_color' => '#2563eb',
                    'secondary_color' => '#1e40af',
                    'accent_color' => '#3b82f6',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#dc2626',
                    'dark_color' => '#1f2937',
                    'light_color' => '#f8fafc'
                ]
            ],
            'green' => [
                'name' => 'Government Green',
                'description' => 'Professional green theme',
                'colors' => [
                    'primary_color' => '#059669',
                    'secondary_color' => '#047857',
                    'accent_color' => '#10b981',
                    'success_color' => '#22c55e',
                    'warning_color' => '#f59e0b',
                    'danger_color' => '#ef4444',
                    'dark_color' => '#1f2937',
                    'light_color' => '#f0fdf4'
                ]
            ],
            'purple' => [
                'name' => 'Royal Purple',
                'description' => 'Elegant purple theme',
                'colors' => [
                    'primary_color' => '#7c3aed',
                    'secondary_color' => '#6d28d9',
                    'accent_color' => '#8b5cf6',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#dc2626',
                    'dark_color' => '#1f2937',
                    'light_color' => '#faf5ff'
                ]
            ],
            'red' => [
                'name' => 'Bold Red',
                'description' => 'Strong red theme',
                'colors' => [
                    'primary_color' => '#dc2626',
                    'secondary_color' => '#b91c1c',
                    'accent_color' => '#ef4444',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#991b1b',
                    'dark_color' => '#1f2937',
                    'light_color' => '#fef2f2'
                ]
            ],
            'orange' => [
                'name' => 'Vibrant Orange',
                'description' => 'Energetic orange theme',
                'colors' => [
                    'primary_color' => '#ea580c',
                    'secondary_color' => '#c2410c',
                    'accent_color' => '#f97316',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#dc2626',
                    'dark_color' => '#1f2937',
                    'light_color' => '#fff7ed'
                ]
            ],
            'teal' => [
                'name' => 'Modern Teal',
                'description' => 'Contemporary teal theme',
                'colors' => [
                    'primary_color' => '#0891b2',
                    'secondary_color' => '#0e7490',
                    'accent_color' => '#06b6d4',
                    'success_color' => '#059669',
                    'warning_color' => '#d97706',
                    'danger_color' => '#dc2626',
                    'dark_color' => '#1f2937',
                    'light_color' => '#f0fdfa'
                ]
            ],
            'dark' => [
                'name' => 'Dark Mode',
                'description' => 'Professional dark theme',
                'colors' => [
                    'primary_color' => '#3b82f6',
                    'secondary_color' => '#1d4ed8',
                    'accent_color' => '#60a5fa',
                    'success_color' => '#10b981',
                    'warning_color' => '#f59e0b',
                    'danger_color' => '#ef4444',
                    'dark_color' => '#111827',
                    'light_color' => '#1f2937'
                ]
            ]
        ];
    }

    /**
     * Reset all settings to defaults
     */
    public function resetToDefaults() {
        $defaultSettings = [
            // General Settings
            'site_name' => 'Bacoor Occupational Health & Safety System',
            'site_tagline' => 'Ensuring Workplace Safety in Bacoor City',
            'site_description' => 'The official occupational health and safety management system for businesses in Bacoor City, Cavite.',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '(*************',
            'office_address' => 'Bacoor City Hall, Government Center, Molino III, Bacoor City, Cavite 4102',
            'office_hours' => 'Monday - Friday: 8:00 AM - 5:00 PM',
            
            // Social Media
            'facebook_url' => '',
            'twitter_url' => '',
            'youtube_url' => '',
            
            // Colors
            'primary_color' => '#2563eb',
            'secondary_color' => '#1e40af',
            'accent_color' => '#3b82f6',
            'success_color' => '#059669',
            'warning_color' => '#d97706',
            'danger_color' => '#dc2626',
            'dark_color' => '#1f2937',
            'light_color' => '#f8fafc',
            
            // Images
            'hero_bg' => '/assets/images/hero-bg.jpg',
            'logo' => '/assets/images/logo.png',
            'about_image' => '/assets/images/about-us.jpg',
            'services_image' => '/assets/images/services.jpg',
            'contact_image' => '/assets/images/contact.jpg'
        ];

        foreach ($defaultSettings as $key => $value) {
            $this->updateSetting($key, $value);
        }

        return true;
    }

    /**
     * Get settings by category
     */
    public function getSettingsByCategory($category) {
        $allSettings = $this->getAllSettings();
        $categorySettings = [];
        
        switch ($category) {
            case 'general':
                $keys = ['site_name', 'site_tagline', 'site_description', 'contact_email', 'contact_phone', 'office_address', 'office_hours'];
                break;
            case 'social':
                $keys = ['facebook_url', 'twitter_url', 'youtube_url'];
                break;
            case 'colors':
                $keys = ['primary_color', 'secondary_color', 'accent_color', 'success_color', 'warning_color', 'danger_color', 'dark_color', 'light_color'];
                break;
            case 'images':
                $keys = ['hero_bg', 'logo', 'about_image', 'services_image', 'contact_image'];
                break;
            default:
                return $allSettings;
        }
        
        foreach ($keys as $key) {
            $categorySettings[$key] = $allSettings[$key] ?? '';
        }
        
        return $categorySettings;
    }

    /**
     * Export settings as JSON
     */
    public function exportSettings() {
        $settings = $this->getAllSettings();
        return json_encode($settings, JSON_PRETTY_PRINT);
    }

    /**
     * Import settings from JSON
     */
    public function importSettings($jsonData) {
        $settings = json_decode($jsonData, true);
        
        if (!$settings) {
            throw new \Exception('Invalid JSON data');
        }
        
        foreach ($settings as $key => $value) {
            $this->updateSetting($key, $value);
        }
        
        return true;
    }

    /**
     * Get image settings with full URLs
     */
    public function getImageSettings() {
        $imageKeys = ['hero_bg', 'logo', 'about_image', 'services_image', 'contact_image'];
        $images = [];
        
        foreach ($imageKeys as $key) {
            $path = $this->getSetting($key);
            $images[$key] = [
                'path' => $path,
                'url' => $path ? (BASE_URL . ltrim($path, '/')) : '',
                'exists' => $path ? file_exists(ROOT_PATH . $path) : false
            ];
        }
        
        return $images;
    }

    /**
     * Validate color value
     */
    public function validateColor($color) {
        // Check if it's a valid hex color
        return preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color);
    }

    /**
     * Get color palette for current theme
     */
    public function getCurrentColorPalette() {
        $colorKeys = ['primary_color', 'secondary_color', 'accent_color', 'success_color', 'warning_color', 'danger_color', 'dark_color', 'light_color'];
        $palette = [];
        
        foreach ($colorKeys as $key) {
            $palette[$key] = $this->getSetting($key, '#2563eb');
        }
        
        return $palette;
    }
}
?>
