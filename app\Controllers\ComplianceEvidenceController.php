<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Business;
use App\Models\ComplianceEvidence;

class ComplianceEvidenceController extends Controller {
    private $businessModel;
    private $evidenceModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->businessModel = new Business();
        $this->evidenceModel = new ComplianceEvidence();
    }

    public function index($businessId = null) {
        $this->auth->requireLogin();

        // Only admin can access inspection results dashboard
        if (!$this->auth->isAdmin()) {
            $_SESSION['error'] = 'You do not have permission to view inspection results.';
            $this->redirect('dashboard');
            return;
        }

        $user = $this->auth->getUser();

        if ($businessId) {
            // Show inspection results for specific business
            $business = $this->businessModel->getById($businessId);
            if (!$business) {
                $_SESSION['error'] = 'Business not found.';
                $this->redirect('admin/compliance');
                return;
            }

            // Get business inspection data
            $inspectionModel = new \App\Models\Inspection();
            $checklistModel = new \App\Models\InspectionChecklist();
            $businessEvidenceModel = new \App\Models\BusinessChecklistEvidence();

            $inspections = $inspectionModel->getByBusiness($businessId);
            $checklistEvidenceStats = $businessEvidenceModel->getBusinessEvidenceStats($businessId);

            // Add checklist completion data to inspections
            foreach ($inspections as &$inspection) {
                $inspection['checklist_completion'] = $checklistModel->getInspectionCompletionStatus($inspection['id']);
                if ($inspection['checklist_completion']['completed_items'] > 0) {
                    $inspection['checklist_score'] = $checklistModel->calculateInspectionScore($inspection['id']);
                }
            }

            return $this->render('admin/compliance_evidence/business', [
                'title' => 'Inspection Results for ' . $business['name'],
                'active_page' => 'compliance',
                'business' => $business,
                'inspections' => $inspections,
                'checklist_evidence_stats' => $checklistEvidenceStats,
                'user' => $user
            ]);
        } else {
            // Show inspection results dashboard
            $inspectionModel = new \App\Models\Inspection();
            $checklistModel = new \App\Models\InspectionChecklist();
            $businessEvidenceModel = new \App\Models\BusinessChecklistEvidence();

            // Get inspection statistics
            $totalInspections = $inspectionModel->countAll();
            $completedInspections = $inspectionModel->countApproved(); // Only count approved inspections
            $inProgressCount = $inspectionModel->countByStatus('in_progress');
            $scheduledInspections = $inspectionModel->countByStatus('scheduled');
            $cancelledInspections = $inspectionModel->countByStatus('cancelled');

            // Get recent approved inspections with scores (only show approved inspections)
            $recentInspections = $inspectionModel->getRecentApproved(10);
            foreach ($recentInspections as &$inspection) {
                $inspection['checklist_completion'] = $checklistModel->getInspectionCompletionStatus($inspection['id']);
                if ($inspection['checklist_completion']['completed_items'] > 0) {
                    $inspection['checklist_score'] = $checklistModel->calculateInspectionScore($inspection['id']);
                }
            }

            // Get recent in-progress inspections with completion status
            $inProgressInspections = $inspectionModel->getRecentInProgress(10);
            foreach ($inProgressInspections as &$inspection) {
                $inspection['checklist_completion'] = $checklistModel->getInspectionCompletionStatus($inspection['id']);
                if ($inspection['checklist_completion']['completed_items'] > 0) {
                    $inspection['checklist_score'] = $checklistModel->calculateInspectionScore($inspection['id']);
                }
            }



            // Get business compliance overview
            $compliantBusinesses = $this->businessModel->countByComplianceStatus('compliant');
            $nonCompliantBusinesses = $this->businessModel->countByComplianceStatus('non_compliant');
            $pendingReviewBusinesses = $this->businessModel->countByComplianceStatus('pending_review');

            return $this->render('admin/compliance_evidence/index', [
                'title' => 'Inspection Results Dashboard',
                'active_page' => 'compliance',
                'stats' => [
                    'total_inspections' => $totalInspections,
                    'completed_inspections' => $completedInspections,
                    'in_progress_inspections' => $inProgressCount,
                    'scheduled_inspections' => $scheduledInspections,
                    'cancelled_inspections' => $cancelledInspections,
                    'compliant_businesses' => $compliantBusinesses,
                    'non_compliant_businesses' => $nonCompliantBusinesses,
                    'pending_review_businesses' => $pendingReviewBusinesses
                ],
                'recent_inspections' => $recentInspections,
                'in_progress_inspections' => $inProgressInspections,
                'user' => $user
            ]);
        }
    }





    public function delete($id) {
        $this->auth->requireLogin();

        $evidence = $this->evidenceModel->getById($id);
          if (!$evidence) {
              $_SESSION['error'] = 'Evidence not found.';
              $this->redirect('admin/compliance');
              return;
          }
  $business = $this->businessModel->getById($evidence['business_id']);
          $user = $this->auth->getUser();
  
          if (!$this->auth->isAdmin() && !$this->auth->isInspector() && $business['owner_id'] !== $user['id']) {
              $_SESSION['error'] = 'You do not have permission to delete this evidence.';
              $this->redirect('dashboard');
              return;
          }
          $filePath = ROOT_PATH . '/public/' . $evidence['photo_path'];
          if (file_exists($filePath)) {
              unlink($filePath);
          }
  
          if ($this->evidenceModel->delete($id)) {
              $_SESSION['success'] = 'Evidence deleted successfully.';
              $this->businessModel->checkAndSetComplianceStatus($evidence['business_id']);
          } else {
              $_SESSION['error'] = 'Failed to delete evidence.';
          }
  
          $this->redirect("business/view/{$evidence['business_id']}");
      }

    public function upload($businessId) {
        $this->auth->requireLogin();
        $user = $this->auth->getUser();

        // Get business details
        $business = $this->businessModel->getById($businessId);
        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('business');
            return;
        }

        // Check permissions - only business owner or admin can upload
        if (!$this->auth->isAdmin() && $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to upload evidence for this business.';
            $this->redirect('business');
            return;
        }

        if ($this->isPost()) {
            $compliance_type = $_POST['compliance_type'] ?? '';
            $notes = $_POST['notes'] ?? '';
            $file = $_FILES['evidence_photo'] ?? null;

            // Validation
            if (empty($compliance_type)) {
                $_SESSION['error'] = 'Please select a compliance type.';
                $this->redirect("business/compliance/{$businessId}/upload");
                return;
            }

            if (empty($file) || $file['error'] !== UPLOAD_ERR_OK) {
                $_SESSION['error'] = 'Please upload a valid file.';
                $this->redirect("business/compliance/{$businessId}/upload");
                return;
            }

            // Create upload directory
            $uploadDir = ROOT_PATH . '/public/uploads/compliance_evidence/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Generate unique filename
            $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
            $targetFilePath = $uploadDir . $fileName;
            $relativePath = 'uploads/compliance_evidence/' . $fileName;

            // Validate file type
            $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'];
            if (!in_array(strtolower($fileExtension), $allowedTypes)) {
                $_SESSION['error'] = 'Only JPG, PNG, PDF, DOC, and DOCX files are allowed.';
                $this->redirect("business/compliance/{$businessId}/upload");
                return;
            }

            // Validate file size (5MB max)
            if ($file['size'] > 5 * 1024 * 1024) {
                $_SESSION['error'] = 'File size must be less than 5MB.';
                $this->redirect("business/compliance/{$businessId}/upload");
                return;
            }

            // Upload file
            if (move_uploaded_file($file['tmp_name'], $targetFilePath)) {
                // Save to database
                $data = [
                    'business_id' => $businessId,
                    'compliance_type' => $compliance_type,
                    'photo_path' => $relativePath,
                    'notes' => $notes
                ];

                if ($this->evidenceModel->create($data)) {
                    $_SESSION['success'] = 'Compliance evidence uploaded successfully and is pending review.';
                    $this->redirect("business/compliance/{$businessId}");
                } else {
                    // Delete uploaded file if database save failed
                    unlink($targetFilePath);
                    $_SESSION['error'] = 'Failed to save evidence record.';
                    $this->redirect("business/compliance/{$businessId}/upload");
                }
            } else {
                $_SESSION['error'] = 'Failed to upload file.';
                $this->redirect("business/compliance/{$businessId}/upload");
            }
            return;
        }

        // Show upload form
        return $this->render('business/compliance_evidence/upload', [
            'title' => 'Upload Compliance Evidence',
            'active_page' => 'compliance',
            'business' => $business,
            'compliance_types' => $this->evidenceModel->getComplianceTypes(),
            'user' => $user,
            'auth' => $this->auth
        ]);
    }



}
