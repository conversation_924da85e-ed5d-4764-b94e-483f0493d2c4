<?php
namespace App\Constants;

/**
 * Business Constants
 * 
 * Centralized business logic constants to eliminate duplication
 */
class BusinessConstants {
    
    // ==================== User Roles ====================
    const USER_ROLES = [
        'admin' => 'System Administrator',
        'inspector' => 'Safety Inspector',
        'business_owner' => 'Business Owner'
    ];
    
    const USER_ROLE_ADMIN = 'admin';
    const USER_ROLE_INSPECTOR = 'inspector';
    const USER_ROLE_BUSINESS_OWNER = 'business_owner';
    
    // ==================== User Status ====================
    const USER_STATUSES = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',
        'pending' => 'Pending Approval'
    ];
    
    const USER_STATUS_ACTIVE = 'active';
    const USER_STATUS_INACTIVE = 'inactive';
    const USER_STATUS_SUSPENDED = 'suspended';
    const USER_STATUS_PENDING = 'pending';
    
    // ==================== Business Status ====================
    const BUSINESS_STATUSES = [
        'pending' => 'Pending Review',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'suspended' => 'Suspended',
        'closed' => 'Closed'
    ];
    
    const BUSINESS_STATUS_PENDING = 'pending';
    const BUSINESS_STATUS_APPROVED = 'approved';
    const BUSINESS_STATUS_REJECTED = 'rejected';
    const BUSINESS_STATUS_SUSPENDED = 'suspended';
    const BUSINESS_STATUS_CLOSED = 'closed';
    
    // ==================== Inspection Types ====================
    const INSPECTION_TYPES = [
        'routine' => 'Routine Inspection',
        'follow_up' => 'Follow-up Inspection',
        'complaint' => 'Complaint Investigation',
        'initial' => 'Initial Inspection',
        'emergency' => 'Emergency Inspection',
        'annual' => 'Annual Review'
    ];
    
    const INSPECTION_TYPE_ROUTINE = 'routine';
    const INSPECTION_TYPE_FOLLOW_UP = 'follow_up';
    const INSPECTION_TYPE_COMPLAINT = 'complaint';
    const INSPECTION_TYPE_INITIAL = 'initial';
    const INSPECTION_TYPE_EMERGENCY = 'emergency';
    const INSPECTION_TYPE_ANNUAL = 'annual';
    
    // ==================== Inspection Status ====================
    const INSPECTION_STATUSES = [
        'scheduled' => 'Scheduled',
        'confirmed' => 'Confirmed',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'rescheduled' => 'Rescheduled'
    ];
    
    const INSPECTION_STATUS_SCHEDULED = 'scheduled';
    const INSPECTION_STATUS_CONFIRMED = 'confirmed';
    const INSPECTION_STATUS_IN_PROGRESS = 'in_progress';
    const INSPECTION_STATUS_COMPLETED = 'completed';
    const INSPECTION_STATUS_CANCELLED = 'cancelled';
    const INSPECTION_STATUS_RESCHEDULED = 'rescheduled';
    
    // ==================== Compliance Types ====================
    const COMPLIANCE_TYPES = [
        'business_permit' => 'Business Permit',
        'sanitary_permit' => 'Sanitary Permit',
        'fire_safety_certificate' => 'Fire Safety Certificate',
        'environmental_permit' => 'Environmental Permit',
        'safety_signage' => 'Safety Signage',
        'first_aid' => 'First Aid Kit',
        'fire_extinguishers' => 'Fire Extinguishers',
        'cctv' => 'CCTV System',
        'waste_segregation' => 'Waste Segregation System'
    ];
    
    const COMPLIANCE_TYPE_BUSINESS_PERMIT = 'business_permit';
    const COMPLIANCE_TYPE_SANITARY_PERMIT = 'sanitary_permit';
    const COMPLIANCE_TYPE_FIRE_SAFETY = 'fire_safety_certificate';
    const COMPLIANCE_TYPE_ENVIRONMENTAL = 'environmental_permit';
    const COMPLIANCE_TYPE_SAFETY_SIGNAGE = 'safety_signage';
    const COMPLIANCE_TYPE_FIRST_AID = 'first_aid';
    const COMPLIANCE_TYPE_FIRE_EXTINGUISHERS = 'fire_extinguishers';
    const COMPLIANCE_TYPE_CCTV = 'cctv';
    const COMPLIANCE_TYPE_WASTE_SEGREGATION = 'waste_segregation';
    
    // ==================== Compliance Status ====================
    const COMPLIANCE_STATUSES = [
        'pending' => 'Pending Review',
        'verified' => 'Verified',
        'rejected' => 'Rejected',
        'expired' => 'Expired',
        'resubmitted' => 'Resubmitted'
    ];
    
    const COMPLIANCE_STATUS_PENDING = 'pending';
    const COMPLIANCE_STATUS_VERIFIED = 'verified';
    const COMPLIANCE_STATUS_REJECTED = 'rejected';
    const COMPLIANCE_STATUS_EXPIRED = 'expired';
    const COMPLIANCE_STATUS_RESUBMITTED = 'resubmitted';
    
    // ==================== Notification Types ====================
    const NOTIFICATION_TYPES = [
        'info' => 'Information',
        'success' => 'Success',
        'warning' => 'Warning',
        'error' => 'Error',
        'inspection' => 'Inspection',
        'compliance' => 'Compliance',
        'system' => 'System'
    ];
    
    const NOTIFICATION_TYPE_INFO = 'info';
    const NOTIFICATION_TYPE_SUCCESS = 'success';
    const NOTIFICATION_TYPE_WARNING = 'warning';
    const NOTIFICATION_TYPE_ERROR = 'error';
    const NOTIFICATION_TYPE_INSPECTION = 'inspection';
    const NOTIFICATION_TYPE_COMPLIANCE = 'compliance';
    const NOTIFICATION_TYPE_SYSTEM = 'system';
    
    // ==================== File Upload Types ====================
    const ALLOWED_FILE_TYPES = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'txt'],
        'compliance' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        'all' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'txt']
    ];
    
    const MAX_FILE_SIZE = 5242880; // 5MB in bytes
    const MAX_FILE_SIZE_MB = 5;
    
    // ==================== Compliance Rating ====================
    const COMPLIANCE_RATINGS = [
        'excellent' => 'Excellent (90-100%)',
        'good' => 'Good (80-89%)',
        'satisfactory' => 'Satisfactory (70-79%)',
        'needs_improvement' => 'Needs Improvement (60-69%)',
        'poor' => 'Poor (Below 60%)'
    ];
    
    const COMPLIANCE_RATING_EXCELLENT = 'excellent';
    const COMPLIANCE_RATING_GOOD = 'good';
    const COMPLIANCE_RATING_SATISFACTORY = 'satisfactory';
    const COMPLIANCE_RATING_NEEDS_IMPROVEMENT = 'needs_improvement';
    const COMPLIANCE_RATING_POOR = 'poor';
    
    // ==================== Priority Levels ====================
    const PRIORITY_LEVELS = [
        'low' => 'Low Priority',
        'medium' => 'Medium Priority',
        'high' => 'High Priority',
        'urgent' => 'Urgent',
        'critical' => 'Critical'
    ];
    
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';
    const PRIORITY_CRITICAL = 'critical';
    
    // ==================== Helper Methods ====================
    
    /**
     * Get all user roles
     */
    public static function getUserRoles() {
        return self::USER_ROLES;
    }
    
    /**
     * Get all inspection types
     */
    public static function getInspectionTypes() {
        return self::INSPECTION_TYPES;
    }
    
    /**
     * Get all inspection statuses
     */
    public static function getInspectionStatuses() {
        return self::INSPECTION_STATUSES;
    }
    
    /**
     * Get all compliance types
     */
    public static function getComplianceTypes() {
        return self::COMPLIANCE_TYPES;
    }
    
    /**
     * Get all compliance statuses
     */
    public static function getComplianceStatuses() {
        return self::COMPLIANCE_STATUSES;
    }
    
    /**
     * Get all business statuses
     */
    public static function getBusinessStatuses() {
        return self::BUSINESS_STATUSES;
    }
    
    /**
     * Get all notification types
     */
    public static function getNotificationTypes() {
        return self::NOTIFICATION_TYPES;
    }
    
    /**
     * Get allowed file types for category
     */
    public static function getAllowedFileTypes($category = 'all') {
        return self::ALLOWED_FILE_TYPES[$category] ?? self::ALLOWED_FILE_TYPES['all'];
    }
    
    /**
     * Get compliance rating by score
     */
    public static function getComplianceRatingByScore($score) {
        if ($score >= 90) return self::COMPLIANCE_RATING_EXCELLENT;
        if ($score >= 80) return self::COMPLIANCE_RATING_GOOD;
        if ($score >= 70) return self::COMPLIANCE_RATING_SATISFACTORY;
        if ($score >= 60) return self::COMPLIANCE_RATING_NEEDS_IMPROVEMENT;
        return self::COMPLIANCE_RATING_POOR;
    }
    
    /**
     * Check if user role is valid
     */
    public static function isValidUserRole($role) {
        return array_key_exists($role, self::USER_ROLES);
    }
    
    /**
     * Check if inspection type is valid
     */
    public static function isValidInspectionType($type) {
        return array_key_exists($type, self::INSPECTION_TYPES);
    }
    
    /**
     * Check if compliance type is valid
     */
    public static function isValidComplianceType($type) {
        return array_key_exists($type, self::COMPLIANCE_TYPES);
    }
    
    /**
     * Check if file type is allowed
     */
    public static function isAllowedFileType($extension, $category = 'all') {
        $allowedTypes = self::getAllowedFileTypes($category);
        return in_array(strtolower($extension), $allowedTypes);
    }
    
    /**
     * Get status color class for UI
     */
    public static function getStatusColorClass($status, $type = 'inspection') {
        $colorMap = [
            'inspection' => [
                'scheduled' => 'primary',
                'confirmed' => 'info',
                'in_progress' => 'warning',
                'completed' => 'success',
                'cancelled' => 'danger'
            ],
            'compliance' => [
                'pending' => 'warning',
                'verified' => 'success',
                'rejected' => 'danger',
                'expired' => 'secondary'
            ],
            'business' => [
                'pending' => 'warning',
                'approved' => 'success',
                'rejected' => 'danger',
                'suspended' => 'secondary'
            ]
        ];
        
        return $colorMap[$type][$status] ?? 'secondary';
    }
}
