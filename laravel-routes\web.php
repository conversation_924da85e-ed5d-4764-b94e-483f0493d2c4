<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\BusinessController as AdminBusinessController;
use App\Http\Controllers\Admin\InspectionController as AdminInspectionController;
use App\Http\Controllers\Admin\InspectorController as AdminInspectorController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\ChecklistController as AdminChecklistController;
use App\Http\Controllers\Admin\ChatController as AdminChatController;
use App\Http\Controllers\Admin\SettingsController as AdminSettingsController;
use App\Http\Controllers\Inspector\DashboardController as InspectorDashboardController;
use App\Http\Controllers\Inspector\InspectionController as InspectorInspectionController;
use App\Http\Controllers\Inspector\BusinessController as InspectorBusinessController;
use App\Http\Controllers\Inspector\ChatController as InspectorChatController;
use App\Http\Controllers\BusinessOwner\DashboardController as BusinessOwnerDashboardController;
use App\Http\Controllers\BusinessOwner\BusinessController as BusinessOwnerBusinessController;
use App\Http\Controllers\BusinessOwner\InspectionController as BusinessOwnerInspectionController;
use App\Http\Controllers\BusinessOwner\ChecklistController as BusinessOwnerChecklistController;
use App\Http\Controllers\BusinessOwner\ChatController as BusinessOwnerChatController;
use App\Http\Controllers\PublicController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', [PublicController::class, 'home'])->name('home');
Route::get('/about', [PublicController::class, 'about'])->name('about');
Route::get('/contact', [PublicController::class, 'contact'])->name('contact');
Route::post('/contact', [PublicController::class, 'submitContact'])->name('contact.submit');

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('password.email');
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// API routes for AJAX
Route::prefix('api')->middleware('auth')->group(function () {
    Route::get('/auth/check', [AuthController::class, 'checkAuth']);
    Route::get('/districts/{district}/barangays', [AdminBusinessController::class, 'getBarangays']);
});

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [AdminDashboardController::class, 'getData'])->name('dashboard.data');
    
    // User Management
    Route::resource('users', AdminUserController::class);
    Route::post('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])->name('users.toggle-status');
    
    // Business Management
    Route::resource('businesses', AdminBusinessController::class);
    Route::get('businesses/export', [AdminBusinessController::class, 'export'])->name('businesses.export');
    
    // Inspector Management
    Route::resource('inspectors', AdminInspectorController::class);
    Route::get('inspectors/{inspector}/assignments', [AdminInspectorController::class, 'assignments'])->name('inspectors.assignments');
    Route::post('inspectors/{inspector}/assign-district', [AdminInspectorController::class, 'assignDistrict'])->name('inspectors.assign-district');
    Route::post('inspectors/{inspector}/assign-barangay', [AdminInspectorController::class, 'assignBarangay'])->name('inspectors.assign-barangay');
    Route::delete('inspector-assignments/{assignment}', [AdminInspectorController::class, 'removeAssignment'])->name('inspector-assignments.destroy');
    
    // Inspection Management
    Route::resource('inspections', AdminInspectionController::class);
    Route::get('inspections/calendar', [AdminInspectionController::class, 'calendar'])->name('inspections.calendar');
    Route::post('inspections/{inspection}/verify', [AdminInspectionController::class, 'verify'])->name('inspections.verify');
    Route::post('inspections/{inspection}/reject', [AdminInspectionController::class, 'reject'])->name('inspections.reject');
    Route::get('pending-verification', [AdminInspectionController::class, 'pendingVerification'])->name('inspections.pending-verification');
    Route::get('inspection-results', [AdminInspectionController::class, 'results'])->name('inspections.results');
    Route::get('inspection-assignments', [AdminInspectionController::class, 'assignments'])->name('inspections.assignments');
    Route::get('inspection-assignments/integrated', [AdminInspectionController::class, 'integratedAssignments'])->name('inspections.assignments.integrated');
    
    // Checklist Management
    Route::resource('checklist-categories', AdminChecklistController::class);
    Route::resource('checklist-items', AdminChecklistController::class, ['except' => ['index', 'show']]);
    Route::get('checklist', [AdminChecklistController::class, 'index'])->name('checklist.index');
    Route::get('checklist/{category}', [AdminChecklistController::class, 'show'])->name('checklist.show');
    Route::post('checklist-items/{item}/toggle-status', [AdminChecklistController::class, 'toggleItemStatus'])->name('checklist-items.toggle-status');
    
    // Chat Management
    Route::get('chat', [AdminChatController::class, 'index'])->name('chat.index');
    Route::get('chat/{chatRoom}', [AdminChatController::class, 'show'])->name('chat.show');
    Route::post('chat/{chatRoom}/message', [AdminChatController::class, 'sendMessage'])->name('chat.send-message');
    Route::get('chat/{chatRoom}/messages', [AdminChatController::class, 'getMessages'])->name('chat.messages');
    
    // Settings
    Route::get('settings', [AdminSettingsController::class, 'index'])->name('settings.index');
    Route::post('settings/website', [AdminSettingsController::class, 'updateWebsite'])->name('settings.website');
    Route::post('settings/system', [AdminSettingsController::class, 'updateSystem'])->name('settings.system');
    Route::post('settings/upload-image', [AdminSettingsController::class, 'uploadImage'])->name('settings.upload-image');
});

// Inspector routes
Route::prefix('inspector')->name('inspector.')->middleware(['auth', 'role:inspector'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [InspectorDashboardController::class, 'index'])->name('dashboard');
    
    // Inspection Management
    Route::get('inspections', [InspectorInspectionController::class, 'index'])->name('inspections.index');
    Route::get('inspections/{inspection}', [InspectorInspectionController::class, 'show'])->name('inspections.show');
    Route::get('inspections/{inspection}/checklist', [InspectorInspectionController::class, 'checklist'])->name('inspections.checklist');
    Route::post('inspections/{inspection}/checklist', [InspectorInspectionController::class, 'submitChecklist'])->name('inspections.checklist.submit');
    Route::post('inspections/{inspection}/complete', [InspectorInspectionController::class, 'complete'])->name('inspections.complete');
    Route::get('inspections/{inspection}/report', [InspectorInspectionController::class, 'report'])->name('inspections.report');
    
    // Business Management
    Route::get('businesses', [InspectorBusinessController::class, 'index'])->name('businesses.index');
    Route::get('businesses/{business}', [InspectorBusinessController::class, 'show'])->name('businesses.show');
    
    // Chat
    Route::get('chat', [InspectorChatController::class, 'index'])->name('chat.index');
    Route::get('chat/{chatRoom}', [InspectorChatController::class, 'show'])->name('chat.show');
    Route::post('chat/{chatRoom}/message', [InspectorChatController::class, 'sendMessage'])->name('chat.send-message');
});

// Business Owner routes
Route::prefix('business-owner')->name('business-owner.')->middleware(['auth', 'role:business_owner'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [BusinessOwnerDashboardController::class, 'index'])->name('dashboard');
    
    // Business Management
    Route::get('business', [BusinessOwnerBusinessController::class, 'show'])->name('business.show');
    Route::get('business/edit', [BusinessOwnerBusinessController::class, 'edit'])->name('business.edit');
    Route::put('business', [BusinessOwnerBusinessController::class, 'update'])->name('business.update');
    
    // Inspection Management
    Route::get('inspections', [BusinessOwnerInspectionController::class, 'index'])->name('inspections.index');
    Route::get('inspections/{inspection}', [BusinessOwnerInspectionController::class, 'show'])->name('inspections.show');
    Route::get('inspections/{inspection}/report', [BusinessOwnerInspectionController::class, 'report'])->name('inspections.report');
    
    // Checklist & Evidence
    Route::get('checklist', [BusinessOwnerChecklistController::class, 'index'])->name('checklist.index');
    Route::post('checklist/{item}/evidence', [BusinessOwnerChecklistController::class, 'uploadEvidence'])->name('checklist.upload-evidence');
    Route::delete('evidence/{evidence}', [BusinessOwnerChecklistController::class, 'deleteEvidence'])->name('evidence.delete');
    
    // Chat
    Route::get('chat', [BusinessOwnerChatController::class, 'index'])->name('chat.index');
    Route::get('chat/{chatRoom}', [BusinessOwnerChatController::class, 'show'])->name('chat.show');
    Route::post('chat/{chatRoom}/message', [BusinessOwnerChatController::class, 'sendMessage'])->name('chat.send-message');
    Route::post('chat/create', [BusinessOwnerChatController::class, 'create'])->name('chat.create');
});
