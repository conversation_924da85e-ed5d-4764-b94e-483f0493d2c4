<?php
namespace App\Models;

use App\Config\Database;
use PDO;

class HomepageContent {
    private $db;

    public function __construct() {
        $this->db = (new Database())->getConnection();
    }

    /**
     * Get homepage content
     */
    public function getContent() {
        $stmt = $this->db->query("
            SELECT * FROM homepage_content 
            WHERE page = 'home' AND status = 'active' 
            ORDER BY sort_order ASC
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get content for specific page
     */
    public function getPageContent($page) {
        $stmt = $this->db->prepare("
            SELECT * FROM homepage_content 
            WHERE page = :page AND status = 'active' 
            ORDER BY sort_order ASC
        ");
        $stmt->execute([':page' => $page]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get announcements
     */
    public function getAnnouncements($limit = null) {
        $sql = "
            SELECT * FROM announcements 
            WHERE status = 'active' AND 
                  (publish_date IS NULL OR publish_date <= NOW()) AND
                  (expire_date IS NULL OR expire_date >= NOW())
            ORDER BY created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->db->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get system statistics for homepage
     */
    public function getSystemStats() {
        $stats = [];
        
        try {
            // Total registered businesses
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM businesses WHERE status = 'active'");
            $stats['businesses'] = $stmt->fetchColumn();
            
            // Total inspections completed
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM inspections WHERE status = 'completed'");
            $stats['inspections'] = $stmt->fetchColumn();
            
            // Total inspectors
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM users WHERE role = 'inspector' AND status = 'active'");
            $stats['inspectors'] = $stmt->fetchColumn();
            
            // Districts covered
            $stmt = $this->db->query("SELECT COUNT(DISTINCT district_id) as count FROM inspector_assignments");
            $stats['districts'] = $stmt->fetchColumn();
            
        } catch (\Exception $e) {
            error_log("Error getting stats: " . $e->getMessage());
            $stats = [
                'businesses' => 0,
                'inspections' => 0,
                'inspectors' => 0,
                'districts' => 0
            ];
        }
        
        return $stats;
    }

    /**
     * Save contact message
     */
    public function saveContactMessage($data) {
        $stmt = $this->db->prepare("
            INSERT INTO contact_messages (name, email, subject, message, created_at)
            VALUES (:name, :email, :subject, :message, :created_at)
        ");
        
        return $stmt->execute([
            ':name' => $data['name'],
            ':email' => $data['email'],
            ':subject' => $data['subject'],
            ':message' => $data['message'],
            ':created_at' => $data['created_at']
        ]);
    }

    /**
     * Update homepage content
     */
    public function updateContent($id, $data) {
        $stmt = $this->db->prepare("
            UPDATE homepage_content 
            SET title = :title, content = :content, image_url = :image_url, 
                button_text = :button_text, button_url = :button_url, 
                sort_order = :sort_order, updated_at = NOW()
            WHERE id = :id
        ");
        
        return $stmt->execute([
            ':id' => $id,
            ':title' => $data['title'],
            ':content' => $data['content'],
            ':image_url' => $data['image_url'],
            ':button_text' => $data['button_text'],
            ':button_url' => $data['button_url'],
            ':sort_order' => $data['sort_order']
        ]);
    }

    /**
     * Create new content section
     */
    public function createContent($data) {
        $stmt = $this->db->prepare("
            INSERT INTO homepage_content 
            (page, section_type, title, content, image_url, button_text, button_url, sort_order, status, created_at)
            VALUES (:page, :section_type, :title, :content, :image_url, :button_text, :button_url, :sort_order, 'active', NOW())
        ");
        
        return $stmt->execute([
            ':page' => $data['page'],
            ':section_type' => $data['section_type'],
            ':title' => $data['title'],
            ':content' => $data['content'],
            ':image_url' => $data['image_url'],
            ':button_text' => $data['button_text'],
            ':button_url' => $data['button_url'],
            ':sort_order' => $data['sort_order']
        ]);
    }

    /**
     * Get all content for admin management
     */
    public function getAllContent($page = null) {
        $sql = "SELECT * FROM homepage_content";
        $params = [];
        
        if ($page) {
            $sql .= " WHERE page = :page";
            $params[':page'] = $page;
        }
        
        $sql .= " ORDER BY page, sort_order ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Delete content section
     */
    public function deleteContent($id) {
        $stmt = $this->db->prepare("DELETE FROM homepage_content WHERE id = :id");
        return $stmt->execute([':id' => $id]);
    }

    /**
     * Get content by ID
     */
    public function getContentById($id) {
        $stmt = $this->db->prepare("SELECT * FROM homepage_content WHERE id = :id");
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
