<?php

// autoload.php @generated by Composer

if (PHP_VERSION_ID < 80100) {
    if (!headers_sent()) {
        header('HTTP/1.1 500 Internal Server Error');
    }
    $err = 'Composer 2.3.0 dropped support for autoloading on PHP <8.1 and you are running '.PHP_VERSION.', please upgrade PHP or use Composer 2.2 LTS.';
    trigger_error($err, E_USER_ERROR);
}

require_once __DIR__ . '/composer/autoload_real.php';

return ComposerAutoloaderInit::getLoader();
