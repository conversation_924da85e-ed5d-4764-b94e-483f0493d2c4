<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Inspection;
use App\Models\Business;
use App\Models\User;
use App\Models\District;
use App\Models\Barangay;
use App\Services\InspectionService;
use App\Services\EmailService;
use App\Services\ReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InspectionController extends Controller
{
    protected $inspectionService;
    protected $emailService;
    protected $reportService;

    public function __construct(
        InspectionService $inspectionService,
        EmailService $emailService,
        ReportService $reportService
    ) {
        $this->inspectionService = $inspectionService;
        $this->emailService = $emailService;
        $this->reportService = $reportService;
    }

    /**
     * Display integrated inspection assignments page
     */
    public function integratedAssignments(Request $request)
    {
        // Get filter options
        $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();
        $districts = District::with('barangays')->get();
        $businesses = Business::where('status', 'active')->get();

        // Get inspections with filters
        $query = Inspection::with(['business.barangay.district', 'inspector', 'assignedBy']);

        if ($request->filled('inspector_id')) {
            $query->where('inspector_id', $request->inspector_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('district_id')) {
            $query->whereHas('business.barangay', function($q) use ($request) {
                $q->where('district_id', $request->district_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
        }

        $inspections = $query->latest('scheduled_date')->paginate(15);

        // Get statistics
        $stats = [
            'total_inspections' => Inspection::count(),
            'scheduled_inspections' => Inspection::where('status', 'scheduled')->count(),
            'in_progress_inspections' => Inspection::where('status', 'in_progress')->count(),
            'completed_inspections' => Inspection::where('status', 'completed')->count(),
            'pending_verification' => Inspection::where('verification_status', 'pending')->count(),
        ];

        return view('admin.inspections.integrated-assignments', compact(
            'inspections',
            'inspectors',
            'districts',
            'businesses',
            'stats'
        ));
    }

    /**
     * Schedule new inspection
     */
    public function schedule(Request $request)
    {
        $validated = $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'inspector_id' => 'required|exists:users,id',
            'scheduled_date' => 'required|date|after:today',
            'inspection_type' => 'required|in:routine,follow_up,complaint,initial',
            'priority' => 'required|in:low,medium,high,urgent',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check for scheduling conflicts
        $conflict = Inspection::where('inspector_id', $validated['inspector_id'])
            ->whereDate('scheduled_date', $validated['scheduled_date'])
            ->where('status', '!=', 'cancelled')
            ->exists();

        if ($conflict) {
            return back()->with('error', 'Inspector already has an inspection scheduled for this date.');
        }

        // Create inspection
        $inspection = Inspection::create([
            'business_id' => $validated['business_id'],
            'inspector_id' => $validated['inspector_id'],
            'assigned_by' => Auth::id(),
            'scheduled_date' => $validated['scheduled_date'],
            'inspection_type' => $validated['inspection_type'],
            'priority' => $validated['priority'],
            'notes' => $validated['notes'],
            'status' => 'scheduled',
        ]);

        // Send notifications
        $this->emailService->sendInspectionScheduledNotification($inspection);

        return back()->with('success', 'Inspection scheduled successfully and notifications sent.');
    }

    /**
     * Display pending verification inspections
     */
    public function pendingVerification(Request $request)
    {
        $query = Inspection::with(['business.barangay.district', 'inspector', 'assignedBy'])
            ->where('status', 'completed')
            ->where('verification_status', 'pending');

        if ($request->filled('inspector_id')) {
            $query->where('inspector_id', $request->inspector_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('completed_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('completed_date', '<=', $request->date_to);
        }

        $inspections = $query->latest('completed_date')->paginate(15);

        $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();

        return view('admin.inspections.pending-verification', compact('inspections', 'inspectors'));
    }

    /**
     * Verify inspection
     */
    public function verify(Request $request, Inspection $inspection)
    {
        $validated = $request->validate([
            'verification_status' => 'required|in:approved,rejected',
            'verification_notes' => 'nullable|string|max:1000',
        ]);

        $inspection->update([
            'verification_status' => $validated['verification_status'],
            'verified_by' => Auth::id(),
            'verified_at' => now(),
            'verification_notes' => $validated['verification_notes'],
            'admin_verified' => true,
        ]);

        // Update business compliance status if approved
        if ($validated['verification_status'] === 'approved') {
            $complianceStatus = match($inspection->compliance_rating) {
                'A', 'B' => 'compliant',
                'C' => 'pending_review',
                'D', 'F' => 'non_compliant',
                default => 'pending_review'
            };

            $inspection->business->update([
                'compliance_status' => $complianceStatus,
                'last_inspection_date' => $inspection->completed_date,
            ]);
        }

        // Send verification notification
        $this->emailService->sendInspectionVerifiedNotification($inspection);

        return back()->with('success', 'Inspection verification completed and notifications sent.');
    }

    /**
     * Display inspection results
     */
    public function results(Request $request)
    {
        $query = Inspection::with(['business.barangay.district', 'inspector', 'verifiedBy'])
            ->where('status', 'completed');

        if ($request->filled('verification_status')) {
            $query->where('verification_status', $request->verification_status);
        }

        if ($request->filled('compliance_rating')) {
            $query->where('compliance_rating', $request->compliance_rating);
        }

        if ($request->filled('inspector_id')) {
            $query->where('inspector_id', $request->inspector_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('completed_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('completed_date', '<=', $request->date_to);
        }

        $inspections = $query->latest('completed_date')->paginate(15);

        $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();

        return view('admin.inspections.results', compact('inspections', 'inspectors'));
    }

    /**
     * Display inspection calendar
     */
    public function calendar(Request $request)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        $inspections = Inspection::with(['business', 'inspector'])
            ->whereMonth('scheduled_date', $month)
            ->whereYear('scheduled_date', $year)
            ->get();

        // Format for calendar
        $calendarEvents = $inspections->map(function($inspection) {
            return [
                'id' => $inspection->id,
                'title' => $inspection->business->name,
                'start' => $inspection->scheduled_date->format('Y-m-d'),
                'backgroundColor' => $this->getStatusColor($inspection->status),
                'borderColor' => $this->getStatusColor($inspection->status),
                'inspector' => $inspection->inspector->full_name,
                'status' => $inspection->status,
                'url' => route('admin.inspections.show', $inspection),
            ];
        });

        return view('admin.inspections.calendar', compact('calendarEvents', 'month', 'year'));
    }

    /**
     * Display specific inspection
     */
    public function show(Inspection $inspection)
    {
        $inspection->load([
            'business.barangay.district',
            'business.category',
            'inspector',
            'assignedBy',
            'verifiedBy',
            'checklistResponses.checklistItem.category'
        ]);

        // Group responses by category
        $responsesByCategory = $inspection->checklistResponses
            ->groupBy('checklistItem.category.name');

        return view('admin.inspections.show', compact('inspection', 'responsesByCategory'));
    }

    /**
     * Generate inspection reports
     */
    public function reports(Request $request)
    {
        $filters = $request->only([
            'date_from', 'date_to', 'status', 'inspector_id', 
            'district_id', 'barangay_id', 'report_type'
        ]);

        $reportType = $request->get('report_type', 'inspection');

        switch ($reportType) {
            case 'compliance':
                $reportData = $this->reportService->generateComplianceReport($filters);
                break;
            case 'inspector':
                $reportData = $this->reportService->generateInspectorReport($filters);
                break;
            default:
                $reportData = $this->reportService->generateInspectionReport($filters);
        }

        // Get filter options
        $inspectors = User::where('role', 'inspector')->where('status', 'active')->get();
        $districts = District::all();

        return view('admin.inspections.reports', compact(
            'reportData', 
            'inspectors', 
            'districts', 
            'reportType'
        ));
    }

    /**
     * Export inspection report
     */
    public function exportReport(Request $request)
    {
        $filters = $request->only([
            'date_from', 'date_to', 'status', 'inspector_id', 
            'district_id', 'barangay_id'
        ]);

        $exportData = $this->reportService->exportInspectionReport($filters);

        // Return as Excel download (you can implement Excel export here)
        return response()->json([
            'success' => true,
            'data' => $exportData,
            'message' => 'Report data prepared for export'
        ]);
    }

    /**
     * Get status color for calendar
     */
    private function getStatusColor($status)
    {
        return match($status) {
            'scheduled' => '#007bff',
            'confirmed' => '#28a745',
            'in_progress' => '#ffc107',
            'completed' => '#17a2b8',
            'cancelled' => '#dc3545',
            'rescheduled' => '#6f42c1',
            default => '#6c757d'
        };
    }
}
