<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-check text-primary me-2"></i>
            <?= htmlspecialchars($inspector['full_name']) ?> - District Assignments
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/inspector-assignments" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Assignments
            </a>
        </div>
    </div>

    <!-- Inspector Workload Summary -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Assigned Districts
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $workload['assigned_districts'] ?? 0 ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Businesses in Districts
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $workload['businesses_in_districts'] ?? 0 ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Inspections
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $workload['total_inspections'] ?? 0 ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Inspections
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $workload['pending_inspections'] ?? 0 ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Inspector Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user me-2"></i>Inspector Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted">Full Name</h6>
                        <p class="mb-0"><?= htmlspecialchars($inspector['full_name']) ?></p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Email</h6>
                        <p class="mb-0"><?= htmlspecialchars($inspector['email']) ?></p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Role</h6>
                        <p class="mb-0">
                            <span class="badge bg-info"><?= ucfirst($inspector['role']) ?></span>
                        </p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted">Status</h6>
                        <p class="mb-0">
                            <span class="badge bg-<?= ($inspector['status'] ?? 'active') === 'active' ? 'success' : 'danger' ?>">
                                <?= ucfirst($inspector['status'] ?? 'Active') ?>
                            </span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= BASE_URL ?>admin/users/edit/<?= $inspector['id'] ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Inspector
                        </a>
                        <a href="<?= BASE_URL ?>admin/inspections?inspector=<?= $inspector['id'] ?>" class="btn btn-info">
                            <i class="fas fa-clipboard-list"></i> View Inspections
                        </a>
                        <a href="<?= BASE_URL ?>chat/create/inspector/<?= $inspector['id'] ?>" class="btn btn-success">
                            <i class="fas fa-comments"></i> Send Message
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- District Assignments -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-map-marked-alt me-2"></i>District Assignments
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($districts)): ?>
                        <div class="row">
                            <?php foreach ($districts as $district): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card border-left-success h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h5 class="card-title text-success mb-1">
                                                        <i class="fas fa-map-marker-alt me-2"></i>
                                                        <?= htmlspecialchars($district['district_name']) ?>
                                                    </h5>
                                                    <p class="text-muted mb-0">
                                                        Assigned by: <?= htmlspecialchars($district['assigned_by_name']) ?>
                                                    </p>
                                                </div>
                                                <span class="badge bg-success">Active</span>
                                            </div>

                                            <?php if ($district['notes']): ?>
                                                <div class="mb-3">
                                                    <h6 class="text-muted">Assignment Notes:</h6>
                                                    <p class="text-secondary mb-0">
                                                        <i class="fas fa-sticky-note text-info me-1"></i>
                                                        <?= htmlspecialchars($district['notes']) ?>
                                                    </p>
                                                </div>
                                            <?php endif; ?>

                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Assigned on: <?= date('M d, Y h:i A', strtotime($district['assigned_at'])) ?>
                                                </small>
                                            </div>

                                            <div class="d-flex gap-2">
                                                <a href="<?= BASE_URL ?>admin/inspector-assignments/district/<?= $district['district_id'] ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View District
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="removeFromDistrict('<?= $district['district_id'] ?>', '<?= htmlspecialchars($district['district_name']) ?>')"
                                                        title="Remove from district">
                                                    <i class="fas fa-times"></i> Remove
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-3">No District Assignments</h5>
                            <p class="text-muted mb-3">
                                This inspector hasn't been assigned to any districts yet.
                            </p>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>To assign this inspector to districts:</strong><br>
                                Go to the main assignments page and use the "Assign" button for specific districts.
                            </div>
                            <a href="<?= BASE_URL ?>admin/inspector-assignments" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Assign to Districts
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove from District Modal -->
<div class="modal fade" id="removeFromDistrictModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove from District</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove <?= htmlspecialchars($inspector['full_name']) ?> from <strong id="removeDistrictName"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="removeFromDistrictForm" method="POST" action="<?= BASE_URL ?>admin/inspector-assignments/remove" style="display: inline;">
                    <input type="hidden" name="inspector_id" value="<?= $inspector['id'] ?>">
                    <input type="hidden" name="district_id" id="removeDistrictIdInput">
                    <button type="submit" class="btn btn-danger">Remove from District</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function removeFromDistrict(districtId, districtName) {
    document.getElementById('removeDistrictName').textContent = districtName;
    document.getElementById('removeDistrictIdInput').value = districtId;
    
    new bootstrap.Modal(document.getElementById('removeFromDistrictModal')).show();
}
</script>
<?php $this->endSection() ?>
