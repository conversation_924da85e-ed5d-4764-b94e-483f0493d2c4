<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit text-primary me-2"></i>Edit Chatbot Response
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/chatbot/responses" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Responses
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot me-2"></i>Response Details
                    </h6>
                    <div>
                        <span class="badge bg-info">Usage: <?= $response['usage_count'] ?> times</span>
                        <span class="badge bg-<?= $response['is_active'] ? 'success' : 'secondary' ?>">
                            <?= $response['is_active'] ? 'Active' : 'Inactive' ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <form action="<?= BASE_URL ?>admin/chatbot/responses/edit/<?= $response['id'] ?>" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="trigger_keywords" class="form-label">
                                        <i class="fas fa-key me-1"></i>Trigger Keywords *
                                    </label>
                                    <textarea class="form-control" id="trigger_keywords" name="trigger_keywords" 
                                              rows="3" required><?= htmlspecialchars($response['trigger_keywords']) ?></textarea>
                                    <div class="form-text">
                                        Separate multiple keywords with commas.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="response_type" class="form-label">
                                        <i class="fas fa-cog me-1"></i>Response Type
                                    </label>
                                    <select class="form-select" id="response_type" name="response_type" required>
                                        <option value="text" <?= $response['response_type'] === 'text' ? 'selected' : '' ?>>
                                            Text Response
                                        </option>
                                        <option value="quick_reply" <?= $response['response_type'] === 'quick_reply' ? 'selected' : '' ?>>
                                            Quick Reply (with options)
                                        </option>
                                        <option value="escalate" <?= $response['response_type'] === 'escalate' ? 'selected' : '' ?>>
                                            Escalate to Human
                                        </option>
                                        <option value="redirect" <?= $response['response_type'] === 'redirect' ? 'selected' : '' ?>>
                                            Redirect to Page
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Category
                                    </label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="general" <?= $response['category'] === 'general' ? 'selected' : '' ?>>
                                            General
                                        </option>
                                        <option value="greeting" <?= $response['category'] === 'greeting' ? 'selected' : '' ?>>
                                            Greeting
                                        </option>
                                        <option value="safety" <?= $response['category'] === 'safety' ? 'selected' : '' ?>>
                                            Safety
                                        </option>
                                        <option value="compliance" <?= $response['category'] === 'compliance' ? 'selected' : '' ?>>
                                            Compliance
                                        </option>
                                        <option value="inspection" <?= $response['category'] === 'inspection' ? 'selected' : '' ?>>
                                            Inspection
                                        </option>
                                        <option value="permits" <?= $response['category'] === 'permits' ? 'selected' : '' ?>>
                                            Permits
                                        </option>
                                        <option value="emergency" <?= $response['category'] === 'emergency' ? 'selected' : '' ?>>
                                            Emergency
                                        </option>
                                        <option value="escalation" <?= $response['category'] === 'escalation' ? 'selected' : '' ?>>
                                            Escalation
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="priority" class="form-label">
                                        <i class="fas fa-sort-numeric-up me-1"></i>Priority
                                    </label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <?php for ($i = 1; $i <= 10; $i++): ?>
                                            <option value="<?= $i ?>" <?= $response['priority'] == $i ? 'selected' : '' ?>>
                                                <?= $i ?> - <?= $i <= 3 ? 'Low' : ($i <= 6 ? 'Medium' : 'High') ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               <?= $response['is_active'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            <strong>Active Response</strong>
                                        </label>
                                        <div class="form-text">
                                            Inactive responses won't be used by the chatbot.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="response_text" class="form-label">
                                        <i class="fas fa-comment me-1"></i>Response Text *
                                    </label>
                                    <textarea class="form-control" id="response_text" name="response_text" 
                                              rows="10" required><?= htmlspecialchars($response['response_text']) ?></textarea>
                                    <div class="form-text">
                                        This is the message the chatbot will send to users.
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-chart-line me-2"></i>Performance Stats:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Times Used:</strong> <?= $response['usage_count'] ?></li>
                                        <li><strong>Created:</strong> <?= date('M j, Y', strtotime($response['created_at'])) ?></li>
                                        <li><strong>Last Updated:</strong> <?= date('M j, Y', strtotime($response['updated_at'])) ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Response
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-test-tube me-2"></i>Test Response
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="test_message" class="form-label">Test Message</label>
                        <input type="text" class="form-control" id="test_message" 
                               placeholder="Type a message to test...">
                    </div>
                    <button type="button" class="btn btn-outline-primary w-100" onclick="testResponse()">
                        <i class="fas fa-play me-2"></i>Test Response
                    </button>
                    <div id="test_result" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <strong>Match Result:</strong>
                            <div id="test_output"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        Deleting this response will permanently remove it from the system.
                        This action cannot be undone.
                    </p>
                    <button type="button" class="btn btn-danger w-100" onclick="deleteResponse()">
                        <i class="fas fa-trash me-2"></i>Delete Response
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testResponse() {
    const message = document.getElementById('test_message').value.toLowerCase();
    const keywords = '<?= strtolower($response['trigger_keywords']) ?>'.split(',');
    
    let matches = 0;
    const messageWords = message.split(' ');
    
    keywords.forEach(keyword => {
        keyword = keyword.trim();
        messageWords.forEach(word => {
            if (word.includes(keyword) || keyword.includes(word)) {
                matches++;
            }
        });
    });
    
    const confidence = messageWords.length > 0 ? (matches / messageWords.length) * 100 : 0;
    
    document.getElementById('test_result').style.display = 'block';
    document.getElementById('test_output').innerHTML = `
        <strong>Confidence:</strong> ${confidence.toFixed(1)}%<br>
        <strong>Matches:</strong> ${matches} out of ${messageWords.length} words<br>
        <strong>Would Trigger:</strong> ${confidence > 30 ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>'}
    `;
}

function deleteResponse() {
    if (confirm('Are you sure you want to delete this response? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= BASE_URL ?>admin/chatbot/responses/delete/<?= $response['id'] ?>';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $this->endSection() ?>
