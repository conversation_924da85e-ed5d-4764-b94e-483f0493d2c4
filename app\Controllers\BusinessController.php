<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Libraries\Auth;
use App\Models\Business;
use App\Models\ComplianceEvidence;
use App\Models\Inspection;
use App\Models\User;
use App\Models\InspectionChecklist;
use App\Models\BusinessChecklistEvidence;

class BusinessController extends Controller {
    private $businessModel;
    private $inspectionModel;
    private $evidenceModel;
    private $checklistModel;
    private $businessEvidenceModel;
    private $userModel;

    public function __construct() {
        parent::__construct();
        $this->auth = Auth::getInstance();
        $this->businessModel = new Business();
        $this->inspectionModel = new Inspection();
        $this->evidenceModel = new ComplianceEvidence();
        $this->checklistModel = new InspectionChecklist();
        $this->businessEvidenceModel = new BusinessChecklistEvidence();
        $this->userModel = new User();
    }

    public function index() {
        $this->auth->requireLogin();
        $user = $this->auth->getUser();

        if ($this->auth->isAdmin()) {
            $businesses = $this->businessModel->getAll();
            $viewPath = 'admin/businesses';
        } else {
            $businesses = $this->businessModel->getByOwnerId($user['id']);
            $viewPath = 'business/index';
        }

        return $this->render($viewPath, [
            'title' => 'Businesses',
            'active_page' => 'businesses',
            'businesses' => $businesses,
            'user' => $user
        ]);
    }    


    public function complianceIndex() {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();
        
        $user = $this->auth->getUser();
        $businesses = $this->businessModel->getByOwnerId($user['id']);
        
        // Get all compliance evidence for the user's businesses
        $allEvidence = [];
        foreach ($businesses as $business) {
            $evidence = $this->evidenceModel->getByBusinessId($business['id']);
            foreach ($evidence as $item) {
                $item['business_name'] = $business['name'];
                $allEvidence[] = $item;
            }
        }
        
        // Sort by creation date (newest first)
        usort($allEvidence, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return $this->render('business/compliance/index', [
            'title' => 'Compliance Evidence',
            'active_page' => 'compliance',
            'businesses' => $businesses,
            'evidence' => $allEvidence,
            'compliance_types' => $this->evidenceModel->getComplianceTypes(),
            'user' => $user,
            'auth' => $this->auth
        ]);
    }

    public function create() {
        $this->auth->requireLogin();
        if ($this->auth->isAdmin()) {
            $users = $this->userModel->getByRole('business_owner'); // Use the initialized userModel
            $ownerId = '';
        } else {
            $users = [];
            $ownerId = $this->auth->getUserId();
        }

        if ($this->isPost()) {
            $owner_id = $this->auth->isAdmin() ? $_POST['owner_id'] : $ownerId;
            // Fetch owner_name based on owner_id if admin, otherwise use current user's name
            if ($this->auth->isAdmin() && !empty($owner_id)) {
                $ownerUser = $this->userModel->find($owner_id);
                $owner_name = $ownerUser ? $ownerUser['full_name'] : 'Unknown Owner'; // Fetch name from user model
            } else {
                $owner_name = $this->auth->getUserName();
            }

            $name = $_POST['name'] ?? '';
            $registration_number = $_POST['registration_number'] ?? '';
            $email = $_POST['email'] ?? '';
            $contact_number = $_POST['contact_number'] ?? '';
            $address = $_POST['address'] ?? '';
            $category_id = $_POST['category_id'] ?? '';
            $barangay_id = $_POST['barangay_id'] ?? '';
            $employee_count = $_POST['employee_count'] ?? 1;
            $date_established = $_POST['date_established'] ?? null;

            // Validate fields
            if (empty($name) || empty($registration_number) || empty($email) || empty($contact_number) || empty($address) || empty($category_id) || empty($barangay_id)) {
                $_SESSION['error'] = 'All fields are required.';
                $this->redirect('business/create');
                return;
            }

            // Check if we should use district_id or barangay_id based on database structure
            $data = [
                'owner_id' => $owner_id,
                'owner_name' => $owner_name,
                'name' => $name,
                'registration_number' => $registration_number,
                'email' => $email,
                'contact_number' => $contact_number,
                'address' => $address,
                'category_id' => $category_id,
                'employee_count' => $employee_count,
                'date_established' => $date_established
            ];

            // Try to determine if we should use district_id or barangay_id
            if ($this->businessModel->hasBarangayIdColumn()) {
                $data['barangay_id'] = $barangay_id;
            } else {
                // Use district_id if barangay_id doesn't exist
                $data['district_id'] = $barangay_id;
            }

            try {
                if ($this->businessModel->create($data)) {
                    $_SESSION['success'] = 'Business created successfully.';
                    $this->redirect('business');
                } else {
                    $_SESSION['error'] = 'Failed to create business.';
                    $this->redirect('business/create');
                }
            } catch (\PDOException $e) {
                // Check if it's a duplicate entry error
                if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    if (strpos($e->getMessage(), 'registration_number') !== false) {
                        $_SESSION['error'] = 'A business with this registration number already exists. Please use a different registration number.';
                    } else {
                        $_SESSION['error'] = 'A business with this information already exists. Please check your details.';
                    }
                } else {
                    $_SESSION['error'] = 'Failed to create business. Please try again.';
                }
                $this->redirect('business/create');
            }

            return;
        }

        $categories = $this->businessModel->getCategories();
        $districts = $this->businessModel->getDistricts();
        $barangays = $this->businessModel->getBarangays();

        return $this->render('business/create', [
            'title' => 'Register New Business',
            'active_page' => 'businesses',
            'categories' => $categories,
            'districts' => $districts,
            'barangays' => $barangays,
            'users' => $users,
            'owner_id' => $ownerId,
            'user' => $this->auth->getUser()
        ]);
    }

    public function edit($id) {
        $this->auth->requireLogin();

        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('business');
            return;
        }

        $user = $this->auth->getUser();

        // Only owner or admin can edit business
        if (!$this->auth->isAdmin() && $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to edit this business.';
            $this->redirect('business');
            return;
        }

        if ($this->isPost()) {
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $contact_number = $_POST['contact_number'] ?? '';
            $address = $_POST['address'] ?? '';
            $category_id = $_POST['category_id'] ?? '';
            $status = $_POST['status'] ?? $business['status'];

            // Validate fields
            if (empty($name) || empty($email) || empty($contact_number) || empty($address) || empty($category_id)) {
                $_SESSION['error'] = 'All fields are required.';
                $this->redirect("business/edit/$id");
                return;
            }

            $barangay_id = $_POST['barangay_id'] ?? '';

            // Validate barangay_id
            if (empty($barangay_id)) {
                $_SESSION['error'] = 'Please select a barangay.';
                $this->redirect("business/edit/$id");
                return;
            }

            $data = [
                'name' => $name,
                'email' => $email,
                'contact_number' => $contact_number,
                'address' => $address,
                'category_id' => $category_id
            ];

            // Try to determine if we should use district_id or barangay_id
            if ($this->businessModel->hasBarangayIdColumn()) {
                $data['barangay_id'] = $barangay_id;
            } else {
                // Use district_id if barangay_id doesn't exist
                $data['district_id'] = $barangay_id;
            }

            // Only admin can update status
            if ($this->auth->isAdmin()) {
                $data['status'] = $status;
            }

            try {
                if ($this->businessModel->update($id, $data)) {
                    $_SESSION['success'] = 'Business updated successfully.';
                    $this->redirect('business');
                } else {
                    $_SESSION['error'] = 'Failed to update business.';
                    $this->redirect("business/edit/$id");
                }
            } catch (\PDOException $e) {
                // Check if it's a duplicate entry error
                if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    if (strpos($e->getMessage(), 'registration_number') !== false) {
                        $_SESSION['error'] = 'A business with this registration number already exists. Please use a different registration number.';
                    } else {
                        $_SESSION['error'] = 'A business with this information already exists. Please check your details.';
                    }
                } else {
                    $_SESSION['error'] = 'Failed to update business. Please try again.';
                }
                $this->redirect("business/edit/$id");
            }

            return;
        }

        $categories = $this->businessModel->getCategories();
        $districts = $this->businessModel->getDistricts();
        $barangays = $this->businessModel->getBarangays();

        return $this->render('business/edit', [
            'title' => 'Edit Business',
            'active_page' => 'businesses',
            'business' => $business,
            'categories' => $categories,
            'districts' => $districts,
            'barangays' => $barangays,
            'user' => $user
        ]);
    }

    public function dashboard() {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $businesses = $this->businessModel->getByOwnerId($user['id']);
        $businessCount = count($businesses);

        $inspectionCount = $this->inspectionModel->countByOwnerId($user['id']);
        $upcomingInspections = $this->inspectionModel->countUpcomingByOwnerId($user['id']);
        $recentInspections = $this->inspectionModel->getRecentByOwnerId($user['id'], 5);

        // Get checklist evidence statistics
        $checklistEvidenceStats = [];
        $businessIds = array_column($businesses, 'id');

        if (!empty($businessIds)) {
            foreach ($businessIds as $businessId) {
                $stats = $this->businessEvidenceModel->getBusinessEvidenceStats($businessId);
                $checklistEvidenceStats[$businessId] = $stats;
            }
        }

        return $this->render('business/dashboard', [
            'title' => 'Business Dashboard',
            'active_page' => 'dashboard',
            'user' => $user,
            'businesses' => $businesses,
            'business_count' => $businessCount,
            'inspection_count' => $inspectionCount,
            'upcoming_inspections' => $upcomingInspections,
            'recent_inspections' => $recentInspections,
            'checklist_evidence_stats' => $checklistEvidenceStats
        ]);
    }

    public function inspections($businessId = null) {
        $this->auth->requireLogin();

        $user = $this->auth->getUser();

        if (!$businessId) {
            if ($this->auth->isAdmin()) {
                // Admin views all inspections
                $inspections = $this->inspectionModel->getAll();
                $viewPath = 'admin/inspections/index';
            } else {
                // Business owner views inspections for all their businesses
                $businesses = $this->businessModel->getByOwnerId($user['id']);
                $inspections = [];

                foreach ($businesses as $business) {
                    $businessInspections = $this->inspectionModel->getByBusiness($business['id']);
                    foreach ($businessInspections as &$inspection) {
                        $inspection['business_name'] = $business['name'];

                        // Add checklist information
                        $inspection['checklist_completion'] = $this->checklistModel->getInspectionCompletionStatus($inspection['id']);
                        $inspection['checklist_score'] = null;

                        if ($inspection['checklist_completion']['completed_items'] > 0) {
                            $inspection['checklist_score'] = $this->checklistModel->calculateInspectionScore($inspection['id']);
                        }
                    }
                    $inspections = array_merge($inspections, $businessInspections);
                }

                // Sort by scheduled date
                usort($inspections, function($a, $b) {
                    return strtotime($b['scheduled_date']) - strtotime($a['scheduled_date']);
                });

                $viewPath = 'business/inspections/index';
            }
        } else {
            // Check if the user has access to this business
            $business = $this->businessModel->getById($businessId);

            if (!$business) {
                $_SESSION['error'] = 'Business not found.';
                $this->redirect('business');
                return;
            }

            if (!$this->auth->isAdmin() && $business['owner_id'] !== $user['id']) {
                $_SESSION['error'] = 'You do not have permission to view this business.';
                $this->redirect('business');
                return;
            }

            $inspections = $this->inspectionModel->getByBusiness($businessId);

            // Add checklist information to each inspection
            foreach ($inspections as &$inspection) {
                $inspection['checklist_completion'] = $this->checklistModel->getInspectionCompletionStatus($inspection['id']);
                $inspection['checklist_score'] = null;

                if ($inspection['checklist_completion']['completed_items'] > 0) {
                    $inspection['checklist_score'] = $this->checklistModel->calculateInspectionScore($inspection['id']);
                }
            }

            if ($this->auth->isAdmin()) {
                $viewPath = 'admin/inspections/business';
            } else {
                $viewPath = 'business/inspections/business';
            }
        }

        return $this->render($viewPath, [
            'title' => 'Inspections',
            'active_page' => 'inspections',
            'inspections' => $inspections,
            'business' => $business ?? null,
            'user' => $user
        ]);
    }

    /**
     * View individual inspection details for business owner
     */
    public function viewInspection($id) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $inspection = $this->inspectionModel->getById($id);

        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('business/inspections');
            return;
        }

        // Check if user owns the business being inspected
        $business = $this->businessModel->getById($inspection['business_id']);
        if (!$business || $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to view this inspection.';
            $this->redirect('business/inspections');
            return;
        }

        // Get checklist information
        $checklistResponses = $this->checklistModel->getInspectionChecklistResponses($id);
        $inspectionScore = null;
        $completionStatus = $this->checklistModel->getInspectionCompletionStatus($id);

        if ($completionStatus['completed_items'] > 0) {
            $inspectionScore = $this->checklistModel->calculateInspectionScore($id);
        }

        return $this->render('business/inspection_details', [
            'title' => 'Inspection Details',
            'active_page' => 'inspections',
            'inspection' => $inspection,
            'business' => $business,
            'checklist_responses' => $checklistResponses,
            'inspection_score' => $inspectionScore,
            'completion_status' => $completionStatus,
            'user' => $user
        ]);
    }

    /**
     * View inspection report for business owner
     */
    public function inspectionReport($id) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $inspection = $this->inspectionModel->getById($id);

        if (!$inspection) {
            $_SESSION['error'] = 'Inspection not found.';
            $this->redirect('business/inspections');
            return;
        }

        // Check if user owns the business being inspected
        $business = $this->businessModel->getById($inspection['business_id']);
        if (!$business || $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to view this inspection.';
            $this->redirect('business/inspections');
            return;
        }

        // Get checklist responses and score
        $checklistResponses = $this->checklistModel->getInspectionChecklistResponses($id);
        $inspectionScore = $this->checklistModel->calculateInspectionScore($id);
        $completionStatus = $this->checklistModel->getInspectionCompletionStatus($id);

        if (!$inspectionScore) {
            $_SESSION['error'] = 'Inspection report is not available yet.';
            $this->redirect('business/inspections');
            return;
        }

        // Group responses by category
        $responsesByCategory = [];
        foreach ($checklistResponses as $response) {
            $categoryName = $response['category_name'];
            if (!isset($responsesByCategory[$categoryName])) {
                $responsesByCategory[$categoryName] = [];
            }
            $responsesByCategory[$categoryName][] = $response;
        }

        return $this->render('business/inspection_report', [
            'title' => 'Inspection Report - ' . $business['name'],
            'active_page' => 'inspections',
            'inspection' => $inspection,
            'business' => $business,
            'checklist_responses' => $checklistResponses,
            'responses_by_category' => $responsesByCategory,
            'inspection_score' => $inspectionScore,
            'completion_status' => $completionStatus,
            'user' => $user
        ]);
    }

    public function settings() {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();

        return $this->render('business/settings', [
            'title' => 'Settings',
            'active_page' => 'settings',
            'user' => $user
        ]);
    }

    public function updateSettings() {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        if ($this->isPost()) {
            $userModel = new \App\Models\User();
            $userId = $this->auth->getUserId();

            $full_name = $_POST['full_name'] ?? '';
            $email = $_POST['email'] ?? '';
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';

            $user = $userModel->find($userId);

            if (!$user) {
                $_SESSION['error'] = 'User not found.';
                $this->redirect('business/settings');
                return;
            }

            // Updating password
            if (!empty($current_password) && !empty($new_password)) {
                if ($new_password !== $confirm_password) {
                    $_SESSION['error'] = 'New passwords do not match.';
                    $this->redirect('business/settings');
                    return;
                }

                if (!password_verify($current_password, $user['password_hash'])) {
                    $_SESSION['error'] = 'Current password is incorrect.';
                    $this->redirect('business/settings');
                    return;
                }

                $data = [
                    'full_name' => $full_name,
                    'email' => $email,
                    'password' => password_hash($new_password, PASSWORD_DEFAULT)
                ];
            } else {
                $data = [
                    'full_name' => $full_name,
                    'email' => $email
                ];
            }

            if ($userModel->update($userId, $data)) {
                $_SESSION['success'] = 'Settings updated successfully.';
                $this->redirect('business/settings');
            } else {
                $_SESSION['error'] = 'Failed to update settings.';
                $this->redirect('business/settings');
            }
        } else {
            $this->redirect('business/settings');
        }
    }

    // New method to handle compliance evidence submission
    public function submitComplianceEvidence($businessId) {
        $this->auth->requireLogin();
        $user = $this->auth->getUser();

        // Ensure the logged-in user is the owner of the business or an admin
        $business = $this->businessModel->getById($businessId);
        if (!$business || (!$this->auth->isAdmin() && $business['owner_id'] !== $user['id'])) {
            $_SESSION['error'] = 'You do not have permission to submit evidence for this business.';
            $this->redirect('business'); // Redirect to business list or dashboard
            return;
        }

        if ($this->isPost()) {
            $compliance_type = $_POST['compliance_type'] ?? '';
            $file = $_FILES['evidence_file'] ?? null;

            // Basic validation
            if (empty($compliance_type) || empty($file) || $file['error'] !== UPLOAD_ERR_OK) {
                $_SESSION['error'] = 'Please select a compliance type and upload a valid file.';
                $this->redirect("business/compliance/submit/{$businessId}"); // Redirect back to the submission form
                return;
            }

            // Define upload directory and file name
            $uploadDir = 'uploads/compliance_evidence/';
            // Ensure upload directory exists
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileName = uniqid() . '_' . basename($file['name']);
            $targetFilePath = $uploadDir . $fileName;
            $fileType = pathinfo($targetFilePath, PATHINFO_EXTENSION);

            // Allow certain file formats
            $allowTypes = array('jpg', 'png', 'jpeg', 'pdf');
            if (!in_array(strtolower($fileType), $allowTypes)) {
                $_SESSION['error'] = 'Sorry, only JPG, JPEG, PNG, & PDF files are allowed.';
                $this->redirect("business/compliance/submit/{$businessId}");
                return;
            }

            // Upload file to server
            if (move_uploaded_file($file['tmp_name'], $targetFilePath)) {
                // File uploaded successfully, now save evidence record to database
                $data = [
                    'business_id' => $businessId,
                    'compliance_type' => $compliance_type,
                    'photo_path' => $targetFilePath, // Store the file path
                ];

                if ($this->evidenceModel->create($data)) {
                    $_SESSION['success'] = 'Compliance evidence submitted successfully. It is now pending review.';
                    // Redirect to a page showing submitted evidence or business details
                    $this->redirect("business/view/{$businessId}");
                } else {
                    // File uploaded but database insertion failed
                    // Consider deleting the uploaded file here to prevent orphaned files
                    unlink($targetFilePath);
                    $_SESSION['error'] = 'Failed to record compliance evidence in the database.';
                    $this->redirect("business/compliance/submit/{$businessId}");
                }
            } else {
                $_SESSION['error'] = 'Sorry, there was an error uploading your file.';
                $this->redirect("business/compliance/submit/{$businessId}");
            }
            return;
        }

        // For GET request, show the submission form
        $complianceTypes = $this->evidenceModel->getComplianceTypes();

        return $this->render('business/compliance/submit', [
            'title' => 'Submit Compliance Evidence',
            'active_page' => 'businesses', // Or a new active page for compliance
            'business' => $business,
            'compliance_types' => $complianceTypes,
            'user' => $user
        ]);
    }

    public function profile() {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        return $this->render('business/profile', [
            'title' => 'My Profile',
            'active_page' => 'profile',
            'user' => $user
        ]);
    }

    public function updateProfile() {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        if (!$this->isPost()) {
            return $this->redirect('business/profile');
        }

        $userId = $this->auth->getUserId();
        $data = [
            'full_name' => $_POST['full_name'] ?? '',
            'email' => $_POST['email'] ?? ''
        ];

        // Validate required fields
        if (empty($data['full_name']) || empty($data['email'])) {
            $_SESSION['error'] = 'Full name and email are required.';
            return $this->redirect('business/profile');
        }

        // Update password if provided
        if (!empty($_POST['current_password']) && !empty($_POST['new_password'])) {
            $currentUser = $this->userModel->find($userId);

            if (!password_verify($_POST['current_password'], $currentUser['password_hash'])) {
                $_SESSION['error'] = 'Current password is incorrect.';
                return $this->redirect('business/profile');
            }

            if ($_POST['new_password'] !== $_POST['confirm_password']) {
                $_SESSION['error'] = 'New passwords do not match.';
                return $this->redirect('business/profile');
            }

            $data['password_hash'] = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
        }

        try {
            if ($this->userModel->update($userId, $data)) {
                $_SESSION['success'] = 'Profile updated successfully.';
            } else {
                $_SESSION['error'] = 'Failed to update profile.';
            }
        } catch (\Exception $e) {
            error_log("Error updating business owner profile: " . $e->getMessage());
            $_SESSION['error'] = 'An error occurred while updating your profile.';
        }

        return $this->redirect('business/profile');
    }

    public function view($id) {
        $this->auth->requireLogin();

        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('business');
            return;
        }

        $user = $this->auth->getUser();

        // Only owner or admin can view business details
        if (!$this->auth->isAdmin() && $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to view this business.';
            $this->redirect('business');
            return;
        }

        // Get business inspections
        $inspections = $this->inspectionModel->getByBusiness($id);

        // Get checklist statistics instead of old evidence
        $checklistStats = null;
        try {
            $checklistStats = $this->businessEvidenceModel->getBusinessEvidenceStats($id);
        } catch (Exception $e) {
            // If there's an error getting stats, just set to null
            $checklistStats = null;
        }

        return $this->render('business/view', [
            'title' => 'Business Details',
            'active_page' => 'businesses',
            'business' => $business,
            'inspections' => $inspections,
            'checklist_stats' => $checklistStats,
            'user' => $user
        ]);
    }

    public function delete($id) {
        $this->auth->requireLogin();

        if (!$this->isPost()) {
            $this->redirect('business');
            return;
        }

        $business = $this->businessModel->getById($id);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('business');
            return;
        }

        $user = $this->auth->getUser();

        // Only owner or admin can delete business
        if (!$this->auth->isAdmin() && $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to delete this business.';
            $this->redirect('business');
            return;
        }

        if ($this->businessModel->delete($id)) {
            $_SESSION['success'] = 'Business deleted successfully.';
        } else {
            $_SESSION['error'] = 'Failed to delete business.';
        }

        $this->redirect('business');
    }

    /**
     * Business owner checklist view
     */
    public function checklist($businessId) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $business = $this->businessModel->getById($businessId);

        if (!$business) {
            $_SESSION['error'] = 'Business not found.';
            $this->redirect('business');
            return;
        }

        // Check if user owns this business
        if ($business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'You do not have permission to access this business checklist.';
            $this->redirect('business');
            return;
        }

        // Get checklist categories and items with evidence status
        $checklistWithEvidence = $this->businessEvidenceModel->getChecklistWithEvidenceStatus($businessId);

        // Group by category
        $categorizedChecklist = [];
        foreach ($checklistWithEvidence as $item) {
            $categoryName = $item['category_name'];
            if (!isset($categorizedChecklist[$categoryName])) {
                $categorizedChecklist[$categoryName] = [
                    'name' => $categoryName,
                    'sort_order' => $item['category_sort'],
                    'items' => []
                ];
            }
            $categorizedChecklist[$categoryName]['items'][] = $item;
        }

        // Sort categories by sort_order
        uasort($categorizedChecklist, function($a, $b) {
            return $a['sort_order'] - $b['sort_order'];
        });

        // Get evidence statistics
        $evidenceStats = $this->businessEvidenceModel->getBusinessEvidenceStats($businessId);

        // Get current/latest inspection for this business to show inspector progress
        $inspectionModel = new \App\Models\Inspection();
        $inspections = $inspectionModel->getByBusiness($businessId);
        $currentInspection = null;
        $inspectorProgress = null;

        // Find the most recent active inspection (in_progress, scheduled, or completed)
        foreach ($inspections as $inspection) {
            if (in_array($inspection['status'], ['in_progress', 'scheduled', 'confirmed', 'completed'])) {
                $currentInspection = $inspection;
                break; // Get the first (most recent) active inspection
            }
        }

        // Get inspector checklist progress if there's an active inspection
        if ($currentInspection) {
            $inspectorProgress = $this->checklistModel->getInspectionCompletionStatus($currentInspection['id']);
        }

        return $this->render('business/checklist/index', [
            'title' => 'Inspection Checklist - ' . $business['name'],
            'active_page' => 'checklist',
            'business' => $business,
            'categorized_checklist' => $categorizedChecklist,
            'evidence_stats' => $evidenceStats,
            'current_inspection' => $currentInspection,
            'inspector_progress' => $inspectorProgress,
            'user' => $user
        ]);
    }

    /**
     * Upload evidence for checklist item
     */
    public function uploadEvidence($businessId, $checklistItemId) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $business = $this->businessModel->getById($businessId);

        if (!$business || $business['owner_id'] !== $user['id']) {
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        if (!isset($_FILES['evidence_file']) || $_FILES['evidence_file']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
            return;
        }

        $file = $_FILES['evidence_file'];
        $notes = $_POST['notes'] ?? '';

        // Validate file type and size
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        $maxSize = 10 * 1024 * 1024; // 10MB

        if (!in_array($file['type'], $allowedTypes)) {
            echo json_encode(['success' => false, 'message' => 'Invalid file type. Only images, PDF, and Word documents are allowed.']);
            return;
        }

        if ($file['size'] > $maxSize) {
            echo json_encode(['success' => false, 'message' => 'File size too large. Maximum 10MB allowed.']);
            return;
        }

        // Create upload directory if it doesn't exist
        $uploadDir = UPLOAD_PATH . '/checklist_evidence/' . $businessId . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate unique filename
        $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
        $filePath = $uploadDir . $fileName;

        // Store relative path for serve-evidence.php (without 'public/uploads/')
        $webPath = 'checklist_evidence/' . $businessId . '/' . $fileName;

        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            // Save to database
            $evidenceData = [
                'business_id' => $businessId,
                'checklist_item_id' => $checklistItemId,
                'file_path' => $webPath, // Use web-accessible path
                'file_name' => $file['name'],
                'file_type' => $file['type'],
                'file_size' => $file['size'],
                'notes' => $notes,
                'uploaded_by' => $user['id']
            ];

            if ($this->businessEvidenceModel->uploadEvidence($evidenceData)) {
                echo json_encode(['success' => true, 'message' => 'Evidence uploaded successfully']);
            } else {
                // Delete uploaded file if database save failed
                unlink($filePath);
                echo json_encode(['success' => false, 'message' => 'Failed to save evidence to database']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to upload file']);
        }
    }

    /**
     * View evidence for a checklist item
     */
    public function viewEvidence($businessId, $checklistItemId) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $business = $this->businessModel->getById($businessId);

        if (!$business || $business['owner_id'] !== $user['id']) {
            $_SESSION['error'] = 'Access denied';
            $this->redirect('business');
            return;
        }

        // Get evidence for this checklist item
        $evidence = $this->businessEvidenceModel->getByBusinessAndItem($businessId, $checklistItemId);

        // Get checklist item details
        $checklistItem = $this->checklistModel->getChecklistItem($checklistItemId);

        return $this->render('business/checklist/evidence', [
            'title' => 'Evidence - ' . $checklistItem['item_name'],
            'active_page' => 'checklist',
            'business' => $business,
            'checklist_item' => $checklistItem,
            'evidence' => $evidence,
            'user' => $user
        ]);
    }

    /**
     * Delete evidence
     */
    public function deleteEvidence($evidenceId) {
        $this->auth->requireLogin();
        $this->auth->requireBusinessOwner();

        $user = $this->auth->getUser();
        $evidence = $this->businessEvidenceModel->find($evidenceId);

        if (!$evidence) {
            echo json_encode(['success' => false, 'message' => 'Evidence not found']);
            return;
        }

        // Check if user owns the business
        $business = $this->businessModel->getById($evidence['business_id']);
        if (!$business || $business['owner_id'] !== $user['id']) {
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }

        if ($this->businessEvidenceModel->deleteEvidence($evidenceId)) {
            echo json_encode(['success' => true, 'message' => 'Evidence deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete evidence']);
        }
    }
}
