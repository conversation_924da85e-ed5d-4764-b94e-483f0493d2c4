<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus"></i> Create Checklist Item
        </h1>
        <a href="<?= BASE_URL ?>admin/checklist" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Checklist
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Item Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= BASE_URL ?>admin/checklist/items/create">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>">
                                                <?= htmlspecialchars($category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="item_code" class="form-label">Item Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="item_code" name="item_code" required 
                                           placeholder="e.g., SE001, BD002"
                                           pattern="[A-Z]{2}[0-9]{3}"
                                           title="Format: 2 letters + 3 numbers (e.g., SE001)">
                                    <div class="form-text">Format: 2 letters + 3 numbers</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="item_name" class="form-label">Item Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="item_name" name="item_name" required 
                                   placeholder="e.g., Fire Extinguisher, Business Permit">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="2"
                                      placeholder="Brief description of what this item checks..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="compliance_requirement" class="form-label">Compliance Requirement</label>
                            <textarea class="form-control" id="compliance_requirement" name="compliance_requirement" rows="3"
                                      placeholder="Detailed requirements for compliance..."></textarea>
                            <div class="form-text">Specific requirements that must be met for compliance</div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="points" class="form-label">Points</label>
                                    <input type="number" class="form-control" id="points" name="points" 
                                           step="0.1" min="0" max="10" value="1.0"
                                           placeholder="1.0">
                                    <div class="form-text">Points for compliance</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           min="0" value="0"
                                           placeholder="0">
                                    <div class="form-text">Display order</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_critical" name="is_critical">
                                        <label class="form-check-label" for="is_critical">
                                            Critical Item
                                        </label>
                                        <div class="form-text">Must be compliant</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            Active Item
                                        </label>
                                        <div class="form-text">Include in inspections</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= BASE_URL ?>admin/checklist" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Item
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Item Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Item Code</h6>
                    <p class="small text-muted mb-3">
                        Use a consistent format: 2 letters for category + 3 numbers.
                        Examples: SE001 (Safety), BD001 (Business Documentation), HS001 (Health & Sanitation)
                    </p>

                    <h6 class="text-primary">Item Name</h6>
                    <p class="small text-muted mb-3">
                        Clear, specific name for what is being inspected.
                        Examples: "Fire Extinguisher", "Business Permit", "First Aid Kit"
                    </p>

                    <h6 class="text-primary">Critical Items</h6>
                    <p class="small text-muted mb-3">
                        Items that are mandatory for compliance. Non-compliance with critical items 
                        may result in immediate action or penalties.
                    </p>

                    <h6 class="text-primary">Points</h6>
                    <p class="small text-muted">
                        Higher points for more important items. Critical safety items might be 5 points, 
                        while documentation might be 3 points.
                    </p>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle"></i> Common Item Codes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small text-muted">
                        <strong>BD</strong> - Business Documentation<br>
                        <strong>SE</strong> - Safety & Emergency<br>
                        <strong>HS</strong> - Health & Sanitation<br>
                        <strong>SM</strong> - Security & Monitoring<br>
                        <strong>EW</strong> - Environmental & Waste<br>
                        <strong>FI</strong> - Fire Safety<br>
                        <strong>OC</strong> - Occupational Safety
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate item code based on category selection
document.getElementById('category_id').addEventListener('change', function() {
    const categorySelect = this;
    const itemCodeInput = document.getElementById('item_code');
    
    if (categorySelect.value) {
        // Get category name and generate code prefix
        const categoryName = categorySelect.options[categorySelect.selectedIndex].text;
        let prefix = '';
        
        if (categoryName.includes('Documentation')) prefix = 'BD';
        else if (categoryName.includes('Safety') || categoryName.includes('Emergency')) prefix = 'SE';
        else if (categoryName.includes('Health') || categoryName.includes('Sanitation')) prefix = 'HS';
        else if (categoryName.includes('Security') || categoryName.includes('Monitoring')) prefix = 'SM';
        else if (categoryName.includes('Environmental') || categoryName.includes('Waste')) prefix = 'EW';
        else if (categoryName.includes('Fire')) prefix = 'FI';
        else prefix = categoryName.substring(0, 2).toUpperCase();
        
        // Suggest next number (this is just a placeholder - in real implementation, 
        // you'd check existing codes via AJAX)
        itemCodeInput.placeholder = prefix + '001 (suggested)';
    }
});
</script>

<?php $this->endSection(); ?>
