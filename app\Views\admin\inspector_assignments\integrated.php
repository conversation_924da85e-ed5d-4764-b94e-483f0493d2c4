<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 text-gray-800">
                <i class="fas fa-calendar-check me-2 text-primary"></i><?= $title ?>
            </h1>
            <p class="text-muted mb-0">Schedule → Inspect → Assign: Complete inspection management workflow</p>
        </div>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                <i class="fas fa-calendar-plus me-2"></i>Schedule Inspections
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Inspectors</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_inspectors'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Barangays</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_barangays'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Pending Inspections</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['pending_inspections'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Ongoing Inspections</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['ongoing_inspections'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content: Inspections Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-clipboard-list me-2"></i>All Inspections
            </h6>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshInspections()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="inspectionsTable">
                    <thead>
                        <tr>
                            <th>Business</th>
                            <th>Location</th>
                            <th>Inspector</th>
                            <th>Scheduled Date</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($inspections)): ?>
                            <?php foreach ($inspections as $inspection): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                            <strong><?= htmlspecialchars($inspection['barangay_name'] ?? 'N/A') ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($inspection['inspector_name']): ?>
                                            <div>
                                                <i class="fas fa-user-shield text-primary me-1"></i>
                                                <?= htmlspecialchars($inspection['inspector_name']) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Not assigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-calendar text-muted me-1"></i>
                                            <?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?>
                                            <br><small class="text-muted"><?= date('h:i A', strtotime($inspection['scheduled_date'])) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info-subtle text-info">
                                            <?= ucfirst($inspection['inspection_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClasses = [
                                            'scheduled' => 'warning',
                                            'in_progress' => 'primary',
                                            'completed' => 'success',
                                            'cancelled' => 'danger'
                                        ];
                                        $statusClass = $statusClasses[$inspection['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>-subtle text-<?= $statusClass ?>">
                                            <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>admin/inspections/view/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDeleteInspection('<?= $inspection['id'] ?>', '<?= htmlspecialchars($inspection['business_name']) ?>')"
                                                    title="Delete Inspection">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                        <h5>No Inspections Scheduled</h5>
                                        <p>Click "Schedule Inspections" to create new inspection schedules.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Inspections Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="scheduleModalLabel">
                    <i class="fas fa-calendar-plus me-2"></i>Schedule Barangay Inspections
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= BASE_URL ?>admin/inspector-assignments/schedule-barangay" method="POST" id="scheduleForm">
                <div class="modal-body">
                    <div class="row">
                        <!-- Left Column: Barangay Selection -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>Select Barangays
                            </h6>

                            <!-- District Filter -->
                            <div class="mb-3">
                                <label for="districtFilter" class="form-label">Filter by District</label>
                                <select class="form-select" id="districtFilter" onchange="filterBarangaysByDistrict()">
                                    <option value="">All Districts (<?= count($barangays) ?> barangays total)</option>
                                    <?php
                                    // Count barangays per district for the dropdown
                                    $districtBarangayCounts = [];
                                    foreach ($barangays as $barangay) {
                                        $districtId = $barangay['district_id'] ?? 'null';
                                        if (!isset($districtBarangayCounts[$districtId])) {
                                            $districtBarangayCounts[$districtId] = 0;
                                        }
                                        $districtBarangayCounts[$districtId]++;
                                    }
                                    ?>
                                    <?php foreach ($districts as $district): ?>
                                        <?php $count = $districtBarangayCounts[$district['id']] ?? 0; ?>
                                        <option value="<?= $district['id'] ?>"><?= htmlspecialchars($district['name']) ?> (<?= $count ?> barangays)</option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Quick Select Options -->
                            <div class="mb-3">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllVisibleBarangays()">
                                        <i class="fas fa-check-double me-1"></i>Select All Visible
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllBarangays()">
                                        <i class="fas fa-times me-1"></i>Clear All
                                    </button>
                                </div>
                            </div>

                            <!-- Barangay Multi-Select -->
                            <div class="mb-3">
                                <label class="form-label">
                                    Barangays <span class="text-danger">*</span>
                                    <span class="badge bg-secondary ms-2" id="selectedCount">0 selected</span>
                                </label>
                                <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;" id="barangayContainer">
                                    <?php if (!empty($barangays)): ?>
                                        <?php foreach ($barangays as $barangay): ?>
                                            <div class="form-check barangay-item mb-2" data-district="<?= $barangay['district_id'] ?? '' ?>">
                                                <input class="form-check-input barangay-checkbox" type="checkbox"
                                                       name="barangay_ids[]" value="<?= $barangay['id'] ?>"
                                                       id="barangay_<?= $barangay['id'] ?>"
                                                       onchange="updateSelectionCount()">
                                                <label class="form-check-label w-100" for="barangay_<?= $barangay['id'] ?>">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <strong><?= htmlspecialchars($barangay['name']) ?></strong>
                                                            <br><small class="text-muted"><?= htmlspecialchars($barangay['district_name'] ?? 'No District') ?></small>
                                                        </div>
                                                        <span class="badge bg-info business-count" data-barangay="<?= $barangay['id'] ?>">
                                                            <?= count($businesses_by_barangay[$barangay['id']] ?? []) ?> businesses
                                                        </span>
                                                    </div>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                                            <p class="text-muted mb-2"><strong>No barangays available</strong></p>
                                            <small class="text-muted">This could be due to:</small>
                                            <ul class="list-unstyled small text-muted mt-2">
                                                <li>• Database not properly configured</li>
                                                <li>• No districts or barangays created yet</li>
                                                <li>• Connection issues</li>
                                            </ul>
                                            <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="location.reload()">
                                                <i class="fas fa-refresh me-1"></i>Refresh Page
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Select multiple barangays to schedule inspections. Business count shown for each barangay.
                                </small>
                            </div>

                            <!-- Business Count Display -->
                            <div class="alert alert-info" id="businessCountAlert" style="display: none;">
                                <i class="fas fa-building me-2"></i>
                                <span id="businessCountText">0 businesses</span> will be scheduled for inspection
                            </div>
                        </div>

                        <!-- Right Column: Inspector Selection & Schedule Details -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user-shield me-2"></i>Select Inspectors & Schedule
                            </h6>

                            <!-- Inspector Multi-Select -->
                            <div class="mb-3">
                                <label class="form-label">
                                    Inspectors <span class="text-danger">*</span>
                                    <span class="badge bg-secondary ms-2" id="inspectorSelectedCount">0 selected</span>
                                </label>

                                <!-- Inspector Quick Select -->
                                <div class="mb-2">
                                    <div class="btn-group w-100" role="group">
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="selectAllInspectors()">
                                            <i class="fas fa-user-check me-1"></i>Select All
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllInspectors()">
                                            <i class="fas fa-user-times me-1"></i>Clear All
                                        </button>
                                    </div>
                                </div>

                                <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    <?php if (!empty($inspectors)): ?>
                                        <?php foreach ($inspectors as $inspector): ?>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input inspector-checkbox" type="checkbox"
                                                       name="inspector_ids[]" value="<?= $inspector['id'] ?>"
                                                       id="inspector_<?= $inspector['id'] ?>"
                                                       onchange="updateInspectorCount()">
                                                <label class="form-check-label w-100" for="inspector_<?= $inspector['id'] ?>">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <strong><?= htmlspecialchars($inspector['full_name']) ?></strong>
                                                            <br><small class="text-muted"><?= htmlspecialchars($inspector['email']) ?></small>
                                                        </div>
                                                        <span class="badge bg-success availability-badge" id="availability_<?= $inspector['id'] ?>">Available</span>
                                                    </div>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="fas fa-user-slash text-warning fa-2x mb-2"></i>
                                            <p class="text-muted mb-0">No inspectors available</p>
                                            <small class="text-muted">Please create inspector accounts first</small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Select multiple inspectors to distribute workload evenly across selected barangays
                                </small>
                            </div>

                            <!-- Schedule Details -->
                            <div class="mb-3">
                                <label for="scheduled_date" class="form-label">Scheduled Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="scheduled_date" name="scheduled_date"
                                       min="<?= date('Y-m-d') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="inspection_type" class="form-label">Inspection Type</label>
                                <select class="form-select" id="inspection_type" name="inspection_type">
                                    <option value="routine">Routine Inspection</option>
                                    <option value="follow_up">Follow-up Inspection</option>
                                    <option value="complaint">Complaint Investigation</option>
                                    <option value="special">Special Inspection</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="Additional notes for the inspection..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Conflict Warning -->
                    <div class="alert alert-warning" id="conflictWarning" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Potential Conflicts:</strong>
                        <ul id="conflictList" class="mb-0 mt-2"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-info me-2" onclick="checkConflicts()">
                        <i class="fas fa-search me-1"></i>Check Conflicts
                    </button>
                    <button type="submit" class="btn btn-primary" id="scheduleSubmitBtn">
                        <i class="fas fa-calendar-plus me-1"></i>Schedule Inspections
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteInspectionModal" tabindex="-1" aria-labelledby="deleteInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteInspectionModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                    <h5>Are you sure you want to delete this inspection?</h5>
                    <p class="text-muted mb-3">This action cannot be undone. The inspection assignment will be permanently removed.</p>
                    <div class="alert alert-warning">
                        <strong>Business:</strong> <span id="deleteBusinessName"></span><br>
                        <strong>Action:</strong> Complete removal of inspection assignment
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>Yes, Delete Inspection
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styles for the scheduling modal */
.barangay-item:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
}

.barangay-item .form-check-input:checked + .form-check-label {
    background-color: #e3f2fd;
    border-radius: 4px;
    padding: 4px;
}

.business-count {
    font-size: 0.75rem;
    min-width: 60px;
}

.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
}

.step-item.active .step-circle {
    background-color: #0d6efd;
}

.step-label {
    font-size: 0.875rem;
    text-align: center;
    color: #6c757d;
}

.step-item.active .step-label {
    color: #0d6efd;
    font-weight: 600;
}

.step-line {
    height: 2px;
    background-color: #dee2e6;
    flex: 1;
    margin: 0 10px;
    margin-top: 15px;
}

.modal-xl {
    max-width: 1200px;
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 10px auto;
    }

    .step-item {
        font-size: 0.8rem;
    }

    .step-circle {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }
}
</style>

<script>
// Store data for JavaScript use
const barangaysData = <?= json_encode($barangays) ?>;
const businessesByBarangay = <?= json_encode($businesses_by_barangay) ?>;
const inspectorsData = <?= json_encode($inspectors) ?>;

// Filter barangays by district
function filterBarangaysByDistrict() {
    const districtId = document.getElementById('districtFilter').value;
    const barangayItems = document.querySelectorAll('.barangay-item');
    let visibleCount = 0;

    barangayItems.forEach(item => {
        const itemDistrictId = item.dataset.district;

        // Show item if no filter selected OR if district matches
        // Handle both string and numeric comparisons
        if (!districtId || itemDistrictId === districtId || itemDistrictId == districtId) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
            // Uncheck hidden items
            const checkbox = item.querySelector('.barangay-checkbox');
            if (checkbox) {
                checkbox.checked = false;
            }
        }
    });

    updateSelectionCount();

    // Update district filter feedback
    const districtFilter = document.getElementById('districtFilter');
    if (districtId) {
        const selectedOption = districtFilter.options[districtFilter.selectedIndex];
        console.log(`Filtered to ${selectedOption.text}: ${visibleCount} barangays visible`);

        // Update the filter option text to show count
        if (visibleCount > 0) {
            selectedOption.textContent = selectedOption.textContent.split(' (')[0] + ` (${visibleCount} barangays)`;
        }
    } else {
        // Reset all option texts when showing all
        Array.from(districtFilter.options).forEach((option, index) => {
            if (index > 0) { // Skip "All Districts" option
                option.textContent = option.textContent.split(' (')[0];
            }
        });
    }
}

// Update selection count and business count
function updateSelectionCount() {
    const selectedBarangays = document.querySelectorAll('.barangay-checkbox:checked');
    const selectedCount = document.getElementById('selectedCount');
    const businessCountAlert = document.getElementById('businessCountAlert');
    const businessCountText = document.getElementById('businessCountText');

    let totalBusinesses = 0;
    selectedBarangays.forEach(checkbox => {
        const barangayId = checkbox.value;
        const businesses = businessesByBarangay[barangayId] || [];
        totalBusinesses += businesses.length;
    });

    // Update selection count badge
    selectedCount.textContent = `${selectedBarangays.length} selected`;
    selectedCount.className = selectedBarangays.length > 0 ? 'badge bg-primary ms-2' : 'badge bg-secondary ms-2';

    // Update business count alert
    if (totalBusinesses > 0) {
        businessCountText.textContent = `${totalBusinesses} business${totalBusinesses !== 1 ? 'es' : ''}`;
        businessCountAlert.style.display = 'block';
    } else {
        businessCountAlert.style.display = 'none';
    }
}

// Select all visible barangays
function selectAllVisibleBarangays() {
    const visibleCheckboxes = document.querySelectorAll('.barangay-item:not([style*="display: none"]) .barangay-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectionCount();
}

// Clear all barangay selections
function clearAllBarangays() {
    const allCheckboxes = document.querySelectorAll('.barangay-checkbox');
    allCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectionCount();
}

// Legacy function for backward compatibility
function updateBusinessCount() {
    updateSelectionCount();
}

// Inspector selection functions
function updateInspectorCount() {
    const selectedInspectors = document.querySelectorAll('.inspector-checkbox:checked');
    const inspectorSelectedCount = document.getElementById('inspectorSelectedCount');

    inspectorSelectedCount.textContent = `${selectedInspectors.length} selected`;
    inspectorSelectedCount.className = selectedInspectors.length > 0 ? 'badge bg-success ms-2' : 'badge bg-secondary ms-2';
}

function selectAllInspectors() {
    const allInspectorCheckboxes = document.querySelectorAll('.inspector-checkbox');
    allInspectorCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateInspectorCount();
}

function clearAllInspectors() {
    const allInspectorCheckboxes = document.querySelectorAll('.inspector-checkbox');
    allInspectorCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateInspectorCount();
}

// Check for scheduling conflicts
function checkConflicts() {
    const selectedBarangays = Array.from(document.querySelectorAll('.barangay-checkbox:checked')).map(cb => cb.value);
    const selectedInspectors = Array.from(document.querySelectorAll('.inspector-checkbox:checked')).map(cb => cb.value);
    const scheduledDate = document.getElementById('scheduled_date').value;

    if (!selectedBarangays.length || !selectedInspectors.length || !scheduledDate) {
        alert('Please select barangays, inspectors, and a date before checking conflicts.');
        return;
    }

    // Show loading state
    const conflictWarning = document.getElementById('conflictWarning');
    const conflictList = document.getElementById('conflictList');

    conflictWarning.style.display = 'block';
    conflictList.innerHTML = '<li><i class="fas fa-spinner fa-spin me-2"></i>Checking for conflicts...</li>';

    // Check conflicts for each inspector
    const conflictPromises = selectedInspectors.map(inspectorId => {
        const formData = new FormData();
        formData.append('inspector_id', inspectorId);
        formData.append('scheduled_date', scheduledDate);

        return fetch('<?= BASE_URL ?>admin/api/check-conflicts', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.conflicts && data.conflicts.length > 0) {
                return data.conflicts;
            }
            return [];
        })
        .catch(error => {
            console.error('Error checking conflicts for inspector:', inspectorId, error);
            return [];
        });
    });

    // Wait for all conflict checks to complete
    Promise.all(conflictPromises).then(results => {
        const allConflicts = results.flat();

        if (allConflicts.length === 0) {
            conflictList.innerHTML = '<li class="text-success"><i class="fas fa-check-circle me-2"></i>No conflicts detected! Safe to proceed with scheduling.</li>';
            conflictWarning.className = 'alert alert-success';
        } else {
            const conflictItems = allConflicts.map(conflict =>
                `<li class="text-warning"><i class="fas fa-exclamation-triangle me-2"></i>${conflict.message}</li>`
            ).join('');
            conflictList.innerHTML = conflictItems;
            conflictWarning.className = 'alert alert-warning';
        }
    }).catch(error => {
        console.error('Error checking conflicts:', error);
        conflictList.innerHTML = '<li class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Error checking conflicts. Please try again.</li>';
        conflictWarning.className = 'alert alert-danger';
    });
}

// Refresh inspections table
function refreshInspections() {
    location.reload();
}

// Enhanced form validation with better user feedback
document.getElementById('scheduleForm').addEventListener('submit', function(e) {
    const selectedBarangays = document.querySelectorAll('.barangay-checkbox:checked');
    const selectedInspectors = document.querySelectorAll('.inspector-checkbox:checked');
    const scheduledDate = document.getElementById('scheduled_date').value;

    // Clear any previous error highlights
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

    let hasErrors = false;
    let errorMessages = [];

    if (selectedBarangays.length === 0) {
        hasErrors = true;
        errorMessages.push('Please select at least one barangay');
        document.getElementById('barangayContainer').classList.add('is-invalid');
    }

    if (selectedInspectors.length === 0) {
        hasErrors = true;
        errorMessages.push('Please select at least one inspector');
        document.querySelector('.inspector-checkbox').closest('.border').classList.add('is-invalid');
    }

    if (!scheduledDate) {
        hasErrors = true;
        errorMessages.push('Please select a scheduled date');
        document.getElementById('scheduled_date').classList.add('is-invalid');
    } else {
        // Check if date is not in the past
        const selectedDateObj = new Date(scheduledDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDateObj < today) {
            hasErrors = true;
            errorMessages.push('Scheduled date cannot be in the past');
            document.getElementById('scheduled_date').classList.add('is-invalid');
        }
    }

    if (hasErrors) {
        e.preventDefault();

        // Show error message with details
        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    ${errorMessages.map(msg => `<li>${msg}</li>`).join('')}
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Insert error message at the top of modal body
        const modalBody = document.querySelector('#scheduleModal .modal-body');
        const existingAlert = modalBody.querySelector('.alert-danger');
        if (existingAlert) {
            existingAlert.remove();
        }
        modalBody.insertAdjacentHTML('afterbegin', errorHtml);

        return;
    }

    // Check for conflicts before submitting
    const conflictWarning = document.getElementById('conflictWarning');
    const hasVisibleConflicts = conflictWarning.style.display !== 'none' &&
                               conflictWarning.classList.contains('alert-warning');

    // Show confirmation before submitting
    const totalBusinesses = Array.from(selectedBarangays).reduce((total, checkbox) => {
        const barangayId = checkbox.value;
        const businesses = businessesByBarangay[barangayId] || [];
        return total + businesses.length;
    }, 0);

    let confirmMessage = `
        You are about to schedule inspections for:
        • ${selectedBarangays.length} barangay(s)
        • ${totalBusinesses} business(es)
        • ${selectedInspectors.length} inspector(s)
        • Date: ${scheduledDate}
    `;

    if (hasVisibleConflicts) {
        confirmMessage += `

        ⚠️  WARNING: Scheduling conflicts detected!
        Some inspectors may already be busy on this date.
        `;
    }

    confirmMessage += `

        Continue with scheduling?
    `;

    if (!confirm(confirmMessage)) {
        e.preventDefault();
        return;
    }

    // Show loading state
    const submitBtn = document.getElementById('scheduleSubmitBtn');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Scheduling...';
    submitBtn.disabled = true;
});

// Initialize modal when opened
document.getElementById('scheduleModal').addEventListener('shown.bs.modal', function() {
    // Reset form state
    clearAllBarangays();
    clearAllInspectors();
    document.getElementById('scheduled_date').value = '';
    document.getElementById('districtFilter').value = '';

    // Clear any error states
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    document.querySelectorAll('.alert-danger').forEach(el => el.remove());

    // Hide conflict warning
    document.getElementById('conflictWarning').style.display = 'none';

    // Reset submit button
    const submitBtn = document.getElementById('scheduleSubmitBtn');
    submitBtn.innerHTML = '<i class="fas fa-calendar-plus me-1"></i>Schedule Inspections';
    submitBtn.disabled = false;

    // Show all barangays initially
    filterBarangaysByDistrict();

    console.log('Schedule modal initialized with', barangaysData.length, 'barangays and', inspectorsData.length, 'inspectors');
});

// Auto-check conflicts when date or inspectors change
document.getElementById('scheduled_date').addEventListener('change', function() {
    const selectedInspectors = document.querySelectorAll('.inspector-checkbox:checked');
    if (selectedInspectors.length > 0 && this.value) {
        setTimeout(checkConflicts, 500); // Small delay to avoid too many requests
    }

    // Update inspector availability status
    updateInspectorAvailability(this.value);
});

// Update inspector availability status for a given date
function updateInspectorAvailability(scheduledDate) {
    if (!scheduledDate) {
        // Reset all to available if no date selected
        document.querySelectorAll('.availability-badge').forEach(badge => {
            badge.textContent = 'Available';
            badge.className = 'badge bg-success availability-badge';
        });
        return;
    }

    // Check availability for each inspector
    inspectorsData.forEach(inspector => {
        const formData = new FormData();
        formData.append('inspector_id', inspector.id);
        formData.append('scheduled_date', scheduledDate);

        fetch('<?= BASE_URL ?>admin/api/check-conflicts', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById(`availability_${inspector.id}`);
            if (badge) {
                if (data.hasConflicts) {
                    const inspectorConflicts = data.conflicts.filter(c => c.type === 'inspector');
                    if (inspectorConflicts.length > 0) {
                        badge.textContent = `Busy (${inspectorConflicts[0].count} inspection${inspectorConflicts[0].count !== 1 ? 's' : ''})`;
                        badge.className = 'badge bg-warning availability-badge';
                    } else {
                        badge.textContent = 'Available';
                        badge.className = 'badge bg-success availability-badge';
                    }
                } else {
                    badge.textContent = 'Available';
                    badge.className = 'badge bg-success availability-badge';
                }
            }
        })
        .catch(error => {
            console.error('Error checking availability for inspector:', inspector.id, error);
        });
    });
}

// Add event listener to inspector checkboxes for auto-conflict checking
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('inspector-checkbox')) {
        const scheduledDate = document.getElementById('scheduled_date').value;
        const selectedInspectors = document.querySelectorAll('.inspector-checkbox:checked');

        if (scheduledDate && selectedInspectors.length > 0) {
            setTimeout(checkConflicts, 500); // Small delay to avoid too many requests
        }
    }
});

// Delete inspection functionality
let inspectionToDelete = null;

function confirmDeleteInspection(inspectionId, businessName) {
    inspectionToDelete = inspectionId;
    document.getElementById('deleteBusinessName').textContent = businessName;

    const modal = new bootstrap.Modal(document.getElementById('deleteInspectionModal'));
    modal.show();
}

// Handle delete confirmation
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (!inspectionToDelete) return;

    // Show loading state
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';
    this.disabled = true;

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= BASE_URL ?>admin/inspections/delete/' + inspectionToDelete;

    // Add CSRF token if needed
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_method';
    csrfInput.value = 'DELETE';
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
});

// Initialize DataTable for inspections
$(document).ready(function() {
    $('#inspectionsTable').DataTable({
        order: [[3, 'desc']], // Sort by scheduled date
        pageLength: 25,
        language: {
            search: '<i class="fas fa-search"></i>',
            searchPlaceholder: 'Search inspections...'
        },
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting for actions column
        ]
    });

    // Initialize counts on page load
    updateSelectionCount();
    updateInspectorCount();
});
</script>

<?php $this->endSection(); ?>
