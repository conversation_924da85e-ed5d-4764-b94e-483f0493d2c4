<?php
namespace App\Models;

use App\Core\Model;
use PDO;

class Inspection extends Model {
    protected $table = 'inspections';
    protected $fillable = [
        'id', 'business_id', 'inspector_id', 'scheduled_date', 'scheduled_time',
        'notes', 'status', 'type', 'completed_date', 'score', 'findings',
        'recommendations', 'compliance_rating', 'admin_verified', 'verification_status',
        'verified_by', 'verified_at', 'verification_notes', 'created_at', 'updated_at'
    ];

    public function __construct() {
        parent::__construct();
    }

    /* ==================== CRUD Operations ==================== */

    public function all($orderBy = null, $direction = 'DESC') {
        return $this->getAll();
    }

    public function getAll() {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email,
                        v.full_name as verified_by_name
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN users v ON i.verified_by = v.id
                 ORDER BY i.scheduled_date DESC";
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getById($id) {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email,
                        brg.name as barangay_name,
                        d.name as district_name,
                        d.id as district_id
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 WHERE i.id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create(array $data) {
        // Set default values
        if (!isset($data['status'])) {
            $data['status'] = 'scheduled';
        }
        if (!isset($data['inspection_type'])) {
            $data['inspection_type'] = 'routine';
        }
        if (!isset($data['priority'])) {
            $data['priority'] = 'medium';
        }

        return parent::create($data);
    }

    // generateUUID() method inherited from base Model class

    public function update($id, array $data) {
        // Build dynamic update query based on provided data
        $setParts = [];
        $params = [':id' => $id];

        $allowedFields = ['business_id', 'inspector_id', 'scheduled_date', 'scheduled_time', 'notes', 'status', 'type'];

        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $setParts[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }

        if (empty($setParts)) {
            return false; // No valid fields to update
        }

        $setParts[] = "updated_at = CURRENT_TIMESTAMP";

        $query = "UPDATE inspections SET " . implode(', ', $setParts) . " WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM inspections WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $id]);
    }

    /* ==================== Business-Specific Methods ==================== */

    public function getByBusiness($businessId) {
        $query = "SELECT i.*,
                        u.full_name as inspector_name,
                        u.email as inspector_email,
                        v.full_name as verified_by_name
                 FROM inspections i
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN users v ON i.verified_by = v.id
                 WHERE i.business_id = :business_id
                 ORDER BY i.scheduled_date DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':business_id' => $businessId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /* ==================== Inspector-Specific Methods ==================== */

    public function getByInspector($inspectorId, $status = null) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email,
                        brg.name as barangay_name, d.name as district_name, d.id as district_id
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 WHERE i.inspector_id = :inspector_id";

        $params = [':inspector_id' => $inspectorId];

        if ($status) {
            if (is_array($status)) {
                // Use named parameters for array of statuses
                $placeholders = [];
                foreach ($status as $key => $value) {
                    $paramName = ":status_$key";
                    $placeholders[] = $paramName;
                    $params[$paramName] = $value;
                }
                $query .= " AND i.status IN (" . implode(',', $placeholders) . ")";
            } else {
                $query .= " AND i.status = :status";
                $params[':status'] = $status;
            }
        }

        $query .= " ORDER BY i.scheduled_date ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get inspections for inspector filtered by their assigned districts only
     */
    public function getByInspectorInAssignedDistricts($inspectorId, $status = null) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email,
                        brg.name as barangay_name, d.name as district_name, iba.assigned_at as barangay_assigned_at
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 INNER JOIN inspector_barangay_assignments iba ON (
                     iba.inspector_id = :inspector_id
                     AND iba.barangay_id = brg.id
                     AND iba.status = 'active'
                 )
                 WHERE i.inspector_id = :inspector_id";

        $params = [':inspector_id' => $inspectorId];

        if ($status) {
            if (is_array($status)) {
                // Use named parameters for array of statuses
                $placeholders = [];
                foreach ($status as $key => $value) {
                    $paramName = ":status_$key";
                    $placeholders[] = $paramName;
                    $params[$paramName] = $value;
                }
                $query .= " AND i.status IN (" . implode(',', $placeholders) . ")";
            } else {
                $query .= " AND i.status = :status";
                $params[':status'] = $status;
            }
        }

        $query .= " ORDER BY i.scheduled_date ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getByInspectorAndStatus($inspectorId, $statuses) {
        if (!is_array($statuses)) {
            $statuses = [$statuses];
        }

        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.inspector_id = :inspector_id
                 AND i.status IN (";

        // Create named parameters for each status
        $params = [':inspector_id' => $inspectorId];
        $statusParams = [];
        foreach ($statuses as $key => $status) {
            $paramName = ":status_$key";
            $statusParams[] = $paramName;
            $params[$paramName] = $status;
        }

        $query .= implode(',', $statusParams) . ") ORDER BY i.scheduled_date ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getUpcomingByInspector($inspectorId, $limit = null) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email,
                        brg.name as barangay_name, d.name as district_name
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 WHERE i.inspector_id = :inspector_id
                 AND i.status = 'scheduled'
                 AND i.scheduled_date > NOW()
                 ORDER BY i.scheduled_date ASC";

        if ($limit) {
            $query .= " LIMIT :limit";
        }

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':inspector_id', $inspectorId);

        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get upcoming inspections for inspector in their assigned districts only
     */
    public function getUpcomingByInspectorInAssignedDistricts($inspectorId, $limit = null) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email,
                        brg.name as barangay_name, d.name as district_name, iba.assigned_at as barangay_assigned_at
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 INNER JOIN inspector_barangay_assignments iba ON (
                     iba.inspector_id = :inspector_id
                     AND iba.barangay_id = brg.id
                     AND iba.status = 'active'
                 )
                 WHERE i.inspector_id = :inspector_id
                 AND i.status = 'scheduled'
                 AND i.scheduled_date > NOW()
                 ORDER BY i.scheduled_date ASC";

        if ($limit) {
            $query .= " LIMIT :limit";
        }

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':inspector_id', $inspectorId);

        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getRecentByInspector($inspectorId, $limit = 5) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.inspector_id = :inspector_id
                 ORDER BY i.completed_date DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':inspector_id', $inspectorId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getRecentCompleted($limit = 10) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.status = 'completed'
                 ORDER BY i.completed_date DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /* ==================== Count Methods ==================== */

    public function countAll() {
        $query = "SELECT COUNT(*) FROM inspections";
        return $this->db->query($query)->fetchColumn();
    }

    public function countByStatus($status) {
        $query = "SELECT COUNT(*) FROM inspections WHERE status = :status";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':status' => $status]);
        return $stmt->fetchColumn();
    }

    public function countApproved() {
        $query = "SELECT COUNT(*) FROM inspections WHERE status = 'completed' AND verification_status = 'approved'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchColumn();
    }

    public function countByInspector($inspectorId, $status = null) {
        $query = "SELECT COUNT(*) as total FROM inspections WHERE inspector_id = :inspector_id";
        $params = [':inspector_id' => $inspectorId];

        if ($status) {
            if (is_array($status)) {
                // Handle array of statuses with named parameters
                $placeholders = [];
                foreach ($status as $key => $value) {
                    $paramName = ":status_$key";
                    $placeholders[] = $paramName;
                    $params[$paramName] = $value;
                }
                $query .= " AND status IN (" . implode(',', $placeholders) . ")";
            } else {
                // Handle single status
                $query .= " AND status = :status";
                $params[':status'] = $status;
            }
        }

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchColumn();
    }

    public function countUpcoming($days = 7) {
        $query = "SELECT COUNT(*) FROM inspections
                 WHERE status = 'scheduled'
                 AND scheduled_date > NOW()
                 AND scheduled_date <= DATE_ADD(NOW(), INTERVAL :days DAY)";
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':days', $days, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchColumn();
    }

    public function getUpcomingInspections($limit = null) {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.status IN ('scheduled', 'confirmed')
                 AND i.scheduled_date >= CURDATE()
                 ORDER BY i.scheduled_date ASC";

        if ($limit) {
            $query .= " LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
        } else {
            $stmt = $this->db->query($query);
        }

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function countByOwnerId($ownerId) {
        $query = "SELECT COUNT(*) FROM inspections i
                 JOIN businesses b ON i.business_id = b.id
                 WHERE b.owner_id = :owner_id";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':owner_id' => $ownerId]);
        return $stmt->fetchColumn();
    }

    public function countUpcomingByOwnerId($ownerId) {
        $query = "SELECT COUNT(*) FROM inspections i
                 JOIN businesses b ON i.business_id = b.id
                 WHERE b.owner_id = :owner_id
                 AND i.status IN ('scheduled', 'confirmed')
                 AND i.scheduled_date >= CURDATE()";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':owner_id' => $ownerId]);
        return $stmt->fetchColumn();
    }

    public function getRecentByOwnerId($ownerId, $limit = 5) {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email
                 FROM inspections i
                 JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE b.owner_id = :owner_id
                 ORDER BY i.scheduled_date DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':owner_id', $ownerId);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /* ==================== Specialized Methods ==================== */

    public function getAvailableInspectors() {
        $query = "SELECT id, full_name, email
                 FROM users
                 WHERE role = 'inspector' AND status = 'active'
                 ORDER BY full_name ASC";
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function completeInspection($id, $data) {
        $query = "UPDATE inspections SET
                  status = 'completed',
                  completed_date = CURRENT_DATE(),
                  score = :score,
                  findings = :findings,
                  recommendations = :recommendations,
                  compliance_rating = :compliance_rating,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        $stmt = $this->db->prepare($query);

        $params = [
            ':id' => $id,
            ':score' => $data['score'] ?? null,
            ':findings' => $data['findings'] ?? null,
            ':recommendations' => $data['recommendations'] ?? null,
            ':compliance_rating' => $data['compliance_rating'] ?? null
        ];



        $result = $stmt->execute($params);

        if ($result) {
            // Update the business's last inspection date and next inspection date
            $inspection = $this->getById($id);
            if ($inspection) {
                $businessModel = new Business();
                $nextInspectionDate = date('Y-m-d', strtotime('+6 months'));
                $businessModel->updateInspectionDates(
                    $inspection['business_id'],
                    $inspection['completed_date'],
                    $nextInspectionDate
                );
            }
        }

        return $result;
    }

    public function getComplianceRatings() {
        return [
            'A' => 'Excellent (90-100%)',
            'B' => 'Good (80-89%)',
            'C' => 'Satisfactory (70-79%)',
            'D' => 'Needs Improvement (60-69%)',
            'F' => 'Non-compliant (<60%)'
        ];
    }

    /* ==================== Utility Methods ==================== */

    public function updateStatus($id, $status) {
        $query = "UPDATE inspections SET
                  status = :status,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':status' => $status
        ]);
    }

    /* ==================== Verification Methods ==================== */

    public function verifyInspection($id, $verifierId, $status = 'approved', $notes = '') {
        $adminVerified = ($status === 'approved') ? 1 : 0;

        $query = "UPDATE inspections SET
                  admin_verified = :admin_verified,
                  verification_status = :verification_status,
                  verified_by = :verified_by,
                  verified_at = NOW(),
                  verification_notes = :verification_notes,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':id' => $id,
            ':admin_verified' => $adminVerified,
            ':verification_status' => $status,
            ':verified_by' => $verifierId,
            ':verification_notes' => $notes
        ]);
    }

    public function unverifyInspection($id) {
        $query = "UPDATE inspections SET
                  admin_verified = 0,
                  verification_status = 'pending',
                  verified_by = NULL,
                  verified_at = NULL,
                  verification_notes = NULL,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':id' => $id]);
    }

    public function getUnverifiedInspections() {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.status = 'completed'
                 AND (i.verification_status = 'pending' OR i.verification_status IS NULL)
                 ORDER BY i.completed_date DESC";
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getVerifiedInspections() {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email,
                        v.full_name as verified_by_name
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN users v ON i.verified_by = v.id
                 WHERE i.status = 'completed'
                 AND i.verification_status IN ('approved', 'rejected')
                 ORDER BY i.verified_at DESC";
        $stmt = $this->db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getRecentApproved($limit = 10) {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.status = 'completed'
                 AND i.verification_status = 'approved'
                 ORDER BY i.completed_date DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getRecentInProgress($limit = 10) {
        $query = "SELECT i.*,
                        b.name as business_name,
                        b.address as business_address,
                        u.full_name as inspector_name,
                        u.email as inspector_email
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 WHERE i.status = 'in_progress'
                 ORDER BY i.scheduled_date DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /* ==================== Assignment Methods ==================== */

    public function createAssignment($data) {
        $query = "INSERT INTO inspection_assignments (
                    id, inspection_id, inspector_id, assigned_by,
                    role, status, assigned_at
                 ) VALUES (
                    UUID(), :inspection_id, :inspector_id, :assigned_by,
                    :role, :status, NOW()
                 )";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':inspection_id' => $data['inspection_id'],
            ':inspector_id' => $data['inspector_id'],
            ':assigned_by' => $data['assigned_by'],
            ':role' => $data['role'] ?? 'primary',
            ':status' => $data['status'] ?? 'assigned'
        ]);
    }

    public function getAssignments($inspectionId) {
        $query = "SELECT ia.*,
                         u.full_name as inspector_name,
                         u.email as inspector_email,
                         assigner.full_name as assigned_by_name
                 FROM inspection_assignments ia
                 LEFT JOIN users u ON ia.inspector_id = u.id
                 LEFT JOIN users assigner ON ia.assigned_by = assigner.id
                 WHERE ia.inspection_id = :inspection_id
                 ORDER BY ia.role ASC, ia.assigned_at ASC";
        $stmt = $this->db->prepare($query);
        $stmt->execute([':inspection_id' => $inspectionId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function removeAssignments($inspectionId) {
        $query = "DELETE FROM inspection_assignments WHERE inspection_id = :inspection_id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([':inspection_id' => $inspectionId]);
    }

    public function updateAssignmentStatus($inspectionId, $inspectorId, $status, $notes = null) {
        $query = "UPDATE inspection_assignments SET
                  status = :status,
                  responded_at = NOW(),
                  notes = :notes
                  WHERE inspection_id = :inspection_id AND inspector_id = :inspector_id";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([
            ':inspection_id' => $inspectionId,
            ':inspector_id' => $inspectorId,
            ':status' => $status,
            ':notes' => $notes
        ]);
    }

    public function getInspectorAssignments($inspectorId, $status = null) {
        $query = "SELECT ia.*,
                         i.scheduled_date, i.status as inspection_status,
                         i.inspection_type, i.priority,
                         b.name as business_name, b.address as business_address
                 FROM inspection_assignments ia
                 LEFT JOIN inspections i ON ia.inspection_id = i.id
                 LEFT JOIN businesses b ON i.business_id = b.id
                 WHERE ia.inspector_id = :inspector_id";

        $params = [':inspector_id' => $inspectorId];

        if ($status) {
            $query .= " AND ia.status = :status";
            $params[':status'] = $status;
        }

        $query .= " ORDER BY i.scheduled_date ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Check if inspector has access to an inspection (simplified - if assigned to inspection)
     */
    public function canInspectorAccessInspection($inspectorId, $inspectionId) {
        $query = "SELECT COUNT(*) as can_access
                 FROM inspections i
                 WHERE i.id = :inspection_id
                 AND i.inspector_id = :inspector_id";

        $stmt = $this->db->prepare($query);
        $stmt->execute([
            ':inspector_id' => $inspectorId,
            ':inspection_id' => $inspectionId
        ]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['can_access'] > 0;
    }

    /**
     * Get inspection for inspector - simplified access validation
     * If inspector is assigned to the inspection, they can view it
     */
    public function getByIdForInspector($inspectionId, $inspectorId) {
        $query = "SELECT i.*, b.name as business_name, b.address as business_address,
                        u.full_name as inspector_name, u.email as inspector_email,
                        brg.name as barangay_name, d.name as district_name
                 FROM inspections i
                 LEFT JOIN businesses b ON i.business_id = b.id
                 LEFT JOIN users u ON i.inspector_id = u.id
                 LEFT JOIN barangays brg ON b.barangay_id = brg.id
                 LEFT JOIN districts d ON brg.district_id = d.id
                 WHERE i.id = :inspection_id
                 AND i.inspector_id = :inspector_id";

        $stmt = $this->db->prepare($query);
        $stmt->execute([
            ':inspection_id' => $inspectionId,
            ':inspector_id' => $inspectorId
        ]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getAllWithDetails() {
        $query = "SELECT i.*,
                         b.name as business_name,
                         b.address as business_address,
                         brg.name as barangay_name,
                         d.name as district_name,
                         u.full_name as inspector_name,
                         u2.full_name as assigned_by_name
                  FROM inspections i
                  LEFT JOIN businesses b ON i.business_id = b.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  LEFT JOIN users u ON i.inspector_id = u.id
                  LEFT JOIN users u2 ON i.assigned_by = u2.id
                  ORDER BY i.scheduled_date DESC, i.created_at DESC";

        $stmt = $this->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getByInspectorAndDate($inspectorId, $date) {
        $query = "SELECT i.*, b.name as business_name
                  FROM inspections i
                  LEFT JOIN businesses b ON i.business_id = b.id
                  WHERE i.inspector_id = ? AND DATE(i.scheduled_date) = ?";
        $stmt = $this->query($query, [$inspectorId, $date]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getByBusinessAndDate($businessId, $date) {
        $query = "SELECT i.*, u.full_name as inspector_name
                  FROM inspections i
                  LEFT JOIN users u ON i.inspector_id = u.id
                  WHERE i.business_id = ? AND DATE(i.scheduled_date) = ?";
        $stmt = $this->query($query, [$businessId, $date]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getByBusinessInspectorAndDate($businessId, $inspectorId, $date) {
        $query = "SELECT i.*, u.full_name as inspector_name, b.name as business_name
                  FROM inspections i
                  LEFT JOIN users u ON i.inspector_id = u.id
                  LEFT JOIN businesses b ON i.business_id = b.id
                  WHERE i.business_id = ? AND i.inspector_id = ? AND DATE(i.scheduled_date) = ?
                  AND i.status != 'cancelled'";
        $stmt = $this->query($query, [$businessId, $inspectorId, $date]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getInspectorWorkload($inspectorId) {
        $query = "SELECT
                    COUNT(*) as total_inspections,
                    SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'scheduled' AND scheduled_date < NOW() THEN 1 ELSE 0 END) as overdue
                  FROM inspections
                  WHERE inspector_id = ?";
        $stmt = $this->query($query, [$inspectorId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getUnassignedInspections() {
        $query = "SELECT i.*,
                         b.name as business_name,
                         b.address as business_address,
                         brg.name as barangay_name,
                         d.name as district_name
                  FROM inspections i
                  LEFT JOIN businesses b ON i.business_id = b.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  WHERE i.inspector_id IS NULL OR i.inspector_id = ''
                  ORDER BY i.scheduled_date ASC";

        $stmt = $this->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getOverdueInspections() {
        $query = "SELECT i.*,
                         b.name as business_name,
                         b.address as business_address,
                         brg.name as barangay_name,
                         d.name as district_name,
                         u.full_name as inspector_name
                  FROM inspections i
                  LEFT JOIN businesses b ON i.business_id = b.id
                  LEFT JOIN barangays brg ON b.barangay_id = brg.id
                  LEFT JOIN districts d ON brg.district_id = d.id
                  LEFT JOIN users u ON i.inspector_id = u.id
                  WHERE i.status = 'scheduled' AND i.scheduled_date < NOW()
                  ORDER BY i.scheduled_date ASC";

        $stmt = $this->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}