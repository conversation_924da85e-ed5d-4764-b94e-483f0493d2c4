<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= $title ?></h1>
        <div>
            <?php if (isset($business['id'])): ?>
                <a href="<?= BASE_URL ?>admin/businesses/compliance/<?= $business['id'] ?>" class="btn btn-outline-primary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Business Compliance
                </a>
            <?php endif; ?>
             <?php if (isset($auth) && $auth->isBusinessOwner() && isset($business['id'])): ?>
                <a href="<?= BASE_URL ?>business/compliance/<?= $business['id'] ?>/upload" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Upload Evidence
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="card border-0 rounded-3 shadow-sm mb-4">
        <div class="card-header bg-transparent border-0 pt-4 pb-3">
             <h5 class="mb-0">Evidence Submissions for <?= htmlspecialchars($business['name'] ?? 'Selected Business') ?></h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($evidence)): ?>
                <p class="text-center py-4">No compliance evidence submitted for this business yet.</p>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="ps-4">Compliance Type</th>
                                <th>Status</th>
                                <th>Uploaded At</th>
                                <th>Verified By</th>
                                <th>Verified At</th>
                                <th>Verification Notes</th>
                                <th class="pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($evidence as $item): ?>
                                <tr>
                                    <td class="ps-4"><?= htmlspecialchars($compliance_types[$item['compliance_type']] ?? $item['compliance_type'] ?? 'N/A') ?></td>
                                    <td>
                                        <span class="badge bg-<?= $item['status'] === 'verified' ? 'success' : ($item['status'] === 'rejected' ? 'danger' : 'warning') ?>">
                                            <?= ucfirst($item['status'] ?? 'N/A') ?>
                                        </span>
                                    </td>
                                    <td><?= isset($item['created_at']) ? date('M d, Y H:i', strtotime($item['created_at'])) : 'N/A' ?></td>
                                    <td><?= htmlspecialchars($item['verifier_name'] ?? 'N/A') ?></td>
                                    <td><?= isset($item['verified_at']) ? date('M d, Y H:i', strtotime($item['verified_at'])) : 'N/A' ?></td>
                                    <td><?= htmlspecialchars($item['verification_notes'] ?? 'N/A') ?></td>
                                    <td class="pe-4">
                                        <?php if (isset($item['photo_path'])): ?>
                                            <a href="<?= BASE_URL ?>public/serve-evidence-simple.php?file=<?= htmlspecialchars(basename($item['photo_path'])) ?>" class="btn btn-sm btn-outline-secondary me-1" target="_blank" title="View Photo">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($item['status'] === 'pending' && ($auth->isAdmin() || $auth->isInspector())): ?>
                                            <a href="<?= BASE_URL ?>admin/compliance/verify/<?= $item['id'] ?>" class="btn btn-sm btn-primary me-1" title="Verify Evidence">
                                                <i class="fas fa-check"></i> Verify
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($auth->isAdmin() || $auth->isInspector() || ($auth->isBusinessOwner() && $business['owner_id'] === $auth->getUserId())): ?>
                                            <form action="<?= BASE_URL ?>admin/compliance/delete/<?= $item['id'] ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this evidence?');">
                                                <button type="submit" class="btn btn-sm btn-danger" title="Delete Evidence">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>
