<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create Chat Room</h1>
        <div>
            <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Business
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comments me-2"></i>Start a Conversation
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Business:</strong> <?= htmlspecialchars($business['name']) ?><br>
                        <small class="text-muted">
                            You're creating a chat room to communicate with administrators and inspectors about this business.
                        </small>
                    </div>

                    <form method="POST" action="<?= BASE_URL ?>chat/create/<?= $business['id'] ?>" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="subject" 
                                   name="subject" 
                                   placeholder="e.g., Question about inspection requirements"
                                   required>
                            <div class="invalid-feedback">Please provide a subject for your message.</div>
                            <div class="form-text">Brief description of what you need help with</div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Initial Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" 
                                      id="message" 
                                      name="message" 
                                      rows="6" 
                                      placeholder="Please describe your question or concern in detail..."
                                      required></textarea>
                            <div class="invalid-feedback">Please provide your initial message.</div>
                            <div class="form-text">Explain your question or concern in detail to help us assist you better</div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Once you create this chat room, administrators and inspectors will be notified. 
                            Please ensure your message is clear and professional.
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-comments"></i> Create Chat Room
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-question-circle me-2"></i>Common Topics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Inspection Related</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Inspection scheduling</li>
                                <li><i class="fas fa-check text-success me-2"></i>Compliance requirements</li>
                                <li><i class="fas fa-check text-success me-2"></i>Safety standards</li>
                                <li><i class="fas fa-check text-success me-2"></i>Inspection results</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Documentation</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Evidence submission</li>
                                <li><i class="fas fa-check text-success me-2"></i>Document verification</li>
                                <li><i class="fas fa-check text-success me-2"></i>Permit renewals</li>
                                <li><i class="fas fa-check text-success me-2"></i>Compliance status</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-light mt-3">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <strong>Tip:</strong> Be specific about your business needs and include relevant details 
                        like permit numbers, inspection dates, or specific requirements you're asking about.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Auto-resize textarea
document.getElementById('message').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

// Character counter for subject
document.getElementById('subject').addEventListener('input', function() {
    const maxLength = 255;
    const currentLength = this.value.length;
    
    // Remove existing counter
    const existingCounter = document.querySelector('.subject-counter');
    if (existingCounter) {
        existingCounter.remove();
    }
    
    // Add counter
    const counter = document.createElement('div');
    counter.className = 'subject-counter form-text text-end';
    counter.innerHTML = `${currentLength}/${maxLength} characters`;
    
    if (currentLength > maxLength * 0.9) {
        counter.classList.add('text-warning');
    }
    if (currentLength >= maxLength) {
        counter.classList.remove('text-warning');
        counter.classList.add('text-danger');
    }
    
    this.parentNode.appendChild(counter);
});
</script>
<?php $this->endSection() ?>
