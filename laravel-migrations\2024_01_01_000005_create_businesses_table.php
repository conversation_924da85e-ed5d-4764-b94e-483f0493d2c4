<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('businesses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('owner_id');
            $table->string('owner_name');
            $table->uuid('category_id');
            $table->uuid('barangay_id');
            $table->string('name');
            $table->string('registration_number')->unique();
            $table->string('email');
            $table->string('contact_number');
            $table->text('address');
            $table->unsignedInteger('employee_count')->default(1);
            $table->date('date_established')->nullable();
            $table->enum('status', ['pending', 'active', 'suspended', 'inactive'])->default('pending');
            $table->enum('compliance_status', ['compliant', 'non_compliant', 'pending_review'])->default('pending_review');
            $table->boolean('business_permit')->default(false);
            $table->boolean('sanitary_permit')->default(false);
            $table->boolean('fire_safety_certificate')->default(false);
            $table->boolean('environmental_permit')->default(false);
            $table->unsignedInteger('safety_officer_count')->default(0);
            $table->boolean('has_safety_signage')->default(false);
            $table->boolean('has_first_aid')->default(false);
            $table->boolean('has_fire_extinguishers')->default(false);
            $table->boolean('has_cctv')->default(false);
            $table->boolean('has_waste_segregation')->default(false);
            $table->date('last_inspection_date')->nullable();
            $table->date('next_inspection_date')->nullable();
            $table->timestamps();

            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('business_categories');
            $table->foreign('barangay_id')->references('id')->on('barangays');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('businesses');
    }
};
