<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-clipboard-check"></i> Inspection Details
        </h1>
        <div>
            <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Schedule
            </a>
            <?php if ($inspection['status'] === 'scheduled'): ?>
                <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-play"></i> Start Inspection
                </a>
            <?php elseif ($inspection['status'] === 'confirmed'): ?>
                <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>" class="btn btn-success">
                    <i class="fas fa-play"></i> Start Inspection
                </a>
            <?php elseif ($inspection['status'] === 'in_progress'): ?>
                <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>" class="btn btn-warning">
                    <i class="fas fa-edit"></i> Continue Inspection
                </a>
            <?php elseif ($inspection['status'] === 'completed'): ?>
                <a href="<?= BASE_URL ?>inspector/inspection-report/<?= $inspection['id'] ?>" class="btn btn-success">
                    <i class="fas fa-file-alt"></i> View Report
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Inspection Info Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-info-circle"></i> Inspection Information
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3"><?= htmlspecialchars($inspection['business_name']) ?></h5>
                    <p class="mb-2">
                        <i class="fas fa-map-marker-alt text-primary"></i>
                        <strong>Address:</strong> <?= htmlspecialchars($inspection['business_address']) ?>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-map text-primary"></i>
                        <strong>Barangay:</strong> <?= htmlspecialchars($inspection['barangay_name']) ?>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-globe text-primary"></i>
                        <strong>District:</strong> <?= htmlspecialchars($inspection['district_name']) ?>
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2">
                        <i class="fas fa-calendar text-primary"></i>
                        <strong>Scheduled Date:</strong> <?= $inspection['scheduled_date'] ? date('M d, Y g:i A', strtotime($inspection['scheduled_date'])) : 'Not scheduled' ?>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-tag text-primary"></i>
                        <strong>Type:</strong> <?= ucfirst(str_replace('_', ' ', $inspection['inspection_type'])) ?>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-flag text-primary"></i>
                        <strong>Status:</strong> 
                        <span class="badge bg-<?=
                            $inspection['status'] === 'completed' ? 'success' :
                            ($inspection['status'] === 'in_progress' ? 'warning' :
                            ($inspection['status'] === 'confirmed' ? 'primary' : 'info'))
                        ?>">
                            <?= ucfirst(str_replace('_', ' ', $inspection['status'])) ?>
                        </span>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-user text-primary"></i>
                        <strong>Inspector:</strong> <?= htmlspecialchars($inspection['inspector_name']) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Details Card -->
    <?php if ($business): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-building"></i> Business Details
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2">
                        <strong>Business Name:</strong> <?= htmlspecialchars($business['name']) ?>
                    </p>
                    <p class="mb-2">
                        <strong>Category:</strong> <?= htmlspecialchars($business['category_name'] ?? 'Not specified') ?>
                    </p>
                    <p class="mb-2">
                        <strong>Owner:</strong> <?= htmlspecialchars($business['owner_email']) ?>
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2">
                        <strong>Registration Number:</strong> <?= htmlspecialchars($business['registration_number'] ?? 'Not specified') ?>
                    </p>
                    <p class="mb-2">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?= $business['status'] === 'active' ? 'success' : 'secondary' ?>">
                            <?= ucfirst($business['status']) ?>
                        </span>
                    </p>
                    <p class="mb-2">
                        <strong>Compliance Status:</strong> 
                        <span class="badge bg-<?= 
                            $business['compliance_status'] === 'compliant' ? 'success' : 
                            ($business['compliance_status'] === 'non_compliant' ? 'danger' : 'warning') 
                        ?>">
                            <?= ucfirst(str_replace('_', ' ', $business['compliance_status'] ?? 'pending')) ?>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Inspection Results (if completed) -->
    <?php if ($inspection['status'] === 'completed' && isset($inspection['findings'])): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-clipboard-list"></i> Inspection Results
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Findings:</h6>
                    <p><?= nl2br(htmlspecialchars($inspection['findings'])) ?></p>
                </div>
                <div class="col-md-6">
                    <h6>Recommendations:</h6>
                    <p><?= nl2br(htmlspecialchars($inspection['recommendations'])) ?></p>
                    
                    <?php if (isset($inspection['compliance_rating'])): ?>
                    <h6 class="mt-3">Compliance Rating:</h6>
                    <span class="badge bg-<?= 
                        in_array($inspection['compliance_rating'], ['A', 'B']) ? 'success' : 
                        ($inspection['compliance_rating'] === 'C' ? 'warning' : 'danger') 
                    ?> fs-6">
                        Grade <?= $inspection['compliance_rating'] ?>
                    </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Business Checklist Evidence -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-clipboard-check"></i> Business Checklist Evidence
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Note:</strong> To view detailed checklist evidence uploaded by the business owner, start the inspection and use the checklist system.
                The checklist will show all uploaded evidence alongside each requirement.
            </div>

            <!-- Business Preparation Summary -->
            <?php if (isset($evidence_summary)): ?>
            <div class="row mb-4">
                <div class="col-md-12">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-chart-pie me-2"></i>Business Preparation Summary
                    </h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center py-3">
                                    <h4 class="text-success mb-1"><?= $evidence_summary['total_items'] ?? 0 ?></h4>
                                    <small class="text-muted">Total Checklist Items</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center py-3">
                                    <h4 class="text-primary mb-1"><?= $evidence_summary['items_with_evidence'] ?? 0 ?></h4>
                                    <small class="text-muted">Items with Evidence</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center py-3">
                                    <h4 class="text-info mb-1"><?= $evidence_summary['total_evidence_files'] ?? 0 ?></h4>
                                    <small class="text-muted">Evidence Files</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center py-3">
                                    <?php
                                    $preparationPercentage = 0;
                                    if (isset($evidence_summary['total_items']) && $evidence_summary['total_items'] > 0) {
                                        $preparationPercentage = round(($evidence_summary['items_with_evidence'] / $evidence_summary['total_items']) * 100);
                                    }
                                    ?>
                                    <h4 class="text-warning mb-1"><?= $preparationPercentage ?>%</h4>
                                    <small class="text-muted">Preparation Level</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preparation Progress Bar -->
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Business Preparation Progress</span>
                            <span class="badge bg-<?= $preparationPercentage >= 80 ? 'success' : ($preparationPercentage >= 50 ? 'warning' : 'danger') ?>">
                                <?= $preparationPercentage ?>% Complete
                            </span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-<?= $preparationPercentage >= 80 ? 'success' : ($preparationPercentage >= 50 ? 'warning' : 'danger') ?>"
                                 style="width: <?= $preparationPercentage ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Detailed Checklist Evidence - Mobile Friendly -->
            <?php if (isset($checklist_evidence) && !empty($checklist_evidence)): ?>
            <div class="mt-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="text-success mb-0">
                        <i class="fas fa-list-check me-2"></i>Checklist Evidence
                    </h6>
                    <button class="btn btn-sm btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#checklistEvidence">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                </div>

                <div class="collapse d-md-block" id="checklistEvidence">
                    <!-- Mobile: Accordion Style -->
                    <div class="accordion d-md-none" id="mobileChecklistAccordion">
                        <?php $categoryIndex = 0; foreach ($checklist_evidence as $categoryName => $items): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading<?= $categoryIndex ?>">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse<?= $categoryIndex ?>" aria-expanded="false">
                                    <i class="fas fa-folder me-2"></i>
                                    <?= htmlspecialchars($categoryName) ?>
                                    <span class="badge bg-primary ms-2"><?= count($items) ?></span>
                                </button>
                            </h2>
                            <div id="collapse<?= $categoryIndex ?>" class="accordion-collapse collapse"
                                 data-bs-parent="#mobileChecklistAccordion">
                                <div class="accordion-body p-2">
                                    <?php foreach ($items as $item): ?>
                                    <div class="card mb-2 border-0 bg-light">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-start mb-2">
                                                <?php if (!empty($item['evidence'])): ?>
                                                    <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle text-danger me-2 mt-1"></i>
                                                <?php endif; ?>
                                                <div class="flex-grow-1">
                                                    <p class="mb-1 fw-bold small"><?= htmlspecialchars($item['item_text']) ?></p>
                                                    <?php if (!empty($item['evidence'])): ?>
                                                        <span class="badge bg-success mb-2">Evidence Uploaded</span>
                                                        <div class="evidence-files">
                                                            <?php foreach ($item['evidence'] as $evidence): ?>
                                                            <div class="d-flex align-items-center mb-1">
                                                                <i class="fas fa-file text-info me-1"></i>
                                                                <a href="<?= BASE_URL ?><?= htmlspecialchars($evidence['file_path']) ?>"
                                                                   target="_blank" class="text-decoration-none small">
                                                                    <?= htmlspecialchars($evidence['file_name']) ?>
                                                                </a>
                                                            </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">No Evidence</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        <?php $categoryIndex++; endforeach; ?>
                    </div>

                    <!-- Desktop: Card Layout -->
                    <div class="d-none d-md-block">
                        <?php foreach ($checklist_evidence as $categoryName => $items): ?>
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0 text-primary">
                                    <i class="fas fa-folder me-2"></i><?= htmlspecialchars($categoryName) ?>
                                    <span class="badge bg-primary ms-2"><?= count($items) ?> items</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($items as $item): ?>
                                <div class="row align-items-center mb-3 pb-3 border-bottom">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-start">
                                            <div class="me-3">
                                                <?php if (!empty($item['evidence'])): ?>
                                                    <i class="fas fa-check-circle text-success fa-lg"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle text-danger fa-lg"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <p class="mb-1 fw-bold"><?= htmlspecialchars($item['item_text']) ?></p>
                                                <small class="text-muted">
                                                    Status:
                                                    <?php if (!empty($item['evidence'])): ?>
                                                        <span class="badge bg-success">Evidence Uploaded</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">No Evidence</span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <?php if (!empty($item['evidence'])): ?>
                                            <div class="evidence-files">
                                                <small class="text-muted d-block mb-2">
                                                    <i class="fas fa-paperclip me-1"></i>
                                                    <?= count($item['evidence']) ?> file(s) uploaded
                                                </small>
                                                <?php foreach ($item['evidence'] as $evidence): ?>
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-file text-info me-2"></i>
                                                    <a href="<?= BASE_URL ?>public/serve-evidence.php?file=<?= urlencode($evidence['file_path']) ?>"
                                                       target="_blank" class="text-decoration-none small">
                                                        <?= htmlspecialchars($evidence['file_name']) ?>
                                                    </a>
                                                    <small class="text-muted ms-2">
                                                        (<?= date('M d, Y', strtotime($evidence['created_at'])) ?>)
                                                    </small>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php else: ?>
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                No evidence uploaded
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="text-center">
                <?php if ($inspection['status'] === 'scheduled'): ?>
                    <p class="text-muted mb-3">Start the inspection to access the enhanced checklist with business evidence.</p>
                    <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                       class="btn btn-success">
                        <i class="fas fa-clipboard-check me-2"></i>Start Inspection Checklist
                    </a>
                <?php elseif ($inspection['status'] === 'confirmed'): ?>
                    <p class="text-muted mb-3">Inspection confirmed. Start the checklist to begin the inspection process.</p>
                    <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                       class="btn btn-success">
                        <i class="fas fa-clipboard-check me-2"></i>Start Inspection Checklist
                    </a>
                <?php elseif ($inspection['status'] === 'in_progress'): ?>
                    <p class="text-muted mb-3">Continue with the inspection checklist to view business evidence.</p>
                    <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                       class="btn btn-warning">
                        <i class="fas fa-clipboard-check me-2"></i>Continue Inspection Checklist
                    </a>
                <?php elseif ($inspection['status'] === 'completed'): ?>
                    <p class="text-muted mb-3">View the completed inspection report to see all evidence and findings.</p>
                    <a href="<?= BASE_URL ?>inspector/inspection-report/<?= $inspection['id'] ?>"
                       class="btn btn-info">
                        <i class="fas fa-file-alt me-2"></i>View Inspection Report
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="text-center">
                <?php if ($inspection['status'] === 'scheduled'): ?>
                    <form method="POST" action="<?= BASE_URL ?>inspector/confirm-inspection/<?= $inspection['id'] ?>" class="d-inline">
                        <button type="submit" class="btn btn-primary btn-lg me-2">
                            <i class="fas fa-check"></i> Confirm Inspection
                        </button>
                    </form>
                    <a href="<?= BASE_URL ?>inspector/start-inspection/<?= $inspection['id'] ?>"
                       class="btn btn-success btn-lg me-2">
                        <i class="fas fa-play"></i> Start Inspection
                    </a>
                    <button type="button" class="btn btn-danger btn-lg" data-bs-toggle="modal" data-bs-target="#cancelModal">
                        <i class="fas fa-times"></i> Cancel Inspection
                    </button>
                <?php elseif ($inspection['status'] === 'confirmed'): ?>
                    <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                       class="btn btn-success btn-lg me-2">
                        <i class="fas fa-play"></i> Start Inspection
                    </a>
                    <button type="button" class="btn btn-danger btn-lg" data-bs-toggle="modal" data-bs-target="#cancelModal">
                        <i class="fas fa-times"></i> Cancel Inspection
                    </button>
                <?php elseif ($inspection['status'] === 'in_progress'): ?>
                    <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                       class="btn btn-warning btn-lg me-2">
                        <i class="fas fa-edit"></i> Continue Inspection
                    </a>
                    <button type="button" class="btn btn-danger btn-lg" data-bs-toggle="modal" data-bs-target="#cancelModal">
                        <i class="fas fa-times"></i> Cancel Inspection
                    </button>
                <?php elseif ($inspection['status'] === 'completed'): ?>
                    <a href="<?= BASE_URL ?>inspector/inspection-report/<?= $inspection['id'] ?>"
                       class="btn btn-info btn-lg">
                        <i class="fas fa-file-alt"></i> View Full Report
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Cancel Inspection Modal -->
    <?php if (in_array($inspection['status'], ['scheduled', 'confirmed', 'in_progress'])): ?>
    <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cancelModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning"></i> Cancel Inspection
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="<?= BASE_URL ?>inspector/cancel-inspection/<?= $inspection['id'] ?>">
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle"></i>
                            <strong>Warning:</strong> This action will cancel the inspection for <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>.
                            The admin will be notified of this cancellation.
                        </div>

                        <div class="mb-3">
                            <label for="cancellation_reason" class="form-label">
                                <i class="fas fa-comment"></i> Reason for Cancellation <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="cancellation_reason" name="cancellation_reason"
                                      rows="3" required placeholder="Please provide a detailed reason for cancelling this inspection..."></textarea>
                            <div class="form-text">This reason will be recorded and sent to the admin.</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> Keep Inspection
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-ban"></i> Cancel Inspection
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php $this->endSection(); ?>
