<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-palette me-2"></i>Website Settings
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <a href="<?= BASE_URL ?>admin/website-settings/preview" class="btn btn-outline-primary" target="_blank">
                    <i class="fas fa-eye me-1"></i>Preview Changes
                </a>
                <button type="button" class="btn btn-outline-secondary" onclick="exportSettings()">
                    <i class="fas fa-download me-1"></i>Export Settings
                </button>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Settings Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                <i class="fas fa-cog me-1"></i>General Settings
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="colors-tab" data-bs-toggle="tab" data-bs-target="#colors" type="button" role="tab">
                <i class="fas fa-palette me-1"></i>Colors & Theme
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="images-tab" data-bs-toggle="tab" data-bs-target="#images" type="button" role="tab">
                <i class="fas fa-images me-1"></i>Images & Media
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                <i class="fas fa-tools me-1"></i>Advanced
            </button>
        </li>
    </ul>

    <div class="tab-content" id="settingsTabContent">
        <!-- General Settings Tab -->
        <div class="tab-pane fade show active" id="general" role="tabpanel">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>General Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= BASE_URL ?>admin/website-settings/update-general" method="POST">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" 
                                               value="<?= htmlspecialchars($settings['site_name'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="site_tagline" class="form-label">Site Tagline</label>
                                        <input type="text" class="form-control" id="site_tagline" name="site_tagline" 
                                               value="<?= htmlspecialchars($settings['site_tagline'] ?? '') ?>">
                                    </div>
                                    <div class="col-12">
                                        <label for="site_description" class="form-label">Site Description</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?= htmlspecialchars($settings['site_description'] ?? '') ?></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="contact_email" class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="<?= htmlspecialchars($settings['contact_email'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="contact_phone" class="form-label">Contact Phone</label>
                                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="<?= htmlspecialchars($settings['contact_phone'] ?? '') ?>">
                                    </div>
                                    <div class="col-12">
                                        <label for="office_address" class="form-label">Office Address</label>
                                        <textarea class="form-control" id="office_address" name="office_address" rows="2"><?= htmlspecialchars($settings['office_address'] ?? '') ?></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="office_hours" class="form-label">Office Hours</label>
                                        <input type="text" class="form-control" id="office_hours" name="office_hours" 
                                               value="<?= htmlspecialchars($settings['office_hours'] ?? '') ?>">
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6 class="mb-3">Social Media Links</h6>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label for="facebook_url" class="form-label">
                                            <i class="fab fa-facebook me-1"></i>Facebook URL
                                        </label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                               value="<?= htmlspecialchars($settings['facebook_url'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="twitter_url" class="form-label">
                                            <i class="fab fa-twitter me-1"></i>Twitter URL
                                        </label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                               value="<?= htmlspecialchars($settings['twitter_url'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="youtube_url" class="form-label">
                                            <i class="fab fa-youtube me-1"></i>YouTube URL
                                        </label>
                                        <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                                               value="<?= htmlspecialchars($settings['youtube_url'] ?? '') ?>">
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save General Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Tips
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <small>
                                    <strong>Site Name:</strong> Appears in browser title and navigation<br>
                                    <strong>Tagline:</strong> Short description shown on homepage<br>
                                    <strong>Description:</strong> Used for SEO meta description
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Colors & Theme Tab -->
        <div class="tab-pane fade" id="colors" role="tabpanel">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Predefined Color Schemes -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-swatchbook me-2"></i>Predefined Color Schemes
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <?php foreach ($color_schemes as $schemeId => $scheme): ?>
                                <div class="col-md-6 col-lg-4">
                                    <div class="card border">
                                        <div class="card-body text-center p-3">
                                            <h6 class="card-title"><?= $scheme['name'] ?></h6>
                                            <p class="card-text small text-muted"><?= $scheme['description'] ?></p>
                                            
                                            <!-- Color Preview -->
                                            <div class="d-flex justify-content-center mb-3">
                                                <div class="color-preview me-1" style="background-color: <?= $scheme['colors']['primary_color'] ?>"></div>
                                                <div class="color-preview me-1" style="background-color: <?= $scheme['colors']['secondary_color'] ?>"></div>
                                                <div class="color-preview me-1" style="background-color: <?= $scheme['colors']['accent_color'] ?>"></div>
                                                <div class="color-preview" style="background-color: <?= $scheme['colors']['success_color'] ?>"></div>
                                            </div>
                                            
                                            <form action="<?= BASE_URL ?>admin/website-settings/apply-color-scheme/<?= $schemeId ?>" method="POST" class="d-inline">
                                                <button type="submit" class="btn btn-sm btn-outline-primary">
                                                    Apply Theme
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Colors -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-palette me-2"></i>Custom Colors
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= BASE_URL ?>admin/website-settings/update-colors" method="POST">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="primary_color" class="form-label">Primary Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" 
                                                   value="<?= $settings['primary_color'] ?? '#2563eb' ?>">
                                            <input type="text" class="form-control" value="<?= $settings['primary_color'] ?? '#2563eb' ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="secondary_color" class="form-label">Secondary Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" 
                                                   value="<?= $settings['secondary_color'] ?? '#1e40af' ?>">
                                            <input type="text" class="form-control" value="<?= $settings['secondary_color'] ?? '#1e40af' ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="accent_color" class="form-label">Accent Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="accent_color" name="accent_color" 
                                                   value="<?= $settings['accent_color'] ?? '#3b82f6' ?>">
                                            <input type="text" class="form-control" value="<?= $settings['accent_color'] ?? '#3b82f6' ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="success_color" class="form-label">Success Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="success_color" name="success_color" 
                                                   value="<?= $settings['success_color'] ?? '#059669' ?>">
                                            <input type="text" class="form-control" value="<?= $settings['success_color'] ?? '#059669' ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="warning_color" class="form-label">Warning Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="warning_color" name="warning_color" 
                                                   value="<?= $settings['warning_color'] ?? '#d97706' ?>">
                                            <input type="text" class="form-control" value="<?= $settings['warning_color'] ?? '#d97706' ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="danger_color" class="form-label">Danger Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color" 
                                                   value="<?= $settings['danger_color'] ?? '#dc2626' ?>">
                                            <input type="text" class="form-control" value="<?= $settings['danger_color'] ?? '#dc2626' ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Apply Custom Colors
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="previewColors()">
                                        <i class="fas fa-eye me-2"></i>Preview Colors
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Color Guide
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <small>
                                    <strong>Primary:</strong> Main brand color (buttons, links)<br>
                                    <strong>Secondary:</strong> Supporting color (gradients)<br>
                                    <strong>Accent:</strong> Highlight color (icons, badges)<br>
                                    <strong>Success:</strong> Positive actions (green)<br>
                                    <strong>Warning:</strong> Caution messages (orange)<br>
                                    <strong>Danger:</strong> Error messages (red)
                                </small>
                            </div>
                            
                            <div class="mt-3">
                                <h6>Current Theme Preview</h6>
                                <div class="theme-preview p-3 border rounded">
                                    <div class="btn btn-primary btn-sm me-1">Primary</div>
                                    <div class="btn btn-success btn-sm me-1">Success</div>
                                    <div class="btn btn-warning btn-sm me-1">Warning</div>
                                    <div class="btn btn-danger btn-sm">Danger</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Images & Media Tab -->
        <div class="tab-pane fade" id="images" role="tabpanel">
            <!-- Image upload and management content will be added here -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>Website Images
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Image management functionality will be implemented in the next update.</p>
                </div>
            </div>
        </div>

        <!-- Advanced Tab -->
        <div class="tab-pane fade" id="advanced" role="tabpanel">
            <div class="row">
                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-tools me-2"></i>System Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <form action="<?= BASE_URL ?>admin/website-settings/reset-defaults" method="POST" onsubmit="return confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')">
                                    <button type="submit" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-undo me-2"></i>Reset to Defaults
                                    </button>
                                </form>
                                
                                <button type="button" class="btn btn-outline-info" onclick="exportSettings()">
                                    <i class="fas fa-download me-2"></i>Export All Settings
                                </button>
                                
                                <button type="button" class="btn btn-outline-secondary" onclick="document.getElementById('importFile').click()">
                                    <i class="fas fa-upload me-2"></i>Import Settings
                                </button>
                                <input type="file" id="importFile" style="display: none;" accept=".json" onchange="importSettings(this)">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>System Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">
                                <strong>Theme System:</strong> Active<br>
                                <strong>Custom CSS:</strong> Generated automatically<br>
                                <strong>Image Uploads:</strong> Enabled<br>
                                <strong>Color Schemes:</strong> 7 predefined themes<br>
                                <strong>Last Updated:</strong> <?= date('Y-m-d H:i:s') ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
}

.form-control-color {
    width: 50px;
    height: 38px;
    border-radius: 0.375rem 0 0 0.375rem;
}

.theme-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
</style>

<script>
// Color picker synchronization with real-time preview
document.querySelectorAll('input[type="color"]').forEach(colorInput => {
    colorInput.addEventListener('change', function() {
        const textInput = this.parentNode.querySelector('input[type="text"]');
        textInput.value = this.value;
        previewColors(); // Apply changes immediately
    });

    // Also apply on input for real-time updates
    colorInput.addEventListener('input', function() {
        const textInput = this.parentNode.querySelector('input[type="text"]');
        textInput.value = this.value;
        previewColors(); // Apply changes immediately
    });
});

// Add cache busting to form submissions
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function() {
        // Add timestamp to force cache refresh
        const timestamp = new Date().getTime();
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'cache_bust';
        input.value = timestamp;
        this.appendChild(input);

        // Show loading indicator
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Applying...';
            submitBtn.disabled = true;
        }
    });
});

// Preview colors function with enhanced cache busting
function previewColors() {
    const colors = {
        primary: document.getElementById('primary_color').value,
        secondary: document.getElementById('secondary_color').value,
        accent: document.getElementById('accent_color').value,
        success: document.getElementById('success_color').value,
        warning: document.getElementById('warning_color').value,
        danger: document.getElementById('danger_color').value,
        dark: document.getElementById('dark_color').value,
        light: document.getElementById('light_color').value
    };

    // Apply preview styles with higher specificity
    const style = document.getElementById('preview-styles') || document.createElement('style');
    style.id = 'preview-styles';
    style.innerHTML = `
        :root {
            --primary-color: ${colors.primary} !important;
            --secondary-color: ${colors.secondary} !important;
            --accent-color: ${colors.accent} !important;
            --success-color: ${colors.success} !important;
            --warning-color: ${colors.warning} !important;
            --danger-color: ${colors.danger} !important;
            --dark-color: ${colors.dark} !important;
            --light-color: ${colors.light} !important;
        }

        /* Force immediate updates */
        .navbar-custom, .sidebar, .btn-primary, .bg-primary {
            background: linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%) !important;
        }

        .text-primary {
            color: ${colors.primary} !important;
        }

        .btn-primary {
            background-color: ${colors.primary} !important;
            border-color: ${colors.primary} !important;
        }
    `;

    if (!document.getElementById('preview-styles')) {
        document.head.appendChild(style);
    }
    
    // Show preview notification
    const alert = document.createElement('div');
    alert.className = 'alert alert-info alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-eye me-2"></i>Color preview applied! Save to make permanent.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alert);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Export settings function
function exportSettings() {
    // This would export current settings as JSON
    alert('Export functionality will be implemented in the next update.');
}

// Import settings function
function importSettings(input) {
    // This would import settings from JSON file
    alert('Import functionality will be implemented in the next update.');
}
</script>

<?php $this->endSection() ?>
