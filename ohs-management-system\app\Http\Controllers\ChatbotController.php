<?php

namespace App\Http\Controllers;

use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatbotController extends Controller
{
    /**
     * Process chatbot message and generate response
     */
    public function processMessage(Request $request, ChatRoom $chatRoom)
    {
        $validated = $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $userMessage = strtolower(trim($validated['message']));
        $response = $this->generateResponse($userMessage, $chatRoom);

        // Save chatbot response as system message
        if ($response['should_respond']) {
            ChatMessage::create([
                'chat_room_id' => $chatRoom->id,
                'sender_id' => $this->getChatbotUserId(),
                'message' => $response['message'],
                'message_type' => 'system',
            ]);
        }

        return response()->json([
            'success' => true,
            'response' => $response['message'],
            'confidence' => $response['confidence'],
            'should_escalate' => $response['should_escalate'],
            'should_respond' => $response['should_respond']
        ]);
    }

    /**
     * Generate chatbot response based on user message
     */
    private function generateResponse($message, ChatRoom $chatRoom)
    {
        $responses = $this->getChatbotResponses();
        $bestMatch = null;
        $highestConfidence = 0;

        // Find best matching response
        foreach ($responses as $pattern => $responseData) {
            $confidence = $this->calculateConfidence($message, $pattern);
            
            if ($confidence > $highestConfidence && $confidence >= 0.6) {
                $highestConfidence = $confidence;
                $bestMatch = $responseData;
            }
        }

        // If no good match found, use default response
        if (!$bestMatch) {
            return [
                'message' => "I understand you need assistance. Let me connect you with one of our staff members who can better help you with your inquiry. Please wait a moment while I notify them.",
                'confidence' => 0.5,
                'should_escalate' => true,
                'should_respond' => true
            ];
        }

        // Personalize response with business information
        $personalizedMessage = $this->personalizeResponse($bestMatch['message'], $chatRoom);

        return [
            'message' => $personalizedMessage,
            'confidence' => $highestConfidence,
            'should_escalate' => $bestMatch['escalate'] ?? false,
            'should_respond' => true
        ];
    }

    /**
     * Calculate confidence score for pattern matching
     */
    private function calculateConfidence($message, $pattern)
    {
        $keywords = explode('|', strtolower($pattern));
        $messageWords = explode(' ', $message);
        $matches = 0;
        $totalKeywords = count($keywords);

        foreach ($keywords as $keyword) {
            $keywordParts = explode(' ', trim($keyword));
            $keywordMatches = 0;

            foreach ($keywordParts as $part) {
                if (in_array($part, $messageWords) || $this->containsWord($message, $part)) {
                    $keywordMatches++;
                }
            }

            if ($keywordMatches > 0) {
                $matches += $keywordMatches / count($keywordParts);
            }
        }

        return min($matches / $totalKeywords, 1.0);
    }

    /**
     * Check if message contains word (partial matching)
     */
    private function containsWord($message, $word)
    {
        return strpos($message, $word) !== false;
    }

    /**
     * Personalize response with business information
     */
    private function personalizeResponse($message, ChatRoom $chatRoom)
    {
        $business = $chatRoom->business;
        
        $replacements = [
            '{business_name}' => $business->name,
            '{owner_name}' => $business->owner_name,
            '{business_address}' => $business->address,
            '{contact_number}' => $business->contact_number,
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $message);
    }

    /**
     * Get chatbot user ID (create if doesn't exist)
     */
    private function getChatbotUserId()
    {
        $chatbot = User::where('email', '<EMAIL>')->first();
        
        if (!$chatbot) {
            $chatbot = User::create([
                'full_name' => 'OHS Assistant',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'status' => 'active',
                'password' => bcrypt('chatbot123'),
            ]);
        }

        return $chatbot->id;
    }

    /**
     * Get predefined chatbot responses
     */
    private function getChatbotResponses()
    {
        return [
            // Greetings
            'hello|hi|hey|good morning|good afternoon|good evening' => [
                'message' => "Hello! Welcome to the Bacoor OHS Management System. I'm here to help you with your occupational health and safety inquiries. How can I assist you today?",
                'escalate' => false
            ],

            // Inspection related
            'inspection|inspect|schedule|when|date' => [
                'message' => "I can help you with inspection-related questions. For {business_name}, you can check your inspection schedule in your dashboard. If you need to schedule a new inspection or have questions about upcoming inspections, I can connect you with our inspection team.",
                'escalate' => false
            ],

            // Compliance questions
            'compliance|compliant|requirements|documents|permits' => [
                'message' => "For compliance requirements, you'll need to ensure you have all necessary permits and documentation. This typically includes business permits, sanitary permits, fire safety certificates, and environmental permits. You can upload your compliance evidence through your dashboard. Would you like me to connect you with our compliance team for specific guidance?",
                'escalate' => false
            ],

            // Safety equipment
            'safety|equipment|fire extinguisher|first aid|signage' => [
                'message' => "Safety equipment requirements include fire extinguishers, first aid kits, safety signage, and proper waste segregation systems. The specific requirements depend on your business type and size. For detailed safety equipment guidelines specific to your business, I recommend speaking with one of our safety inspectors.",
                'escalate' => true
            ],

            // Business registration
            'register|registration|new business|apply' => [
                'message' => "For new business registration in our OHS system, you can apply through our website or contact our office directly. You'll need to provide business details, owner information, and initial compliance documentation. Would you like me to connect you with our registration team?",
                'escalate' => true
            ],

            // Contact information
            'contact|phone|email|address|office|hours' => [
                'message' => "You can reach our office at:\n📍 Bacoor City Hall, Cavite, Philippines\n📞 (*************\n📧 <EMAIL>\n🕒 Monday - Friday: 8:00 AM - 5:00 PM\n\nFor urgent matters, you can also use this chat system and we'll respond as soon as possible.",
                'escalate' => false
            ],

            // Emergency or urgent matters
            'emergency|urgent|accident|incident|help' => [
                'message' => "For emergency situations or urgent safety concerns, please contact our office immediately at (************* or call emergency services if needed. I'm connecting you with our emergency response team right away.",
                'escalate' => true
            ],

            // Violations or penalties
            'violation|penalty|fine|non-compliant|warning' => [
                'message' => "If you've received a violation notice or have questions about penalties, our compliance team can help you understand the requirements and steps for resolution. Each case is handled individually based on the specific circumstances. Let me connect you with our compliance officer.",
                'escalate' => true
            ],

            // Training and education
            'training|education|seminar|workshop|learn' => [
                'message' => "We offer various safety training programs and educational workshops for businesses. These include workplace safety training, emergency response procedures, and compliance education. For information about upcoming training sessions and how to register, I can connect you with our training coordinator.",
                'escalate' => true
            ],

            // Technical issues
            'website|system|login|password|technical|error' => [
                'message' => "For technical issues with the website or system access problems, our IT support team can assist you. Common issues include password resets, account access, and system navigation. Let me connect you with our technical support team.",
                'escalate' => true
            ],

            // Fees and payments
            'fee|payment|cost|price|how much' => [
                'message' => "Information about fees and payments varies depending on the service type and business category. Our administrative team can provide you with detailed fee schedules and payment options. Would you like me to connect you with our billing department?",
                'escalate' => true
            ],

            // General help
            'help|assist|support|question' => [
                'message' => "I'm here to help! I can provide information about:\n• Inspection schedules and procedures\n• Compliance requirements\n• Safety equipment guidelines\n• Contact information\n• General OHS questions\n\nWhat specific information do you need?",
                'escalate' => false
            ],

            // Thank you
            'thank you|thanks|appreciate' => [
                'message' => "You're welcome! I'm glad I could help. If you have any other questions about occupational health and safety, feel free to ask. Have a safe and productive day!",
                'escalate' => false
            ],

            // Goodbye
            'bye|goodbye|see you|talk later' => [
                'message' => "Thank you for using the Bacoor OHS Management System. Stay safe and don't hesitate to reach out if you need any assistance with workplace safety matters. Goodbye!",
                'escalate' => false
            ]
        ];
    }

    /**
     * Escalate to human agent
     */
    public function escalateToHuman(Request $request, ChatRoom $chatRoom)
    {
        // Notify available admins
        $admins = User::where('role', 'admin')
            ->where('status', 'active')
            ->get();

        foreach ($admins as $admin) {
            // Create notification for admin
            \App\Models\Notification::create([
                'user_id' => $admin->id,
                'title' => 'Chat Escalation Required',
                'message' => 'A chat conversation with ' . $chatRoom->business->name . ' needs human assistance.',
                'type' => 'chat',
                'related_id' => $chatRoom->id,
                'related_type' => 'chat_room',
                'action_url' => route('chat.room', $chatRoom),
            ]);
        }

        // Send system message
        ChatMessage::create([
            'chat_room_id' => $chatRoom->id,
            'sender_id' => $this->getChatbotUserId(),
            'message' => "I've notified our team about your inquiry. A staff member will join this conversation shortly to provide you with personalized assistance.",
            'message_type' => 'system',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Your conversation has been escalated to our support team.'
        ]);
    }

    /**
     * Get chatbot status for a chat room
     */
    public function getStatus(ChatRoom $chatRoom)
    {
        $isActive = $this->isChatbotActive($chatRoom);
        
        return response()->json([
            'active' => $isActive,
            'message' => $isActive 
                ? 'Chatbot is available to assist you'
                : 'Our team is available to help you directly'
        ]);
    }

    /**
     * Check if chatbot should be active
     */
    private function isChatbotActive(ChatRoom $chatRoom)
    {
        // Chatbot is active when no admin is assigned or admin is offline
        if (!$chatRoom->admin_id) {
            return true;
        }

        $admin = User::find($chatRoom->admin_id);
        
        // Check if admin was last active more than 30 minutes ago
        return !$admin || $admin->last_activity_at < now()->subMinutes(30);
    }
}
