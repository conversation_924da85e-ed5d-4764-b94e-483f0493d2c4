<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'related_id',
        'related_type',
        'is_read',
        'action_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_read' => 'boolean',
        ];
    }

    /**
     * Get the user this notification belongs to
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the related model (polymorphic)
     */
    public function related()
    {
        return match($this->related_type) {
            'inspection' => $this->belongsTo(Inspection::class, 'related_id'),
            'business' => $this->belongsTo(Business::class, 'related_id'),
            'compliance_evidence' => $this->belongsTo(BusinessChecklistEvidence::class, 'related_id'),
            'chat_room' => $this->belongsTo(ChatRoom::class, 'related_id'),
            default => null
        };
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): void
    {
        $this->update(['is_read' => true]);
    }

    /**
     * Check if notification is read
     */
    public function isRead(): bool
    {
        return $this->is_read;
    }

    /**
     * Get notification icon based on type
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            'inspection' => 'fas fa-clipboard-check',
            'compliance' => 'fas fa-shield-alt',
            'chat' => 'fas fa-comments',
            'success' => 'fas fa-check-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'error' => 'fas fa-times-circle',
            default => 'fas fa-info-circle'
        };
    }

    /**
     * Get notification color class based on type
     */
    public function getColorClassAttribute(): string
    {
        return match($this->type) {
            'success' => 'text-success',
            'warning' => 'text-warning',
            'error' => 'text-danger',
            'inspection' => 'text-primary',
            'compliance' => 'text-info',
            'chat' => 'text-secondary',
            default => 'text-muted'
        };
    }

    /**
     * Get formatted timestamp
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    /**
     * Get time ago format
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for notifications by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for recent notifications
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
