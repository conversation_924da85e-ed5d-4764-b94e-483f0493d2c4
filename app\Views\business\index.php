<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
        <h1 class="h3 text-gray-800">My Businesses</h1>
        <?php if (App\Libraries\Auth::getInstance()->isBusinessOwner()): ?>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="<?= BASE_URL ?>business/create" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i>Register New Business
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 font-weight-bold text-primary">Business List</h6>
        </div>
        <div class="card-body">
            <?php if (!empty($businesses)): ?>
                <div class="table-responsive">
                    <table class="table table-hover datatable">
                        <thead>
                            <tr>
                                <th>Business Name</th>
                                <th>Contact Information</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($businesses as $business): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary-subtle rounded-circle me-2">
                                                <span class="avatar-title"><?= strtoupper(substr($business['name'], 0, 2)) ?></span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($business['name']) ?></h6>
                                                <small class="text-muted">ID: <?= substr($business['id'], 0, 8) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-envelope text-muted me-1"></i>
                                            <?= htmlspecialchars($business['email']) ?>
                                        </div>
                                        <div>
                                            <i class="fas fa-phone text-muted me-1"></i>
                                            <?= htmlspecialchars($business['contact_number']) ?>
                                        </div>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                        <?= htmlspecialchars($business['address']) ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClasses = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'rejected' => 'danger',
                                            'active' => 'success',
                                            'inactive' => 'secondary'
                                        ];
                                        $businessStatus = $business['status'] ?? 'pending';
                                        $statusClass = $statusClasses[$businessStatus] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>-subtle text-<?= $statusClass ?>">
                                            <?= ucfirst($businessStatus) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-center gap-1">
                                            <a href="<?= BASE_URL ?>business/view/<?= $business['id'] ?>"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= BASE_URL ?>business/checklist/<?= $business['id'] ?>"
                                               class="btn btn-sm btn-outline-success"
                                               data-bs-toggle="tooltip"
                                               title="Update Checklist">
                                                <i class="fas fa-clipboard-check"></i>
                                            </a>
                                            <a href="<?= BASE_URL ?>business/edit/<?= $business['id'] ?>"
                                               class="btn btn-sm btn-outline-secondary"
                                               data-bs-toggle="tooltip"
                                               title="Edit Business">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <img src="<?= BASE_URL ?>assets/images/empty-business.svg" alt="No businesses" class="mb-3" style="width: 200px;">
                    <h5 class="text-muted mb-3">No Businesses Found</h5>
                    <p class="text-muted mb-3">You haven't registered any businesses yet.</p>
                    <a href="<?= BASE_URL ?>business/create" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i>Register Your First Business
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>