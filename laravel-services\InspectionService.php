<?php

namespace App\Services;

use App\Models\Inspection;
use App\Models\Business;
use App\Models\User;
use App\Models\InspectionChecklistResponse;
use App\Models\InspectionChecklistItem;
use App\Models\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class InspectionService
{
    /**
     * Create a new inspection
     */
    public function createInspection(array $data): Inspection
    {
        return DB::transaction(function () use ($data) {
            $inspection = Inspection::create([
                'business_id' => $data['business_id'],
                'inspector_id' => $data['inspector_id'],
                'assigned_by' => $data['assigned_by'],
                'scheduled_date' => $data['scheduled_date'],
                'inspection_type' => $data['inspection_type'] ?? 'routine',
                'priority' => $data['priority'] ?? 'medium',
                'status' => 'scheduled',
                'notes' => $data['notes'] ?? null,
            ]);

            // Send notifications
            $this->sendInspectionNotifications($inspection, 'scheduled');

            return $inspection;
        });
    }

    /**
     * Complete an inspection
     */
    public function completeInspection(Inspection $inspection, array $data): bool
    {
        return DB::transaction(function () use ($inspection, $data) {
            // Calculate final score
            $totalScore = $this->calculateInspectionScore($inspection);

            // Update inspection
            $inspection->update([
                'status' => 'completed',
                'completed_date' => now(),
                'score' => $totalScore,
                'findings' => $data['findings'],
                'recommendations' => $data['recommendations'],
                'inspector_notes' => $data['inspector_notes'] ?? null,
                'compliance_rating' => $data['compliance_rating'],
                'verification_status' => 'pending',
            ]);

            // Update business compliance status
            $this->updateBusinessComplianceStatus($inspection);

            // Send notifications
            $this->sendInspectionNotifications($inspection, 'completed');

            return true;
        });
    }

    /**
     * Verify an inspection (admin action)
     */
    public function verifyInspection(Inspection $inspection, User $admin, array $data): bool
    {
        return DB::transaction(function () use ($inspection, $admin, $data) {
            $inspection->update([
                'verification_status' => $data['status'], // 'approved' or 'rejected'
                'verified_by' => $admin->id,
                'verified_at' => now(),
                'verification_notes' => $data['notes'] ?? null,
                'admin_notes' => $data['admin_notes'] ?? null,
            ]);

            // Send notifications
            $this->sendInspectionNotifications($inspection, 'verified');

            return true;
        });
    }

    /**
     * Calculate inspection score based on checklist responses
     */
    public function calculateInspectionScore(Inspection $inspection): int
    {
        $responses = InspectionChecklistResponse::where('inspection_id', $inspection->id)
            ->with('checklistItem')
            ->get();

        $totalScore = 0;
        foreach ($responses as $response) {
            $totalScore += $response->score;
        }

        return $totalScore;
    }

    /**
     * Update business compliance status based on inspection results
     */
    private function updateBusinessComplianceStatus(Inspection $inspection): void
    {
        $business = $inspection->business;
        
        $complianceStatus = match($inspection->compliance_rating) {
            'A', 'B' => 'compliant',
            'C' => 'pending_review',
            'D', 'F' => 'non_compliant',
            default => 'pending_review'
        };

        $business->update([
            'compliance_status' => $complianceStatus,
            'last_inspection_date' => $inspection->completed_date,
            'next_inspection_date' => $this->calculateNextInspectionDate($inspection),
        ]);
    }

    /**
     * Calculate next inspection date based on compliance rating
     */
    private function calculateNextInspectionDate(Inspection $inspection): \Carbon\Carbon
    {
        $baseDate = $inspection->completed_date;
        
        return match($inspection->compliance_rating) {
            'A' => $baseDate->addYear(), // Annual for excellent compliance
            'B' => $baseDate->addMonths(9), // 9 months for good compliance
            'C' => $baseDate->addMonths(6), // 6 months for fair compliance
            'D' => $baseDate->addMonths(3), // 3 months for poor compliance
            'F' => $baseDate->addMonth(), // Monthly for failing compliance
            default => $baseDate->addMonths(6)
        };
    }

    /**
     * Send inspection-related notifications
     */
    private function sendInspectionNotifications(Inspection $inspection, string $event): void
    {
        $business = $inspection->business;
        $inspector = $inspection->inspector;
        $admin = $inspection->assignedBy;

        switch ($event) {
            case 'scheduled':
                // Notify inspector
                Notification::create([
                    'user_id' => $inspector->id,
                    'title' => 'New Inspection Assigned',
                    'message' => "You have been assigned to inspect {$business->name} on {$inspection->scheduled_date->format('M j, Y g:i A')}",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('inspector.inspections.show', $inspection),
                ]);

                // Notify business owner
                Notification::create([
                    'user_id' => $business->owner_id,
                    'title' => 'Inspection Scheduled',
                    'message' => "An inspection has been scheduled for {$inspection->scheduled_date->format('M j, Y g:i A')}",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('business-owner.inspections.show', $inspection),
                ]);
                break;

            case 'completed':
                // Notify admin
                Notification::create([
                    'user_id' => $admin->id,
                    'title' => 'Inspection Completed',
                    'message' => "Inspection of {$business->name} has been completed and is pending verification",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('admin.inspections.show', $inspection),
                ]);

                // Notify business owner
                Notification::create([
                    'user_id' => $business->owner_id,
                    'title' => 'Inspection Completed',
                    'message' => "Your business inspection has been completed. Results are pending verification.",
                    'type' => 'inspection',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('business-owner.inspections.show', $inspection),
                ]);
                break;

            case 'verified':
                $status = $inspection->verification_status === 'approved' ? 'approved' : 'rejected';
                
                // Notify inspector
                Notification::create([
                    'user_id' => $inspector->id,
                    'title' => 'Inspection Verified',
                    'message' => "Your inspection of {$business->name} has been {$status}",
                    'type' => $inspection->verification_status === 'approved' ? 'success' : 'warning',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('inspector.inspections.show', $inspection),
                ]);

                // Notify business owner
                Notification::create([
                    'user_id' => $business->owner_id,
                    'title' => 'Inspection Results Available',
                    'message' => "Your inspection results have been {$status} and are now available",
                    'type' => $inspection->verification_status === 'approved' ? 'success' : 'warning',
                    'related_id' => $inspection->id,
                    'related_type' => 'inspection',
                    'action_url' => route('business-owner.inspections.show', $inspection),
                ]);
                break;
        }
    }

    /**
     * Get inspection statistics for dashboard
     */
    public function getInspectionStatistics(array $filters = []): array
    {
        $query = Inspection::query();

        // Apply filters
        if (isset($filters['inspector_id'])) {
            $query->where('inspector_id', $filters['inspector_id']);
        }

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('scheduled_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('scheduled_date', '<=', $filters['date_to']);
        }

        return [
            'total' => $query->count(),
            'completed' => (clone $query)->where('status', 'completed')->count(),
            'scheduled' => (clone $query)->where('status', 'scheduled')->count(),
            'pending_verification' => (clone $query)->where('status', 'completed')
                ->where('verification_status', 'pending')->count(),
            'approved' => (clone $query)->where('verification_status', 'approved')->count(),
            'average_score' => (clone $query)->where('status', 'completed')
                ->whereNotNull('score')->avg('score'),
        ];
    }

    /**
     * Check for scheduling conflicts
     */
    public function hasSchedulingConflict(string $inspectorId, \Carbon\Carbon $scheduledDate): bool
    {
        return Inspection::where('inspector_id', $inspectorId)
            ->where('status', '!=', 'cancelled')
            ->whereBetween('scheduled_date', [
                $scheduledDate->copy()->subHours(2),
                $scheduledDate->copy()->addHours(2)
            ])
            ->exists();
    }

    /**
     * Get available inspectors for a specific date and location
     */
    public function getAvailableInspectors(\Carbon\Carbon $date, string $barangayId): \Illuminate\Database\Eloquent\Collection
    {
        return User::where('role', 'inspector')
            ->where('status', 'active')
            ->whereHas('barangayAssignments', function($query) use ($barangayId) {
                $query->where('barangay_id', $barangayId)
                      ->where('status', 'active');
            })
            ->whereDoesntHave('assignedInspections', function($query) use ($date) {
                $query->where('status', '!=', 'cancelled')
                      ->whereBetween('scheduled_date', [
                          $date->copy()->subHours(2),
                          $date->copy()->addHours(2)
                      ]);
            })
            ->get();
    }
}
