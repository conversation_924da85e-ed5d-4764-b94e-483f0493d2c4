<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InspectorDistrictAssignment extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'inspector_id',
        'district_id',
        'assigned_by',
        'assigned_at',
        'is_active',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'assigned_at' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the inspector
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Get the district
     */
    public function district()
    {
        return $this->belongsTo(District::class);
    }

    /**
     * Get the admin who made the assignment
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Scope for active assignments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for assignments by inspector
     */
    public function scopeByInspector($query, User $inspector)
    {
        return $query->where('inspector_id', $inspector->id);
    }

    /**
     * Scope for assignments by district
     */
    public function scopeByDistrict($query, District $district)
    {
        return $query->where('district_id', $district->id);
    }
}
