<?php $this->extend('layouts/app') ?>

<?php $this->section('content') ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-comments text-primary me-2"></i>Manage Chatbot Responses
        </h1>
        <div>
            <a href="<?= BASE_URL ?>admin/chatbot/responses/add" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Response
            </a>
            <a href="<?= BASE_URL ?>admin/chatbot" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-robot me-2"></i>Chatbot Responses (<?= count($responses) ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($responses)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Keywords</th>
                                <th>Response</th>
                                <th>Type</th>
                                <th>Category</th>
                                <th>Priority</th>
                                <th>Usage</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($responses as $response): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary me-1">
                                            <?= htmlspecialchars(substr($response['trigger_keywords'], 0, 30)) ?>
                                            <?= strlen($response['trigger_keywords']) > 30 ? '...' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="max-width: 300px;">
                                            <?= htmlspecialchars(substr($response['response_text'], 0, 100)) ?>
                                            <?= strlen($response['response_text']) > 100 ? '...' : '' ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $response['response_type'] === 'escalate' ? 'warning' : 'info' ?>">
                                            <?= ucfirst($response['response_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?= ucfirst($response['category']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $response['priority'] >= 8 ? 'danger' : ($response['priority'] >= 5 ? 'warning' : 'secondary') ?>">
                                            <?= $response['priority'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?= $response['usage_count'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $response['is_active'] ? 'success' : 'secondary' ?>">
                                            <?= $response['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= BASE_URL ?>admin/chatbot/responses/edit/<?= $response['id'] ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteResponse('<?= $response['id'] ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Responses Found</h5>
                    <p class="text-muted">Start by adding your first chatbot response.</p>
                    <a href="<?= BASE_URL ?>admin/chatbot/responses/add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add First Response
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteResponse(id) {
    if (confirm('Are you sure you want to delete this response? This action cannot be undone.')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= BASE_URL ?>admin/chatbot/responses/delete/' + id;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $this->endSection() ?>
