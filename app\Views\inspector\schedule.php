<?php $this->extend('layouts/app'); ?>

<?php $this->section('content'); ?>
<div class="container-fluid px-4">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-calendar-alt"></i> My Schedule
            </h1>
            <?php if ($current_district_filter): ?>
                <?php
                $districtName = 'Unknown District';
                foreach ($assigned_districts as $district) {
                    if ($district['district_id'] === $current_district_filter) {
                        $districtName = $district['district_name'];
                        break;
                    }
                }
                ?>
                <div class="mt-2">
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-filter"></i> Filtered by: <?= htmlspecialchars($districtName) ?>
                    </span>
                    <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-sm btn-outline-secondary ms-2">
                        <i class="fas fa-times"></i> Clear Filter
                    </a>
                </div>
            <?php endif; ?>
        </div>

    </div>

    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Inspector Schedule:</strong> This page shows inspections assigned to you by the administrator. You can view upcoming inspections and perform inspections using the checklist system. Contact your administrator for new inspection scheduling.
    </div>

    <!-- District Filter -->
    <?php if (!empty($assigned_districts)): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-filter me-2"></i>Filter by District
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="btn-group" role="group">
                            <a href="<?= BASE_URL ?>inspector/schedule"
                               class="btn btn-<?= empty($current_district_filter) ? 'primary' : 'outline-primary' ?>">
                                <i class="fas fa-globe"></i> All Districts
                            </a>
                            <?php foreach ($assigned_districts as $district): ?>
                                <a href="<?= BASE_URL ?>inspector/schedule?district=<?= $district['district_id'] ?>"
                                   class="btn btn-<?= $current_district_filter === $district['district_id'] ? 'success' : 'outline-success' ?>">
                                    <i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($district['district_name']) ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <?php if ($current_district_filter): ?>
                            <span class="badge bg-info">
                                Showing: <?= htmlspecialchars(array_filter($assigned_districts, function($d) use ($current_district_filter) {
                                    return $d['district_id'] === $current_district_filter;
                                })[0]['district_name'] ?? 'Unknown District') ?>
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary">Showing: All Districts</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-warning mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $stats['total_pending'] ?? 0 ?></h4>
                            <small>Scheduled</small>
                        </div>
                        <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $stats['total_completed'] ?? 0 ?></h4>
                            <small>Completed</small>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-danger mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $stats['overdue_inspections'] ?? 0 ?></h4>
                            <small>Overdue</small>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $stats['businesses_needing_inspection'] ?? 0 ?></h4>
                            <small>Need Scheduling</small>
                        </div>
                        <i class="fas fa-building fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Businesses Needing Inspection (Information Only) -->
    <?php if (!empty($businesses_needing_inspection) || !empty($overdue_inspections)): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-2"></i>Businesses in My Districts Requiring Attention
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> These businesses need inspection scheduling. Please contact your administrator to schedule inspections for these businesses.
                </div>

                <!-- Overdue Inspections -->
                <?php if (!empty($overdue_inspections)): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-clock"></i> Overdue Inspections (<?= count($overdue_inspections) ?>)</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Business</th>
                                        <th>District</th>
                                        <th>Due Date</th>
                                        <th>Days Overdue</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($overdue_inspections as $item): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($item['business_name']) ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($item['address']) ?></small>
                                            </td>
                                            <td><?= htmlspecialchars($item['district_name']) ?></td>
                                            <td><?= date('M d, Y', strtotime($item['due_date'])) ?></td>
                                            <td>
                                                <span class="badge bg-danger"><?= $item['days_overdue'] ?> days</span>
                                            </td>
                                            <td>
                                                <?php
                                                $priorityClasses = [
                                                    'urgent' => 'danger',
                                                    'high' => 'warning',
                                                    'medium' => 'info',
                                                    'low' => 'secondary'
                                                ];
                                                $priorityClass = $priorityClasses[$item['priority']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $priorityClass ?>"><?= ucfirst($item['priority']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">Needs Admin Scheduling</span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Businesses Needing Inspection -->
                <?php if (!empty($businesses_needing_inspection)): ?>
                    <h6><i class="fas fa-building"></i> Businesses Needing Inspection (<?= count($businesses_needing_inspection) ?>)</h6>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Business</th>
                                    <th>District</th>
                                    <th>Type</th>
                                    <th>Due Date</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($businesses_needing_inspection as $item): ?>
                                    <tr class="<?= $item['days_until_due'] < 0 ? 'table-danger' : ($item['days_until_due'] <= 7 ? 'table-warning' : '') ?>">
                                        <td>
                                            <strong><?= htmlspecialchars($item['business_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($item['address']) ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($item['district_name']) ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= ucfirst(str_replace('_', ' ', $item['inspection_type'])) ?></span>
                                        </td>
                                        <td>
                                            <?= date('M d, Y', strtotime($item['due_date'])) ?>
                                            <?php if ($item['days_until_due'] < 0): ?>
                                                <br><small class="text-danger"><?= abs($item['days_until_due']) ?> days overdue</small>
                                            <?php elseif ($item['days_until_due'] <= 7): ?>
                                                <br><small class="text-warning"><?= $item['days_until_due'] ?> days left</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $priorityClasses = [
                                                'urgent' => 'danger',
                                                'high' => 'warning',
                                                'medium' => 'info',
                                                'low' => 'secondary'
                                            ];
                                            $priorityClass = $priorityClasses[$item['priority']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?= $priorityClass ?>"><?= ucfirst($item['priority']) ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">Awaiting Admin Scheduling</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- My Assigned Inspections -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-calendar-check me-2"></i>My Assigned Inspections
                <span class="badge bg-primary ms-2"><?= count($upcoming_inspections) ?></span>
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($upcoming_inspections)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Ready to Inspect:</strong> These inspections have been scheduled by your administrator. Click "Checklist" to begin the inspection process.
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>Business</th>
                                <th>District</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($upcoming_inspections as $inspection): ?>
                                <?php
                                // Check if this inspection is in my assigned districts
                                $isMyDistrict = false;
                                if (!empty($assigned_districts) && isset($inspection['district_id'])) {
                                    foreach ($assigned_districts as $district) {
                                        if ($district['district_id'] === $inspection['district_id']) {
                                            $isMyDistrict = true;
                                            break;
                                        }
                                    }
                                }
                                ?>
                                <tr class="<?= $isMyDistrict ? 'table-success' : '' ?>">
                                    <td>
                                        <strong><?= date('M d, Y', strtotime($inspection['scheduled_date'])) ?></strong>
                                        <?php if ($inspection['scheduled_date']): ?>
                                            <br><small class="text-muted"><?= date('h:i A', strtotime($inspection['scheduled_date'])) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                        <?php if (isset($inspection['business_address']) && $inspection['business_address']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($inspection['business_address']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($isMyDistrict): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle"></i> My District
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Other District</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= ucfirst($inspection['inspection_type'] ?? 'routine') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $inspection['status'] ?? 'pending';
                                        $statusClasses = [
                                            'scheduled' => 'primary',
                                            'confirmed' => 'success',
                                            'completed' => 'success',
                                            'cancelled' => 'danger',
                                            'in_progress' => 'warning',
                                            'pending' => 'warning'
                                        ];
                                        $statusClass = $statusClasses[$status] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>">
                                            <?= ucfirst(str_replace('_', ' ', $status)) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= BASE_URL ?>inspector/inspection/<?= $inspection['id'] ?>"
                                           class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <?php if ($inspection['status'] !== 'completed'): ?>
                                            <a href="<?= BASE_URL ?>inspector/inspection-checklist/<?= $inspection['id'] ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-clipboard-check"></i> Checklist
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <?php if ($current_district_filter): ?>
                        <?php
                        $districtName = 'Unknown District';
                        foreach ($assigned_districts as $district) {
                            if ($district['district_id'] === $current_district_filter) {
                                $districtName = $district['district_name'];
                                break;
                            }
                        }
                        ?>
                        <h5 class="text-muted">No Inspections in <?= htmlspecialchars($districtName) ?></h5>
                        <p class="text-muted">You don't have any inspections assigned in this district at the moment.</p>
                        <div class="mt-3">
                            <a href="<?= BASE_URL ?>inspector/schedule" class="btn btn-primary">
                                <i class="fas fa-globe"></i> View All Districts
                            </a>

                        </div>
                    <?php else: ?>
                        <h5 class="text-muted">No Assigned Inspections</h5>
                        <p class="text-muted">You don't have any inspections assigned by your administrator at the moment.</p>
                        <p class="text-muted">Contact your administrator if you believe there should be inspections scheduled for you.</p>

                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Completed Inspections -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-check-circle me-2"></i>Recent Completed Inspections
                <span class="badge bg-success ms-2"><?= count($completed_inspections) ?></span>
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($completed_inspections)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Inspection ID</th>
                                <th>Completed Date</th>
                                <th>Business</th>
                                <th>District</th>
                                <th>Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($completed_inspections, 0, 10) as $inspection): ?>
                            <tr>
                                <td>
                                    <code><?= substr($inspection['id'], 0, 8) ?>...</code>
                                </td>
                                <td><?= date('M d, Y', strtotime($inspection['updated_at'])) ?></td>
                                <td>
                                    <strong><?= htmlspecialchars($inspection['business_name']) ?></strong>
                                </td>
                                <td><?= htmlspecialchars($inspection['district_name'] ?? 'N/A') ?></td>
                                <td>
                                    <?php if (isset($inspection['compliance_rating'])): ?>
                                        <span class="badge bg-<?=
                                            $inspection['compliance_rating'] === 'A' ? 'success' :
                                            ($inspection['compliance_rating'] === 'B' ? 'info' :
                                            ($inspection['compliance_rating'] === 'C' ? 'warning' : 'danger'))
                                        ?>">
                                            Grade <?= $inspection['compliance_rating'] ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">No score</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= BASE_URL ?>inspector/inspection/<?= $inspection['id'] ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="<?= BASE_URL ?>inspector/inspection-report/<?= $inspection['id'] ?>"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-file-alt"></i> Report
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (count($completed_inspections) > 10): ?>
                <div class="text-center mt-3">
                    <a href="<?= BASE_URL ?>inspector/inspections?status=completed" class="btn btn-outline-success">
                        <i class="fas fa-list"></i> View All Completed Inspections (<?= count($completed_inspections) ?>)
                    </a>
                </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Completed Inspections</h5>
                    <p class="text-muted">You haven't completed any inspections yet.</p>
                </div>
            <?php endif; ?>
        </div>
</div>

<?php $this->endSection(); ?>