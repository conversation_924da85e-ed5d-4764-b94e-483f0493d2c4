<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inspection_checklist_responses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('inspection_id');
            $table->uuid('checklist_item_id');
            $table->uuid('inspector_id');
            $table->enum('compliance_status', ['compliant', 'needs_improvement', 'non_compliant', 'not_applicable']);
            $table->integer('score')->default(0);
            $table->text('notes')->nullable();
            $table->string('photo_evidence')->nullable();
            $table->text('corrective_action')->nullable();
            $table->date('deadline')->nullable();
            $table->timestamps();

            $table->foreign('inspection_id')->references('id')->on('inspections')->onDelete('cascade');
            $table->foreign('checklist_item_id')->references('id')->on('inspection_checklist_items')->onDelete('cascade');
            $table->foreign('inspector_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->unique(['inspection_id', 'checklist_item_id'], 'unique_inspection_item');
            $table->index('inspection_id');
            $table->index('checklist_item_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inspection_checklist_responses');
    }
};
