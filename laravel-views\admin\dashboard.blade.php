@extends('layouts.admin')

@section('title', 'Admin Dashboard')

@section('content')
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
    <a href="#" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
        <i class="fas fa-download fa-sm text-white-50"></i> Generate Report
    </a>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Total Businesses Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Businesses
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_businesses'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Inspectors Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Inspectors
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_inspectors'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Inspections Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Completed Inspections
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completed_inspections'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compliance Rate Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Compliance Rate
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">{{ $stats['compliance_rate'] }}%</div>
                            </div>
                            <div class="col">
                                <div class="progress progress-sm mr-2">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: {{ $stats['compliance_rate'] }}%" 
                                         aria-valuenow="{{ $stats['compliance_rate'] }}" 
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Monthly Inspections Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Monthly Inspections Overview</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="monthlyInspectionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Pending Verification</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['pending_verification'] }}</div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Scheduled</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['scheduled_inspections'] }}</div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Evidence</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['pending_evidence'] }}</div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Active Chats</div>
                        <div class="h5 mb-2 font-weight-bold text-gray-800">{{ $stats['active_chat_rooms'] }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Inspections -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Inspections</h6>
            </div>
            <div class="card-body">
                @if($recentInspections->count() > 0)
                    @foreach($recentInspections as $inspection)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-{{ $inspection->status === 'completed' ? 'success' : 'warning' }}">
                                    <i class="fas fa-clipboard-check text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small text-gray-500">{{ $inspection->scheduled_date->format('M j, Y') }}</div>
                                <div class="font-weight-bold">{{ $inspection->business->name }}</div>
                                <div class="small">Inspector: {{ $inspection->inspector->full_name }}</div>
                            </div>
                            <div>
                                <span class="badge badge-{{ $inspection->status === 'completed' ? 'success' : 'warning' }}">
                                    {{ ucfirst($inspection->status) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                    <div class="text-center">
                        <a href="{{ route('admin.inspections.results') }}" class="btn btn-sm btn-primary">View All</a>
                    </div>
                @else
                    <div class="text-center text-gray-500">
                        <i class="fas fa-clipboard-check fa-3x mb-3"></i>
                        <p>No recent inspections</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Pending Evidence -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Pending Evidence Review</h6>
            </div>
            <div class="card-body">
                @if($pendingEvidence->count() > 0)
                    @foreach($pendingEvidence as $evidence)
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="icon-circle bg-warning">
                                    <i class="fas fa-file-image text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small text-gray-500">{{ $evidence->created_at->format('M j, Y') }}</div>
                                <div class="font-weight-bold">{{ $evidence->business->name }}</div>
                                <div class="small">{{ $evidence->checklistItem->item_name }}</div>
                            </div>
                            <div>
                                <span class="badge badge-warning">Pending</span>
                            </div>
                        </div>
                    @endforeach
                    <div class="text-center">
                        <a href="#" class="btn btn-sm btn-warning">Review All</a>
                    </div>
                @else
                    <div class="text-center text-gray-500">
                        <i class="fas fa-file-image fa-3x mb-3"></i>
                        <p>No pending evidence</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="{{ asset('vendor/chart.js/Chart.min.js') }}"></script>
<script>
// Monthly Inspections Chart
var ctx = document.getElementById("monthlyInspectionsChart");
var monthlyInspectionsChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: @json($stats['monthly_inspections']['months']),
        datasets: [{
            label: "Total Inspections",
            lineTension: 0.3,
            backgroundColor: "rgba(78, 115, 223, 0.05)",
            borderColor: "rgba(78, 115, 223, 1)",
            pointRadius: 3,
            pointBackgroundColor: "rgba(78, 115, 223, 1)",
            pointBorderColor: "rgba(78, 115, 223, 1)",
            pointHoverRadius: 3,
            pointHoverBackgroundColor: "rgba(78, 115, 223, 1)",
            pointHoverBorderColor: "rgba(78, 115, 223, 1)",
            pointHitRadius: 10,
            pointBorderWidth: 2,
            data: @json($stats['monthly_inspections']['totals']),
        }, {
            label: "Completed Inspections",
            lineTension: 0.3,
            backgroundColor: "rgba(28, 200, 138, 0.05)",
            borderColor: "rgba(28, 200, 138, 1)",
            pointRadius: 3,
            pointBackgroundColor: "rgba(28, 200, 138, 1)",
            pointBorderColor: "rgba(28, 200, 138, 1)",
            pointHoverRadius: 3,
            pointHoverBackgroundColor: "rgba(28, 200, 138, 1)",
            pointHoverBorderColor: "rgba(28, 200, 138, 1)",
            pointHitRadius: 10,
            pointBorderWidth: 2,
            data: @json($stats['monthly_inspections']['completed']),
        }]
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            xAxes: [{
                time: {
                    unit: 'month'
                },
                gridLines: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 12
                }
            }],
            yAxes: [{
                ticks: {
                    maxTicksLimit: 5,
                    padding: 10,
                    callback: function(value, index, values) {
                        return value;
                    }
                },
                gridLines: {
                    color: "rgb(234, 236, 244)",
                    zeroLineColor: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                }
            }],
        },
        legend: {
            display: true
        },
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            titleMarginBottom: 10,
            titleFontColor: '#6e707e',
            titleFontSize: 14,
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            intersect: false,
            mode: 'index',
            caretPadding: 10,
        }
    }
});
</script>
@endpush
