<?php
namespace App\Libraries;

class EmailService {
    private $fromEmail;
    private $fromName;
    
    public function __construct() {
        $this->fromEmail = '<EMAIL>';
        $this->fromName = 'Bacoor OHS System';
    }
    
    /**
     * Send email using <PERSON><PERSON>'s mail function
     * In production, you should use a proper email service like PHPMailer, SendGrid, etc.
     */
    public function sendEmail($to, $subject, $message, $isHtml = true) {
        $headers = [];
        $headers[] = "From: {$this->fromName} <{$this->fromEmail}>";
        $headers[] = "Reply-To: {$this->fromEmail}";
        $headers[] = "X-Mailer: PHP/" . phpversion();
        
        if ($isHtml) {
            $headers[] = "MIME-Version: 1.0";
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        }
        
        $headerString = implode("\r\n", $headers);
        
        // Log email for development (since mail() might not work in local environment)
        $this->logEmail($to, $subject, $message);
        
        // Try to send email
        $result = mail($to, $subject, $message, $headerString);
        
        if (!$result) {
            error_log("Failed to send email to: $to, Subject: $subject");
        }
        
        return $result;
    }
    
    /**
     * Send welcome email with login credentials
     */
    public function sendWelcomeEmail($userEmail, $userName, $password, $role) {
        $subject = "Welcome to Bacoor OHS System - Your Account Details";
        
        $message = $this->getWelcomeEmailTemplate($userName, $userEmail, $password, $role);
        
        return $this->sendEmail($userEmail, $subject, $message, true);
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($userEmail, $userName, $newPassword) {
        $subject = "Bacoor OHS System - Password Reset";
        
        $message = $this->getPasswordResetEmailTemplate($userName, $userEmail, $newPassword);
        
        return $this->sendEmail($userEmail, $subject, $message, true);
    }
    
    /**
     * Generate random password
     */
    public function generateRandomPassword($length = 12) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        $charactersLength = strlen($characters);
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        
        return $password;
    }
    
    /**
     * Log email for development purposes
     */
    private function logEmail($to, $subject, $message) {
        $logEntry = "\n" . str_repeat("=", 80) . "\n";
        $logEntry .= "EMAIL SENT: " . date('Y-m-d H:i:s') . "\n";
        $logEntry .= "TO: $to\n";
        $logEntry .= "SUBJECT: $subject\n";
        $logEntry .= "MESSAGE:\n$message\n";
        $logEntry .= str_repeat("=", 80) . "\n";
        
        error_log($logEntry);
        
        // Also save to a file for easy viewing
        $logFile = ROOT_PATH . '/logs/emails.log';
        if (!file_exists(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Welcome email template
     */
    private function getWelcomeEmailTemplate($userName, $userEmail, $password, $role) {
        $roleDisplayName = ucfirst(str_replace('_', ' ', $role));
        $loginUrl = BASE_URL . 'login';
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Welcome to Bacoor OHS System</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f8f9fa; }
                .credentials { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Welcome to Bacoor OHS System</h1>
                </div>
                <div class='content'>
                    <h2>Hello $userName,</h2>
                    <p>Your account has been created in the Bacoor Occupational Health and Safety System. You have been assigned the role of <strong>$roleDisplayName</strong>.</p>
                    
                    <div class='credentials'>
                        <h3>Your Login Credentials:</h3>
                        <p><strong>Email:</strong> $userEmail</p>
                        <p><strong>Password:</strong> $password</p>
                        <p><strong>Role:</strong> $roleDisplayName</p>
                    </div>
                    
                    <p><strong>Important Security Notes:</strong></p>
                    <ul>
                        <li>Please change your password after your first login</li>
                        <li>Keep your login credentials secure and confidential</li>
                        <li>Do not share your password with anyone</li>
                    </ul>
                    
                    <p style='text-align: center; margin: 30px 0;'>
                        <a href='$loginUrl' class='btn'>Login to Your Account</a>
                    </p>
                    
                    <p>If you have any questions or need assistance, please contact the system administrator.</p>
                </div>
                <div class='footer'>
                    <p>This is an automated message from the Bacoor OHS System.<br>
                    Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * Password reset email template
     */
    private function getPasswordResetEmailTemplate($userName, $userEmail, $newPassword) {
        $loginUrl = BASE_URL . 'login';
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Password Reset - Bacoor OHS System</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f8f9fa; }
                .credentials { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                .btn { display: inline-block; padding: 10px 20px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Password Reset</h1>
                </div>
                <div class='content'>
                    <h2>Hello $userName,</h2>
                    <p>Your password has been reset by the system administrator.</p>
                    
                    <div class='credentials'>
                        <h3>Your New Login Credentials:</h3>
                        <p><strong>Email:</strong> $userEmail</p>
                        <p><strong>New Password:</strong> $newPassword</p>
                    </div>
                    
                    <p><strong>Important:</strong></p>
                    <ul>
                        <li>Please login and change your password immediately</li>
                        <li>This password is temporary and should be changed for security</li>
                        <li>Keep your new password secure and confidential</li>
                    </ul>
                    
                    <p style='text-align: center; margin: 30px 0;'>
                        <a href='$loginUrl' class='btn'>Login Now</a>
                    </p>
                </div>
                <div class='footer'>
                    <p>This is an automated message from the Bacoor OHS System.<br>
                    Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
}
?>
