<?php
namespace App\Services;

/**
 * Enhanced Validation Service
 * 
 * Centralized validation for all application data with business rules
 */
class ValidationService {
    
    private $errors = [];
    private $customMessages = [];
    
    public function __construct() {
        $this->setDefaultMessages();
    }
    
    /**
     * Validate data against rules
     */
    public function validate(array $data, array $rules, array $customMessages = []) {
        $this->errors = [];
        $this->customMessages = array_merge($this->customMessages, $customMessages);
        
        foreach ($rules as $field => $ruleSet) {
            $value = $data[$field] ?? null;
            $this->validateField($field, $value, $ruleSet);
        }
        
        return empty($this->errors);
    }
    
    /**
     * Validate single field
     */
    private function validateField($field, $value, $ruleSet) {
        $rules = is_string($ruleSet) ? explode('|', $ruleSet) : $ruleSet;
        
        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }
    
    /**
     * Apply validation rule
     */
    private function applyRule($field, $value, $rule) {
        $params = [];
        
        // Parse rule with parameters (e.g., "min:5")
        if (strpos($rule, ':') !== false) {
            list($rule, $paramString) = explode(':', $rule, 2);
            $params = explode(',', $paramString);
        }
        
        $methodName = 'validate' . ucfirst($rule);
        
        if (method_exists($this, $methodName)) {
            $result = call_user_func_array([$this, $methodName], array_merge([$value], $params));
            if ($result !== true) {
                $this->addError($field, $rule, $result, $params);
            }
        }
    }
    
    /**
     * Add validation error
     */
    private function addError($field, $rule, $message, $params = []) {
        $key = "{$field}.{$rule}";
        
        if (isset($this->customMessages[$key])) {
            $message = $this->customMessages[$key];
        } elseif (is_string($message)) {
            // Use custom message if provided
        } else {
            $message = $this->getDefaultMessage($field, $rule, $params);
        }
        
        $this->errors[$field][] = $message;
    }
    
    // ==================== Validation Rules ====================
    
    public function validateRequired($value) {
        return !empty($value) || $value === '0' || $value === 0;
    }
    
    public function validateEmail($value) {
        if (empty($value)) return true; // Skip if empty (use required rule separately)
        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public function validateMin($value, $min) {
        if (empty($value)) return true;
        return strlen($value) >= (int)$min;
    }
    
    public function validateMax($value, $max) {
        if (empty($value)) return true;
        return strlen($value) <= (int)$max;
    }
    
    public function validateNumeric($value) {
        if (empty($value)) return true;
        return is_numeric($value);
    }
    
    public function validateInteger($value) {
        if (empty($value)) return true;
        return filter_var($value, FILTER_VALIDATE_INT) !== false;
    }
    
    public function validateAlpha($value) {
        if (empty($value)) return true;
        return ctype_alpha($value);
    }
    
    public function validateAlphaNum($value) {
        if (empty($value)) return true;
        return ctype_alnum($value);
    }
    
    public function validateUrl($value) {
        if (empty($value)) return true;
        return filter_var($value, FILTER_VALIDATE_URL) !== false;
    }
    
    public function validateDate($value) {
        if (empty($value)) return true;
        $date = \DateTime::createFromFormat('Y-m-d', $value);
        return $date && $date->format('Y-m-d') === $value;
    }
    
    public function validateDatetime($value) {
        if (empty($value)) return true;
        $date = \DateTime::createFromFormat('Y-m-d H:i:s', $value);
        return $date && $date->format('Y-m-d H:i:s') === $value;
    }
    
    public function validateIn($value, ...$options) {
        if (empty($value)) return true;
        return in_array($value, $options);
    }
    
    public function validateNotIn($value, ...$options) {
        if (empty($value)) return true;
        return !in_array($value, $options);
    }
    
    public function validateUnique($value, $table, $column = null, $except = null) {
        if (empty($value)) return true;
        
        // This would require database connection - implement based on your needs
        // For now, return true as placeholder
        return true;
    }
    
    public function validatePassword($value) {
        if (empty($value)) return true;
        
        // Password must be at least 8 characters with at least one letter and one number
        return strlen($value) >= 8 && 
               preg_match('/[A-Za-z]/', $value) && 
               preg_match('/[0-9]/', $value);
    }
    
    public function validateStrongPassword($value) {
        if (empty($value)) return true;
        
        // Strong password: 8+ chars, uppercase, lowercase, number, special char
        return strlen($value) >= 8 && 
               preg_match('/[A-Z]/', $value) && 
               preg_match('/[a-z]/', $value) && 
               preg_match('/[0-9]/', $value) && 
               preg_match('/[^A-Za-z0-9]/', $value);
    }
    
    public function validatePhone($value) {
        if (empty($value)) return true;
        
        // Basic phone validation (adjust pattern as needed)
        return preg_match('/^[\+]?[0-9\s\-\(\)]{10,}$/', $value);
    }
    
    public function validateFileType($value, ...$allowedTypes) {
        if (empty($value)) return true;
        
        if (is_array($value) && isset($value['name'])) {
            $extension = strtolower(pathinfo($value['name'], PATHINFO_EXTENSION));
            return in_array($extension, $allowedTypes);
        }
        
        return false;
    }
    
    public function validateFileSize($value, $maxSize) {
        if (empty($value)) return true;
        
        if (is_array($value) && isset($value['size'])) {
            return $value['size'] <= (int)$maxSize;
        }
        
        return false;
    }
    
    // ==================== Business-Specific Validations ====================
    
    public function validateBusinessName($value) {
        if (empty($value)) return true;
        
        // Business name should be 2-100 characters, alphanumeric with spaces and common punctuation
        return strlen($value) >= 2 && 
               strlen($value) <= 100 && 
               preg_match('/^[A-Za-z0-9\s\.\,\-\&\'\"]+$/', $value);
    }
    
    public function validateUserRole($value) {
        $allowedRoles = ['admin', 'inspector', 'business_owner'];
        return in_array($value, $allowedRoles);
    }
    
    public function validateInspectionType($value) {
        $allowedTypes = ['routine', 'follow_up', 'complaint', 'initial', 'emergency', 'annual'];
        return in_array($value, $allowedTypes);
    }
    
    public function validateInspectionStatus($value) {
        $allowedStatuses = ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled'];
        return in_array($value, $allowedStatuses);
    }
    
    public function validateComplianceType($value) {
        $allowedTypes = [
            'business_permit', 'sanitary_permit', 'fire_safety_certificate',
            'environmental_permit', 'safety_signage', 'first_aid',
            'fire_extinguishers', 'cctv', 'waste_segregation'
        ];
        return in_array($value, $allowedTypes);
    }
    
    public function validateComplianceStatus($value) {
        $allowedStatuses = ['pending', 'verified', 'rejected'];
        return in_array($value, $allowedStatuses);
    }
    
    // ==================== Helper Methods ====================
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getFirstError($field = null) {
        if ($field) {
            return isset($this->errors[$field]) ? $this->errors[$field][0] : null;
        }
        
        foreach ($this->errors as $fieldErrors) {
            return $fieldErrors[0];
        }
        
        return null;
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    public function hasError($field) {
        return isset($this->errors[$field]);
    }
    
    private function setDefaultMessages() {
        $this->customMessages = [
            'required' => 'This field is required',
            'email' => 'Please enter a valid email address',
            'min' => 'This field must be at least :min characters',
            'max' => 'This field must not exceed :max characters',
            'numeric' => 'This field must be a number',
            'integer' => 'This field must be an integer',
            'alpha' => 'This field must contain only letters',
            'alphaNum' => 'This field must contain only letters and numbers',
            'url' => 'Please enter a valid URL',
            'date' => 'Please enter a valid date (YYYY-MM-DD)',
            'password' => 'Password must be at least 8 characters with letters and numbers',
            'strongPassword' => 'Password must contain uppercase, lowercase, number, and special character',
            'phone' => 'Please enter a valid phone number',
            'businessName' => 'Business name must be 2-100 characters with valid characters only',
            'userRole' => 'Invalid user role',
            'inspectionType' => 'Invalid inspection type',
            'inspectionStatus' => 'Invalid inspection status',
            'complianceType' => 'Invalid compliance type',
            'complianceStatus' => 'Invalid compliance status'
        ];
    }
    
    private function getDefaultMessage($field, $rule, $params = []) {
        $message = $this->customMessages[$rule] ?? "Invalid {$field}";
        
        // Replace parameter placeholders
        foreach ($params as $index => $param) {
            $message = str_replace(":{$index}", $param, $message);
            $message = str_replace(":{$rule}", $param, $message); // For rules like min:5
        }
        
        return $message;
    }
    
    /**
     * Quick validation methods for common use cases
     */
    public static function validateEmailQuick($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public static function validatePasswordQuick($password) {
        return strlen($password) >= 8 && 
               preg_match('/[A-Za-z]/', $password) && 
               preg_match('/[0-9]/', $password);
    }
    
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}
